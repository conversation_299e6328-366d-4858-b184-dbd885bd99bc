{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Services/GoTRUST.EMR.API/GoTRUST.EMR.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/Services/GoTRUST.EMR.API/GoTRUST.EMR.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/Services/GoTRUST.EMR.API/GoTRUST.EMR.API.csproj"], "problemMatcher": "$msCompile"}, {"label": "Add EF Migration", "type": "shell", "command": "dotnet", "args": ["ef", "migrations", "add", "${input:migrationName}", "-p", "Services/GoTRUST.EMR.Infrastructure", "--startup-project", "Services/GoTRUST.EMR.API", "--context", "ApplicationDbContext"], "group": {"kind": "build", "isDefault": true}, "presentation": {"reveal": "always"}, "problemMatcher": []}], "inputs": [{"id": "migrationName", "type": "promptString", "description": "Enter the migration name"}]}