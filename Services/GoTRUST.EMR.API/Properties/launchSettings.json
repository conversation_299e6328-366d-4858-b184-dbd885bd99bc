{"profiles": {"GoTRUST.EMR.API": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DISCORD_LOGGING_WEBHOOK_URL": "https://discord.com/api/webhooks/1380435688301920339/56DBJAfiqT0qdPu_MZjc4kPBZvDDvDBOrOqAW5BnMzoPSTsMVu75FSLjupxcycZ9sAPy", "DISCORD_LOGGING_WEBHOOK_ID": "1380435688301920339", "DISCORD_LOGGING_WEBHOOK_TOKEN": "56DBJAfiqT0qdPu_MZjc4kPBZvDDvDBOrOqAW5BnMzoPSTsMVu75FSLjupxcycZ9sAPy", "EASY_SIGN_BASE_URL": "http://demosign.easyca.vn:8080", "EASY_SIGN_USERNAME": "demo_easysign", "EASY_SIGN_PASSWORD": "demo_easysign", "IDENTITY_BASE_URL": "https://api-dev.gotrust.vn/identity/v1", "IDENTITY_PROJECT": "GOTRUST_EMR", "IDENTITY_DEVICE_TYPE": "KIOSK", "IDENTITY_MATCHING_THRESHOLD": "82.5", "EMR_BASE_URL": "https://api-dev.gotrust.vn/emr-management", "GO_TRUST_API_BASE_URL": "https://api-dev.gotrust.vn/file-converter", "EIDCA_BASE_URL": "https://api-sandbox.jth.vn", "EIDCA_API_KEY": "********************************", "EIDCA_CUSTOMER_CODE": "DEMO", "CA_SERVICE_BASE_URL": "https://api-dev.gotrust.vn/ca-service", "CA_SERVICE_ACCOUNT": "<EMAIL>", "CA_SERVICE_PASSWORD": "Pa$$w0rd", "PUSH_NOTIFICATION_BASE_URL": "https://api-dev.gotrust.vn/medipay-mobile/api/users"}, "applicationUrl": "https://localhost:56436;http://localhost:56437"}}}