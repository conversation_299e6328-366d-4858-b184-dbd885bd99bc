using BuildingBlocks.Exceptions.Handler;
using BuildingBlocks.HttpClients.Discords;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.OpenApi.Models;
using Serilog.Events;
using Serilog;
using Serilog.Sinks.Discord;
using Refit;
using GoTRUST.EMR.Application.Globals;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Amazon.S3;
using Hangfire;
using GoTRUST.EMR.Application.Jobs;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Common.EasySign.Extensions;
using BuildingBlocks.Common.IdentityClient.Extensions;
using BuildingBlocks.Common.GoTrustApi.Extensions;
using BuildingBlocks.Common.PushNotification.Extensions;
using BuildingBlocks.Common.Interface;
using BuildingBlocks.Common.Service;
using BuildingBlocks.Common.EMRApi.Extensions;
using BuildingBlocks.Common.EidCAGtel.Extensions;
using BuildingBlocks.Common.CAService.Extensions;

namespace GoTRUST.EMR.API;

public static class DependencyInjection
{
    private static readonly string[] notAdminRoles = [
                    "HOSPITAL_DIRECTOR",
                    "PLANNING_DEPARTMENT",
                    "NURSE_DOCTOR",
                    "IT_DEPARTMENT",
                    "PATIENT",
                    "MEDICAL_RECORD_REQUESTOR"
                ];

    public static IServiceCollection AddApiServices(this IServiceCollection services,
        IConfiguration configuration,
        ConfigureHostBuilder host)
    {
        // Log configuration
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .WriteTo.Async(a => a.Console())
            .WriteTo.Async(a => a.Discord(
                ulong.Parse(configuration["DISCORD_LOGGING_WEBHOOK_ID"]!),
                configuration["DISCORD_LOGGING_WEBHOOK_TOKEN"],
                restrictedToMinimumLevel: LogEventLevel.Error))
            .CreateLogger();

        host.UseSerilog();

        // Add swagger
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(opt =>
        {
            opt.SwaggerDoc("v1", new OpenApiInfo { Title = "GoTRUST.EMR.API", Version = "v1" });
            opt.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                In = ParameterLocation.Header,
                Description = "Please enter token",
                Name = "Authorization",
                Type = SecuritySchemeType.Http,
                BearerFormat = "JWT",
                Scheme = "bearer"
            });
            opt.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });
        });

        services.AddSingleton<IAmazonS3>(sp =>
        {
            var accessKey = configuration["S3_AccessKey"];
            var secretKey = configuration["S3_SecretKey"];
            return new AmazonS3Client(accessKey, secretKey, new AmazonS3Config
            {
                ServiceURL = configuration["S3_ServiceUrl"],
                ForcePathStyle = true
            });
        });
        services.AddSignalR();

        services.AddCarter();
        services.AddExceptionHandler<CustomExceptionHandler>();
        services.AddHealthChecks()
            .AddNpgSql(configuration.GetConnectionString("Database")!);
        services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(options =>
        {
            options.SaveToken = true;
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JwtKey"]!)),
                ValidIssuer = configuration["JwtIssuer"],
                ValidateIssuer = true,
                ValidAudience = configuration["JwtAudience"],
                ValidateAudience = true,
            };

            options.Events = new JwtBearerEvents
            {
                OnMessageReceived = context =>
                {
                    var accessToken = context.Request.Query["access_token"];

                    // If the request is for our hub...
                    var path = context.HttpContext.Request.Path;
                    if (!string.IsNullOrEmpty(accessToken) &&
                        path.StartsWithSegments("/notification", StringComparison.OrdinalIgnoreCase))
                    {
                        // Read the token out of the query string
                        context.Token = accessToken;
                    }
                    return Task.CompletedTask;
                }
            };
        });
        services.Configure<IdentityOptions>(options =>
        {
            options.Password.RequireDigit = false;
            options.Password.RequiredLength = 1;
            options.Password.RequireLowercase = false;
            options.Password.RequireNonAlphanumeric = false;
            options.Password.RequireUppercase = false;
            options.Password.RequiredUniqueChars = 0;
        });

        services.AddAuthorizationBuilder()
            .AddPolicy(PolicyNames.REQUIRE_HOSPITAL_DIRECTOR_ROLE, policy => policy.RequireRole("HOSPITAL_DIRECTOR"))
            .AddPolicy(PolicyNames.REQUIRE_PLANNING_DEPARTMENT_ROLE, policy => policy.RequireRole("PLANNING_DEPARTMENT"))
            .AddPolicy(PolicyNames.REQUIRE_NURSE_DOCTOR_ROLE, policy => policy.RequireRole("NURSE_DOCTOR"))
            .AddPolicy(PolicyNames.REQUIRE_IT_DEPARTMENT_ROLE, policy => policy.RequireRole("IT_DEPARTMENT"))
            .AddPolicy(PolicyNames.REQUIRE_PATIENT_ROLE, policy => policy.RequireRole("PATIENT"))
            .AddPolicy(PolicyNames.REQUIRE_MEDICAL_RECORD_REQUESTOR_ROLE, policy => policy.RequireRole("MEDICAL_RECORD_REQUESTOR"))
            .AddPolicy(PolicyNames.REQUIRE_ADMIN_ROLE, policy => policy.RequireRole("ADMIN"))
            .AddPolicy(PolicyNames.REQUIRE_NOT_ADMIN_ROLE, policy => policy.RequireRole(notAdminRoles));

        //Refit client
        services.AddRefitClient<IDiscordWebhookClient>()
            .ConfigureHttpClient(c =>
            {
                c.BaseAddress = new Uri(configuration["DISCORD_LOGGING_WEBHOOK_URL"]!);
            });

        services.AddEasySign(c =>
        {
            c.BaseUrl = configuration["EASY_SIGN_BASE_URL"]!;
            c.Username = configuration["EASY_SIGN_USERNAME"]!;
            c.Password = configuration["EASY_SIGN_PASSWORD"]!;
            c.TimeoutSeconds = 60;
        });

        services.AddIdentityClient(c =>
        {
            c.BaseUrl = configuration["IDENTITY_BASE_URL"]!;
            c.Project = configuration["IDENTITY_PROJECT"]!;
            c.DeviceType = configuration["IDENTITY_DEVICE_TYPE"]!;
            c.TimeoutSeconds = 60;
            c.MatchingThreshold = 82.5;
        });

        services.AddGoTrustApi(c =>
        {
            c.BaseUrl = configuration["GO_TRUST_API_BASE_URL"]!;
            c.TimeoutSeconds = 60;
        });

        services.AddPushNotification(c =>
        {
            c.BaseUrl = configuration["PUSH_NOTIFICATION_BASE_URL"]!;
            c.TimeoutSeconds = 60;
        });

        services.AddEidca(c =>
        {
            c.BaseUrl = configuration["EIDCA_BASE_URL"]!;
            c.ApiKey = configuration["EIDCA_API_KEY"]!;
            c.CustomerCode = configuration["EIDCA_CUSTOMER_CODE"]!;
            c.TimeoutSeconds = 60;
        });

        services.AddCAService(c =>
        {
            c.BaseUrl = configuration["CA_SERVICE_BASE_URL"]!;
            c.Account = configuration["CA_SERVICE_ACCOUNT"];
            c.Password = configuration["CA_SERVICE_PASSWORD"];
            c.TimeoutSeconds = 60;
        });

        services.AddScoped<IFileConverterService, FileConverterService>();

        services.AddInterHospitalEMRApi(c =>
        {
            c.BaseUrl = configuration["EMR_BASE_URL"]!;
            c.TimeoutSeconds = 60;
        });

        services.AddCors(options =>
        {
            options.AddPolicy(name: "CorsPolicy",
                            policy =>
                            {
                                policy.AllowAnyOrigin();
                                policy.AllowAnyHeader();
                                policy.AllowAnyMethod();
                            });
            options.AddPolicy(name: "SignalRPolicy",
                            policy =>
                            {
                                policy.WithOrigins(configuration["ClientUrl"] ?? string.Empty);
                                policy.AllowAnyHeader();
                                policy.AllowAnyMethod();
                                policy.AllowCredentials();
                            });
            options.AddPolicy(name: "TestCorsPolicy",
                            policy =>
                            {
                                policy.SetIsOriginAllowed(origin => true);
                                policy.AllowAnyHeader();
                                policy.AllowAnyMethod();
                                policy.AllowCredentials();
                            });
        });
        return services;
    }

    public static WebApplication UseApiServices(this WebApplication app)
    {
        app.UseSwagger();
        app.UseSwaggerUI();
        app.UseCors("CorsPolicy");
        app.UseAuthentication();
        app.UseAuthorization();

        app.UseExceptionHandler(options => { });
        app.UseHealthChecks("/health",
            new HealthCheckOptions
            {
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });
        app.MapCarter();

        app.UseHangfireDashboard("/hangfire");
        using (var scope = app.Services.CreateScope())
        {
            var jobManager = scope.ServiceProvider.GetRequiredService<IRecurringJobManager>();

            jobManager.AddOrUpdate<ForcePasswordChangeJob>(
                "force-password-change",
                x => x.ExecuteAsync(default),
                "0 0 * * *",
                new RecurringJobOptions
                {
                    TimeZone = TimeZoneInfo.Local,
                });
            jobManager.AddOrUpdate<DeleteFileSystemNodeJob>(
                "delete-file-system-node",
                x => x.ExecuteAsync(default),
                "0 0 * * *",
                new RecurringJobOptions
                {
                    TimeZone = TimeZoneInfo.Local,
                });
        }
        return app;
    }
}