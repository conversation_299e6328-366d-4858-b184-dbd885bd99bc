{"ConnectionStrings": {"Database": "Server=**************;User Id=MasterUsr;Password=***************;Port=5432;Database=GoTRUST.EMR.API.Staging;Pooling=true"}, "FeatureManagement": {"OrderFullfilment": false}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "JwtTokenExpireInMinutes": "1000", "JwtRefreshTokenExpireInMinutes": "43200", "JwtKey": "This is my custom Secret key for authentication and authorization", "JwtIssuer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "JwtAudience": "EMRAudience", "ClientUrl": "https://emr-dev.gotrust.vn", "EmailHost": "mail.gotrust.vn", "EmailPort": "587", "EmailPassword": "trung.nq@", "EmailFromMail": "<EMAIL>", "EmailDisplayName": "GoTRUST EMR", "UpdatePasswordExpireInMinutes": "10080", "UpdatePasswordEmailSubject": "[EMR GoTRUST] Đặt mật khẩu mới", "2FAEmailSubject": "[EMR GoTRUST] Mã xác thực 2FA", "TwoFactorExpireTime": "600", "S3_AccessKey": "MVT04V942I425XYUZ3B3", "S3_SecretKey": "VJf2Is1G4e7L8Vm3FhHdueLDTWXu9waisdw1VsGh", "S3_ServiceUrl": "https://s3.cloud.cmctelecom.vn", "S3_BucketName": "emr", "S3_KeyPrefix": "emr-pdf", "File_SercretKey": "X7LQ2M9ADTB1ZVKC", "File_Watermark": "GoTRUST EMR", "MongoDatabaseUrl": "**********************************************", "MongoDatabaseHangfire": "EMRHangfire"}