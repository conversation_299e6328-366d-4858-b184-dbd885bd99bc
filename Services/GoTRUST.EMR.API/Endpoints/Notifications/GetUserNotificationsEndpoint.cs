using GoTRUST.EMR.Application.Features.Notifications.Queries;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.API.Endpoints.Notifications;

public record GetUserNotificationsQueryParams : PaginationRequest
{
    public bool? IsSortByStatus { get; init; }
    public NotificationType? FilterType { get; init; }
}

public class GetUserNotificationsEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/notifications/user", async ([AsParameters] GetUserNotificationsQueryParams queryParams, ISender sender) =>
        {
            var request = new GetUserNotificationsQuery
            {
                IsSortByStatus = queryParams.IsSortByStatus,
                FilterType = queryParams.FilterType,
                PageIndex = queryParams.PageIndex,
                PageSize = queryParams.PageSize
            };
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization()
        .WithTags("Notifications")
        .WithName("GetUserNotifications")
        .Produces<PaginationResponse<GetUserNotificationsResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get user notifications")
        .WithDescription("Retrieves notifications for the current user with optional filters and pagination.");
    }
}
