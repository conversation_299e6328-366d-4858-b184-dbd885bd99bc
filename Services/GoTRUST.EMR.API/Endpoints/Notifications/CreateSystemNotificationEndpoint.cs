using GoTRUST.EMR.Application.Features.Notifications.Commands;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.Notifications;

public class CreateSystemNotificationEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/notifications/system", async (CreateSystemNotificationRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = "MSC" })
        .WithTags("Notifications")
        .WithName("CreateSystemNotification")
        .Produces<Response<CreateSystemNotificationResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Create a system notification")
        .WithDescription("Creates a new system notification and returns the result.");
    }
}
