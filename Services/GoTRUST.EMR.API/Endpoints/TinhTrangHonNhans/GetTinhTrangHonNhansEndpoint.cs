using GoTRUST.EMR.Application.Features.TinhTrangHonNhans.Queries;

namespace GoTRUST.EMR.API.Endpoints.TinhTrangHonNhans;

public class GetTinhTrangHonNhansEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/tinhtranghonnhan", async (ISender sender) =>
        {
            var result = await sender.Send(new GetTinhTrangHonNhansRequest());
            return Results.Ok(result);
        })
        .WithTags("TinhTrangHonNhans")
        .WithName("GetTinhTrangHonNhans")
        .Produces<Response<List<GetTinhTrangHonNhansResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get all TinhTrangHonNhans")
        .WithDescription("Returns a list of all TinhTrangHonNhans.");
    }
}
