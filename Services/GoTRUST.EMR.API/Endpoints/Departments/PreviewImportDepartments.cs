using GoTRUST.EMR.Application.Features.Departments.Commands;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.Departments;

public class PreviewImportDepartments : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/departments/preview-import", async (
            IFormFile file,
            ISender sender) =>
        {
            var request = new PreviewImportDepartmentsRequest(file);
            var result = await sender.Send(request);
            
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .Accepts<PreviewImportDepartmentsRequest>("multipart/form-data")
        .RequireAuthorization(new AuthorizeAttribute { Roles = "DMR" })
        .WithTags("Departments")
        .WithName("ReviewImportDepartments")
        .Produces<Response<PreviewImportDepartmentsResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Review import danh sách khoa từ Excel")
        .WithDescription("Upload file Excel/CSV để review danh sách khoa trước khi import. " +
                         "File cần có các cột: STT, Tên khoa theo chuẩn, Mã khoa theo chuẩn, " +
                         "Tên khoa theo bệnh viện, Mã khoa theo bệnh viện, Mô tả");
    }
}
