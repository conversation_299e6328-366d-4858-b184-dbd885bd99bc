using GoTRUST.EMR.Application.Features.Departments.Commands;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.Departments;

public class CreateDepartment : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/departments", async (CreateDepartmentRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Created($"/departments/{result.Data!.Id}", result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.DMC })
        .WithTags("Departments")
        .WithName("CreateDepartment")
        .Produces<Response<CreateDepartmentResponse>>(StatusCodes.Status201Created)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Tạo khoa mới")
        .WithDescription("Tạo khoa mới trong hệ thống");
    }
}