using GoTRUST.EMR.Application.Features.Departments.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Departments;

public class GetDepartmentById : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/departments/{id:guid}", async (Guid id, ISender sender) =>
        {
            var request = new GetDepartmentByIdRequest(id);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.DMR })
        .WithTags("Departments")
        .WithName("GetDepartmentById")
        .Produces<Response<GetDepartmentByIdResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Lấy thông tin chi tiết khoa")
        .WithDescription("<PERSON><PERSON>y thông tin chi tiết của một khoa theo Id");
    }
}
