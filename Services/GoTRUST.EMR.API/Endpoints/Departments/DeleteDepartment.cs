using GoTRUST.EMR.Application.Features.Departments.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Departments;

public class DeleteDepartment : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapDelete("/departments/{id:guid}", async (Guid id, ISender sender) =>
        {
            var request = new DeleteDepartmentRequest(id);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.DMD })
        .WithTags("Departments")
        .WithName("DeleteDepartment")
        .Produces<Response<DeleteDepartmentResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Xóa khoa")
        .WithDescription("Xóa một khoa khỏi hệ thống (soft delete)")
        .RequireAuthorization();
    }
}
