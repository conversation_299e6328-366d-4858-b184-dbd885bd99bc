using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.Departments.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Departments;

public class GetAllDepartments : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/departments", async (
            ISender sender, [AsParameters] GetAllDepartmentsRequest request) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.DMR })
        .WithTags("Departments")
        .WithName("GetAllDepartments")
        .Produces<Response<List<DepartmentDto>>>(StatusCodes.Status200OK)
        .WithSummary("Lấy danh sách khoa")
        .WithDescription("Lấy danh sách khoa")
        .RequireAuthorization();
    }
}
