using GoTRUST.EMR.Application.Features.Departments.Commands;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.Departments;

public class SyncDepartments : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/departments/sync", async (
            SyncDepartmentsRequest request, 
            ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = "DMC" })
        .WithTags("Departments")
        .WithName("SyncDepartments")
        .Produces<Response<SyncDepartmentsResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Đồng bộ danh sách khoa")
        .WithDescription("Đồng bộ danh sách khoa vào hệ thống. " +
                         "Nếu khoa chưa tồn tại (dựa trên mã khoa) thì tạo mới, " +
                         "nếu đã tồn tại thì cập nhật thông tin. " +
                         "Nếu tên/mã khoa theo bệnh viện để trống sẽ tự động lấy tên/mã chuẩn.");
    }
}
