using GoTRUST.EMR.Application.Features.Departments.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Departments;

public class UpdateDepartment : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPut("/departments/{id:guid}", async (Guid id, UpdateDepartmentRequest request, ISender sender) =>
        {
            if (id != request.Id)
                return Results.BadRequest("Id trong URL không khớp với Id trong body");

            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.DMU })
        .WithTags("Departments")
        .WithName("UpdateDepartment")
        .Produces<Response<UpdateDepartmentResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Cập nhật thông tin khoa")
        .WithDescription("Cập nhật thông tin của một khoa")
        .RequireAuthorization();
    }
}
