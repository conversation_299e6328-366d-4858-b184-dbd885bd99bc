using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Features.BorrowRequests.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.BorrowRequest
{
    public class AsyncBorrowRequestEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapPost("/borrow-requests/async", async (AsyncBorrowRequestRequest request, ISender sender) =>
            {
                var response = await sender.Send(request);
                return Results.Ok(response);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMU })
            .WithTags("BorrowRequest")
            .WithName("AsyncBorrowRequest")
            .Produces<Response<AsyncBorrowRequestResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Asynchronously process a borrow request")
            .WithDescription("Processes a borrow request asynchronously and returns the updated request.");
        }
    }
}