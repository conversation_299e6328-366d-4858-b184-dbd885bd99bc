using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Features.BorrowRequests.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.BorrowRequest
{
    public record GetBorrowRequestsRequestBody : PaginationWithSortRequest
    {
        public string? Search { get; set; }
        public string? Status { get; set; }
    }
    public class GetBorrowRequestEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/borrow-requests", async ([AsParameters] GetBorrowRequestsRequestBody request, ISender sender) =>
            {
                var req = new GetBorrowRequestsRequest
                {
                    Search = request.Search,
                    Status = request.Status,
                    PageIndex = request.PageIndex,
                    PageSize = request.PageSize,
                    SortBy = request.SortBy,
                    SortDescending = request.SortDescending
                };
                var result = await sender.Send(req);
                return Results.Ok(result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMR })
            .WithTags("BorrowRequest")
            .WithName("GetBorrowRequest")
            .Produces<PaginationResponse<BorrowRequestResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Lấy thông tin yêu cầu mượn")
            .WithDescription("Lấy thông tin yêu cầu mượn theo ID. Optional filters: null means no filter, true means sort ascending, false means sort descending");
        }
    }
}