using GoTRUST.EMR.Application.Features.BorrowRequests.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.BorrowRequest
{
    public record RejectBorrowRequestBody(string Reason);
    public class RejectBorrowRequestEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapPut("/borrow-requests/{id}/reject", async (Guid id, RejectBorrowRequestBody body, ISender sender) =>
            {
                var result = await sender.Send(new RejectBorrowRequestCommand { BorrowRequestId = id, Reason = body.Reason });
                return Results.Ok(result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMA })
            .WithTags("BorrowRequest")
            .WithName("RejectBorrowRequest")
            .Produces<Response<RejectBorrowRequestResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Reject a borrow request")
            .WithDescription("Rejects a borrow request and returns the updated request.");
        }
    }
}