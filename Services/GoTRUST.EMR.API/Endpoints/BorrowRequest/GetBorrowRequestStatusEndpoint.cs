using GoTRUST.EMR.Application.Features.BorrowRequests.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;


namespace GoTRUST.EMR.API.Endpoints.BorrowRequest
{
    public class GetBorrowRequestStatusEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/borrow-request/statuses", async (ISender sender) =>
            {
                var result = await sender.Send(new GetBorrowRequestStatusQuery());
                return Results.Ok(result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMR })
            .WithTags("BorrowRequest")
            .WithName("GetBorrowRequestStatus")
            .Produces<Response<List<BorrowRequestStatusDto>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("<PERSON><PERSON><PERSON> danh sách loại yêu cầu mượn")
            .WithDescription("<PERSON><PERSON><PERSON> danh sách loại yêu cầu mượn");
        }
    }
}