using GoTRUST.EMR.Application.Features.BorrowRequests.Commands;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;


namespace GoTRUST.EMR.API.Endpoints.BorrowRequest
{
    public class RevokeBorrowRequestEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapPut("/borrow-requests/{id}/revoke", async (Guid id, ISender sender) =>
            {
                var result = await sender.Send(new RevokeBorrowRequestCommand { BorrowRequestId = id });
                return Results.Ok(result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMA })
            .WithTags("BorrowRequest")
            .WithName("RevokeBorrowRequest")
            .Produces<Response<RevokeBorrowRequestResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Revoke a borrow request")
            .WithDescription("Revokes a borrow request and returns the updated request.");
        }
    }
}