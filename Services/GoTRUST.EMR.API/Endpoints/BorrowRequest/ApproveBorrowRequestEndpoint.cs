using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Features.BorrowRequests.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.BorrowRequest
{
    public class ApproveBorrowRequestEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapPut("/borrow-requests/{id}/approve", async (Guid id, ISender sender) =>
            {
                var result = await sender.Send(new ApproveBorrowRequestCommand { BorrowRequestId = id });
                return Results.Ok(result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMA })
            .WithTags("BorrowRequest")
            .WithName("ApproveBorrowRequest")
            .Produces<Response<ApproveBorrowRequestResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Approve a borrow request")
            .WithDescription("Approves a borrow request and returns the updated request.");
        }
    }
}