using GoTRUST.EMR.Application.Features.BorrowRequests.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;


namespace GoTRUST.EMR.API.Endpoints.BorrowRequest
{
    public class GetBorrowRequestTypeEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/borrow-request/types", async (ISender sender) =>
            {
                var result = await sender.Send(new GetBorrowRequestTypeQuery());
                return Results.Ok(result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMR })
            .WithTags("BorrowRequest")
            .WithName("GetBorrowRequestType")
            .Produces<Response<List<BorrowRequestReasonDto>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("L<PERSON>y danh sách loại yêu cầu mượn")
            .WithDescription("<PERSON><PERSON><PERSON> danh sách loại yêu cầu mượn");
        }
    }
}