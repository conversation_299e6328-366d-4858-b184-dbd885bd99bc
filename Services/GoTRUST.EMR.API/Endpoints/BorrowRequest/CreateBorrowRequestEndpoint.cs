using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Features.BorrowRequests.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.BorrowRequest
{
    public class CreateBorrowRequestEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapPost("/borrow-requests", async (CreateBorrowRequestRequest command, IMediator mediator) =>
            {
                var result = await mediator.Send(command);
                return Results.Created($"/borrow-requests", result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
            .WithTags("BorrowRequest")
            .WithName("CreateBorrowRequest")
            .Produces<Response<List<CreateBorrowRequestResponse>>>(StatusCodes.Status201Created)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Tạo yêu cầu mượn hàng loạt")
            .WithDescription("Tạo nhiều yêu cầu mượn tài liệu y tế từ kho lưu trữ của b<PERSON>nh viện.");
        }
    }
}