using GoTRUST.EMR.Application.Features.BorrowerOrganizations.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.BorrowerOrganizations;

public class GetBorrowerOrganizationDetailEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/borrower-organizations/{id:guid}", async (Guid id, ISender sender) =>
        {
            var request = new GetBorrowerOrganizationDetailQuery(id);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSR })
        .WithTags("BorrowerOrganizations")
        .WithName("GetBorrowerOrganizationDetail")
        .Produces<Response<GetBorrowerOrganizationDetailResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get borrower organization detail")
        .WithDescription("Returns the detail of a specific borrower organization.");
    }
}
