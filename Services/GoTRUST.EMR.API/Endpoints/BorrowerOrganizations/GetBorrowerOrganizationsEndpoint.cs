using GoTRUST.EMR.Application.Features.BorrowerOrganizations.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.BorrowerOrganizations;

public record GetBorrowerOrganizationsParameters : PaginationRequest
{
    public string? SearchTerm { get; init; }
}

public class GetBorrowerOrganizationsEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/borrower-organizations", async ([AsParameters] GetBorrowerOrganizationsParameters req, ISender sender) =>
        {
            var result = await sender.Send(new GetBorrowerOrganizationQuery
            {
                SearchTerm = req.SearchTerm,
                PageIndex = req.PageIndex,
                PageSize = req.PageSize
            });
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSR })
        .WithTags("BorrowerOrganizations")
        .WithName("GetBorrowerOrganizations")
        .Produces<PaginationResponse<GetBorrowerOrganizationResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get all borrower organizations")
        .WithDescription("Returns a list of all borrower organizations.");
    }
}
