using GoTRUST.EMR.Application.Features.BorrowerOrganizations.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.BorrowerOrganizations;

public class CreateBorrowerOrganizationEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/borrower-organizations", async ([FromForm] CreateBorrowerOrganizationRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSC })
        .WithTags("BorrowerOrganizations")
        .WithName("CreateBorrowerOrganization")
        .Produces<Response<CreateBorrowerOrganizationResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Create a borrower organization")
        .WithDescription("Creates a new borrower organization and returns the created organization.");
    }
}
