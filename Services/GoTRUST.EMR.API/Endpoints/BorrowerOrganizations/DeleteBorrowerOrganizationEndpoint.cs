using GoTRUST.EMR.Application.Features.BorrowerOrganizations.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.BorrowerOrganizations;

public class DeleteBorrowerOrganizationEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapDelete("/borrower-organizations/{id:guid}", async (Guid id, ISender sender) =>
        {
            var request = new DeleteBorrowerOrganizationRequest(id);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSD })
        .WithTags("BorrowerOrganizations")
        .WithName("DeleteBorrowerOrganization")
        .Produces<Response<DeleteBorrowerOrganizationResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Delete a borrower organization")
        .WithDescription("Deletes a borrower organization by id.");
    }
}
