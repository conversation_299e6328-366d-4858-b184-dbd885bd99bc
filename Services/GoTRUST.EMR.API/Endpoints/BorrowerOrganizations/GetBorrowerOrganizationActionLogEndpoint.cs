using GoTRUST.EMR.Application.Features.BorrowerOrganizations.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.BorrowerOrganizations;

public record GetBorrowerOrganizationActionLogQueryParams : PaginationRequest
{
    public string? SearchTerm { get; init; }
    public DateTime? SearchDate { get; init; }
}


public class GetBorrowerOrganizationActionLogEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/borrower-organizations/{id:guid}/action-logs", async (
            Guid id,
            [AsParameters] GetBorrowerOrganizationActionLogQueryParams queryParams,
            ISender sender) =>
        {
            var request = new GetBorrowerOrganizationActionLogQuery
            {
                BorrowerOrganizationId = id,
                SearchTerm = queryParams.SearchTerm,
                SearchDate = queryParams.SearchDate,
                PageIndex = queryParams.PageIndex,
                PageSize = queryParams.PageSize
            };
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSR })
        .WithTags("BorrowerOrganizations")
        .WithName("GetBorrowerOrganizationActionLog")
        .Produces<PaginationResponse<GetBorrowerOrganizationActionLogResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get borrower organization action logs")
        .WithDescription("Retrieves the action logs for a specific borrower organization.");
    }
}
