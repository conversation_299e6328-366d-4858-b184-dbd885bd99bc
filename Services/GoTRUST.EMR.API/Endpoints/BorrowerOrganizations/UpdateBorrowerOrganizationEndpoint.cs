using GoTRUST.EMR.Application.Features.BorrowerOrganizations.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.BorrowerOrganizations;

public class UpdateBorrowerOrganizationRequestBody
{
    public string? ContactPerson { get; init; }
    public Guid? RoleId { get; init; }
    public IFormFile? Avatar { get; init; }
    public DateTime DateOfBirth { get; init; }
    public string? Gender { get; init; }
    public string? CitizenId { get; init; }
    public DateTime? CitizenIdIssueDate { get; init; }
    public string? MaritalStatusId { get; init; }
    public string? PhoneNumber { get; init; }
    public string? ReligionId { get; init; }
    public string? EthnicityId { get; init; }
    public string? Address { get; init; }
    public string? TemporaryAddress { get; init; }
    public string? Name { get; init; }
    public string? OfficeUnit { get; init; }
    public string? Position { get; init; }
    public string? Note { get; init; }
}

public class UpdateBorrowerOrganizationEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPut("/borrower-organizations/{id:guid}", async (Guid id, [FromForm] UpdateBorrowerOrganizationRequestBody body, ISender sender) =>
        {
            var request = new UpdateBorrowerOrganizationRequest
            {
                Id = id,
                ContactPerson = body.ContactPerson ?? string.Empty,
                RoleId = body.RoleId,
                Avatar = body.Avatar,
                DateOfBirth = body.DateOfBirth,
                Gender = body.Gender,
                CitizenId = body.CitizenId,
                CitizenIdIssueDate = body.CitizenIdIssueDate,
                MaritalStatusId = body.MaritalStatusId,
                PhoneNumber = body.PhoneNumber,
                ReligionId = body.ReligionId,
                EthnicityId = body.EthnicityId,
                Address = body.Address,
                TemporaryAddress = body.TemporaryAddress,
                Name = body.Name,
                OfficeUnit = body.OfficeUnit,
                Position = body.Position,
                Note = body.Note
            };
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSU })
        .WithTags("BorrowerOrganizations")
        .WithName("UpdateBorrowerOrganization")
        .Produces<Response<UpdateBorrowerOrganizationResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Update a borrower organization")
        .WithDescription("Updates an existing borrower organization and returns the updated organization.");
    }
}
