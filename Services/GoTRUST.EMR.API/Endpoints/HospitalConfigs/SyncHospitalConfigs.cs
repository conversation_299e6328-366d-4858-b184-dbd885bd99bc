using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.HospitalConfigs.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.HospitalConfigs;

public class SyncHospitalConfigs : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/hospital-configs/sync", async (SyncHospitalConfigsRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = $"{RoleConstants.SEC},{RoleConstants.SEU}" })
        .WithTags("HospitalConfigs")
        .WithName("SyncHospitalConfigs")
        .Produces<Response<SyncHospitalConfigsResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .ProducesProblem(StatusCodes.Status500InternalServerError)
        .WithSummary("Đồng bộ cấu hình bệnh viện")
        .WithDescription("Đồng bộ (tạo mới hoặc cập nhật) danh sách cấu hình cho một bệnh viện")
        .RequireAuthorization();
    }
}
