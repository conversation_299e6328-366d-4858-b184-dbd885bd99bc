using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.HospitalConfigs.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.HospitalConfigs;

public class GetHospitalConfigs : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/hospital-configs", async (ISender sender) =>
        {
            var result = await sender.Send(new GetHospitalConfigsRequest());
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.SER })
        .WithTags("HospitalConfigs")
        .WithName("GetHospitalConfigs")
        .Produces<Response<List<HospitalConfigDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("<PERSON><PERSON><PERSON> danh sách cấu hình bệnh viện")
        .WithDescription("Lấy tất cả cấu hình của một bệnh viện")
        .RequireAuthorization();
    }
}
