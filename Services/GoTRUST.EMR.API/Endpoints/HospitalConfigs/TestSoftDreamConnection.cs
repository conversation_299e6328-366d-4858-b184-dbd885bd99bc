using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.HospitalConfigs.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.HospitalConfigs;

/// <summary>
/// Request body cho test SoftDream connection
/// </summary>
public record TestSoftDreamConnectionRequestBody(
    string? Username = null,
    string? Password = null
);

/// <summary>
/// Endpoint để test kết nối SoftDream
/// </summary>
public class TestSoftDreamConnection : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/hospital-configs/test-softdream-connection", async (
            TestSoftDreamConnectionRequestBody? requestBody,
            ISender sender) =>
        {
            var request = new TestSoftDreamConnectionRequest(
                requestBody?.Username,
                requestBody?.Password
            );

            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.SER })
        .WithTags("HospitalConfigs")
        .WithName("TestSoftDreamConnection")
        .Produces<Response<TestSoftDreamConnectionResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Test kết nối SoftDream")
        .WithDescription("Kiểm tra xem thông tin đăng nhập SoftDream có hợp lệ không. Có thể truyền username/password trong request body hoặc sử dụng thông tin từ cấu hình bệnh viện.")
        .RequireAuthorization();
    }
}
