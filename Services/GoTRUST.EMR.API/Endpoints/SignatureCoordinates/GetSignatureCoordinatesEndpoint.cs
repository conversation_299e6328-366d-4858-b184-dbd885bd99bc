using GoTRUST.EMR.Application.Features.SignatureCoordinates.Queries;
using MediatR;
using Carter;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.API.Endpoints.SignatureCoordinates;

public class GetSignatureCoordinatesEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/medical-record-templates/{templateId:guid}/signature-coordinates",
            async (Guid templateId, ISender sender) =>
            {
                var result = await sender.Send(new GetSignatureCoordinatesQuery(templateId));
                return Results.Ok(result);
            })
            .RequireAuthorization()
            .WithTags("SignatureCoordinates")
            .WithName("GetSignatureCoordinates")
            .Produces<Response<List<SignatureCoordinateDto>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .WithSummary("Get signature coordinates for medical record template")
            .WithDescription("Retrieves signature coordinates for a specific medical record template based on the current user's role(s). Returns coordinates where the user has permission to sign.");
    }
}
