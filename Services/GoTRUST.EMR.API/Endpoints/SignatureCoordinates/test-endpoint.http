### Test SignatureCoordinates Endpoint
### Requires: 
### 1. Running GoTRUST.EMR.API server
### 2. Valid JWT token from login
### 3. Sample data in database

### Variables
@baseUrl = https://localhost:56436/emr-management
@templateId = 3fa85f64-5717-4562-b3fc-2c963f66afa6
@token = YOUR_JWT_TOKEN_HERE

### 1. Test GET SignatureCoordinates - Success Case
GET {{baseUrl}}/medical-record-templates/{{templateId}}/signature-coordinates
Authorization: Bearer {{token}}
Content-Type: application/json

### 2. Test GET SignatureCoordinates - Invalid Template ID
GET {{baseUrl}}/medical-record-templates/invalid-guid/signature-coordinates
Authorization: Bearer {{token}}
Content-Type: application/json

### 3. Test GET SignatureCoordinates - Unauthorized (no token)
GET {{baseUrl}}/medical-record-templates/{{templateId}}/signature-coordinates
Content-Type: application/json

### 4. Test GET SignatureCoordinates - Invalid Token
GET {{baseUrl}}/medical-record-templates/{{templateId}}/signature-coordinates
Authorization: Bearer invalid_token_here
Content-Type: application/json

### Sample Response Format:
### {
###   "data": [
###     {
###       "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
###       "medicalRecordTemplateId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
###       "roleId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
###       "name": "Chữ ký bác sĩ",
###       "coordinateX": 100,
###       "coordinateY": 200,
###       "pageNumber": 1,
###       "width": 150,
###       "height": 50,
###       "allowAutoSign": true,
###       "roleName": "Bác sĩ",
###       "templateName": "Phiếu khám bệnh"
###     }
###   ],
###   "isSuccess": true,
###   "message": null
### }

### To get a valid JWT token, first login:
### POST {{baseUrl}}/authenticates/login
### Content-Type: application/json
### 
### {
###   "email": "<EMAIL>",
###   "password": "your_password"
### }

### To create sample data, you can use SQL:
### INSERT INTO "MedicalRecordTemplates" ("Id", "TemplateName", "CreatedAt", "IsDeleted") 
### VALUES ('3fa85f64-5717-4562-b3fc-2c963f66afa6', 'Phiếu khám bệnh', NOW(), false);
### 
### INSERT INTO "Roles" ("Id", "Name", "NormalizedName", "CreatedAt") 
### VALUES ('2fa85f64-5717-4562-b3fc-2c963f66afa6', 'Bác sĩ', 'BÁC SĨ', NOW());
### 
### INSERT INTO "SignatureCoordinates" ("Id", "MedicalRecordTemplateId", "RoleId", "Name", "CoordinateX", "CoordinateY", "PageNumber", "Width", "Height", "AllowAutoSign", "CreatedAt", "IsDeleted")
### VALUES ('1fa85f64-5717-4562-b3fc-2c963f66afa6', '3fa85f64-5717-4562-b3fc-2c963f66afa6', '2fa85f64-5717-4562-b3fc-2c963f66afa6', 'Chữ ký bác sĩ', 100.5, 200.75, 1, 150.0, 50.0, true, NOW(), false);
