using GoTRUST.EMR.Application.Features.PatientSignatures.Commands;

namespace GoTRUST.EMR.API.Endpoints.PatientSignatures;

public class CreateTransactionSignEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/patient-signatures/create-transaction", async ([FromBody] CreateTransactionSignCommand request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("PatientSignatures")
        .WithName("CreateTransactionSign")
        .Produces<Response<CreateTransactionSignResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Create a transaction sign")
        .WithDescription("Creates a transaction sign for a patient using their identity number.");
    }
}
