using GoTRUST.EMR.Application.Features.PatientSignatures.Queries;

namespace GoTRUST.EMR.API.Endpoints.PatientSignatures;

public class GetMedicalRecordFileContentByTransactionEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/patient-signatures/medical-record-content/{id:guid}", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new GetMedicalRecordFileContentByTransactionQuery(id));
            return Results.Ok(result);
        })
        .WithTags("PatientSignatures")
        .WithName("GetMedicalRecordFileContentByTransaction")
        .Produces<Response<MedicalRecordFileContentDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get Medical Record File Content by Transaction ID")
        .WithDescription("Retrieves the content of a medical record file associated with a specific transaction");
    }
}
