using GoTRUST.EMR.Application.Features.PatientSignatures.Queries;

namespace GoTRUST.EMR.API.Endpoints.PatientSignatures;

public class GetUnsignedMedicalRecordFilesByIdentityNoEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/patient-signatures/unsigned-medical-record/{identityNo}", async (string identityNo, ISender sender) =>
        {
            var result = await sender.Send(new GetUnsignedMedicalRecordFilesByIdentityNoQuery(identityNo));
            return Results.Ok(result);
        })
        .WithTags("PatientSignatures")
        .WithName("GetUnsignedMedicalRecordFilesByIdentityNo")
        .Produces<Response<List<UnsignedMedicalRecordFileDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get Unsigned Medical Record Files by Identity Number")
        .WithDescription("Retrieves unsigned medical record files associated with a specific identity number");
    }
}
