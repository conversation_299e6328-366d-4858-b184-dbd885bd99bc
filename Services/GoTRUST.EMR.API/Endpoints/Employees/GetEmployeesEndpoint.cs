using GoTRUST.EMR.Application.Features.Employees.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Employees;

public record GetEmployeesParameters : PaginationRequest
{
    public string? SearchTerm { get; init; }
};

public class GetEmployeesEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/employees", async ([AsParameters] GetEmployeesParameters req, ISender sender) =>
        {
            var result = await sender.Send(new GetEmployeesQuery
            {
                PageIndex = req.PageIndex,
                PageSize = req.PageSize,
                SearchTerm = req.SearchTerm
            });
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSR })
        .WithTags("Employees")
        .WithName("GetEmployees")
        .Produces<PaginationResponse<GetEmployeesResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get all employees")
        .WithDescription("Returns a list of all employees by.");
    }
}