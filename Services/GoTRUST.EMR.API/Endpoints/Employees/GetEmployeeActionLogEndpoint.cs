using GoTRUST.EMR.Application.Features.Employees.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Employees;

public record GetEmployeeActionLogQueryParams : PaginationRequest
{
    public string? SearchTerm { get; init; }
    public DateTime? SearchDate { get; init; }
}

public class GetEmployeeActionLogEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/employees/{id:guid}/action-logs", async (
            Guid id,
            [AsParameters] GetEmployeeActionLogQueryParams queryParams,
            ISender sender) =>
        {
            var request = new GetEmployeeActionLogQuery
            {
                EmployeeId = id,
                SearchTerm = queryParams.SearchTerm,
                SearchDate = queryParams.SearchDate,
                PageIndex = queryParams.PageIndex,
                PageSize = queryParams.PageSize
            };
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSR })
        .WithTags("Employees")
        .WithName("GetEmployeeActionLog")
        .Produces<PaginationResponse<GetEmployeeActionLogResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get employee action logs")
        .WithDescription("Retrieves the action logs for a specific employee.");
    }
}
