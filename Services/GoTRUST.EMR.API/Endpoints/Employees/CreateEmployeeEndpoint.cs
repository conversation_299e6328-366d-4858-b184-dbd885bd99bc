using GoTRUST.EMR.Application.Features.Employees.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Employees;

public class CreateEmployeeEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/employees", async ([FromForm] CreateEmployeeRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSC })
        .WithTags("Employees")
        .WithName("CreateEmployee")
        .Produces<Response<CreateEmployeeResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Create an employee")
        .WithDescription("Creates a new employee and returns the created employee.");
    }
}
