using GoTRUST.EMR.Application.Features.Employees.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Employees;

public class DeleteEmployeeEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapDelete("/employees/{id:guid}", async (Guid id, ISender sender) =>
        {
            var request = new DeleteEmployeeRequest(id);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSD })
        .WithTags("Employees")
        .WithName("DeleteEmployee")
        .Produces<Response<DeleteEmployeeResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Delete an employee")
        .WithDescription("Marks an employee as deleted and returns the deleted employee.");
    }
}
