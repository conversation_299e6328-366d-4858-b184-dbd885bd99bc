using GoTRUST.EMR.Application.Features.Employees.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Employees;

public class GetAllEmployeesEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/employees/combobox", async (ISender sender) =>
        {
            var result = await sender.Send(new GetAllEmployeesQuery());
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSR })
        .WithTags("Employees")
        .WithName("GetAllEmployees")
        .Produces<Response<List<GetAllEmployeesResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get all employees (no paging)")
        .WithDescription("Returns a list of all employees with id, name, avatar only.");
    }
}
