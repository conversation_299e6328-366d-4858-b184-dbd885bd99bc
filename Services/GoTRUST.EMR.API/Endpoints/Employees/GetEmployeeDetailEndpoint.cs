using GoTRUST.EMR.Application.Features.Employees.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Employees;

public class GetEmployeeDetailEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/employees/{id:guid}", async (Guid id, ISender sender) =>
        {
            var request = new GetEmployeeDetailQuery(id);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSR })
        .WithTags("Employees")
        .WithName("GetEmployeeDetail")
        .Produces<Response<GetEmployeeDetailResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get employee detail")
        .WithDescription("Returns the detail of a specific employee.");
    }
}
