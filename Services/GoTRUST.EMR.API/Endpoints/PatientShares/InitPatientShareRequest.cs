using GoTRUST.EMR.Application.Features.PatientShares.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.API.Endpoints.PatientShares;

public class InitPatientShareRequest : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/patient-shares/init", async (InitPatientShareRequestRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithTags("PatientShares")
        .WithName("InitPatientShareRequest")
        .Produces<Response<InitPatientShareRequestResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Khởi tạo yêu cầu chia sẻ bệnh án liên viện")
        .WithDescription("Tạo yêu cầu chia sẻ thông tin bệnh án cho bệnh nhân dựa trên số CCCD. Yêu cầu quyền EMC (EMR Management).");
    }
}
