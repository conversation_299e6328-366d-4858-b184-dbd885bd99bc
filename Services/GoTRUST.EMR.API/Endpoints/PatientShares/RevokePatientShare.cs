using GoTRUST.EMR.Application.Features.PatientShares.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.API.Endpoints.PatientShares;

public class RevokePatientShare : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/patient-shares/revoke", async (RevokePatientShareRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithTags("PatientShares")
        .WithName("RevokePatientShare")
        .Produces<Response<RevokePatientShareResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Thu hồi quyền chia sẻ bệnh án")
        .WithDescription("Thu hồi quyền chia sẻ thông tin bệnh án đã được đồng ý trước đó. Yêu cầu quyền EMC (EMR Management).");
    }
}
