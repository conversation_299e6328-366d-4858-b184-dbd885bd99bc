using GoTRUST.EMR.Application.Features.PatientShares.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.API.Endpoints.PatientShares;

public class ProcessPatientShareConsent : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/patient-shares/consent", async (ProcessPatientShareConsentRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithTags("PatientShares")
        .WithName("ProcessPatientShareConsent")
        .Produces<Response<ProcessPatientShareConsentResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Xử lý phản hồi đồng ý chia sẻ bệnh án")
        .WithDescription("Xử lý phản hồi đồng ý hoặc từ chối chia sẻ thông tin bệnh án từ bệnh nhân. Yêu cầu quyền EMC (EMR Management).");
    }
}
