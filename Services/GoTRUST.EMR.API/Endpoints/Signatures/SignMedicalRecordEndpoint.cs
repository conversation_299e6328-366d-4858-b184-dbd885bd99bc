using GoTRUST.EMR.Application.Features.Signatures.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Signatures;

public class SignMedicalRecordEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/signatures/sign-medical-record", async ([FromBody] SignMedicalRecordCommand request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.DSA })
        .WithTags("Signatures")
        .WithName("SignMedicalRecordFile")
        .Produces<Response<SignMedicalRecordResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Sign a medical record")
        .WithDescription("Signs a medical record using the provided signature details.");
    }
}
