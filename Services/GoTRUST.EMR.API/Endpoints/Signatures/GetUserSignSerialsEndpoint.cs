using GoTRUST.EMR.Application.Features.Signatures.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Signatures;

public class GetUserSignSerialsEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/signatures/user/serials", async (ISender sender) =>
        {
            var result = await sender.Send(new GetUserSignSerialsQuery());
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.DSR })
        .WithTags("Signatures")
        .WithName("GetUserSignSerials")
        .Produces<Response<List<string>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get user's sign serials")
        .WithDescription("Returns all sign serials for the current user.");
    }
}
