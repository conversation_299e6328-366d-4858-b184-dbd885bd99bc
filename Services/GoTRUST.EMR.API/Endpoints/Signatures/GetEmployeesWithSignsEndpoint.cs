using GoTRUST.EMR.Application.Features.Signatures.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Signatures;

public record GetEmployeesWithSignsParameters : PaginationRequest
{
    public string? SearchTerm { get; init; }
}

public class GetEmployeesWithSignsEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/signatures/employees", async ([AsParameters] GetEmployeesWithSignsParameters req, ISender sender) =>
        {
            var result = await sender.Send(new GetEmployeesWithSignsQuery
            {
                PageIndex = req.PageIndex,
                PageSize = req.PageSize,
                SearchTerm = req.SearchTerm
            });
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.DSR })
        .WithTags("Signatures")
        .WithName("GetEmployeesWithSigns")
        .Produces<PaginationResponse<GetEmployeesWithSignsResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get employees with signatures")
        .WithDescription("Returns a paginated list of employees with signature information.");
    }
}
