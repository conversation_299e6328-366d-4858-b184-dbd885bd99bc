using GoTRUST.EMR.Application.Features.Signatures.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Signatures;

public class GetUserSignaturesEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/signatures/user", async (ISender sender) =>
        {
            var result = await sender.Send(new GetUserSignaturesQuery());
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.DSR })
        .WithTags("Signatures")
        .WithName("GetUserSignatures")
        .Produces<Response<List<GetUserSignaturesResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get user's signatures")
        .WithDescription("Returns all signature templates for the current user.");
    }
}
