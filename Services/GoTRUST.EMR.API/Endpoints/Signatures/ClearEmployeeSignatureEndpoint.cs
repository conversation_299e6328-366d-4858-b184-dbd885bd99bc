using GoTRUST.EMR.Application.Features.Signatures.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Signatures;

public class ClearEmployeeSignatureEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/signatures/employees/{employeeId:guid}/clear", async (Guid employeeId, ISender sender) =>
        {
            var request = new ClearEmployeeSignatureCommand
            {
                EmployeeId = employeeId
            };
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.DSU })
        .WithTags("Signatures")
        .WithName("ClearEmployeeSignature")
        .Produces<Response<ClearEmployeeSignatureResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Clear employee signature")
        .WithDescription("Clears the signature information for an employee.");
    }
}
