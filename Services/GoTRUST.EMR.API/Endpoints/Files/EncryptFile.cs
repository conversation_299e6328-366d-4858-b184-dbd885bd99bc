using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.Upload.Commands;
using Microsoft.AspNetCore.Mvc;

namespace GoTRUST.EMR.API.Endpoints.Files
{
    public class EncryptFile : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapPost("/api/files/encrypt", async ([FromForm] EncryptFileCommand request, IMediator mediator) =>
            {
                return Results.Ok(await mediator.Send(request));
            })
            .WithName("EncryptFile")
            .WithTags("Files")
            .Produces<Response<string>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .DisableAntiforgery()
            .WithSummary("Upload and Encrypt File")
            .WithDescription("This endpoint allows you to upload a file and encrypt it using the specified secret key.");
        }
    }
}