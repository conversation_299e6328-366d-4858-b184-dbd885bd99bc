using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.Upload.Commands;
using Microsoft.AspNetCore.Mvc;

namespace GoTRUST.EMR.API.Endpoints.Pdfs
{
    public class DecryptFile: ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapPost("/api/files/decrypt", async ([FromForm] DecryptFileCommand request, IMediator mediator) =>
            {
                return Results.Ok(await mediator.Send(request));
            })
            .WithName("DecryptFile")
            .WithTags("Files")
            .Produces<Response<string>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .DisableAntiforgery()
            .WithSummary("Decrypt File")
            .WithDescription("This endpoint allows you to upload a file and decrypt it using the specified secret key.");
        }
    }
}