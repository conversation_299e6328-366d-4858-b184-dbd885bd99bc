using GoTRUST.EMR.Application.Features.ActionLog.Queries;

namespace GoTRUST.EMR.API.Endpoints.UserActionLogs
{
    public record GetUserActionLogsQueryBody : PaginationRequest
    {
        public DateOnly? ActionDate { get; init; }
        public string? SearchTerm { get; init; }
    }
    
    public class GetUserActionLogEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/user/{id}/logs", async (Guid id, [AsParameters] GetUserActionLogsQueryBody query, ISender sender) =>
            {
                var result = await sender.Send(new GetUserActionLogsRequest
                {
                    UserId = id,
                    ActionDate = query.ActionDate,
                    SearchTerm = query.SearchTerm,
                    PageIndex = query.PageIndex,
                    PageSize = query.PageSize
                });
                return Results.Ok(result);
            })
            .WithTags("UserActionLogs")
            .WithName("GetUserActionLogs")
            .Produces<PaginationResponse<UserActionLogDto>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
            .WithSummary("Lấy danh sách lịch sử hành động của người dùng")
            .WithDescription("Lấy danh sách lịch sử hành động của người dùng dựa từ khoá và ngày");
        }
    }
}