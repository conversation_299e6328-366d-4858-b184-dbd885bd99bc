using GoTRUST.EMR.Application.Features.ActionLog.Queries;

namespace GoTRUST.EMR.API.Endpoints.UserActionLogs;

public class GetUserActionLogTypesEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/user-action-log-types", async (ISender sender) =>
        {
            var result = await sender.Send(new GetUserActionLogTypesRequest());
            return Results.Ok(result);
        })
        .WithTags("UserActionLogs")
        .WithName("GetUserActionLogTypes")
        .Produces<Response<List<GetUserActionLogTypeResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get all User Action Log Types")
        .WithDescription("Returns a list of all user action log types.");
    }
}