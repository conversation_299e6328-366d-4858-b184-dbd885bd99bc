using GoTRUST.EMR.Application.Features.ActionLog.Queries;

namespace GoTRUST.EMR.API.Endpoints.UserActionLogs;

public class GetUserActionLogStatusEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/user-action-log-statuses", async (ISender sender) =>
        {
            var result = await sender.Send(new GetUserActionLogStatusRequest());
            return Results.Ok(result);
        })
        .WithTags("UserActionLogs")
        .WithName("GetUserActionLogStatuses")
        .Produces<Response<List<GetUserActionLogStatusResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get all User Action Log Statuses")
        .WithDescription("Returns a list of all user action log statuses.");
    }
}