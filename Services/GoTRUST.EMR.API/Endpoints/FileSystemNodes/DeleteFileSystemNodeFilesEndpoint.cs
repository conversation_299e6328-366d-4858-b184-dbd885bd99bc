using GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes;

public class DeleteFileSystemNodeFilesRequest
{
    public List<Guid> FileSystemNodeIds { get; init; } = new();
}

public class DeleteFileSystemNodeFilesEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapDelete("/file-system-nodes/files", async ([FromBody] DeleteFileSystemNodeFilesRequest request, ISender sender) =>
        {
            var command = new DeleteFileSystemNodeFilesCommand(request.FileSystemNodeIds);
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FFD })
        .WithTags("FileSystemNodes")
        .WithName("DeleteFileSystemNodeFiles")
        .Produces<Response<DeleteFileSystemNodeFilesResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Delete file system node files")
        .WithDescription("Deletes file system node files and returns the result.");
    }
}
