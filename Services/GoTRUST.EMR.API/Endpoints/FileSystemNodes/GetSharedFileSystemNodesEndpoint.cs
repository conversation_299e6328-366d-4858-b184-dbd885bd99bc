using GoTRUST.EMR.Application.Features.FileSystemNodes.Queries;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes;

/// <summary>
/// Endpoint để lấy danh sách file/folder được chia sẻ với user hiện tại
/// </summary>
public class GetSharedFileSystemNodesEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/file-system-nodes/shared", async ([AsParameters] GetSharedFileSystemNodesQuery request, ISender sender) =>
            {
                var result = await sender.Send(request);
                return Results.Ok(result);
            })
            .RequireAuthorization()
            .WithTags("FileSystemNodes")
            .WithName("GetSharedFileSystemNodes")
            .Produces<PaginationResponse<FileSystemNodeDto>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized)
            .WithSummary("Lấy danh sách file/folder được chia sẻ")
            .WithDescription("Lấy danh sách các file và folder mà user hiện tại có quyền truy cập (được chia sẻ)");
    }
}
