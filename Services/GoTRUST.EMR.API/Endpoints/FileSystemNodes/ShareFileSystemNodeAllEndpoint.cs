using GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes;

public class ShareFileSystemNodeAllEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/file-system-nodes/share-all", async (ShareFileSystemNodeAllRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FFR })
        .WithName("ShareFileSystemNodeAll")
        .WithTags("FileSystemNodes")
        .Produces<Response<ShareFileSystemNodeAllResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status403Forbidden)
        .WithSummary("Share or Unshare All File System Nodes")
        .WithDescription("Share or unshare all file system nodes (files and folders) for the current user.");
    }
}
