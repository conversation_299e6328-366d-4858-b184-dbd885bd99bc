using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Queries;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes;

public class GetPersonalFileSystemNodesEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/file-system-nodes/personal", async ([AsParameters] GetPersonalNodeQuery request, ISender sender) =>
            {
                var result = await sender.Send(request);
                return Results.Ok(result);
            })
            .RequireAuthorization()
            .WithTags("FileSystemNodes")
            .WithName("GetPersonalFileSystemNodes")
            .Produces<PaginationResponse<FileSystemNodeDto>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized)
            .WithSummary("Lấy danh sách file/folder cá nhân (private)")
            .WithDescription("<PERSON><PERSON><PERSON> danh sách các file và folder cá nhân (tr<PERSON><PERSON> thái private, owner là user hiện tại)");
    }
}
