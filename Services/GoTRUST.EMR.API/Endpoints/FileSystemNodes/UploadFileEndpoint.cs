using GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Queries;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes
{
    public class UploadFileCommandBody
    {
        public IFormFileCollection? Files { get; init; }
        public Guid? ParentId { get; init; }
    }
    public class UploadFileEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapPost("/file-system-nodes/upload", async ([FromForm] UploadFileCommandBody request, ISender sender) =>
            {
                var command = new UploadFileCommand
                {
                    Files = request.Files?.ToList() ?? [],
                    ParentId = request.ParentId.HasValue ? request.ParentId.Value : null
                };
                return Results.Ok(await sender.Send(command));
            })
            .DisableAntiforgery()
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FFR })
            .WithName("UploadFile")
            .WithTags("FileSystemNodes")
            .Produces<Response<List<UploadFileResult>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Upload Files")
            .WithDescription("This endpoint uploads files to the file system.");
        }
    }
}