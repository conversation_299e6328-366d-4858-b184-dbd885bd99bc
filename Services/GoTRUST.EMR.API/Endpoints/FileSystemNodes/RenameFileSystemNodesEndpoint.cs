using GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes;

public class RenameFileSystemNodesRequest
{
    public string Name { get; init; } = string.Empty;
}

public class RenameFileSystemNodesEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPut("/file-system-nodes/{id:guid}/rename", async (Guid id, [FromBody] RenameFileSystemNodesRequest request, ISender sender) =>
        {
            var command = new RenameFileSystemNodesCommand(id, request.Name);
            var result = await sender.Send(command);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FFU })
        .WithTags("FileSystemNodes")
        .WithName("RenameFileSystemNode")
        .Produces<Response<RenameFileSystemNodesResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Rename a file system node")
        .WithDescription("Renames a file system node and returns the result.");
    }
}
