using GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;
using Microsoft.AspNetCore.Authorization;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes;

/// <summary>
/// Endpoint để tạo thư mục mới
/// </summary>
public class CreateFolderEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/file-system-nodes/folders", async (CreateFolderRequest request, ISender sender) =>
            {
                var command = new CreateFolderCommand(request.Name, request.ParentId);
                var result = await sender.Send(command);
                return Results.Created($"/file-system-nodes/{result.Data!.Id}", result);
            })
            .RequireAuthorization()
            .WithTags("FileSystemNodes")
            .WithName("CreateFolder")
            .Produces<Response<CreateFolderResponse>>(StatusCodes.Status201Created)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized)
            .ProducesProblem(StatusCodes.Status409Conflict)
            .WithSummary("Tạo thư mục mới")
            .WithDescription("Tạo một thư mục mới trong hệ thống file. User phải có quyền truy cập thư mục cha nếu có.");
    }
}
