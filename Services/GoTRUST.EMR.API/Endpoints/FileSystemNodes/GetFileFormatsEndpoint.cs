using GoTRUST.EMR.Application.Features.FileSystemNodes.Queries;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;
using MediatR;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes
{
    public class GetFileFormatsEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/file-system-nodes/formats", async (ISender sender) =>
            {
                var result = await sender.Send(new GetFileFormatsQuery());
                return Results.Ok(result);
            })
            .WithName("GetFileFormats")
            .WithTags("FileSystemNodes")
            .Produces<Response<List<FileFormatType>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get supported file formats")
            .WithDescription("This endpoint returns the list of supported file formats, grouped by type.");
        }
    }
}
