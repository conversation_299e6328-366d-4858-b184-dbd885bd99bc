using GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes;

public class MoveFileSystemNodesEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/file-system-nodes/move", async (MoveFileSystemNodesRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FFC })
        .WithName("MoveFileSystemNodes")
        .WithTags("FileSystemNodes")
        .Produces<Response<MoveFileSystemNodesResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status403Forbidden)
        .WithSummary("Move File System Nodes")
        .WithDescription("Move file system nodes (files and folders) to a new parent folder, update path and share status.");
    }
}
