using GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes;

public class ShareFileSystemNodesByRoleEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/file-system-nodes/share-by-role", async (ShareFileSystemNodesByRoleRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FFR })
        .WithName("ShareFileSystemNodesByRole")
        .WithTags("FileSystemNodes")
        .Produces<Response<ShareFileSystemNodesByRoleResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status403Forbidden)
        .WithSummary("Share or Unshare File System Nodes")
        .WithDescription("Share or unshare file system nodes (files and folders) with specified roles.");
    }
}
