using GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;
using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes;
public class DownloadFoldersEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/file-system-nodes/download-folder", async ([FromBody] DownloadFolderCommand command, IMediator mediator) =>
        {
            var result = await mediator.Send(command);
            var fileStreamResult = result.Data;
            // Chuẩn hóa trả về file cho Minimal API
            if (fileStreamResult == null || fileStreamResult.FileStream == null)
                return Results.Problem("Không thể tải thư mục.", statusCode: StatusCodes.Status400BadRequest);

            return Results.File(
                fileStreamResult.FileStream,
                fileStreamResult.ContentType,
                fileStreamResult.FileDownloadName
            );
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FFR })
        .WithName("DownloadFileSystemFolders")
        .WithTags("FileSystemNodes")
        .Produces<FileStreamResult>(StatusCodes.Status200OK, MediaTypeNames.Application.Zip)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Download File System Folders")
        .WithDescription("Download one or multiple folders as a zip archive. Returns 400 if any folder or file is not accessible.");
    }
}
