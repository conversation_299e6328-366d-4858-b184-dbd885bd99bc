using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Queries;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes
{
    public class GetRecentNodeEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/file-system-nodes/recent", async ([AsParameters] GetRecentNodeQuery query, ISender sender) =>
            {
                var result = await sender.Send(query);
                return Results.Ok(result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FFR })
            .WithName("GetRecentFileSystemNodes")
            .WithTags("FileSystemNodes")
            .Produces<Response<List<FileSystemNodeDto>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get Recent File System Nodes")
            .WithDescription("This endpoint retrieves recent file system nodes for the authenticated user, optionally filtered by parent ID and node type.");
        }
    }
}