using GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;
using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes;
public class DownloadFilesEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/file-system-nodes/download-file", async ([FromBody] DownloadFileCommand command, IMediator mediator) =>
        {
            var result = await mediator.Send(command);
            var fileStreamResult = result.Data;
            // Chuẩn hóa trả về file cho Minimal API
            if (fileStreamResult == null || fileStreamResult.FileStream == null)
                return Results.Problem("Không thể tải file.", statusCode: StatusCodes.Status400BadRequest);

            return Results.File(
                fileStreamResult.FileStream,
                fileStreamResult.ContentType,
                fileStreamResult.FileDownloadName
            );
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FFR })
        .WithName("DownloadFileSystemNodes")
        .WithTags("FileSystemNodes")
        .Produces<FileStreamResult>(StatusCodes.Status200OK, MediaTypeNames.Application.Octet)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Download File System Nodes")
        .WithDescription("Download one or multiple file system nodes as a file or zip archive. Returns 400 if any file is not accessible.");
    }
}
