using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Queries;
using GoTRUST.EMR.Domain.Constants;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GoTRUST.EMR.API.Endpoints.FileSystemNodes
{
    public class RecentFoldersEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/file-system-nodes/recent-folders", async ([FromQuery] Guid? parentId, ISender sender) =>
            {
                var query = new GetRecentFoldersQuery { ParentId = parentId };
                var result = await sender.Send(query);
                return Results.Ok(result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FFR })
            .WithName("GetRecentFolders")
            .WithTags("FileSystemNodes")
            .Produces<Response<List<FileSystemNodeSimpleDto>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get recent folders (no paging, only Id, Name, ParentId)")
            .WithDescription("Lấy danh sách thư mục gần đây (không phân trang, chỉ trả về Id, Name, ParentId)");
        }
    }
}
