using GoTRUST.EMR.Application.Features.Hospitals.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Hospitals;

public class DeleteHospitalEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapDelete("/hospitals/{id:guid}", async (Guid id, ISender sender) =>
        {
            var request = new DeleteHospitalRequest(id);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.ADMIN })
        .WithTags("Hospitals")
        .WithName("DeleteHospital")
        .Produces<Response<DeleteHospitalResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Delete a hospital")
        .WithDescription("Marks a hospital as deleted and returns the deleted hospital.");
    }
}
