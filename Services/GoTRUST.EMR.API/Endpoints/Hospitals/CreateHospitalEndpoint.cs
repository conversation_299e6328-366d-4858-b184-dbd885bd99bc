using GoTRUST.EMR.Application.Features.Hospitals.Commands;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.Hospitals;

public class CreateHospitalEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/hospitals", async (CreateHospitalRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.ADMIN })
        .WithTags("Hospitals")
        .WithName("CreateHospital")
        .Produces<Response<CreateHospitalResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Create a new hospital")
        .WithDescription("Creates a new hospital and returns the created hospital.");
    }
}
