using GoTRUST.EMR.Application.Features.Hospitals.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Hospitals;

public record UpdateHospitalRequestBody(
    string Code,
    string Name,
    string Address,
    string Language,
    string AdminEmail,
    string NotificationTypes,
    int LoginSessionTimeout,
    int MaxLoginAttempts,
    string PasswordPolicyJson
);
public class UpdateHospitalEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPut("/hospitals/{id:guid}", async (Guid id, UpdateHospitalRequestBody body, ISender sender) =>
        {
            var request = new UpdateHospitalRequest(
                id,
                body.Code,
                body.Name,
                body.Address,
                body.Language,
                body.AdminEmail,
                body.NotificationTypes,
                body.LoginSessionTimeout,
                body.MaxLoginAttempts,
                body.PasswordPolicyJson
            );
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.ADMIN })
        .WithTags("Hospitals")
        .WithName("UpdateHospital")
        .Produces<Response<UpdateHospitalResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Update a hospital")
        .WithDescription("Updates an existing hospital and returns the updated hospital.");
    }
}
