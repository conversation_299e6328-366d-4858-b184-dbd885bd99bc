using GoTRUST.EMR.Application.Features.Hospitals.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Hospitals;

public class GetHospitalsEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/hospitals", async (ISender sender) =>
        {
            var result = await sender.Send(new GetHospitalsRequest());
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.ADMIN })
        .WithTags("Hospitals")
        .WithName("GetHospitals")
        .Produces<Response<List<GetHospitalsResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get all hospitals")
        .WithDescription("Returns a list of all hospitals.");
    }
}
