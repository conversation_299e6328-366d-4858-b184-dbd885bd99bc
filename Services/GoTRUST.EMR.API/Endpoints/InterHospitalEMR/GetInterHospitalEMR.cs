using GoTRUST.EMR.Application.Features.InterHospitalEMR.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.API.Endpoints.InterHospitalEMR;

public class GetInterHospitalEMR : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/inter-hospital-emr", async (
            [AsParameters] GetInterHospitalEMRRequest request, 
            ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("InterHospitalEMR")
        .WithName("GetInterHospitalEMR")
        .Produces<Response<GetInterHospitalEMRResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status403Forbidden)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Tra cứu bệnh án liên viện")
        .WithDescription(@"
API tra cứu thông tin bệnh án liên viện với các bước xử lý:

1. **Kiểm tra quyền truy cập**: Xác thực TxnId và CCCD có được đồng ý chia sẻ không
2. **Truy vấn thông tin bệnh nhân**: Lấy HospitalId và PatientId từ CCCD
3. **Gọi EMR API**: Truy xuất dữ liệu bệnh án từ hệ thống EMR
4. **Trả về kết quả**: Thông tin bệnh án đầy đủ

**Yêu cầu:**
- Role: EMC (EMR Management)
- TxnId phải có trạng thái đồng ý (Result = '1')
- CCCD phải tồn tại trong hệ thống

**Tham số:**
- `citizenId`: Số CCCD (9-12 chữ số)
- `txnId`: Mã giao dịch chia sẻ thông tin

**Ví dụ:** 
```
GET /inter-hospital-emr?citizenId=123456789&txnId=TXN_20241213120000_abc12345
```
        ");
    }
}
