using GoTRUST.EMR.Application.Features.Khoas.Queries;

namespace GoTRUST.EMR.API.Endpoints.Khoas;

public class GetKhoasEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/khoas", async (ISender sender) =>
        {
            var result = await sender.Send(new GetKhoasRequest());
            return Results.Ok(result);
        })
        .WithTags("Khoas")
        .WithName("GetKhoas")
        .Produces<Response<List<GetKhoasResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get all Khoas")
        .WithDescription("Returns a list of all Khoas.");
    }
}
