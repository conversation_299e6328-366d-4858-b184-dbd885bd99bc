using GoTRUST.EMR.Application.Features.TonGiaos.Queries;

namespace GoTRUST.EMR.API.Endpoints.TonGiaos;

public class GetTonGiaosEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/tongiaos", async (ISender sender) =>
        {
            var result = await sender.Send(new GetTonGiaosRequest());
            return Results.Ok(result);
        })
        .WithTags("TonGiaos")
        .WithName("GetTonGiaos")
        .Produces<Response<List<GetTonGiaosResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get all TonGiaos")
        .WithDescription("Returns a list of all TonGiaos.");
    }
}
