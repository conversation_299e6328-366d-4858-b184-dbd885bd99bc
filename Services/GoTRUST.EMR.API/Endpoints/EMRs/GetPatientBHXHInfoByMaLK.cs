using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class GetPatientBHXHInfoByMaLK : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/emrs/bhxh/{maLk}", async (
            string maLk,
            IMediator mediator) =>
        {
            var result = await mediator.Send(new GetPatientBHXHInfoByMaLKQuery(maLk));
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMR })
        .WithName("GetPatientBHXHInfoByMaLK")
        .WithTags("EMRs")
        .Produces<Response<GetPatientBHXHInfoByMaLKQueryResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get BHXH patient info by MA_LK")
        .WithDescription("This endpoint retrieves the BHXH patient info for a given MA_LK.");
    }
}
