using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrDichVuCLS : ICarterModule
{
    public record CreateEMRDichVuCLSRequest(CreateEMRDichVuCLSRequestModel DICH_VU_CLS, string HospitalCode);
    public record CreateEMRDichVuCLSResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/dich-vu-cls", async ([FromBody] CreateEMRDichVuCLSRequest request, ISender sender) =>
        {
            var command = new CreateEMRDichVuCLSCommand(
                request.DICH_VU_CLS,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRDichVuCLSResponse>>();

            return Results.Created($"/emrs/dich-vu-cls/{response.Data!.Id}", response);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithName("CreateEmrDichVuCLS")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRDichVuCLSResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR Dich Vu CLS XML file")
        .WithDescription("This endpoint allows you to upload an EMR Dich Vu CLS XML file and decode its contents.");
    }
}
