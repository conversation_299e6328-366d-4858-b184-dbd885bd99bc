using GoTRUST.EMR.Application.Features.EMRs.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public record GetPatientsBHXHInfoParameters : PaginationRequest
{
    public string? SearchTerm { get; init; }
    public string? MaKhoa { get; init; }
    public DateTime? SyncedAt { get; init; }
}

public class GetPatientsBHXHInfoEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/emrs/patients-bhxh-info", async ([AsParameters] GetPatientsBHXHInfoParameters req, ISender sender) =>
        {
            var result = await sender.Send(new GetPatientsBHXHInfoQuery(
                req.SearchTerm,
                req.MaKhoa,
                req.SyncedAt
            )
            {
                PageIndex = req.PageIndex,
                PageSize = req.PageSize
            });
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.PMR })
        .WithTags("EMRs")
        .WithName("GetPatientsBHXHInfo")
        .Produces<PaginationResponse<GetPatientsBHXHInfoQueryResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get paginated BHXH patient info")
        .WithDescription("Returns a paginated list of patients' BHXH info, filterable by search term, department, and sync date.");
    }
}