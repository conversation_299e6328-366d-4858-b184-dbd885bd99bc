using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrGiayChuyenTuyenBHYT : ICarterModule
{
    public record CreateEMRGiayChuyenTuyenBHYTRequest(CreateEMRGiayChuyenTuyenBHYTRequestModel GIAY_CHUYEN_TUYEN_BHYT, string HospitalCode);
    public record CreateEMRGiayChuyenTuyenBHYTResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/giay-chuyen-tuyen-bhyt", async ([FromBody] CreateEMRGiayChuyenTuyenBHYTRequest request, ISender sender) =>
        {
            var command = new CreateEMRGiayChuyenTuyenBHYTCommand(
                request.GIAY_CHUYEN_TUYEN_BHYT,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRGiayChuyenTuyenBHYTResponse>>();

            return Results.Created($"/emrs/giay-chuyen-tuyen-bhyt/{response.Data!.Id}", response);
        })
        .WithName("CreateEmrGiayChuyenTuyenBHYT")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRGiayChuyenTuyenBHYTResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR Giay Chuyen Tuyen BHYT XML file")
        .WithDescription("This endpoint allows you to upload an EMR Giay Chuyen Tuyen BHYT XML file and decode its contents.")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}
