using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrTomTatHSBA : ICarterModule
{
    public record CreateEMRTomTatHSBARequest(CreateEMRTomTatHSBARequestModel TOM_TAT_HSBA, string HospitalCode);
    public record CreateEMRTomTatHSBAResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/tom-tat-hsba", async ([FromBody] CreateEMRTomTatHSBARequest request, ISender sender) =>
        {
            var command = new CreateEMRTomTatHSBACommand(
                request.TOM_TAT_HSBA,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRTomTatHSBAResponse>>();

            return Results.Created($"/emrs/tom-tat-hsba/{response.Data!.Id}", response);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithName("CreateEmrTomTatHSBA")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRTomTatHSBAResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR Tom Tat HSBA XML file")
        .WithDescription("This endpoint allows you to upload an EMR Tom Tat HSBA XML file and decode its contents.");
    }
}
