using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrGDYK : ICarterModule
{
    public record CreateEMRGDYKRequest(CreateEMRGDYKRequestModel GDYK, string HospitalCode);
    public record CreateEMRGDYKResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/gdyk", async ([FromBody] CreateEMRGDYKRequest request, ISender sender) =>
        {
            var command = new CreateEMRGDYKCommand(
                request.GDYK,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRGDYKResponse>>();

            return Results.Created($"/emrs/gdyk/{response.Data!.Id}", response);
        })
        .WithName("CreateEmrGDYK")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRGDYKResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR GDYK XML file")
        .WithDescription("This endpoint allows you to upload an EMR GDYK XML file and decode its contents.")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}
