using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrCheckin : ICarterModule
{
    public record CreateEMRCheckinRequest(CreateEMRCheckinRequestModel CHECK_IN, string HospitalCode);
    public record CreateEMRCheckinResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/checkin", async ([FromBody] CreateEMRCheckinRequest request, ISender sender) =>
        {
            var command = new CreateEMRCheckinCommand(
                request.CHECK_IN,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRCheckinResponse>>();

            return Results.Created($"/emrs/checkin/{response.Data!.Id}", response);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithName("CreateEmrCheckin")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRCheckinResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR Checkin XML file")
        .WithDescription("This endpoint allows you to upload an EMR Checkin XML file and decode its contents.");
    }
}
