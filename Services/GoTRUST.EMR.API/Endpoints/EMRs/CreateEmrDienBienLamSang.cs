using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrDienBienLamSang : ICarterModule
{
    public record CreateEMRDienBienLamSangRequest(CreateEMRDienBienLamSangRequestModel DIEN_BIEN_LAM_SANG, string HospitalCode);
    public record CreateEMRDienBienLamSangResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/dien-bien-lam-sang", async ([FromBody] CreateEMRDienBienLamSangRequest request, ISender sender) =>
        {
            var command = new CreateEMRDienBienLamSangCommand(
                request.DIEN_BIEN_LAM_SANG,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRDienBienLamSangResponse>>();

            return Results.Created($"/emrs/dien-bien-lam-sang/{response.Data!.Id}", response);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithName("CreateEmrDienBienLamSang")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRDienBienLamSangResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR Dien Bien Lam Sang XML file")
        .WithDescription("This endpoint allows you to upload an EMR Dien Bien Lam Sang XML file and decode its contents.");
    }
}
