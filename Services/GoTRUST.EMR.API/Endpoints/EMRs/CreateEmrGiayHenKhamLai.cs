using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrGiayHenKhamLai : ICarterModule
{
    public record CreateEMRGiayHenKhamLaiRequest(CreateEMRGiayHenKhamLaiRequestModel GIAY_HEN_KHAM_LAI, string HospitalCode);
    public record CreateEMRGiayHenKhamLaiResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/giay-hen-kham-lai", async ([FromBody] CreateEMRGiayHenKhamLaiRequest request, ISender sender) =>
        {
            var command = new CreateEMRGiayHenKhamLaiCommand(
                request.GIAY_HEN_KHAM_LAI,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRGiayHenKhamLaiResponse>>();

            return Results.Created($"/emrs/giay-hen-kham-lai/{response.Data!.Id}", response);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithName("CreateEmrGiayHenKhamLai")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRGiayHenKhamLaiResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR Giay Hen Kham Lai XML file")
        .WithDescription("This endpoint allows you to upload an EMR Giay Hen Kham Lai XML file and decode its contents.");
    }
}
