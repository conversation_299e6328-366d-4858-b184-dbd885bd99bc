using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrThuoc : ICarterModule
{
    public record CreateEMRThuocRequest(CreateEMRThuocRequestModel THUOC, string HospitalCode);
    public record CreateEMRThuocResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/thuoc", async ([FromBody] CreateEMRThuocRequest request, ISender sender) =>
        {
            var command = new CreateEMRThuocCommand(
                request.THUOC,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRThuocResponse>>();

            return Results.Created($"/emrs/thuoc/{response.Data!.Id}", response);
        })
        .WithName("CreateEmrThuoc")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRThuocResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR Thuoc XML file")
        .WithDescription("This endpoint allows you to upload an EMR Thuoc XML file and decode its contents.")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}
