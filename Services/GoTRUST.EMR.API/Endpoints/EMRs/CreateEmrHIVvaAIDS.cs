using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrHIVvaAIDS : ICarterModule
{
    public record CreateEMRHIVvaAIDSRequest(CreateEMRHIVvaAIDSRequestModel HIV_VA_AIDS, string HospitalCode);
    public record CreateEMRHIVvaAIDSResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/hiv-va-aids", async ([FromBody] CreateEMRHIVvaAIDSRequest request, ISender sender) =>
        {
            var command = new CreateEMRHIVvaAIDSCommand(
                request.HIV_VA_AIDS,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRHIVvaAIDSResponse>>();

            return Results.Created($"/emrs/hiv-va-aids/{response.Data!.Id}", response);
        })
        .WithName("CreateEmrHIVvaAIDS")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRHIVvaAIDSResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR HIV va AIDS XML file")
        .WithDescription("This endpoint allows you to upload an EMR HIV va AIDS XML file and decode its contents.")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}
