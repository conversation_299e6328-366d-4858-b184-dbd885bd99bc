using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Domain.Models;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrGtel : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/emr/create", async ([FromBody] CreateEMRCommand request, IMediator mediator) =>
        {
            return Results.Ok(await mediator.Send(request));
        })
        .WithName("CreateEmrGtel")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRResult>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Create EMR Gtel hook")
        .WithDescription("This endpoint allows you to create an EMR Gtel record by providing the necessary details in the request body.")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}