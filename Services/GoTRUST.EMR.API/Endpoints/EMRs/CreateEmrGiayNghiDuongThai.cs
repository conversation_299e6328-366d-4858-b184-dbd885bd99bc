using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrGiayNghiDuongThai : ICarterModule
{
    public record CreateEMRGiayNghiDuongThaiRequest(CreateEMRGiayNghiDuongThaiRequestModel GIAY_NGHI_DUONG_THAI, string HospitalCode);
    public record CreateEMRGiayNghiDuongThaiResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/giay-nghi-duong-thai", async ([FromBody] CreateEMRGiayNghiDuongThaiRequest request, ISender sender) =>
        {
            var command = new CreateEMRGiayNghiDuongThaiCommand(
                request.GIAY_NGHI_DUONG_THAI,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRGiayNghiDuongThaiResponse>>();

            return Results.Created($"/emrs/giay-nghi-duong-thai/{response.Data!.Id}", response);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithName("CreateEmrGiayNghiDuongThai")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRGiayNghiDuongThaiResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR Giay Nghi Duong Thai XML file")
        .WithDescription("This endpoint allows you to upload an EMR Giay Nghi Duong Thai XML file and decode its contents.");
    }
}
