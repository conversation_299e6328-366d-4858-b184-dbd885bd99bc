using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrLao : ICarterModule
{
    public record CreateEMRLaoRequest(CreateEMRLaoRequestModel LAO, string HospitalCode);
    public record CreateEMRLaoResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/lao", async ([FromBody] CreateEMRLaoRequest request, ISender sender) =>
        {
            var command = new CreateEMRLaoCommand(
                request.LAO,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRLaoResponse>>();

            return Results.Created($"/emrs/lao/{response.Data!.Id}", response);
        })
        .WithName("CreateEmrLao")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRLaoResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR Lao XML file")
        .WithDescription("This endpoint allows you to upload an EMR Lao XML file and decode its contents.")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}
