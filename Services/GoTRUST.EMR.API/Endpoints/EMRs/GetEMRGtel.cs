using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Features.EMRs.Queries;
using GoTRUST.EMR.Domain.Models;

namespace GoTRUST.EMR.API.Endpoints.EMRs
{
    public class GetEMRGtel : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/api/emr/get", async (IMediator mediator, [AsParameters] GetEMRQuery query) =>
            {
                var response = await mediator.Send(query);
                return Results.Ok(response);
            })
            .WithName("GetEMRGtel")
            .WithTags("EMRs")
            .Produces<Response<List<HoSoBenhAn>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .WithSummary("Get EMR by CCCD")
            .WithDescription("This endpoint retrieves an EMR.");
        }
    }
}