using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrGiayNghiHuongBHXH : ICarterModule
{
    public record CreateEMRGiayNghiHuongBHXHRequest(CreateEMRGiayNghiHuongBHXHRequestModel GIAY_NGHI_HUONG_BHXH, string HospitalCode);
    public record CreateEMRGiayNghiHuongBHXHResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/giay-nghi-huong-bhxh", async ([FromBody] CreateEMRGiayNghiHuongBHXHRequest request, ISender sender) =>
        {
            var command = new CreateEMRGiayNghiHuongBHXHCommand(
                request.GIAY_NGHI_HUONG_BHXH,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRGiayNghiHuongBHXHResponse>>();

            return Results.Created($"/emrs/giay-nghi-huong-bhxh/{response.Data!.Id}", response);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithName("CreateEmrGiayNghiHuongBHXH")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRGiayNghiHuongBHXHResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR Giay Nghi Huong BHXH XML file")
        .WithDescription("This endpoint allows you to upload an EMR Giay Nghi Huong BHXH XML file and decode its contents.");
    }
}
