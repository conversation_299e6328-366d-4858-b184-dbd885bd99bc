using GoTRUST.EMR.Application.Features.EMRs.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class UploadEmrXml : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/emrs/upload-xml", async (IFormFile file, IMediator mediator) =>
        {
            if (file == null || file.Length == 0)
                return Results.BadRequest("No file uploaded.");

            return Results.Ok(await mediator.Send(new DecodeEMRXmlCommand(file)));
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .DisableAntiforgery()
        .Accepts<IFormFile>("multipart/form-data")
        .WithName("UploadEmrXml")
        .WithTags("EMRs")
        .Produces(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR XML file")
        .WithDescription("This endpoint allows you to upload an EMR XML file and decode its contents.");
    }
}