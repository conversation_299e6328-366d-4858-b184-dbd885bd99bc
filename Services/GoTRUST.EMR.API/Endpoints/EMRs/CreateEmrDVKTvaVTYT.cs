using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrDVKTvaVTYT : ICarterModule
{
    public record CreateEMRDVKTvaVTYTRequest(CreateEMRDVKTvaVTYTRequestModel DVKT_VA_VTYT, string HospitalCode);
    public record CreateEMRDVKTvaVTYTResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/dvkt-va-vtyt", async ([FromBody] CreateEMRDVKTvaVTYTRequest request, ISender sender) =>
        {
            var command = new CreateEMRDVKTvaVTYTCommand(
                request.DVKT_VA_VTYT,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRDVKTvaVTYTResponse>>();

            return Results.Created($"/emrs/dvkt-va-vtyt/{response.Data!.Id}", response);
        })
        .WithName("CreateEmrDVKTvaVTYT")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRDVKTvaVTYTResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR DVKT va VTYT XML file")
        .WithDescription("This endpoint allows you to upload an EMR DVKT va VTYT XML file and decode its contents.")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}
