using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrGiayRaVien : ICarterModule
{
    public record CreateEMRGiayRaVienRequest(CreateEMRGiayRaVienRequestModel GIAY_RA_VIEN, string HospitalCode);
    public record CreateEMRGiayRaVienResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/giay-ra-vien", async ([FromBody] CreateEMRGiayRaVienRequest request, ISender sender) =>
        {
            var command = new CreateEMRGiayRaVienCommand(
                request.GIAY_RA_VIEN,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRGiayRaVienResponse>>();

            return Results.Created($"/emrs/giay-ra-vien/{response.Data!.Id}", response);
        })
        .WithName("CreateEmrGiayRaVien")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRGiayRaVienResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR Giay Ra Vien XML file")
        .WithDescription("This endpoint allows you to upload an EMR Giay Ra Vien XML file and decode its contents.")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}
