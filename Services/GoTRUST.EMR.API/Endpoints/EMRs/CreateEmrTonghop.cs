using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrTonghop : ICarterModule
{
    public record CreateEMRTongHopRequest(CreateEMRTongHopRequestModel TONG_HOP, string HospitalCode);
    public record CreateEMRTongHopResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/tong-hop", async ([FromBody] CreateEMRTongHopRequest request, ISender sender) =>
        {
            var command = new CreateEMRTongHopCommand(
                request.TONG_HOP,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRTongHopResponse>>();

            return Results.Created($"/emrs/tong-hop/{response.Data!.Id}", response);
        })
        .WithName("CreateEmrTonghop")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRTongHopResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR XML file")
        .WithDescription("This endpoint allows you to upload an EMR XML file and decode its contents.")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}