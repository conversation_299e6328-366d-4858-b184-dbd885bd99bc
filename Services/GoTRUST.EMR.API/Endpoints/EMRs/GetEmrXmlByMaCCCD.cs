using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class GetEmrXmlByMaCCCD : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/emrs/{maCccd}/xml", async (string maCccd, IMediator mediator) =>
        {
            var result = await mediator.Send(new GetEMRByCCCDAsXML(maCccd));
            if (result.Code != "000" || result.Data == null)
                return Results.BadRequest(result);

            var fileName = $"EMR_{maCccd}_{DateTime.Now:yyyyMMddHHmmss}.xml";
            return Results.File(result.Data, "application/xml", fileName);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMR })
        .WithName("GetEmrXmlByMaCCCD")
        .WithTags("EMRs")
        .Produces<Response<GetEMRByCCCDAsJSONResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get EMR by CCCD as XML")
        .WithDescription("This endpoint retrieves the EMR for a patient by their code (MA_BN) and returns it as an XML file. The file is named using the patient's code and the current timestamp.");
    }
}