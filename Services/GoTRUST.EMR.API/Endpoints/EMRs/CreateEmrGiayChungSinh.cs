using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Commands;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class CreateEmrGiayChungSinh : ICarterModule
{
    public record CreateEMRGiayChungSinhRequest(CreateEMRGiayChungSinhRequestModel GIAY_CHUNG_SINH, string HospitalCode);
    public record CreateEMRGiayChungSinhResponse(Guid Id, string MA_LK);

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/emrs/giay-chung-sinh", async ([FromBody] CreateEMRGiayChungSinhRequest request, ISender sender) =>
        {
            var command = new CreateEMRGiayChungSinhCommand(
                request.GIAY_CHUNG_SINH,
                request.HospitalCode
            );

            var result = await sender.Send(command);

            var response = result.Adapt<Response<CreateEMRGiayChungSinhResponse>>();

            return Results.Created($"/emrs/giay-chung-sinh/{response.Data!.Id}", response);
        })
        .WithName("CreateEmrGiayChungSinh")
        .WithTags("EMRs")
        .Produces<Response<CreateEMRGiayChungSinhResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Upload and decode EMR Giay Chung Sinh XML file")
        .WithDescription("This endpoint allows you to upload an EMR Giay Chung Sinh XML file and decode its contents.")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}
