using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EMRs;

public class GetEmrByMaCCCD : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/emrs/{maLk}/json", async (
            string maLk,
            IMediator mediator) =>
        {
            return Results.Ok(await mediator.Send(
                new GetEMRByCCCDAsJSON(maLk)
            ));
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMR })
        .WithName("GetEmrByMaCCCD")
        .WithTags("EMRs")
        .Produces<Response<GetEMRByCCCDAsJSONResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get EMR by MA_LK as json")
        .WithDescription("This endpoint retrieves the EMR for a patient by MA_LK and returns it as a JSON object. The response includes the patient's EMR details in a structured format.");
    }
}