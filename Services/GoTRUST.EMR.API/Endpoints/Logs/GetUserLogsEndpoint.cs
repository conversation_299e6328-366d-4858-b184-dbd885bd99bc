using GoTRUST.EMR.Application.Features.ActionLog.Queries;

namespace GoTRUST.EMR.API.Endpoints.Logs
{
    public record GetUserLogsParameters : PaginationRequest
    {
        public string? SearchTerm { get; init; }
        public DateOnly? FromDate { get; init; }
        public DateOnly? ToDate { get; init; }
        public string? ActionType { get; init; }
        public string? Result { get; init; }
    }
    public class GetUserLogsEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/api/logs/user", async (IMediator mediator, [AsParameters] GetUserLogsParameters query, CancellationToken cancellationToken) =>
            {
                var request = new GetAllUserActionLogsRequest
                {
                    SearchTerm = query.SearchTerm,
                    FromDate = query.FromDate,
                    ToDate = query.ToDate,
                    ActionType = query.ActionType ?? string.Empty,
                    Result = query.Result,
                    PageIndex = query.PageIndex,
                    PageSize = query.PageSize
                };
                var result = await mediator.Send(request, cancellationToken);
                return Results.Ok(result);
            })
            .WithTags("Logs")
            .WithName("GetUserLogs")
            .Produces<PaginationResponse<ActionLogResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
            .WithSummary("Lấy danh sách lịch sử hành động của người dùng")
            .WithDescription("Lấy danh sách lịch sử hành động của người dùng dựa trên từ khoá, ngày và các bộ lọc khác.");
        }
    }
}