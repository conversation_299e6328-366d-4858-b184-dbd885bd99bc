using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Features.ActionLog.Queries;

namespace GoTRUST.EMR.API.Endpoints.Logs
{
    public class ExportUserLogsCsvEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/api/logs/user/export/csv", async (IMediator mediator, [AsParameters] ExportCsvUserActionLogsRequest query, CancellationToken cancellationToken) =>
            {
                var result = await mediator.Send(query, cancellationToken);

                if (result.Data?.FileContent == null || string.IsNullOrEmpty(result.Data?.FileName))
                    return Results.BadRequest("Không có dữ liệu xuất.");
                // Logic to convert result to CSV and return as file

                return Results.File(
                    fileContents: result.Data.FileContent,
                    contentType: "text/csv; charset=utf-8",
                    fileDownloadName: result.Data.FileName
                );
            })
            .WithTags("Logs")
            .WithName("ExportUserLogsCsv")
            .Produces<Response<ExportCsvUserActionLogsResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
            .WithSummary("Xuất lịch sử hành động của người dùng dưới dạng CSV")
            .WithDescription("Xuất lịch sử hành động của người dùng dựa trên từ khoá, ngày và các bộ lọc khác dưới dạng tệp CSV.");
        }
    }
}