using GoTRUST.EMR.Application.Features.Patients.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Patients;

public class GetPatientByCodeAndCitizenId : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/patients/by-search-term", async (string searchTerm, ISender sender) =>
        {
            var result = await sender.Send(new GetPatientByCodeAndCitizenIdRequest(searchTerm));
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.PMR })
        .WithTags("Patients")
        .WithName("GetPatientByCodeAndCitizenId")
        .Produces<Response<GetPatientByCodeAndCitizenIdResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("<PERSON><PERSON>y thông tin bệnh nhân theo mã bệnh nhân và CCCD")
        .WithDescription("Lấy thông tin chi tiết của bệnh nhân theo mã bệnh nhân và số CCCD");
    }
}
