using GoTRUST.EMR.Application.Features.Patients.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Patients;

public class GetPatientMedicalRecordYears : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/patients/{patientId:guid}/medical-record-years", async (
            Guid patientId,
            ISender sender) =>
        {
            var request = new GetPatientMedicalRecordYearsRequest(patientId);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.PMR })
        .WithTags("Patients")
        .WithName("GetPatientMedicalRecordYears")
        .Produces<Response<List<int>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("<PERSON><PERSON>y danh sách các năm có bệnh án của bệnh nhân")
        .WithDescription("Lấy danh sách các năm mà bệnh nhân có ít nhất một bệnh án. " +
                        "Kết quả được sắp xếp theo thứ tự giảm dần (năm mới nhất trước) và loại bỏ trùng lặp.");
    }
}
