using GoTRUST.EMR.Application.Features.Patients.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Patients;

public class CreatePatient : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/patients", async (CreatePatientRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Created($"/patients/{result.Data!.Id}", result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.PMC })
        .WithTags("Patients")
        .WithName("CreatePatient")
        .Produces<Response<CreatePatientResponse>>(StatusCodes.Status201Created)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Tạo bệnh nhân mới")
        .WithDescription("Tạo bệnh nhân mới trong hệ thống. HospitalId sẽ được lấy từ thông tin nhân viên đăng nhập.");
    }
}
