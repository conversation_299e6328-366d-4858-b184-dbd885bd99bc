using GoTRUST.EMR.Application.Features.Patients.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Patients;

public class GetAllPatients : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/patients", async (ISender sender, [AsParameters] GetAllPatientsRequest request) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.PMR })
        .WithTags("Patients")
        .WithName("GetAllPatients")
        .Produces<PaginationResponse<PatientDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("L<PERSON>y danh sách bệnh nhân có phân trang")
        .WithDescription("<PERSON><PERSON><PERSON> danh sách bệnh nhân của bệnh viện hiện tại với hỗ trợ phân trang, tìm kiếm và sắp xếp");
    }
}
