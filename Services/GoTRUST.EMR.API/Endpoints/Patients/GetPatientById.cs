using GoTRUST.EMR.Application.Features.Patients.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Patients;

public class GetPatientById : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/patients/{id}", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new GetPatientByIdRequest(id));
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.PMR })
        .WithTags("Patients")
        .WithName("GetPatientById")
        .Produces<Response<GetPatientByIdResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Lấy thông tin bệnh nhân theo ID")
        .WithDescription("<PERSON><PERSON>y thông tin chi tiết của bệnh nhân theo ID");
    }
}
