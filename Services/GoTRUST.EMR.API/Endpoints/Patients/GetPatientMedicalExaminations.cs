using GoTRUST.EMR.Application.Features.Patients.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Patients;

public class GetPatientMedicalExaminations : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/patients/{patientId:guid}/medical-examinations", async (
            Guid patientId,
            int? year,
            ISender sender) =>
        {
            var request = new GetPatientMedicalExaminationsRequest(patientId, year);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.PMR })
        .WithTags("Patients")
        .WithName("GetPatientMedicalExaminations")
        .Produces<Response<List<PatientMedicalExaminationDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Lấy danh sách các lần khám sức khỏe của bệnh nhân")
        .WithDescription("Lấy tất cả thông tin các lần khám sức khỏe của bệnh nhân (không phân trang). " +
                        "Có thể lọc theo năm. Trả về danh sách bao gồm tên bệnh án, ngày tiếp nhận, mã tiếp nhận và mã hồ sơ bệnh án.");
    }
}
