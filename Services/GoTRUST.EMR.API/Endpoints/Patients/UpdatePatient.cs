using GoTRUST.EMR.Application.Features.Patients.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Patients;

public class UpdatePatient : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPut("/patients/{id:guid}", async (
            Guid id,
            UpdatePatientRequest request,
            ISender sender) =>
        {
            // Ensure the ID from route matches the request
            var updateRequest = request with { Id = id };
            var result = await sender.Send(updateRequest);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.PMU })
        .WithTags("Patients")
        .WithName("UpdatePatient")
        .Produces<Response<UpdatePatientResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Cập nhật thông tin bệnh nhân")
        .WithDescription("Cập nhật thông tin bệnh nhân dựa trên ID");
    }
}
