using GoTRUST.EMR.Application.Features.Patients.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Patients;

public class DeletePatient : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapDelete("/patients/{id:guid}", async (
            Guid id,
            Guid approveId,
            ISender sender) =>
        {
            var request = new DeletePatientRequest(id, approveId);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.PMD })
        .WithTags("Patients")
        .WithName("DeletePatient")
        .Produces<Response<DeletePatientResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Xóa bệnh nhân")
        .WithDescription("<PERSON><PERSON>a bệnh nhân dựa trên ID (soft delete bao gồm tất cả dữ liệu liên quan)");
    }
}
