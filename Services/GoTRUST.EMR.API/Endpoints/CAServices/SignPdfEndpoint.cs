using GoTRUST.EMR.Application.Features.CAServices.Commands;

namespace GoTRUST.EMR.API.Endpoints.CAServices;

public class SignPdfEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/ca-service/sign-pdf", async ([FromBody] SignPdfCommand request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("CAService")
        .WithName("SignPdf")
        .Produces<Response<SignPdfResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Sign PDF document")
        .WithDescription("Signs a PDF document from MedicalRecordFile using CA Service digital signature, then re-encrypts and saves it back.");
    }
}