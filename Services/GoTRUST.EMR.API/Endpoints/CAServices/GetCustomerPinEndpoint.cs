using GoTRUST.EMR.Application.Features.CAServices.Commands;

namespace GoTRUST.EMR.API.Endpoints.CAServices;

public class GetCustomerPinEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/ca-services/customer/pin", async ([FromForm] GetCustomerPinCommand request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization()
        .WithTags("CAService")
        .WithName("GetCustomerPin")
        .Produces<Response<GetCustomerPinResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get customer PIN using face authentication")
        .WithDescription("Retrieves customer PIN by validating face image against customer ID.");
    }
}
