using GoTRUST.EMR.Application.Features.CAServices.Queries;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.CAService;

public class GetSignedPdfEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/ca-service/signed-pdf/{signedPdfId:guid}", async (Guid signedPdfId, ISender sender) =>
        {
            var query = new GetSignedPdfQuery { SignedPdfId = signedPdfId };
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithTags("CAService")
        .WithName("GetSignedPdf")
        .Produces<Response<GetSignedPdfResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Get signed PDF information")
        .WithDescription("Retrieves information about a signed PDF document, including download URL and decrypted file content if available.");
    }
}
