using GoTRUST.EMR.Application.Features.CAServices.Commands;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.CAService;

public class FindCustomerByFaceEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/ca-service/customers/search-by-face", async ([FromForm] FindCustomerByFaceCommand request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization()
        .WithTags("CAService")
        .WithName("FindCustomerByFace")
        .Produces<Response<FindCustomerByFaceResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Find customers by face image")
        .WithDescription("Searches for customers in CA Service using face recognition technology and returns matching customers.");
    }
}
