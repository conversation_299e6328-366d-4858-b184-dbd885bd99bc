using GoTRUST.EMR.Application.Features.CAServices.Commands;

namespace GoTRUST.EMR.API.Endpoints.CAServices;

public class CreateSignPdfRequestEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/ca-services/sign-pdf/create-request", async ([FromForm] CreateSignPdfRequestCommand request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization()
        .WithTags("CAService")
        .WithName("CreateSignPdfRequest")
        .Produces<Response<CreateSignPdfRequestResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Create a PDF signing request")
        .WithDescription("Creates a PDF signing request in CA Service and returns the signed PDF ID for later signing.");
    }
}
