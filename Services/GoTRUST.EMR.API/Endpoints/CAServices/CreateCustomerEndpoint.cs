using GoTRUST.EMR.Application.Features.CAServices.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.CAService;

public class CreateCustomerEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/ca-service/customers", async ([FromForm] CreateCustomerCommand request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .WithTags("CAService")
        .WithName("CreateCustomer")
        .Produces<Response<CreateCustomerResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Create a customer in CA Service")
        .WithDescription("Creates a new customer in CA Service with face recognition data and returns the created customer ID.");
    }
}
