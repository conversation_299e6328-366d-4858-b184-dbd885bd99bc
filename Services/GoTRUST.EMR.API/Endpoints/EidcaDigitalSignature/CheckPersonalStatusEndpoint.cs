using GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EidcaDigitalSignature;

public class CheckPersonalStatusEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/eidca/personal/status", async (
            [AsParameters] CheckPersonalStatusQuery req,
            ISender sender) =>
        {
            // var query = new CheckPersonalStatusQuery(transactionCode, tokenSignature);
            var result = await sender.Send(req);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithTags("EIDCA Digital Signature")
        .WithName("CheckPersonalStatus")
        .Produces<Response<CheckPersonalStatusResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Check personal certificate status")
        .WithDescription("Step 3: Check certificate status for personal onboarding process");
    }
}
