using GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EidcaDigitalSignature;

public class ConfirmDocumentSignatureEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/eidca/document/confirm-signature", async ([FromBody] ConfirmDocumentSignatureRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithTags("EIDCA Digital Signature")
        .WithName("ConfirmDocumentSignature")
        .Produces<Response<ConfirmDocumentSignatureResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Confirm document signature")
        .WithDescription("Step 2: Confirm document signature with digital signature data");
    }
}
