using GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EidcaDigitalSignature;

public class DownloadSignedFileEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/eidca/document/download", async (
            [AsParameters] DownloadSignedFileQuery query,
            ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithTags("EIDCA Digital Signature")
        .WithName("DownloadSignedFile")
        .Produces<Response<DownloadSignedFileResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Download signed file")
        .WithDescription("Download the signed document file after successful signing process");
    }
}
