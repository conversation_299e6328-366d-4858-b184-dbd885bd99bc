using GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EidcaDigitalSignature;

public class CheckDocumentSigningStatusEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/eidca/document/signing-status", async (
            [FromQuery] string transactionCode,
            [FromQuery] string tokenSign,
            ISender sender) =>
        {
            var query = new CheckDocumentSigningStatusQuery(transactionCode, tokenSign);
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithTags("EIDCA Digital Signature")
        .WithName("CheckDocumentSigningStatus")
        .Produces<Response<CheckDocumentSigningStatusResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Check document signing status")
        .WithDescription("Step 3: Check document signing status to monitor the signing process");
    }
}
