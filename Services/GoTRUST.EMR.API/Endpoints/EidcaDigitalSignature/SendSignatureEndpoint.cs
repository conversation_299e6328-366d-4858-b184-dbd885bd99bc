using GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EidcaDigitalSignature;

public class SendSignatureEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/eidca/personal/signature", async ([FromBody] SendSignatureRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithTags("EIDCA Digital Signature")
        .WithName("SendSignature")
        .Produces<Response<SendSignatureResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Send signature and selfie for personal onboarding")
        .WithDescription("Step 2: Send signature and selfie for personal onboarding verification");
    }
}
