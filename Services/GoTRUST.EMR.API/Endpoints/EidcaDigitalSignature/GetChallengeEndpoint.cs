using GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EidcaDigitalSignature;

public class GetChallengeEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/eidca/personal/challenge", async ([FromBody] GetChallengeRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .DisableAntiforgery()
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithTags("EIDCA Digital Signature")
        .WithName("GetChallenge")
        .Produces<Response<GetChallengeResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get challenge token for personal onboarding")
        .WithDescription("Step 1: Get challenge token for personal onboarding using ID number from citizen card");
    }
}
