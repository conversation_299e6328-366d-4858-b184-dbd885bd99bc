using GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Commands;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EidcaDigitalSignature;

public class SendDocumentForSigningEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/eidca/document/send-for-signing", async (
            [FromForm] SendDocumentForSigningRequest request,
            ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .DisableAntiforgery()
        .WithTags("EIDCA Digital Signature")
        .WithName("SendDocumentForSigning")
        .Produces<Response<SendDocumentForSigningResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Send document for signing challenge")
        .WithDescription("Step 1: Send document for signing challenge to initiate document signing process. Use multipart/form-data with fields: idNumber (string), signProps (string), docName (string, optional), securityLevel (string, optional), document (file)");
    }
}
