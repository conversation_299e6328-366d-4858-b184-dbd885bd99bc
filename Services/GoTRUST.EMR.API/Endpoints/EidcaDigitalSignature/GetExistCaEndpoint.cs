using GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.EidcaDigitalSignature;

public class GetExistCaEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/eidca/certificate/exist", async (
            [AsParameters] GetExistCaQuery req,
            ISender sender) =>
        {
            var result = await sender.Send(req);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
        .WithTags("EIDCA Digital Signature")
        .WithName("GetExistCa")
        .Produces<Response<GetExistCaResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get existing Certificate Authority")
        .WithDescription("Check if a Certificate Authority already exists for the given ID number");
    }
}
