using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.MedicalRecords.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords;

public class GetMedicalRecordById : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/medical-records/{id:guid}", async (Guid id, ISender sender) =>
            {
                var request = new GetMedicalRecordByIdRequest(id);
                var result = await sender.Send(request);
                return Results.Ok(result);
            })
            .WithTags("MedicalRecords")
            .WithName("GetMedicalRecordById")
            .Produces<Response<GetMedicalRecordByIdResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .WithSummary("Lấy thông tin chi tiết bệnh án")
            .WithDescription("L<PERSON>y thông tin chi tiết của một bệnh án theo ID bao gồm thông tin bệnh nhân và các tài liệu được nhóm theo template")
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMR });
    }
}