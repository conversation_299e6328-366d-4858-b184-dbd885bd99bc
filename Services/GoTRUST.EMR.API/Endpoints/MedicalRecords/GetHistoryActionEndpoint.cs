using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Features.MedicalRecords.Queries;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords
{
    public class GetHistoryActionEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/medical-records/history-actions", async (ISender sender) =>
            {
                var result = await sender.Send(new GetHistoryActionQuery());
                return Results.Ok(result);
            })
            .WithTags("MedicalRecords")
            .WithName("GetHistoryAction")
            .Produces<Response<List<HistoryActionDto>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Lấy danh sách hành động lịch sử")
            .WithDescription("Lấy danh sách hành động lịch sử của hồ sơ y tế")
            .RequireAuthorization();
        }
    }
}