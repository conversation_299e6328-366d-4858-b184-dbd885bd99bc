using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.MedicalRecords.Queries;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords;

public class GetAllMedicalRecords : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/medical-records", async (
                ISender sender, [AsParameters] GetAllMedicalRecordsRequest request) =>
            {
                var result = await sender.Send(request);
                return Results.Ok(result);
            })
            .WithTags("MedicalRecords")
            .WithName("GetAllMedicalRecords")
            .Produces<PaginationResponse<MedicalRecordsDto>>(StatusCodes.Status200OK)
            .WithSummary("L<PERSON>y danh sách tất cả bệnh án có phân trang")
            .WithDescription("<PERSON><PERSON><PERSON> danh sách tất cả bệnh án với tìm kiếm và phân trang")
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMR });
    }
}