using GoTRUST.EMR.Application.Features.MedicalRecords.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords;

public class UpdateMedicalRecord : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPut("/medical-records/{id:guid}",
            async (
                Guid id,
                UpdateMedicalRecordDto body,
                ISender sender) =>
            {
                var result = await sender.Send(new UpdateMedicalRecordRequest(id, body));
                return Results.Ok(result);
            })
            .WithTags("MedicalRecords")
            .WithName("UpdateMedicalRecord")
            .Produces<Response<UpdateMedicalRecordResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
            .WithSummary("Cập nhật thông tin bệnh án")
            .WithDescription("Cập nhật thông tin của một bệnh án bao gồm template data, JSON data")
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMU });
    }
}