using GoTRUST.EMR.Application.Features.MedicalRecords.Commands;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;
using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Mvc;
using System.Net.Mime;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords;

public class DownloadMedicalRecordFileEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/medical-records/files/{fileId:guid}/download",
            async (Guid fileId, ISender sender) =>
            {
                var request = new DownloadMedicalRecordFileRequest(fileId);
                var result = await sender.Send(request);

                var fileStreamResult = result.Data;

                if (fileStreamResult == null || fileStreamResult.FileStream == null)
                    return Results.Problem("Không thể tải file.", statusCode: StatusCodes.Status400BadRequest);

                return Results.File(
                    fileStreamResult.FileStream,
                    fileStreamResult.ContentType,
                    fileStreamResult.FileDownloadName
                );
            })
            .WithTags("MedicalRecords")
            .WithName("DownloadMedicalRecordFile")
            .Produces<FileStreamResult>(StatusCodes.Status200OK, MediaTypeNames.Application.Octet)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .WithSummary("Tải xuống file bệnh án")
            .WithDescription("Tải xuống một file cụ thể của bệnh án. File sẽ được tự động giải mã nếu đã được mã hóa.")
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}
