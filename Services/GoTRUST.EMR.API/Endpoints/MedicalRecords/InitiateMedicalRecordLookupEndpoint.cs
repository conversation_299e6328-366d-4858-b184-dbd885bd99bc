using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.MedicalRecordLookup.Commands;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords;

/// <summary>
/// Endpoint để khởi tạo phiên tra cứu bệnh án
/// </summary>
public class InitiateMedicalRecordLookupEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/medical-records/initiate-lookup", async (
            InitiateMedicalRecordLookupRequest request,
            ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("MedicalRecords")
        .WithName("InitiateMedicalRecordLookup")
        .Produces<Response<InitiateMedicalRecordLookupResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Khởi tạo phiên tra cứu bệnh án")
        .WithDescription("Khởi tạo phiên tra cứu bệnh án bằng CCCD, tạo OTP và lưu vào cache");
    }
}
