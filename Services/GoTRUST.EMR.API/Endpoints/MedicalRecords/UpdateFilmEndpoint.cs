using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Features.MedicalRecords.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords
{
    public record UpdateFilmRequestBody(
        int XRayCount,
        int RMICount,
        int CTCount,
        int ExternalFilmCount
    );
    public class UpdateFilmEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapPut("/medical-records/{id}/films", async (Guid id, UpdateFilmRequestBody request, ISender sender) =>
            {
                var req = new UpdateFilmRequest
                {
                    Id = id,
                    XRayCount = request.XRayCount,
                    RMICount = request.RMICount,
                    CTCount = request.CTCount,
                    ExternalFilmCount = request.ExternalFilmCount
                };
                var result = await sender.Send(req);
                return Results.Ok(result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMU })
            .WithTags("MedicalRecords")
            .WithName("UpdateFilm")
            .Produces<Response<string>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Cập nhật phim y tế")
            .WithDescription("Cập nhật thông tin phim y tế theo ID");
        }
    }
}