using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Features.MedicalRecords.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords
{
    public record RejectedMedicalRecordRequestBody(
        string Reason
    );
    public class RejectedMedicalRecordEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapPut("/medical-records/{id}/reject", async (Guid id, [FromBody] RejectedMedicalRecordRequestBody request, ISender sender) =>
            {
                var req = new RejectedMedicalRecordRequest
                {
                    Id = id,
                    Reason = request.Reason
                };
                var result = await sender.Send(req);
                return Results.Ok(result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMA })
            .WithTags("MedicalRecords")
            .WithName("RejectedMedicalRecord")
            .Produces<Response<ApprovedMedicalRecordResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Từ chối hồ sơ bệnh án")
            .WithDescription("Từ chối hồ sơ bệnh án theo ID");
        }
    }
}