using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Features.MedicalRecords.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords
{
    public class ApprovedMedicalRecordEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapPut("/medical-records/{id}/approve", async (Guid id, ISender sender) =>
            {
                var req = new ApprovedMedicalRecordRequest
                {
                    Id = id
                };
                var result = await sender.Send(req);
                return Results.Ok(result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMA })
            .WithTags("MedicalRecords")
            .WithName("ApprovedMedicalRecord")
            .Produces<Response<ApprovedMedicalRecordResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("<PERSON>ấ<PERSON> nhận hồ sơ bệnh án")
            .WithDescription("Chấ<PERSON> nhận hồ sơ bệnh án theo ID");
        }
    }
}