using GoTRUST.EMR.Application.Features.MedicalRecords.Commands;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords;

public class AddMedicalRecordFiles : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/medical-records/{medicalRecordId:guid}/files",
            async (
                Guid medicalRecordId,
                List<AddMedicalRecordFileItem> items,
                ISender sender) =>
            {
                var request = new AddMedicalRecordFilesRequest(medicalRecordId, items);
                var result = await sender.Send(request);
                return Results.Created($"/medical-records/{medicalRecordId}/files", result);
            })
            .WithTags("MedicalRecords")
            .WithName("AddMedicalRecordFiles")
            .Produces<Response<AddMedicalRecordFilesResponse>>(StatusCodes.Status201Created)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
            .WithSummary("Thêm files/records vào bệnh án hiện có")
            .WithDescription("Thêm các file và dữ liệu bổ sung vào một bệnh án đã tồn tại. Mỗi item sẽ tạo ra một file PDF từ template và data JSON tương ứng.")
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}
