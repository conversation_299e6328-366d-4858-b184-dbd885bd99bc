using GoTRUST.EMR.Application.Features.MedicalRecordLookup.Queries;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords;

/// <summary>
/// Endpoint để xác thực OTP và tra cứu lịch sử khám chữa bệnh
/// </summary>
public class VerifyAndLookupMedicalRecordsEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/medical-records/verify-and-lookup", async (
            VerifyAndLookupMedicalRecordsRequest request,
            ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("MedicalRecords")
        .WithName("VerifyAndLookupMedicalRecords")
        .Produces<Response<VerifyAndLookupMedicalRecordsResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("<PERSON><PERSON><PERSON> thực OTP và tra cứu lịch sử khám chữa bệnh")
        .WithDescription("Xác thực mã OTP và trả về thông tin bệnh nhân cùng lịch sử khám chữa bệnh trong khoảng thời gian được chỉ định");
    }
}
