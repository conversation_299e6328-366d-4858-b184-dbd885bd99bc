using GoTRUST.EMR.Application.Features.MedicalRecords.Queries;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords;

public class GetAllMedicalRecordsByPatient : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/medical-records/patient", async (
                ISender sender, [AsParameters] GetMedicalRecordsByPatientsRequest request) =>
            {
                var result = await sender.Send(request);
                return Results.Ok(result);
            })
            .WithTags("MedicalRecords")
            .WithName("GetMedicalRecordsByPatient")
            .Produces<Response<GetMedicalRecordsByPatientsResponse>>(StatusCodes.Status200OK)
            .WithSummary("<PERSON><PERSON>y danh sách bệnh án theo bệnh nhân")
            .WithDescription("<PERSON><PERSON><PERSON> danh sách bệnh án của bệnh nhân theo từ khoá.")
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMR });
    }
}