using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Application.Features.MedicalRecords.Queries;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords;

/// <summary>
/// Endpoint để lấy danh sách các lần nhập viện của bệnh nhân từ HIS
/// </summary>
public class GetPatientAdmissionsEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/medical-records/{patientId:guid}/admissions", async (
            Guid patientId,
            ISender sender) =>
        {
            var request = new GetPatientAdmissionsRequest(patientId);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMR })
        .WithTags("MedicalRecords")
        .WithName("GetPatientAdmissions")
        .Produces<Response<GetPatientAdmissionsResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .WithSummary("Lấy danh sách các lần nhập viện của bệnh nhân")
        .WithDescription("Lấy danh sách tất cả các lần nhập viện của bệnh nhân từ hệ thống HIS. " +
                        "Yêu cầu quyền truy cập Patient Medical Records (PMR). " +
                        "Chỉ có thể lấy thông tin bệnh nhân thuộc bệnh viện hiện tại.");
    }
}