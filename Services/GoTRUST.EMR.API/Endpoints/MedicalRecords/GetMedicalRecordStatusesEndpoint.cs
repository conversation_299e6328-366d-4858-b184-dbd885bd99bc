using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.MedicalRecords.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords;

public class GetMedicalRecordStatuses : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/medical-records/statuses", async (ISender sender) =>
            {
                var request = new GetMedicalRecordStatusesRequest();
                var result = await sender.Send(request);
                return Results.Ok(result);
            })
            .WithTags("MedicalRecords")
            .WithName("GetMedicalRecordStatuses")
            .Produces<Response<GetMedicalRecordStatusesResponse>>(StatusCodes.Status200OK)
            .WithSummary("Lấy danh sách trạng thái bệnh án")
            .WithDescription("<PERSON><PERSON>y danh sách tất cả trạng thái bệnh án có thể với mô tả tiếng Việt")
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMR });
    }
}
