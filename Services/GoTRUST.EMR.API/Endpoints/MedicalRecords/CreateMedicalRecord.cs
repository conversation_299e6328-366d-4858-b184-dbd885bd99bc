using GoTRUST.EMR.Application.Features.MedicalRecords.Commands;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords;

public class CreateMedicalRecord : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/medical-records",
            async (CreateMedicalRecordRequest request, ISender sender) =>
            {
                var result = await sender.Send(request);
                return Results.Created($"/medical-records/{result.Data?.Id}", result);
            })
            .WithTags("MedicalRecords")
            .WithName("CreateMedicalRecord")
            .Produces<Response<CreateMedicalRecordResponse>>(StatusCodes.Status201Created)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
            .WithSummary("Tạo bệnh án mới")
            .WithDescription("Tạo bệnh án mới cho bệnh nhân")
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC });
    }
}