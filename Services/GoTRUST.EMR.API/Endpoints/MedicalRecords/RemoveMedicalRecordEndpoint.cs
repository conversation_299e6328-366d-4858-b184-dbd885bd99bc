using GoTRUST.EMR.Application.Features.MedicalRecords.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords
{
    public class RemoveMedicalRecordEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapDelete("/medical-records/{id:guid}",
                async (Guid id, ISender sender) =>
                {
                    var request = new RemoveMedicalRecordRequest(id);
                    var result = await sender.Send(request);
                    return Results.Ok(result);
                })
                .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMD })
                .WithTags("MedicalRecords")
                .WithName("RemoveMedicalRecord")
                .Produces<Response<object>>(StatusCodes.Status200OK)
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
                .WithSummary("Remove Medical Record")
                .WithDescription("Removes a medical record by its ID.");
        }
    }
}