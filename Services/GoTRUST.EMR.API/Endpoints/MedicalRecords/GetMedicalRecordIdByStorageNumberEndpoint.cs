using GoTRUST.EMR.Application.Features.MedicalRecords.Queries;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords
{
    public class GetMedicalRecordIdByStorageNumberEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/api/medical-records/storage-number/{storageNumber}", async (string storageNumber, ISender sender) =>
            {
                var query = new GetMedicalRecordIdByStorageNumberRequest(storageNumber);
                var result = await sender.Send(query);
                return Results.Ok(result);
            })
            .WithTags("MedicalRecords")
            .WithName("GetMedicalRecordIdByStorageNumber")
            .Produces<Response<GetMedicalRecordIdByStorageNumberResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status404NotFound)
            .WithSummary("<PERSON><PERSON>y <PERSON> hồ sơ bệnh án theo số lưu trữ")
            .WithDescription("Truy xuất ID của hồ sơ bệnh án dựa trên số lưu trữ đã cho.")
            .RequireAuthorization();
        }
    }
}