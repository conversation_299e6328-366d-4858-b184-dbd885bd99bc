
using GoTRUST.EMR.Application.Features.MedicalRecords.Queries;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords
{
    public class GetTotalBrrowingMedicalRecordEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/medical-records/total-borrowing", async (IMediator mediator) =>
            {
                var result = await mediator.Send(new GetTotalBrrowingMedicalRecordRequest());
                return Results.Ok(result);
            })
            .WithName("GetTotalBrrowingMedicalRecord")
            .WithTags("MedicalRecords")
            .Produces<Response<GetTotalBrrowingMedicalRecordResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
            .WithSummary("Get total borrowing medical record")
            .WithDescription("Get total borrowing medical record")
            .RequireAuthorization();
        }
    }
}