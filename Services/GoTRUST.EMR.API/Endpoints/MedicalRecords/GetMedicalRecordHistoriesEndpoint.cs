using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Features.MedicalRecords.Queries;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords
{
    public class GetMedicalRecordHistoriesEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapGet("/api/medical-records/{id}/logs", async (Guid id, ISender sender) =>
            {
                var result = await sender.Send(new GetMedicalRecordHistoryQuery { MedicalRecordId = id });
                return Results.Ok(result);
            })
            .WithTags("MedicalRecords")
            .WithName("GetMedicalRecordHistories")
            .Produces<Response<List<MedicalRecordHistoryDto>>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("<PERSON><PERSON>y lịch sử hồ sơ y tế")
            .WithDescription("<PERSON><PERSON><PERSON> lịch sử của hồ sơ y tế theo ID")
            .RequireAuthorization();
        }
    }
}