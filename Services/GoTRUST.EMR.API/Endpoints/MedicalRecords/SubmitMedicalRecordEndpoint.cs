using Carter;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using GoTRUST.EMR.Application.Features.MedicalRecords.Commands;
using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecords
{
    public class SubmitMedicalRecordEndpoint : ICarterModule
    {
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            app.MapPut("/medical-records/{id}/submit", async (Guid id, ISender sender) =>
            {
                var req = new SubmitMedicalRecordRequest { Id = id };
                var result = await sender.Send(req);
                return Results.Ok(result);
            })
            .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.EMC })
            .WithTags("MedicalRecords")
            .WithName("SubmitMedicalRecord")
            .Produces<Response<SubmitMedicalRecordResponse>>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Gửi du<PERSON><PERSON><PERSON> hồ sơ bệnh án")
            .WithDescription("Cập nhật trạng thái hồ sơ bệnh án từ Draft sang Pending theo ID");
        }
    }
}
