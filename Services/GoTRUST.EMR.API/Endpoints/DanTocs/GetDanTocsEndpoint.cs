using GoTRUST.EMR.Application.Features.DanTocs.Queries;

namespace GoTRUST.EMR.API.Endpoints.DanTocs;

public class GetDanTocsEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/dantocs", async (ISender sender) =>
        {
            var result = await sender.Send(new GetDanTocsRequest());
            return Results.Ok(result);
        })
        .WithTags("DanTocs")
        .WithName("GetDanTocs")
        .Produces<Response<List<GetDanTocsResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get all DanTocs")
        .WithDescription("Returns a list of all DanTocs.");
    }
}
