﻿using GoTRUST.EMR.Application.Features.Authenticates.Commands;

namespace GoTRUST.EMR.API.Endpoints.Authenticates;

public class SetPassword : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/authenticates/set-password", async (SetPasswordRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization()
        .WithTags("Authenticates")
        .WithName("SetPassword")
        .Produces<Response<SetPasswordResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Set user password")
        .WithDescription("Change the password for the authenticated user");
    }
}