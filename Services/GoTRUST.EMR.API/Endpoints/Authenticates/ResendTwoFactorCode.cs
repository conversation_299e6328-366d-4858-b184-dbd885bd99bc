﻿using GoTRUST.EMR.Application.Features.Authenticates.Commands;

namespace GoTRUST.EMR.API.Endpoints.Authenticates;

public class ResendTwoFactorCode : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/authenticates/resend-2fa", async (ResendTwoFactorCodeRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("Authenticates")
        .WithName("ResendTwoFactorCode")
        .Produces<Response<ResendTwoFactorCode>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Resend two factor code for user")
        .WithDescription("Resend two factor code for user to email if the code is not consumed or expired");
    }
}