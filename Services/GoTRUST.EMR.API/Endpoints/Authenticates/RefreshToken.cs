﻿using GoTRUST.EMR.Application.Features.Authenticates.Commands;

namespace GoTRUST.EMR.API.Endpoints.Authenticates;

public class RefreshToken : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/authenticates/refresh-token", async (RefreshTokenRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("Authenticates")
        .WithName("RefreshToken")
        .Produces<Response<RefreshTokenResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Refresh token")
        .WithDescription("Refresh access and refresh tokens");
    }
}