﻿using GoTRUST.EMR.Application.Features.Authenticates.Commands;

namespace GoTRUST.EMR.API.Endpoints.Authenticates;

public class ApproveLogin : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/authenticates/approve-login", async (ApproveLoginRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("Authenticates")
        .WithName("ApproveLogin")
        .Produces<Response<ApproveLoginResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("ApproveLogin user")
        .WithDescription("Authenticate user and return tokens");
    }
}