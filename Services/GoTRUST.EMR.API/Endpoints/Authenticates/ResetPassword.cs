﻿using GoTRUST.EMR.Application.Features.Authenticates.Commands;

namespace GoTRUST.EMR.API.Endpoints.Authenticates;

public class ResetPassword : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/authenticates/reset-password", async (ResetPasswordRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("Authenticates")
        .WithName("ResetPassword")
        .Produces<Response<SetPasswordResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Reset user password")
        .WithDescription("Reset the password for a user");
    }
}