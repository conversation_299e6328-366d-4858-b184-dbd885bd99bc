﻿using GoTRUST.EMR.Application.Features.Authenticates.Commands;

namespace GoTRUST.EMR.API.Endpoints.Authenticates;

public class Register : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/authenticates/register", async (RegisterRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("Authenticates")
        .WithName("Register")
        .Produces<RegisterResponse>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Register new user")
        .WithDescription("Register a new user account");
    }
}