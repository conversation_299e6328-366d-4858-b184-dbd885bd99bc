using GoTRUST.EMR.Application.Features.Authenticates.Queries;
using MediatR;

namespace GoTRUST.EMR.API.Endpoints.Authenticates;

public class GetUserRoleFunctionsEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/authenticates/user-role-functions", async (
            ISender sender) =>
        {
            var result = await sender.Send(new GetUserRoleFunctionsQuery());
            return Results.Ok(result);
        })
        .RequireAuthorization()
        .WithTags("Authenticates")
        .WithName("GetUserRoleFunctions")
        .Produces<Response<List<GetUserRoleFunctionRes>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get user role functions")
        .WithDescription("Retrieves the list of functions available to the current user's roles.");
    }
}
