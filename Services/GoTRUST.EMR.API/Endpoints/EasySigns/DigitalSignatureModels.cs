namespace GoTRUST.EMR.API.Endpoints.EasySigns;

#region Request Models

public class SignMedicalRecordRequest
{
    public string PatientId { get; set; } = string.Empty;
    public string DoctorId { get; set; } = string.Empty;
    public string DoctorSerial { get; set; } = string.Empty;
    public string DoctorPin { get; set; } = string.Empty;
    public string PdfBase64 { get; set; } = string.Empty;
    public SignaturePosition? SignaturePosition { get; set; }
}

public class SignaturePosition
{
    public int X { get; set; } = 400;
    public int Y { get; set; } = 100;
    public int Width { get; set; } = 200;
    public int Height { get; set; } = 80;
    public int PageNum { get; set; } = 1;
}

public class SignPrescriptionRequest
{
    public string PatientId { get; set; } = string.Empty;
    public string DoctorId { get; set; } = string.Empty;
    public string HospitalId { get; set; } = string.Empty;
    public string DoctorSerial { get; set; } = string.Empty;
    public string DoctorPin { get; set; } = string.Empty;
    public List<Medication> Medications { get; set; } = new();
    public string Notes { get; set; } = string.Empty;
}

public class Medication
{
    public string Name { get; set; } = string.Empty;
    public string Dosage { get; set; } = string.Empty;
    public string Frequency { get; set; } = string.Empty;
    public string Duration { get; set; } = string.Empty;
    public string Instructions { get; set; } = string.Empty;
}

#endregion
