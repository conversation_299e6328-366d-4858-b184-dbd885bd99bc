using BuildingBlocks.Common.EasySign.Extensions;
using BuildingBlocks.Common.EasySign.Services;
using Carter;
using Microsoft.AspNetCore.Mvc;

namespace GoTRUST.EMR.API.Endpoints.EasySigns;

/// <summary>
/// <PERSON>le cho chức năng ký số trong GoTRUST EMR
/// T<PERSON><PERSON> hợp ký số cho hồ sơ bệnh án điện tử
/// </summary>
public class DigitalSignatureModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("api/digital-signature")
            .WithTags("Digital Signature")
            .WithDescription("Chức năng ký số cho hồ sơ bệnh án điện tử");

        // Medical Record Signing
        group.MapPost("sign-medical-record", SignMedicalRecord)
            .WithName("SignMedicalRecord")
            .WithSummary("<PERSON><PERSON> hồ sơ bệnh án PDF")
            .WithDescription("<PERSON><PERSON> hồ sơ bệnh án PDF với chứng thư số của bác sĩ")
            .Produces<FileResult>(200, "application/pdf")
            .Produces<object>(400)
            .Produces<object>(500);

        group.MapPost("sign-prescription", SignPrescription)
            .WithName("SignPrescription")
            .WithSummary("Ký đơn thuốc điện tử")
            .WithDescription("Ký đơn thuốc điện tử theo chuẩn XML")
            .Produces<FileResult>(200, "application/xml")
            .Produces<object>(400)
            .Produces<object>(500);

        // Verification
        group.MapPost("verify-medical-record", VerifyMedicalRecord)
            .WithName("VerifyMedicalRecord")
            .WithSummary("Xác thực chữ ký hồ sơ bệnh án")
            .WithDescription("Xác thực tính hợp lệ của chữ ký trong file PDF")
            .Accepts<IFormFile>("multipart/form-data")
            .Produces<object>(200)
            .Produces<object>(400)
            .Produces<object>(500);

        // Certificate Management
        group.MapGet("doctor-certificate", GetDoctorCertificate)
            .WithName("GetDoctorCertificate")
            .WithSummary("Lấy thông tin chứng thư số của bác sĩ")
            .WithDescription("Truy xuất thông tin chi tiết chứng thư số")
            .Produces<object>(200)
            .Produces<object>(400)
            .Produces<object>(500);

        group.MapGet("doctor-signature-image", GetDoctorSignatureImage)
            .WithName("GetDoctorSignatureImage")
            .WithSummary("Lấy ảnh chữ ký của bác sĩ")
            .WithDescription("Tải về ảnh chữ ký từ chứng thư số")
            .Produces<FileResult>(200, "image/png")
            .Produces<object>(400)
            .Produces<object>(500);
    }

    private static async Task<IResult> SignMedicalRecord(
        SignMedicalRecordRequest request,
        EasySignService easySignService,
        ILogger<DigitalSignatureModule> logger)
    {
        try
        {
            logger.LogInformation("Bắt đầu ký hồ sơ bệnh án cho bệnh nhân: {PatientId}", request.PatientId);

            // Validate input
            if (string.IsNullOrEmpty(request.PdfBase64))
                return Results.BadRequest(new { error = "Dữ liệu PDF không hợp lệ" });

            // Lấy ảnh chữ ký của bác sĩ
            var imageResult = await easySignService.GetCertificateImageAsync(
                request.DoctorSerial,
                request.DoctorPin
            );

            string? signatureImage = null;
            if (EasySignService.IsSuccess(imageResult))
            {
                signatureImage = imageResult.Data;
            }

            // Ký PDF tại vị trí chỉ định
            var result = await easySignService.SignVisiblePdfAsync(
                pdfBase64: request.PdfBase64,
                documentName: $"Medical_Record_{request.PatientId}_{DateTime.Now:yyyyMMdd}.pdf",
                serial: request.DoctorSerial,
                pin: request.DoctorPin,
                x: request.SignaturePosition?.X ?? 400,
                y: request.SignaturePosition?.Y ?? 100,
                width: request.SignaturePosition?.Width ?? 200,
                height: request.SignaturePosition?.Height ?? 80,
                pageNum: request.SignaturePosition?.PageNum ?? 1,
                imageSignature: signatureImage
            );

            if (EasySignService.IsSuccess(result))
            {
                var signedPdfBytes = Convert.FromBase64String(result.Data!);

                logger.LogInformation("Ký hồ sơ bệnh án thành công cho bệnh nhân: {PatientId}", request.PatientId);

                return Results.File(
                    signedPdfBytes,
                    "application/pdf",
                    $"Signed_Medical_Record_{request.PatientId}.pdf"
                );
            }
            else
            {
                logger.LogError("Ký hồ sơ bệnh án thất bại: {Error}", EasySignService.GetErrorMessage(result));
                return Results.BadRequest(new { error = EasySignService.GetErrorMessage(result) });
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Lỗi khi ký hồ sơ bệnh án cho bệnh nhân: {PatientId}", request.PatientId);
            return Results.Problem(
                detail: "Lỗi hệ thống khi ký hồ sơ",
                statusCode: 500
            );
        }
    }

    private static async Task<IResult> SignPrescription(
        SignPrescriptionRequest request,
        EasySignService easySignService,
        ILogger<DigitalSignatureModule> logger)
    {
        try
        {
            logger.LogInformation("Bắt đầu ký đơn thuốc cho bệnh nhân: {PatientId}", request.PatientId);

            // Tạo nội dung đơn thuốc dạng XML để ký
            var prescriptionXml = GeneratePrescriptionXml(request);
            var xmlBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(prescriptionXml));

            var result = await easySignService.SignXmlAsync(
                xmlBase64: xmlBase64,
                documentName: $"Prescription_{request.PatientId}_{DateTime.Now:yyyyMMdd}.xml",
                serial: request.DoctorSerial,
                pin: request.DoctorPin
            );

            if (EasySignService.IsSuccess(result))
            {
                var signedDocument = result.Data?.ResponseContentList?.FirstOrDefault()?.SignedDocument;
                if (!string.IsNullOrEmpty(signedDocument))
                {
                    var signedXmlBytes = Convert.FromBase64String(signedDocument);

                    logger.LogInformation("Ký đơn thuốc thành công cho bệnh nhân: {PatientId}", request.PatientId);

                    return Results.File(
                        signedXmlBytes,
                        "application/xml",
                        $"Signed_Prescription_{request.PatientId}.xml"
                    );
                }
                return Results.BadRequest(new { error = "Không có dữ liệu đơn thuốc đã ký" });
            }
            else
            {
                logger.LogError("Ký đơn thuốc thất bại: {Error}", EasySignService.GetErrorMessage(result));
                return Results.BadRequest(new { error = EasySignService.GetErrorMessage(result) });
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Lỗi khi ký đơn thuốc cho bệnh nhân: {PatientId}", request.PatientId);
            return Results.Problem(
                detail: "Lỗi hệ thống khi ký đơn thuốc",
                statusCode: 500
            );
        }
    }

    private static async Task<IResult> VerifyMedicalRecord(
        IFormFile file,
        EasySignService easySignService,
        ILogger<DigitalSignatureModule> logger)
    {
        try
        {
            if (file == null || file.Length == 0)
                return Results.BadRequest(new { error = "File không hợp lệ" });

            logger.LogInformation("Bắt đầu xác thực hồ sơ bệnh án: {FileName}", file.FileName);

            using var stream = file.OpenReadStream();
            var result = await easySignService.VerifyPdfAsync(stream, file.FileName);

            if (EasySignService.IsSuccess(result))
            {
                var signatures = result.Data?.SignatureVfDTOs;
                var verificationResult = new
                {
                    fileName = file.FileName,
                    isValid = signatures?.Any(s => s.Integrity) == true,
                    signatureCount = signatures?.Count ?? 0,
                    signatures = signatures?.Select(s => new
                    {
                        revision = $"{s.Revision}/{s.TotalRevision}",
                        signTime = s.SignTime,
                        integrity = s.Integrity,
                        coverWholeDocument = s.CoverWholeDocument,
                        certificates = s.CertificateVfDTOs?.Select(c => new
                        {
                            subject = ExtractDoctorName(c.SubjectDn),
                            issuer = c.Issuer,
                            validFrom = c.ValidFrom,
                            validTo = c.ValidTo,
                            status = c.CurrentStatus,
                            revocationStatus = c.RevocationStatus
                        })
                    }),
                    verificationTime = DateTime.Now
                };

                logger.LogInformation("Xác thực hồ sơ bệnh án thành công: {FileName}", file.FileName);
                return Results.Ok(verificationResult);
            }
            else
            {
                logger.LogError("Xác thực hồ sơ bệnh án thất bại: {Error}", EasySignService.GetErrorMessage(result));
                return Results.BadRequest(new { error = EasySignService.GetErrorMessage(result) });
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Lỗi khi xác thực hồ sơ bệnh án: {FileName}", file?.FileName);
            return Results.Problem(
                detail: "Lỗi hệ thống khi xác thực",
                statusCode: 500
            );
        }
    }

    private static async Task<IResult> GetDoctorCertificate(
        string serial,
        string pin,
        EasySignService easySignService,
        ILogger<DigitalSignatureModule> logger)
    {
        try
        {
            var result = await easySignService.GetCertificateAsync(serial, pin);

            if (EasySignService.IsSuccess(result))
            {
                return Results.Ok(new
                {
                    serial = serial,
                    certificateData = result.Data,
                    retrievedAt = DateTime.Now
                });
            }
            else
            {
                return Results.BadRequest(new { error = EasySignService.GetErrorMessage(result) });
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Lỗi khi lấy thông tin chứng thư số: {Serial}", serial);
            return Results.Problem(
                detail: "Lỗi hệ thống",
                statusCode: 500
            );
        }
    }

    private static async Task<IResult> GetDoctorSignatureImage(
        string serial,
        string pin,
        EasySignService easySignService,
        ILogger<DigitalSignatureModule> logger)
    {
        try
        {
            var result = await easySignService.GetCertificateImageAsync(serial, pin);

            if (EasySignService.IsSuccess(result) && !string.IsNullOrEmpty(result.Data))
            {
                var imageBytes = Convert.FromBase64String(result.Data);
                return Results.File(imageBytes, "image/png", $"signature_{serial}.png");
            }
            else
            {
                return Results.BadRequest(new { error = EasySignService.GetErrorMessage(result) });
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Lỗi khi lấy ảnh chữ ký: {Serial}", serial);
            return Results.Problem(
                detail: "Lỗi hệ thống",
                statusCode: 500
            );
        }
    }

    #region Helper Methods

    private static string GeneratePrescriptionXml(SignPrescriptionRequest request)
    {
        // Tạo XML đơn thuốc theo chuẩn
        var xml = $@"<?xml version=""1.0"" encoding=""UTF-8""?>
<Prescription xmlns=""http://schema.emr.gov.vn/prescription"" Id=""SigningData"">
    <Header>
        <PatientId>{request.PatientId}</PatientId>
        <DoctorId>{request.DoctorId}</DoctorId>
        <HospitalId>{request.HospitalId}</HospitalId>
        <Date>{DateTime.Now:yyyy-MM-dd}</Date>
        <Time>{DateTime.Now:HH:mm:ss}</Time>
    </Header>
    <Medications>
        {string.Join("", request.Medications.Select(m => $@"
        <Medication>
            <Name>{m.Name}</Name>
            <Dosage>{m.Dosage}</Dosage>
            <Frequency>{m.Frequency}</Frequency>
            <Duration>{m.Duration}</Duration>
            <Instructions>{m.Instructions}</Instructions>
        </Medication>"))}
    </Medications>
    <Notes>{request.Notes}</Notes>
</Prescription>";
        return xml;
    }

    private static string ExtractDoctorName(string? subjectDn)
    {
        if (string.IsNullOrEmpty(subjectDn))
            return "N/A";

        // Trích xuất tên bác sĩ từ Subject DN
        var cnIndex = subjectDn.IndexOf("CN=", StringComparison.OrdinalIgnoreCase);
        if (cnIndex >= 0)
        {
            var cnValue = subjectDn.Substring(cnIndex + 3);
            var commaIndex = cnValue.IndexOf(',');
            if (commaIndex > 0)
            {
                cnValue = cnValue.Substring(0, commaIndex);
            }
            return cnValue.Trim().Replace("\\", "");
        }

        return "N/A";
    }

    #endregion
}
