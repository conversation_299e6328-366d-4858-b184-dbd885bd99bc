using GoTRUST.EMR.Application.Features.Roles.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Roles;

public class GetRolesEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/roles", async (ISender sender) =>
        {
            var result = await sender.Send(new GetRolesQuery());
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSR })
        .WithTags("Roles")
        .WithName("GetRoles")
        .Produces<Response<List<GetRolesResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get all roles")
        .WithDescription("Returns a list of all roles.");
    }
}
