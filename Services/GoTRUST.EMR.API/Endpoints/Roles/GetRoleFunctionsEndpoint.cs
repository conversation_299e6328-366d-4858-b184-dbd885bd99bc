using GoTRUST.EMR.Application.Features.Roles.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Roles;

public class GetRoleFunctionsEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/roles/{id:guid}/functions", async (Guid id, ISender sender) =>
        {
            var result = await sender.Send(new GetRoleFunctionsQuery(id));
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSR })
        .WithTags("Roles")
        .WithName("GetRoleFunctions")
        .Produces<Response<GetRoleFunctionsResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Get role functions")
        .WithDescription("Gets the functions and permissions assigned to a role.");
    }
}
