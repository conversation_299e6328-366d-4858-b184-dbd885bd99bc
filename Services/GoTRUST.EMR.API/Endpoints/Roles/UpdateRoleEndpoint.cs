using GoTRUST.EMR.Application.Features.Roles.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Roles;

public record UpdateRoleRequestBody(
    string Name
);

public class UpdateRoleEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPut("/roles/{id:guid}", async (Guid id, UpdateRoleRequestBody body, ISender sender) =>
        {
            var request = new UpdateRoleRequest(id, body.Name);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSU })
        .WithTags("Roles")
        .WithName("UpdateRole")
        .Produces<Response<UpdateRoleResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Update a role")
        .WithDescription("Updates an existing role and returns the updated role.");
    }
}
