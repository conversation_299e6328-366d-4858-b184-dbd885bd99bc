using GoTRUST.EMR.Application.Features.Roles.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Roles;

public class UpdateRoleFunctionsEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPut("/roles/{id:guid}/functions", async (Guid id, UpdateRoleFunctionRequestBody body, ISender sender) =>
        {
            var request = new UpdateRoleFunctionRequest(id, body.RoleFunctionList);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSU })
        .WithTags("Roles")
        .WithName("UpdateRoleFunctions")
        .Produces<Response<UpdateRoleFunctionResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Update role functions")
        .WithDescription("Updates the functions and permissions assigned to a role.");
    }
}

public record UpdateRoleFunctionRequestBody(List<UpdateRoleFunctionReq> RoleFunctionList);
