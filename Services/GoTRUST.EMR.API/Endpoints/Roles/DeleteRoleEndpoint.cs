using GoTRUST.EMR.Application.Features.Roles.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.Roles;

public class DeleteRoleEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapDelete("/roles/{id:guid}", async (Guid id, ISender sender) =>
        {
            var request = new DeleteRoleRequest(id);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.MSD })
        .WithTags("Roles")
        .WithName("DeleteRole")
        .Produces<Response<DeleteRoleResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .WithSummary("Delete a role")
        .WithDescription("Deletes a role by id.");
    }
}
