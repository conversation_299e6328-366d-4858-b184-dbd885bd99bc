using GoTRUST.EMR.Application.Features.MedicalRecordTemplates.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecordTemplates;

public class GetMedicalRecordTemplateById : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/medical-record-templates/{id}", async (Guid id, ISender sender) =>
        {
            var request = new GetMedicalRecordTemplateByIdRequest(id);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("MedicalRecordTemplates")
        .WithName("GetMedicalRecordTemplateById")
        .Produces<Response<GetMedicalRecordTemplateByIdResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("<PERSON><PERSON>y chi tiết mẫu phiếu")
        .WithDescription("<PERSON><PERSON><PERSON> chi tiết mẫu phiếu theo ID")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FMR });
    }
}
