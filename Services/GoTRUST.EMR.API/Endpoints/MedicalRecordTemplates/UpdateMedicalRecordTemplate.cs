using GoTRUST.EMR.Application.Features.MedicalRecordTemplates.Commands;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecordTemplates;

public class UpdateMedicalRecordTemplate : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPut("/medical-record-templates/{id}", async (Guid id, UpdateMedicalRecordTemplateRequest request, ISender sender) =>
        {
            if (id != request.Id)
                return Results.BadRequest("ID trong URL không khớp với ID trong request body");

            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("MedicalRecordTemplates")
        .WithName("UpdateMedicalRecordTemplate")
        .Produces<Response<UpdateMedicalRecordTemplateResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Cập nhật mẫu phiếu")
        .WithDescription("Cập nhật thông tin mẫu phiếu")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FMU });
    }
}
