using GoTRUST.EMR.Application.Features.MedicalRecordTemplates.Queries;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecordTemplates;

public class GetAllMedicalRecordTemplates : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/medical-record-templates", async (
            ISender sender, [AsParameters] GetAllMedicalRecordTemplatesRequest request) =>
        {
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("MedicalRecordTemplates")
        .WithName("GetAllMedicalRecordTemplates")
        .Produces<Response<List<MedicalRecordTemplateDto>>>(StatusCodes.Status200OK)
        .WithSummary("<PERSON><PERSON>y danh sách mẫu phiếu")
        .WithDescription("<PERSON><PERSON>y danh sách tất cả mẫu phiếu")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FMR });
    }
}
