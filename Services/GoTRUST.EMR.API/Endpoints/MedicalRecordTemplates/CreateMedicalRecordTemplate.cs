using GoTRUST.EMR.Application.Features.MedicalRecordTemplates.Commands;
using Microsoft.AspNetCore.Authorization;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecordTemplates;

public class CreateMedicalRecordTemplate : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/medical-record-templates", async (CreateMedicalRecordTemplateRequest request, ISender sender) =>
        {
            var result = await sender.Send(request);
            return Results.Created($"/medical-record-templates/{result.Data!.Id}", result);
        })
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FMC })
        .WithTags("MedicalRecordTemplates")
        .WithName("CreateMedicalRecordTemplate")
        .Produces<Response<CreateMedicalRecordTemplateResponse>>(StatusCodes.Status201Created)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesValidationProblem(StatusCodes.Status422UnprocessableEntity)
        .WithSummary("Tạo mẫu phiếu mới")
        .WithDescription("Tạo mẫu phiếu mới trong hệ thống");
    }
}
