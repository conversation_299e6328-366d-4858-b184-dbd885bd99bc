using GoTRUST.EMR.Application.Features.MedicalRecordTemplates.Commands;
using GoTRUST.EMR.Domain.Constants;
using Microsoft.AspNetCore.Authorization;

namespace GoTRUST.EMR.API.Endpoints.MedicalRecordTemplates;

public class DeleteMedicalRecordTemplate : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapDelete("/medical-record-templates/{id}", async (Guid id, ISender sender) =>
        {
            var request = new DeleteMedicalRecordTemplateRequest(id);
            var result = await sender.Send(request);
            return Results.Ok(result);
        })
        .WithTags("MedicalRecordTemplates")
        .WithName("DeleteMedicalRecordTemplate")
        .Produces<Response<DeleteMedicalRecordTemplateResponse>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .WithSummary("Xóa mẫu phiếu")
        .WithDescription("Xóa mẫu phiếu khỏi hệ thống (soft delete)")
        .RequireAuthorization(new AuthorizeAttribute { Roles = RoleConstants.FMD });
    }
}
