﻿using GoTRUST.EMR.API;

using GoTRUST.EMR.Application;
using GoTRUST.EMR.Application.Hubs;
using GoTRUST.EMR.Infrastructure;
using GoTRUST.EMR.Infrastructure.Data.Extensions;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services
    .AddApplicationServices(builder.Configuration)
    .AddInfrastructureServices(builder.Configuration)
    .AddApiServices(builder.Configuration, builder.Host);

var app = builder.Build();

app.UsePathBase("/emr-management");

// Configure the HTTP request pipeline.
app.UseApiServices();

app.MapHub<NotificationHub>("/notification", options =>
{
    // Bật tính năng đóng kết nối khi token hết hạn
    options.CloseOnAuthenticationExpiration = true;
    
    // Giới hạn buffer size để tránh memory issues
    options.ApplicationMaxBufferSize = 64 * 1024; // 64KB
    
    // Timeout cho WebSocket transport
    options.WebSockets.CloseTimeout = TimeSpan.FromSeconds(30);
})
    .RequireCors(builder.Configuration["SignalRPolicy"] ?? "SignalRPolicy");

app.UseStaticFiles("");

if (!app.Environment.IsProduction())
{
    await app.InitialiseDatabaseAsync();
}
app.Run();
