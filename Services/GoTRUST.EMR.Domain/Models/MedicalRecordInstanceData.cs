namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Medical record instance data
    /// </summary>
    public class MedicalRecordInstanceData : Entity<Guid>
    {
        /// <summary>
        /// Medical record ID
        /// </summary>
        public Guid MedicalRecordId { get; set; }
        
        /// <summary>
        /// Medical record template ID
        /// </summary>
        public Guid MedicalRecordTemplateId { get; set; }
        
        /// <summary>
        /// Medical record file ID
        /// </summary>
        public Guid? MedicalRecordFileId { get; set; }
        
        /// <summary>
        /// Date when data was recorded (for templates with different dates)
        /// </summary>
        public DateTime DateRecorded { get; set; }
        
        /// <summary>
        /// Template-specific data as JSO<PERSON> string
        /// </summary>
        public string DataJson { get; set; } = string.Empty;
        
        /// <summary>
        /// Navigation property to the Medical Record
        /// </summary>
        public virtual MedicalRecord MedicalRecord { get; set; } = null!;
        
        /// <summary>
        /// Navigation property to the Medical Record Template
        /// </summary>
        public virtual MedicalRecordTemplate MedicalRecordTemplate { get; set; } = null!;
        
        /// <summary>
        /// Navigation property to the Medical Record File
        /// </summary>
        public virtual MedicalRecordFile? MedicalRecordFile { get; set; }
    }
}
