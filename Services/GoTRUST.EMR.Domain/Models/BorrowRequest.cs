namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Internal borrow request for medical records
    /// </summary>
    public class BorrowRequest : Aggregate<Guid>
    {
        /// <summary>
        /// Hospital ID
        /// </summary>
        public Guid HospitalId { get; set; }

        /// <summary>
        /// Medical record ID
        /// </summary>
        public Guid MedicalRecordId { get; set; }
        /// <summary>
        /// Medical record code associated with the borrow request
        /// </summary>
        public string MedicalRecordCode { get; set; } = string.Empty;

        /// <summary>
        /// User ID of the borrower (internal doctor)
        /// </summary>
        public Guid? BorrowerUserId { get; set; }

        /// <summary>
        /// User Code of the borrower (internal doctor)
        /// </summary>
        public string? BorrowerUserCode { get; set; }

        /// <summary>
        /// Purpose of borrowing (from list)
        /// </summary>
        public string Purpose { get; set; } = string.Empty;

        /// <summary>
        /// Notes
        /// </summary>
        public string Note { get; set; } = string.Empty;

        /// <summary>
        /// Request date
        /// </summary>
        public DateTime RequestedDate { get; set; }

        /// <summary>
        /// Borrow date (if approved)
        /// </summary>
        public DateTime? BorrowedDate { get; set; }

        /// <summary>
        /// Return date
        /// </summary>
        public DateTime? ReturnedDate { get; set; }

        /// <summary>
        /// User ID who approved the request (Hospital Tech Dept)
        /// </summary>
        public Guid? ApprovedByUserId { get; set; }

        /// <summary>
        /// User Code of the approver (Hospital Tech Dept)
        /// </summary>
        public string? ApprovedByUserCode { get; set; }

        /// <summary>
        /// Approval status (Pending approval, Approved, Rejected)
        /// </summary>
        public BorrowRequestStatus ApprovalStatus { get; set; } = BorrowRequestStatus.Pending;

        /// <summary>
        /// Reason for approval/rejection
        /// </summary>
        public string? ApprovalReason { get; set; }

        /// <summary>
        ///  Organization ID making the request (if external)
        /// </summary>
        public Guid? OrganizationId { get; set; }

        /// <summary>
        ///  User type of the borrower (UserTypeConstants: "Internal", "External")
        ///  This can be used to differentiate between internal and external borrow requests.
        /// </summary>
        public string UserType { get; set; } = null!;

        /// <summary>
        /// Navigation property to the Hospital
        /// </summary>
        public virtual Hospital Hospital { get; set; } = null!;

        /// <summary>
        /// Navigation property to the Medical Record
        /// </summary>
        public virtual MedicalRecord MedicalRecord { get; set; } = null!;

        /// <summary>
        ///  Navigation property to the Borrower User
        ///  </summary>
        public virtual User BorrowerUser { get; set; } = null!;

        ///  <summary>
        ///  Navigation property to the User who approved the request
        ///  </summary>
        public virtual User? ApprovedByUser { get; set; } = null!;

        /// <summary>
        /// Navigation property to the External Borrower Organization (if applicable)
        ///  </summary>
        public virtual BorrowerOrganization? Organization { get; set; } = null!;
    }
}
