namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Medical record file information
    /// </summary>
    public class MedicalRecordFile : Entity<Guid>
    {
        /// <summary>
        /// Medical record ID
        /// </summary>
        public Guid MedicalRecordId { get; set; }

        /// <summary>
        /// File name
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// File path (in cloud storage)
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// File type (e.g., application/pdf)
        /// </summary>
        public string FileType { get; set; } = string.Empty;

        /// <summary>
        /// Template type ("Mẫu phiếu", "Mẫu giấy", "Mẫu bệnh án", etc.)
        /// </summary>
        public MedicalRecordTemplateType Type { get; set; } = MedicalRecordTemplateType.MedicalRecord;

        /// <summary>
        /// Indicates if the file is encrypted
        /// </summary>
        public bool IsEncrypted { get; set; }

        /// <summary>
        /// Indicates if the file is signed by the patient
        /// </summary>
        public bool IsSignedByPatient { get; set; } = false;

        /// <summary>
        /// Initial vector of the medical record (IV)
        /// </summary>
        public string InitialVector { get; set; } = string.Empty;

        /// <summary>
        /// Upload time
        /// </summary>
        public DateTime UploadedAt { get; set; }

        /// <summary>
        /// Indicates if watermark is enabled
        /// </summary>
        public bool WatermarkEnabled { get; set; }

        /// <summary>
        /// Navigation property to the Medical Record
        /// </summary>
        public virtual MedicalRecord MedicalRecord { get; set; } = null!;

        /// <summary>
        /// Medical record data instances referring to this file
        /// </summary>
        public virtual ICollection<MedicalRecordInstanceData> InstanceData { get; set; } = [];
    }
}
