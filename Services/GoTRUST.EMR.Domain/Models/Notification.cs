namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Thông báo
    /// </summary>
    public class Notification : Aggregate<Guid>
    {
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        /// <summary>
        /// Các vai trò đ<PERSON> nhận. VD: "FMC,FMR"
        /// </summary>
        public List<string> RoleFunctionIds { get; set; } = [];
        public virtual ICollection<NotificationRecipient> NotificationRecipients { get; set; } = [];
    }
}
