namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// User action log
    /// </summary>
    public class UserActionLog : Entity<Guid>
    {
        /// <summary>
        /// Hospital ID
        /// </summary>
        public Guid? HospitalId { get; set; }

        /// <summary>
        /// Action name
        /// </summary>
        public string ActionName { get; set; } = string.Empty;

        /// <summary>
        /// Action details
        /// </summary>
        public string ActionDetail { get; set; } = string.Empty;

        /// <summary>
        /// Action icon (font awesome, SVG)
        /// </summary>
        public string ActionIcon { get; set; } = string.Empty;
        /// <summary>
        /// Action type
        /// </summary>
        public UserActionLogType? ActionType { get; set; }

        /// <summary>
        /// User ID who performed the action
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// Navigation property to the medical record
        /// </summary>
        public Guid? MedicalRecordId { get; set; }

        /// <summary>
        /// Action time
        /// </summary>
        public DateTime ActionTime { get; set; }

        /// <summary>
        /// IP address
        /// </summary>
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// Result (Success/Fail)
        /// </summary>
        public string Result { get; set; } = string.Empty;

        /// <summary>
        /// Navigation property to the Hospital
        /// </summary>
        public virtual Hospital? Hospital { get; set; }

        /// <summary>
        /// Navigation property to the MedicalRecord
        /// </summary>
        public virtual MedicalRecord? MedicalRecord { get; set; }
        
        public virtual User User { get; set; } = null!;
    }
}
