namespace GoTRUST.EMR.Domain.Models
{
    public class RefreshToken : Entity<Guid>
    {
        /// <summary>
        /// The token that will be used to refresh the access token
        /// </summary>
        public string Token { get; set; } = null!;

        /// <summary>
        /// The JWT ID of the access token that this refresh token is linked to
        /// </summary>
        public string JwtId { get; set; } = null!;

        /// <summary>
        /// The date and time when the refresh token was created
        /// </summary>
        public DateTime GeneratedAt { get; set; }

        /// <summary>
        /// The date and time when the refresh token expires
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// Created by IP address
        /// </summary>
        public string? CreatedByIp { get; set; }

        /// <summary>
        /// The ID of the user that this refresh token belongs to
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// The user that this refresh token belongs to
        /// </summary>
        public User User { get; set; } = null!;
    }
}