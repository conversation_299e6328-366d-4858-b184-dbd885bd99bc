
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIEU_LAO")]
    public class EMRLaoWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRLao? CHI_TIEU_LAO { get; set; }
    }
    public class EMRLao
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất.
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// L<PERSON> số thứ tự tăng từ 1 đến hết.
        ///</summary>
        public int STT { get; set; }

        ///<summary>
        /// Là mã người bệnh theo quy định của cơ sở KBCB.
        ///</summary>
        [StringLength(100)]
        public string MA_BN { get; set; } = string.Empty;

        ///<summary>
        /// Là họ và tên của người bệnh.
        ///</summary>
        [StringLength(255)]
        public string HO_TEN { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số căn cước công dân hoặc số chứng minh thư nhân dân hoặc số hộ chiếu của người bệnh.
        ///</summary>
        public string SO_CCCD { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã phân loại Bệnh nhân lao theo vị trí giải phẫu.
        ///</summary>
        public int PHANLOAI_LAO_VITRI { get; set; }

        ///<summary>
        /// Ghi mã phân loại Bệnh nhân lao theo tiền sử điều trị.
        ///</summary>
        public int PHANLOAI_LAO_TS { get; set; }

        ///<summary>
        /// Ghi mã phân loại bệnh nhân lao theo tình trạng nhiễm HIV.
        ///</summary>
        public int PHANLOAI_LAO_HIV { get; set; }

        ///<summary>
        /// Ghi mã phân loại Bệnh nhân lao theo bằng chứng vi khuẩn học.
        ///</summary>
        public int PHANLOAI_LAO_VK { get; set; }

        ///<summary>
        /// Ghi mã phân loại BN lao theo tình trạng kháng thuốc.
        ///</summary>
        public int PHANLOAI_LAO_KT { get; set; }

        ///<summary>
        /// Ghi mã loại điều trị lao.
        ///</summary>
        public int LOAI_DTRI_LAO { get; set; }

        ///<summary>
        /// Ghi thời điểm bắt đầu điều trị bệnh lao hoặc lao tiềm ẩn tại cơ sở KBCB, định dạng yyyymmdd.
        ///</summary>
        [StringLength(8)]
        public string NGAYBD_DTRI_LAO { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã phác đồ điều trị.
        ///</summary>
        public int PHACDO_DTRI_LAO { get; set; }

        [StringLength(8)]
        public string? NGAYKT_DTRI_LAO { get; set; }

        ///<summary>
        /// Ghi mã đánh giá kết quả điều trị.
        ///</summary>
        public int KET_QUA_DTRI_LAO { get; set; }

        ///<summary>
        /// Ghi mã cơ sở KBCB nơi người bệnh đến khám bệnh, điều trị do cơ quan có thẩm quyền cấp.
        ///</summary>
        [StringLength(5)]
        public string MA_CSKCB { get; set; } = string.Empty;

        [StringLength(8)]
        public string? NGAYKD_HIV { get; set; }

        [StringLength(8)]
        public string? BDDT_ARV { get; set; }

        [StringLength(8)]
        public string? NGAY_BAT_DAU_DT_CTX { get; set; }

        public string? DU_PHONG { get; set; }
    }
}
