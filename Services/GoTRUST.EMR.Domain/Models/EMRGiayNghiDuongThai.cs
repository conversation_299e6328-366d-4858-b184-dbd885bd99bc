
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIEU_GIAYNGHIDUONGTHAI")]
    public class EMRGiayNghiDuongThaiWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRGiayNghiDuongThai? CHI_TIEU_GIAYNGHIDUONGTHAI { get; set; }
    }
    public class EMRGiayNghiDuongThai
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất (dùng để liên kết giữa Bảng chỉ tiêu tổng hợp khám bệnh, chữa bệnh (bảng XML 1) và các bảng còn lại ban hành kèm theo Quyết định này trong một lần khám bệnh, chữa bệnh (PRIMARY KEY)).
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// <PERSON>hi số <PERSON>i chứng từ (Gi<PERSON><PERSON> chứng nhận nghỉ dưỡng thai) do cơ sở KBCB quy định theo mẫu tại Phụ lục 6 ban hành kèm theo Thông tư 56/2017/TT-BYT của Bộ trưởng Bộ Y tế.
        ///</summary>
        [StringLength(200)]
        public string SO_SERI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số chứng từ phục vụ việc quản lý nội bộ của cơ sở KBCB quy định theo mẫu tại Phụ lục 6 ban hành kèm theo Thông tư 56/2017/TT-BYT của Bộ trưởng Bộ Y tế.
        ///</summary>
        [StringLength(200)]
        public string SO_CT { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số ngày nghỉ căn cứ vào tình trạng sức khỏe của người bệnh (SO_NGAY = DEN_NGAY - TU_NGAY).
        ///</summary>
        public int SO_NGAY { get; set; }

        ///<summary>
        /// Ghi tên đơn vị của người hưởng.
        ///</summary>
        [StringLength(1024)]
        public string DON_VI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi cụm từ “dưỡng thai”.
        ///</summary>
        public string CHAN_DOAN_RV { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày bắt đầu nghỉ dưỡng thai, theo định dạng yyyymmdd. Ví dụ: Ngày khám là ngày 13 tháng 7 năm 2018 và phải nghỉ 30 ngày thì tại phần số ngày nghỉ để điều trị bệnh ghi là 30 ngày và ghi rõ là từ ngày 13 tháng 7 năm 2018 đến ngày 11 tháng 8 năm 2018).
        ///</summary>
        [StringLength(8)]
        public string TU_NGAY { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày kết thúc nghỉ dưỡng thai, theo định dạng yyyymmdd
        ///</summary>
        [StringLength(8)]
        public string DEN_NGAY { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã của người đứng đầu cơ sở KBCB hoặc người được người đứng đầu cơ sở KBCB ủy quyền được ký để cấp giấy chứng nhận nghỉ dưỡng thai (mã hoá theo số GPHN).
        ///</summary>
        [StringLength(255)]
        public string MA_TTDV { get; set; } = string.Empty;

        ///<summary>
        /// Ghi họ và tên của Trưởng khoa hoặc Phó trưởng khoa hoặc Bác sỹ hành nghề KBCB được uỷ quyền ký tên theo quy định của Thủ trưởng cơ sở KBCB.
        ///</summary>
        [StringLength(255)]
        public string TEN_BS { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã của Trưởng khoa hoặc Trưởng phòng hoặc Phó trưởng khoa hoặc Phó trưởng phòng hoặc Bác sỹ hành nghề KBCB ký tên theo quy định của Thủ trưởng cơ sở KBCB (mã hoá theo số GPHN).
        ///</summary>
        [StringLength(255)]
        public string MA_BS { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày cấp chứng từ (Giấy chứng nhận nghỉ dưỡng thai), theo định dạng yyyymmdd
        ///</summary>
        [StringLength(8)]
        public string NGAY_CT { get; set; } = string.Empty;

        public string? DU_PHONG { get; set; }
    }
}
