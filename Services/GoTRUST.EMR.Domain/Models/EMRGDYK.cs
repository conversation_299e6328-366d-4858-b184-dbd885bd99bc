
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIEU_GIAM_DINH_Y_KHOA")]
    public class EMRGDYKWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRGDYK? CHI_TIEU_GIAM_DINH_Y_KHOA { get; set; }
    }
    public class EMRGDYK
    {
        ///<summary>
        /// Ghi số thứ tự trong biên bản họp hội đồng giám định y khoa.
        ///</summary>
        [StringLength(200)]
        public string SO_BIEN_BAN { get; set; } = string.Empty;

        ///<summary>
        /// Ghi họ và tên người chủ trì trong danh mục người chủ trì hội đồng giám định y khoa đã nhập trên Cổng tiếp nhận của cơ quan BHXH.
        ///</summary>
        [StringLength(255)]
        public string NGUOI_CHU_TRI { get; set; } = string.Empty;

        ///<summary>
        /// <PERSON>hi chức vụ của người chủ trì, trong đó: mã "1": Chủ tịch; mã "2": Người ký thay chủ tịch.
        ///</summary>
        public int CHUC_VU { get; set; }

        ///<summary>
        /// Ghi ngày, tháng, năm họp hội đồng giám định y khoa, theo định dạng yyyymmdd
        ///</summary>
        [StringLength(8)]
        public string NGAY_HOP { get; set; } = string.Empty;

        ///<summary>
        /// Ghi họ và tên người được giám định y khoa.
        ///</summary>
        [StringLength(255)]
        public string HO_TEN { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày, tháng, năm sinh của người được giám định y khoa, theo định dạng yyyymmdd
        ///</summary>
        [StringLength(8)]
        public string NGAY_SINH { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số định danh cá nhân trên thẻ căn cước hoặc căn cước công dân hoặc giấy chứng nhận căn cước hoặc giấy khai sinh của người được giám định y khoa.
        ///</summary>
        public string SO_CCCD { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày cấp chứng minh nhân dân hoặc thẻ căn cước công dân hoặc hộ chiếu của người được giám định y khoa, theo định dạng yyyymmdd
        ///</summary>
        [StringLength(8)]
        public string NGAY_CAP_CCCD { get; set; } = string.Empty;

        ///<summary>
        /// Ghi nơi cấp chứng minh nhân dân hoặc thẻ căn cước công dân hoặc hộ chiếu của người được giám định y khoa.
        ///</summary>
        [StringLength(1024)]
        public string NOI_CAP_CCCD { get; set; } = string.Empty;

        ///<summary>
        /// Ghi địa chỉ nơi cư trú hiện tại của người được giám định y khoa.
        ///</summary>
        [StringLength(1024)]
        public string DIA_CHI { get; set; } = string.Empty;

        ///<summary>
        /// Mã đơn vị hành chính cấp tỉnh nơi người bệnh đang cư trú (thường trú hoặc tạm trú). Ghi theo 02 ký tự cuối của mã đơn vị hành chính của tỉnh, thành phố trực thuộc Trung ương nơi người bệnh cư trú.
        ///</summary>
        [StringLength(3)]
        public string MATINH_CU_TRU { get; set; } = string.Empty;

        ///<summary>
        /// Mã đơn vị hành chính cấp huyện nơi người bệnh đang cư trú (thường trú hoặc tạm trú).
        ///</summary>
        [StringLength(3)]
        public string MAHUYEN_CU_TRU { get; set; } = string.Empty;

        ///<summary>
        /// Mã đơn vị hành chính cấp xã nơi người bệnh đang cư trú (thường trú hoặc tạm trú).
        ///</summary>
        [StringLength(5)]
        public string MAXA_CU_TRU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã số bảo hiểm xã hội của người được giám định y khoa.
        ///</summary>
        [StringLength(10)]
        public string MA_BHXH { get; set; } = string.Empty;

        [StringLength(15)]
        public string? MA_THE_BHYT { get; set; }

        [StringLength(100)]
        public string? NGHE_NGHIEP { get; set; }

        ///<summary>
        /// Ghi số điện thoại liên hệ của người đề nghị giám định y khoa
        ///</summary>
        [StringLength(15)]
        public string DIEN_THOAI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã đối tượng giám định.
        ///</summary>
        [StringLength(20)]
        public string MA_DOI_TUONG { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã khám giám định.
        ///</summary>
        public int KHAM_GIAM_DINH { get; set; }

        ///<summary>
        /// Ghi ngày chứng từ (ngày họp Hội đồng giám định y khoa), theo định dạng yyyymmdd
        ///</summary>
        [StringLength(8)]
        public string NGAY_CHUNG_TU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số giấy giới thiệu.
        ///</summary>
        [StringLength(200)]
        public string SO_GIAY_GIOI_THIEU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày đề nghị, theo định dạng yyyymmdd
        ///</summary>
        [StringLength(8)]
        public string NGAY_DE_NGHI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã cơ quan, đơn vị quản lý hoặc cơ quan, đơn vị giới thiệu đối tượng khám giám định y khoa.
        ///</summary>
        [StringLength(200)]
        public string MA_DONVI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi tên đầy đủ của cơ quan, đơn vị quản lý hoặc cơ quan, đơn vị giới thiệu đối tượng khám giám định y khoa.
        ///</summary>
        [StringLength(1024)]
        public string GIOI_THIEU_CUA { get; set; } = string.Empty;

        ///<summary>
        /// Ghi kết quả khám của Hội đồng Giám định y khoa.
        ///</summary>
        public string KET_QUA_KHAM { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số văn bản làm căn cứ khám giám định y khoa phù hợp với đối tượng giám định.
        ///</summary>
        [StringLength(200)]
        public string SO_VAN_BAN_CAN_CU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi tỷ lệ (%) tổn thương cơ thể do thương tật, bệnh tật, bệnh nghề nghiệp của lần giám định này.
        ///</summary>
        public int TYLE_TTCT_MOI { get; set; }

        public int? TYLE_TTCT_CU { get; set; }

        [StringLength(10)]
        public string? DANG_HUONG_CHE_DO { get; set; }

        public int? TONG_TYLE_TTCT { get; set; }

        public int? DANG_KHUYETTAT { get; set; }

        public int? MUC_DO_KHUYETTAT { get; set; }

        ///<summary>
        /// Ghi nội dung đề nghị.
        ///</summary>
        public string DE_NGHI { get; set; } = string.Empty;

        public string? DUOC_XACDINH { get; set; }

        public string? DU_PHONG { get; set; }
    }
}
