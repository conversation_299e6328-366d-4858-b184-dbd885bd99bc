namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Khoa phòng trong bệnh viện
    /// </summary>
    public class Department : Aggregate<Guid>
    {
        /// <summary>
        /// Mã khoa
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Mã bệnh viện
        /// </summary>
        public Guid HospitalId { get; set; }

        /// <summary>
        /// Tên khoa
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Tên khoa theo bệnh viện
        /// </summary>
        public string NameByHospital { get; set; } = string.Empty;

        /// <summary>
        /// Mã khoa theo bệnh viện
        /// </summary>
        public string CodeByHospital { get; set; } = string.Empty;

        /// <summary>
        /// Mô tả khoa
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// B<PERSON>nh viện
        /// </summary>
        public virtual Hospital Hospital { get; set; } = null!;
    }
}
