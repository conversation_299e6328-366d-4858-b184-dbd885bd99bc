
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHITIET_THUOC")]
    public class EMRThuocWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRThuoc? CHITIET_THUOC { get; set; }
    }
    public class EMRThuoc
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất (dùng để liên kết giữa Bảng chỉ tiêu tổng hợp khám bệnh, chữa bệnh (bảng XML 1) và các bảng còn lại ban hành kèm theo Quyết định này trong một lần khám bệnh, chữa bệnh (PRIMARY KEY)).
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// <PERSON><PERSON> thứ tự tăng từ 1 đến hết trong một lần gửi dữ liệu.
        ///</summary>
        public int STT { get; set; }

        ///<summary>
        /// Ghi mã hoạt chất theo quy định tại Bộ mã danh mục dùng chung do Bộ Y tế ban hành.
        ///</summary>
        [StringLength(255)]
        public string MA_THUOC { get; set; } = string.Empty;

        [StringLength(255)]
        public string? MA_PP_CHEBIEN { get; set; }

        [StringLength(10)]
        public string? MA_CSKCB_THUOC { get; set; }

        ///<summary>
        /// Là mã nhóm theo chi phí, dùng để phân loại, sắp xếp các chi phí vào các nhóm.
        ///</summary>
        public int MA_NHOM { get; set; }

        ///<summary>
        /// Ghi tên thuốc theo đúng tên thuốc được Cục Quản lý Dược hoặc Cục Quản lý Y, dược cổ truyền cấp số đăng ký.
        ///</summary>
        [StringLength(1024)]
        public string TEN_THUOC { get; set; } = string.Empty;

        ///<summary>
        /// Ghi đơn vị tính nhỏ nhất, đơn vị tính của thuốc thực tế sử dụng cho người bệnh.
        ///</summary>
        [StringLength(50)]
        public string DON_VI_TINH { get; set; } = string.Empty;

        ///<summary>
        /// Bổ sung lưu ý.
        ///</summary>
        [StringLength(1024)]
        public string HAM_LUONG { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã đường dùng tương ứng với đường dùng của thuốc theo thông tin được Cục Quản lý Dược hoặc Cục Quản lý Y, dược cổ truyền cấp giấy đăng ký lưu hành.
        ///</summary>
        [StringLength(4)]
        public string DUONG_DUNG { get; set; } = string.Empty;

        ///<summary>
        /// Ghi dạng bào chế của thuốc (đối với thuốc hoá dược) hoặc dạng bào chế, chế biến của thuốc (đối với thuốc cổ truyền, thuốc dược liệu).
        ///</summary>
        [StringLength(1024)]
        public string DANG_BAO_CHE { get; set; } = string.Empty;

        ///<summary>
        /// Bổ sung lưu ý.
        ///</summary>
        [StringLength(1024)]
        public string LIEU_DUNG { get; set; } = string.Empty;

        ///<summary>
        /// Ghi lời dặn của thầy thuốc trên đơn thuốc hoặc y lệnh.
        ///</summary>
        [StringLength(1024)]
        public string CACH_DUNG { get; set; } = string.Empty;

        [StringLength(255)]
        public string? SO_DANG_KY { get; set; }

        [StringLength(50)]
        ///<summary>
        /// Ghi thông tin thầu của thuốc theo thứ tự.
        ///</summary>
        public string TT_THAU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã để xác định phạm vi của thuốc.
        ///</summary>
        public int PHAM_VI { get; set; }

        ///<summary>
        /// Ghi tỷ lệ thanh toán BHYT đối với thuốc có quy định tỷ lệ phần trăm (ký hiệu: %).
        ///</summary>
        public int TYLE_TT_BH { get; set; }

        ///<summary>
        /// Ghi số lượng thuốc thực tế sử dụng cho người bệnh, làm tròn số đến 3 chữ số thập phân.
        ///</summary>
        public decimal SO_LUONG { get; set; }

        ///<summary>
        /// Bổ sung hướng dẫn.
        ///</summary>
        public decimal DON_GIA { get; set; }

        ///<summary>
        /// Được tính theo công thức: THANH_TIEN_BV = SO_LUONG * DON_GIA.
        ///</summary>
        public decimal THANH_TIEN_BV { get; set; }

        ///<summary>
        /// Được tính theo công thức: THANH_TIEN_BH = SO_LUONG * DON_GIA * TYLE_TT_BH/100.
        ///</summary>
        public decimal THANH_TIEN_BH { get; set; }

        ///<summary>
        /// Ghi số tiền thuốc được ngân sách nhà nước (Trung ương và/hoặc địa phương) hỗ trợ.
        ///</summary>
        public decimal T_NGUONKHAC_NSNN { get; set; }

        ///<summary>
        /// Ghi số tiền thuốc được các tổ chức, đơn vị có trụ sở ngoài lãnh thổ Việt Nam hoặc các cá nhân đang sinh sống, học tập, lao động ngoài lãnh thổ Việt Nam hỗ trợ.
        ///</summary>
        public decimal T_NGUONKHAC_VTNN { get; set; }

        ///<summary>
        /// Ghi số tiền thuốc được các tổ chức, cơ quan, đơn vị có trụ sở trong lãnh thổ Việt Nam hoặc các cá nhân đang sinh sống, học tập, lao động trong lãnh thổ Việt Nam hỗ trợ.
        ///</summary>
        public decimal T_NGUONKHAC_VTTN { get; set; }

        ///<summary>
        /// Ghi số tiền thuốc được các nguồn khác còn lại hỗ trợ.
        ///</summary>
        public decimal T_NGUONKHAC_CL { get; set; }

        ///<summary>
        /// Là số tiền do nguồn khác chi trả.
        ///</summary>
        public decimal T_NGUONKHAC { get; set; }

        ///<summary>
        /// Ghi mức hưởng tương ứng với từng loại chi phí.
        ///</summary>
        public int MUC_HUONG { get; set; }

        ///<summary>
        /// Ghi số tiền người bệnh tự trả ngoài phạm vi chi trả của quỹ BHYT.
        ///</summary>
        public decimal T_BNTT { get; set; }

        ///<summary>
        /// Ghi số tiền người bệnh cùng chi trả trong phạm vi quyền lợi mức hưởng BHYT.
        ///</summary>
        public decimal T_BNCCT { get; set; }

        ///<summary>
        /// Ghi số tiền cơ sở KBCB đề nghị cơ quan BHXH thanh toán theo phạm vi quyền lợi mức hưởng BHYT của người bệnh.
        ///</summary>
        public decimal T_BHTT { get; set; }

        ///<summary>
        /// Ghi mã khoa nơi người bệnh được chỉ định sử dụng thuốc.
        ///</summary>
        [StringLength(50)]
        public string MA_KHOA { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã bác sỹ khám, chỉ định thuốc (mã hóa theo số Giấy phép hành nghề).
        ///</summary>
        [StringLength(255)]
        public string MA_BAC_SI { get; set; } = string.Empty;

        [StringLength(255)]
        public string? MA_DICH_VU { get; set; }

        ///<summary>
        /// Bãi bỏ nội dung hướng dẫn bổ sung tại Quyết định số 4750/QĐ-BYT.
        ///</summary>
        [StringLength(12)]
        public string NGAY_YL { get; set; } = string.Empty;

        ///<summary>
        /// Sửa đổi, bổ sung một số nội dung.
        ///</summary>
        [StringLength(12)]
        public string NGAY_TH_YL { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã phương thức thanh toán đối với thuốc.
        ///</summary>
        public int MA_PTTT { get; set; }

        ///<summary>
        /// Ghi mã để xác định nguồn thuốc chi trả cho người bệnh.
        ///</summary>
        public int NGUON_CTRA { get; set; }

        public int? VET_THUONG_TP { get; set; }

        public string? DU_PHONG { get; set; }
    }

}
