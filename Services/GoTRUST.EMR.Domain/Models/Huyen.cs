﻿

namespace GoTRUST.EMR.Domain.Models
{
    public class Huyen : Entity<string>
    {
        public string Name { get; set; } = string.Empty;
        public string? OtherName { get; set; } = string.Empty;
        public string? Level { get; set; } = string.Empty;
        public string? TinhId { get; set; }
        public virtual Tinh? Tinh { get; set; }
        public virtual List<Xa>? Xas { get; set; } = new();
    }
}
