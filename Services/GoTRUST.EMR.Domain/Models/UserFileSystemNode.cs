namespace GoTRUST.EMR.Domain.Models
{
    public class UserFileSystemNode
    {
        /// <summary>
        /// Khóa ngoại, trỏ đến Node được chia sẻ.
        /// </summary>
        public Guid FileSystemNodeId { get; set; }

        /// <summary>
        /// Kh<PERSON><PERSON> ngoại, trỏ đến User được chia sẻ.
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// (Tối ưu) Cờ đánh dấu đây có phải là "gốc" của một lượt chia sẻ hay không.
        /// true: Mụ<PERSON> này sẽ hiển thị ở cấp cao nhất trong danh sách "Được chia sẻ với tôi".
        /// false: Mục này được chia sẻ gián tiếp thông qua một thư mục cha.
        /// </summary>
        public bool IsRootShare { get; set; }

        /// <summary>
        /// Ngày và giờ tạo bản ghi.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Ngày và giờ truy cập bản ghi lần cuối.
        /// </summary>
        public DateTime? LastAccessedAt { get; set; }

        /// <summary>
        /// Kiểm tra xem còn truy cập được hay không.
        /// </summary>
        public bool IsAccessible { get; set; }

        /// <summary>
        /// Tham chiếu đến đối tượng Node được chia sẻ.
        /// </summary>
        public virtual FileSystemNode? Node { get; set; }

        /// <summary>
        /// Tham chiếu đến đối tượng User được chia sẻ.
        /// </summary>
        public virtual User? User { get; set; }
    }
}