
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIEU_TOMTATHSBA")]
    public class EMRTomTatHSBAWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRTomTatHSBA? CHI_TIEU_TOMTATHSBA { get; set; }
    }
    public class EMRTomTatHSBA
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất (dùng để liên kết giữa Bảng chỉ tiêu tổng hợp khám bệnh, chữa bệnh (bảng XML 1) và các bảng còn lại ban hành kèm theo Quyết định này trong một lần khám bệnh, chữa bệnh (PRIMARY KEY)).
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// <PERSON><PERSON> sung các mã hình thức KBCB.
        ///</summary>
        [StringLength(2)]
        public string MA_LOAI_KCB { get; set; } = string.Empty;

        [StringLength(255)]
        public string? HO_TEN_CHA { get; set; }

        [StringLength(255)]
        public string? HO_TEN_ME { get; set; }

        [StringLength(255)]
        public string? NGUOI_GIAM_HO { get; set; }

        [StringLength(1024)]
        public string? DON_VI { get; set; }

        ///<summary>
        /// Ghi thời điểm người bệnh đến KBCB, gồm 12 ký tự, theo định dạng yyyymmddHHMM.
        ///</summary>
        [StringLength(12)]
        public string NGAY_VAO { get; set; } = string.Empty;

        [StringLength(12)]
        ///<summary>
        /// Ghi thời điểm người bệnh kết thúc lần khám bệnh hoặc đợt điều trị tại cơ sở KBCB, gồm 12 ký tự theo định dạng yyyymmddHHMM.
        ///</summary>
        public string NGAY_RA { get; set; } = string.Empty;

        ///<summary>
        /// Ghi chẩn đoán của cơ sở KBCB ở thời điểm tiếp nhận người bệnh (Chẩn đoán sơ bộ).
        ///</summary>
        public string CHAN_DOAN_VAO { get; set; } = string.Empty;

        ///<summary>
        /// Ghi đầy đủ chẩn đoán xác định bệnh chính, bệnh kèm theo và/hoặc các triệu chứng hoặc hội chứng, được bác sỹ ghi trong hồ sơ KBCB tại thời điểm kết thúc KBCB đối với người bệnh.
        ///</summary>
        public string CHAN_DOAN_RV { get; set; } = string.Empty;

        ///<summary>
        /// Ghi quá trình bệnh lý và diễn biến lâm sàng.
        ///</summary>
        public string QT_BENHLY { get; set; } = string.Empty;

        ///<summary>
        /// Ghi tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chẩn đoán.
        ///</summary>
        public string TOMTAT_KQ { get; set; } = string.Empty;

        ///<summary>
        /// Ghi phương pháp điều trị cho người bệnh như nội khoa, ngoại khoa, xạ trị, hoá trị hoặc xạ trị + nội khoa.
        ///</summary>
        public string PP_DIEUTRI { get; set; } = string.Empty;

        [StringLength(8)]
        public string? NGAY_SINHCON { get; set; }

        [StringLength(8)]
        public string? NGAY_CONCHET { get; set; }

        public int? SO_CONCHET { get; set; }

        ///<summary>
        /// Sửa đổi, bổ sung một số nội dung:
        /// - Mã "5": Tử vong tại cơ sở KBCB
        ///</summary>
        public int KET_QUA_DTRI { get; set; }

        public string? GHI_CHU { get; set; }

        ///<summary>
        /// Ghi mã của người đứng đầu cơ sở KBCB hoặc người được người đứng đầu cơ sở KBCB ủy quyền được ký và đóng dấu của cơ sở KBCB đó (mã hoá theo số GPHN).
        ///</summary>
        [StringLength(255)]
        public string MA_TTDV { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày chứng từ (Tóm tắt hồ sơ bệnh án), theo định dạng yyyymmdd.
        ///</summary>
        [StringLength(8)]
        public string NGAY_CT { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã thẻ BHYT tạm thời của trẻ em sinh ra hoặc của người hiến tạng nhưng chưa được cơ quan BHXH cấp thẻ BHYT.
        ///</summary>
        [StringLength(15)]
        public string MA_THE_TAM { get; set; } = string.Empty;

        public string? DU_PHONG { get; set; }
    }
}
