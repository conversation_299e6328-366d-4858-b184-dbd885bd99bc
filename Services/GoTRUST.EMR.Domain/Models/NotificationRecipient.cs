namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// <PERSON><PERSON>i tượng nhận thông báo
    /// </summary>
    public class NotificationRecipient
    {
        public Guid NotificationId { get; set; }
        public Guid UserId { get; set; }
        public bool IsRead { get; set; }
        public DateTime? ReadAt { get; set; }
        public virtual Notification Notification { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }
}
