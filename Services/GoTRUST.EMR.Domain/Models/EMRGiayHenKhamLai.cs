
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIEU_GIAYHENKHAMLAI")]
    public class EMRGiayHenKhamLaiWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRGiayHenKhamLai? CHI_TIEU_GIAYHENKHAMLAI { get; set; }
    }
    public class EMRGiayHenKhamLai
    {
        ///<summary>
        /// Mã đợt điều trị duy nhất.
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// Số giấy hẹn khám lại.
        ///</summary>
        [StringLength(50)]
        public string SO_GIAYHEN_KL { get; set; } = string.Empty;

        ///<summary>
        /// Mã cơ sở KBCB.
        ///</summary>
        [StringLength(5)]
        public string MA_CSKCB { get; set; } = string.Empty;

        ///<summary>
        /// Họ và tên người bệnh.
        ///</summary>
        [StringLength(255)]
        public string HO_TEN { get; set; } = string.Empty;

        ///<summary>
        /// Ngày sinh.
        ///</summary>
        [StringLength(12)]
        public string NGAY_SINH { get; set; } = string.Empty;

        ///<summary>
        /// Giới tính.
        ///</summary>
        public int GIOI_TINH { get; set; }

        ///<summary>
        /// Địa chỉ.
        ///</summary>
        [StringLength(1024)]
        public string DIA_CHI { get; set; } = string.Empty;

        ///<summary>
        /// Mã thẻ BHYT.
        ///</summary>
        public string MA_THE_BHYT { get; set; } = string.Empty;

        ///<summary>
        /// Giá trị thẻ đến.
        ///</summary>
        public string GT_THE_DEN { get; set; } = string.Empty;

        ///<summary>
        /// Ngày vào.
        ///</summary>
        [StringLength(12)]
        public string NGAY_VAO { get; set; } = string.Empty;

        ///<summary>
        /// Ngày vào nội trú.
        ///</summary>
        [StringLength(12)]
        public string NGAY_VAO_NOI_TRU { get; set; } = string.Empty;

        ///<summary>
        /// Ngày ra.
        ///</summary>
        [StringLength(12)]
        public string NGAY_RA { get; set; } = string.Empty;

        ///<summary>
        /// Thời điểm hẹn khám lại.
        ///</summary>
        [StringLength(8)]
        public string NGAY_HEN_KL { get; set; } = string.Empty;

        ///<summary>
        /// Chẩn đoán ra viện.
        ///</summary>
        public string CHAN_DOAN_RV { get; set; } = string.Empty;

        ///<summary>
        /// Mã bệnh chính.
        ///</summary>
        [StringLength(7)]
        public string MA_BENH_CHINH { get; set; } = string.Empty;

        ///<summary>
        /// Mã bệnh kèm theo.
        ///</summary>
        [StringLength(100)]
        public string MA_BENH_KT { get; set; } = string.Empty;

        ///<summary>
        /// Mã bệnh YHCT.
        ///</summary>
        [StringLength(255)]
        public string MA_BENH_YHCT { get; set; } = string.Empty;

        ///<summary>
        /// Mã đối tượng KCB.
        ///</summary>
        [StringLength(4)]
        public string MA_DOITUONG_KCB { get; set; } = string.Empty;

        ///<summary>
        /// Mã bác sỹ.
        ///</summary>
        [StringLength(255)]
        public string MA_BAC_SI { get; set; } = string.Empty;

        ///<summary>
        /// Mã số định danh y tế.
        ///</summary>
        [StringLength(255)]
        public string MA_TTDV { get; set; } = string.Empty;

        ///<summary>
        /// Ngày cấp giấy hẹn khám lại.
        ///</summary>
        [StringLength(8)]
        public string NGAY_CT { get; set; } = string.Empty;

        public string? DU_PHONG { get; set; }
    }
}
