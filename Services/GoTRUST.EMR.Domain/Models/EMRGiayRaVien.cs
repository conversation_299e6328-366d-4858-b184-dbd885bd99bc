
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIEU_GIAYRAVIEN")]
    public class EMRGiayRaVienWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRGiayRaVien? CHI_TIEU_GIAYNGHIHUONGBHXH { get; set; }
    }
    public class EMRGiayRaVien
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất (PRIMARY KEY).
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// G<PERSON> số lưu trữ, l<PERSON> số hồ sơ bệnh án của người bệnh trong đợt điều trị.
        ///</summary>
        [StringLength(200)]
        public string SO_LUU_TRU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã y tế, l<PERSON>y theo mã số người bệnh quy định tại trường MA_BN.
        ///</summary>
        [StringLength(200)]
        public string MA_YTE { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã khoa nơi tổng kết hồ sơ bệnh án của người bệnh.
        ///</summary>
        [StringLength(200)]
        public string MA_KHOA_RV { get; set; } = string.Empty;

        ///<summary>
        /// Ghi thời điểm người bệnh đến KBCB, gồm 12 ký tự, theo định dạng yyyymmddHHMM. Ví dụ: 201703311520.
        ///</summary>
        [StringLength(12)]
        public string NGAY_VAO { get; set; } = string.Empty;

        ///<summary>
        /// Ghi thời điểm người bệnh kết thúc lần khám bệnh hoặc đợt điều trị tại cơ sở KBCB, gồm 12 ký tự theo định dạng yyyymmddHHMM.
        ///</summary>
        [StringLength(12)]
        public string NGAY_RA { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã "1" là đình chỉ thai nghén, mã "0" là không đình chỉ thai nghén.
        ///</summary>
        public int MA_DINH_CHI_THAI { get; set; }

        public string? NGUYENNHAN_DINHCHI { get; set; }

        [StringLength(12)]
        public string? THOIGIAN_DINHCHI { get; set; }

        public int? TUOI_THAI { get; set; }

        ///<summary>
        /// Ghi đầy đủ chẩn đoán xác định bệnh chính, bệnh kèm theo và/hoặc các triệu chứng hoặc hội chứng.
        ///</summary>
        [StringLength(1500)]
        public string CHAN_DOAN_RV { get; set; } = string.Empty;

        ///<summary>
        /// Ghi phương pháp điều trị cho người bệnh như nội khoa, ngoại khoa, xạ trị, hoá trị hoặc xạ trị kết hợp nội khoa.
        ///</summary>
        public string PP_DIEUTRI { get; set; } = string.Empty;

        ///<summary>
        /// Trường thông tin này áp dụng đối với trường hợp cấp giấy ra viện để giải quyết chế độ BHXH.
        ///</summary>
        [StringLength(1500)]
        public string GHI_CHU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã của người đứng đầu cơ sở KBCB hoặc người được người đứng đầu cơ sở KBCB ủy quyền được ký và đóng dấu của cơ sở KBCB đó.
        ///</summary>
        [StringLength(255)]
        public string MA_TTDV { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã của Trưởng khoa hoặc Phó trưởng khoa được uỷ quyền ký tên theo quy định của Thủ trưởng cơ sở KBCB.
        ///</summary>
        [StringLength(255)]
        public string MA_BS { get; set; } = string.Empty;

        ///<summary>
        /// Ghi họ và tên của Trưởng khoa hoặc Phó trưởng khoa được uỷ quyền ký tên theo quy định của Thủ trưởng cơ sở KBCB.
        ///</summary>
        [StringLength(255)]
        public string TEN_BS { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày chứng từ (Giấy ra viện), theo định dạng yyyymmdd.
        ///</summary>
        [StringLength(8)]
        public string NGAY_CT { get; set; } = string.Empty;

        [StringLength(10)]
        public string? MA_CHA { get; set; }

        [StringLength(10)]
        public string? MA_ME { get; set; }

        ///<summary>
        /// Ghi mã thẻ BHYT tạm thời của trẻ em sinh ra hoặc của người hiến tạng nhưng chưa được cơ quan BHXH cấp thẻ BHYT.
        ///</summary>
        [StringLength(15)]
        public string MA_THE_TAM { get; set; } = string.Empty;

        [StringLength(255)]
        public string? HO_TEN_CHA { get; set; }

        [StringLength(255)]
        public string? HO_TEN_ME { get; set; }

        ///<summary>
        /// Tăng kích thước tối đa lên 3 ký tự.
        ///</summary>
        public int SO_NGAY_NGHI { get; set; }

        ///<summary>
        /// Ghi ngày bắt đầu nghỉ ngoại trú sau khi điều trị của người được cấp giấy ra viện theo định dạng yyyymmdd.
        ///</summary>
        [StringLength(8)]
        public string NGOAITRU_TUNGAY { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày kết thúc nghỉ ngoại trú sau khi điều trị của người được cấp giấy ra viện theo định dạng yyyymmdd.
        ///</summary>
        [StringLength(8)]
        public string NGOAITRU_DENNGAY { get; set; } = string.Empty;

        public string? DU_PHONG { get; set; }
    }
}
