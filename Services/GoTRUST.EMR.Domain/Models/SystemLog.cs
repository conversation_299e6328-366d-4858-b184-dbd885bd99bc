namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// System log
    /// </summary>
    public class SystemLog : Entity<Guid>
    {
        /// <summary>
        /// Hospital ID
        /// </summary>
        public Guid HospitalId { get; set; }
        
        /// <summary>
        /// System action name
        /// </summary>
        public string ActionName { get; set; } = string.Empty;
        
        /// <summary>
        /// System action details
        /// </summary>
        public string ActionDetail { get; set; } = string.Empty;
        
        /// <summary>
        /// Action icon (font awesome, SVG)
        /// </summary>
        public string ActionIcon { get; set; } = string.Empty;
        
        /// <summary>
        /// Action time
        /// </summary>
        public DateTime ActionTime { get; set; }
        
        /// <summary>
        /// Severity level (Info, Warning, Error, Critical)
        /// </summary>
        public string SeverityLevel { get; set; } = string.Empty;
        
        /// <summary>
        /// Navigation property to the Hospital
        /// </summary>
        public virtual Hospital Hospital { get; set; } = null!;
    }
}
