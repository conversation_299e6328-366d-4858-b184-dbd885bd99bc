﻿

namespace GoTRUST.EMR.Domain.Models
{
    public class Xa : Entity<string>
    {
        public string Name { get; set; } = string.Empty;
        public string? OtherName { get; set; } = string.Empty;
        public string? Level { get; set; } = string.Empty;

        /// <summary>
        /// 
        /// </summary>
        public string HuyenId { get; set; } = string.Empty;
        public string TinhId { get; set; } = string.Empty;
        public virtual Huyen? Huyen { get; set; }
        public virtual Tinh? Tinh { get; set; }
    }
}
