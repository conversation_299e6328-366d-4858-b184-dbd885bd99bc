namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Đại diện cho một đối tượng trong hệ thống, có thể là File hoặc Folder.
    /// </summary>
    public class FileSystemNode : Aggregate<Guid>
    {
        /// <summary>
        /// (Tối ưu) Chuỗi đường dẫn đầy đủ chứa ID của các cấp cha.
        /// Ví dụ: /1/5/12/
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// Tên của file hoặc folder.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Loại đối tượng (File hoặc Folder).
        /// </summary>
        public FileSystemNodeType NodeType { get; set; }

        /// <summary>
        /// ID của thư mục cha trực tiếp. Null nếu nằm ở thư mục gốc.
        /// </summary>
        public Guid? ParentId { get; set; }

        /// <summary>
        /// ID của người dùng sở hữu mục này.
        /// </summary>
        public Guid OwnerId { get; set; }

        /// <summary>
        /// (Chỉ cho File) URL S3, MinIO hoặc URL đích khác để truy cập file.
        /// </summary>
        public string? DestinationUrl { get; set; }

        /// <summary>
        /// (Chỉ cho File) Mã IV để mã hóa file, nếu có.
        /// </summary>
        public string? EncryptionIV { get; set; }

        /// <summary>
        /// (Chỉ cho File) Kiểu file (MIME type) để hiển thị icon phù hợp.
        /// </summary>
        public string? MimeType { get; set; }

        /// <summary>
        /// (Chỉ cho File) Kích thước file tính bằng byte.
        /// </summary>
        public long? Size { get; set; }
        /// <summary>
        /// Ngày xóa file trên S3
        /// </summary>
        public DateTime? DeleteS3At { get; set; }

        /// <summary>
        /// Thời gian truy cập cuối cùng của chủ sở hữu.
        /// </summary>
        public DateTime? OwnerLastAccessedAt { get; set; }

        /// <summary>
        /// Cờ sở hữu có thể truy cập công khai.
        /// </summary>
        public FileSystemNodeShareStatus ShareStatus { get; set; }

        // --- Navigation Properties ---

        /// <summary>
        /// Tham chiếu đến đối tượng User sở hữu mục này.
        /// </summary>
        public virtual User? Owner { get; set; }

        /// <summary>
        /// Tham chiếu đến thư mục cha trực tiếp.
        /// </summary>
        public virtual FileSystemNode? Parent { get; set; }

        /// <summary>
        /// Danh sách các mục con trực tiếp.
        /// </summary>
        public virtual ICollection<FileSystemNode> Children { get; set; } = [];

        /// <summary>
        /// Danh sách các quyền chia sẻ được áp dụng trên mục này.
        /// </summary>
        public virtual ICollection<UserFileSystemNode> UserNodes { get; set; } = [];
        /// <summary>
        /// Danh sách các quyền chia sẻ theo Role được áp dụng trên mục này.
        /// </summary>
        public virtual ICollection<RoleFileSystemNode> RoleNodes { get; set; } = [];
    }
}