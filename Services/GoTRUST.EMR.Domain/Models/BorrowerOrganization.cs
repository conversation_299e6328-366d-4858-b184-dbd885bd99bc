namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// External borrower organization information
    /// </summary>
    public class BorrowerOrganization : Aggregate<Guid>
    {
        /// <summary>
        /// Hospital ID
        /// </summary>
        public Guid HospitalId { get; set; }

        /// <summary>
        /// Mã cán bộ
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Contact person name
        /// </summary>
        public string ContactPerson { get; set; } = string.Empty;

        /// <summary>
        /// Email
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Ng<PERSON>y sinh
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// Giới tính: Nam, Nữ
        /// </summary>
        public string? Gender { get; set; } = string.Empty;

        /// <summary>
        /// Số Căn cước công dân
        /// </summary>
        public string? CitizenId { get; set; }

        /// <summary>
        /// <PERSON><PERSON><PERSON> cấp CCCD
        /// </summary>
        public DateTime? CitizenIdIssueDate { get; set; }

        /// <summary>
        /// Nơi cấp CCCD
        /// </summary>
        public string? CitizenIdIssuePlace { get; set; }

        /// <summary>
        /// Tình trạng hôn nhân, nối với bảng TinhTrangHonNhan
        /// </summary>
        public string? MaritalStatusId { get; set; }

        /// <summary>
        /// Phone number
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Quốc tịch, nối với bảng TonGiao
        /// </summary>
        public string? ReligionId { get; set; } = string.Empty;

        /// <summary>
        /// Dân tộc, nối với bảng DanToc
        /// </summary>
        public string? EthnicityId { get; set; } = string.Empty;

        /// <summary>
        /// Address
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// Địa chỉ tạm trú
        /// </summary>
        public string? TemporaryAddress { get; set; }

        /// <summary>
        /// Organization name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Phòng ban
        /// </summary>
        public string? OfficeUnit { get; set; }

        /// <summary>
        /// Vị trí làm việc
        /// </summary>
        public string? Position { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string? Note { get; set; }

        /// <summary>
        /// API Key ID linked to this organization (optional)
        /// </summary>
        public Guid? ApiKeyId { get; set; }

        /// <summary>
        /// Mã người dùng (nếu nhân viên là người dùng hệ thống)
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// Navigation property to the Hospital
        /// </summary>
        public virtual Hospital Hospital { get; set; } = null!;

        /// <summary>
        /// Navigation property to the API Key
        /// </summary>
        public virtual ApiKey? ApiKey { get; set; }

        /// <summary>
        /// Người dùng (nếu có)
        /// </summary>
        public virtual User? User { get; set; }

        /// <summary>
        /// External borrow requests from this organization
        /// </summary>
        public virtual ICollection<BorrowRequest> BorrowRequests { get; set; } = [];
    }
}
