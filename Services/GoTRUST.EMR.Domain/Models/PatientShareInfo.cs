namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Thông tin chia sẻ bệnh án liên viện
    /// </summary>
    public class PatientShareInfo : Entity<Guid>
    {
        /// <summary>
        /// Id của session share thông tin của bệnh nhân
        /// </summary>
        public string? TxnId { get; set; }

        /// <summary>
        /// Số CCCD của bệnh nhân
        /// </summary>
        public string? IdentityNo { get; set; }

        /// <summary>
        /// Trạng thái đồng ý chia sẻ thông tin
        /// 0 - đang đợi đồng ý truy cập
        /// 1 - đồng ý truy cập  
        /// 2 - không đồng ý truy cập
        /// 3 - thu hồi truy cập
        /// </summary>
        public string? Result { get; set; }

        /// <summary>
        /// Thời gian đồng ý chia sẻ
        /// </summary>
        public DateTime? ConsentGivenAt { get; set; }

        /// <summary>
        /// Thời gian thu hồi đồng ý
        /// </summary>
        public DateTime? ConsentRevokedAt { get; set; }
    }
}
