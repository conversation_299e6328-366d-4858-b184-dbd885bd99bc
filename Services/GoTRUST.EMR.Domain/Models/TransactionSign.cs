using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GoTRUST.EMR.Domain.Models
{
    public class TransactionSign : Aggregate<Guid>
    {
        /// <summary>
        /// ID của bệnh nhân liên kết với giao dịch ký.
        /// </summary>
        public Guid MedicalRecordFileId { get; set; }

        /// <summary>
        /// Thời gian hết hạn của giao dịch.
        /// </summary>
        public DateTime ExpiredAt { get; set; }
    }
}