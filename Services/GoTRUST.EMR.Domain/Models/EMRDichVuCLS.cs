
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIET_CLS")]
    public class EMRDichVuCLSWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRDichVuCLS? CHI_TIET_CLS { get; set; }
    }
    public class EMRDichVuCLS
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất (dùng để liên kết giữa Bảng chỉ tiêu tổng hợp khám bệnh, chữa bệnh (bảng XML 1) và các bảng còn lại ban hành kèm theo Quyết định này trong một lần khám bệnh, chữa bệnh (PRIMARY KEY)).
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// <PERSON><PERSON> thứ tự tăng từ 1 đến hết trong một lần gửi dữ liệu.
        ///</summary>
        public int STT { get; set; }

        ///<summary>
        /// Mã dịch vụ kỹ thuật.
        ///</summary>
        [StringLength(50)]
        public string MA_DICH_VU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã chỉ số xét nghiệm, chẩn đoán hình ảnh, thăm dò chức năng.
        ///</summary>
        [StringLength(255)]
        public string MA_CHI_SO { get; set; } = string.Empty;

        ///<summary>
        /// Ghi tên chỉ số xét nghiệm, chẩn đoán hình ảnh, thăm dò chức năng.
        ///</summary>
        [StringLength(255)]
        public string TEN_CHI_SO { get; set; } = string.Empty;

        [StringLength(255)]
        public string? GIA_TRI { get; set; }

        [StringLength(50)]
        public string? DON_VI_DO { get; set; }

        public string? MO_TA { get; set; }

        public string? KET_LUAN { get; set; }

        [StringLength(12)]
        public string? NGAY_KQ { get; set; }

        ///<summary>
        /// Ghi mã của người có thẩm quyền đọc hoặc duyệt kết quả đọc (mã hóa theo số GPHN).
        ///</summary>
        [StringLength(255)]
        public string MA_BS_DOC_KQ { get; set; } = string.Empty;

        public string? DU_PHONG { get; set; }
    }
}
