namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Film details for a medical record
    /// </summary>
    public class FilmDetail : Entity<Guid>
    {
        /// <summary>
        /// Medical record ID
        /// </summary>
        public Guid MedicalRecordId { get; set; }
        
        /// <summary>
        /// X-ray count
        /// </summary>
        public int XRayCount { get; set; }
        
        /// <summary>
        /// RMI count
        /// </summary>
        public int RMICount { get; set; }
        
        /// <summary>
        /// CT count
        /// </summary>
        public int CTCount { get; set; }
        
        /// <summary>
        /// External film count
        /// </summary>
        public int ExternalFilmCount { get; set; }
        
        /// <summary>
        /// Navigation property to the Medical Record
        /// </summary>
        public virtual MedicalRecord MedicalRecord { get; set; } = null!;
    }
}
