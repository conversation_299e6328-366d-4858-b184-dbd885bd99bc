namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Patient health metrics
    /// </summary>
    public class PatientHealthMetric : Entity<Guid>
    {
        /// <summary>
        /// Patient ID
        /// </summary>
        public Guid PatientId { get; set; }
        
        /// <summary>
        /// Metric name (e.g., Height, Weight, BMI, BloodGroup, BloodPressure, Pulse, HbA1c)
        /// </summary>
        public string MetricName { get; set; } = string.Empty;
        
        /// <summary>
        /// Value of the metric
        /// </summary>
        public decimal Value { get; set; }
        
        /// <summary>
        /// Unit of measurement
        /// </summary>
        public string Unit { get; set; } = string.Empty;
        
        /// <summary>
        /// Time when the metric was recorded
        /// </summary>
        public DateTime RecordedAt { get; set; }
        
        /// <summary>
        /// Navigation property to the Patient
        /// </summary>
        public virtual Patient Patient { get; set; } = null!;
    }
}
