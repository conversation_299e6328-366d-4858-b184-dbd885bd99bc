﻿
namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Mã xác thực hai yếu tố (Two-Factor Code)
    /// </summary>
    public class TwoFactorCode : Entity<Guid>
    {
        public Guid UserId { get; set; }
        public string Code { get; set; } = string.Empty;
        public DateTime ExpiresAt { get; set; }
        public bool Consumed { get; set; } = false;
        public DateTime? ConsumedAt { get; set; }
        public virtual User User { get; set; } = null!;
    }
}
