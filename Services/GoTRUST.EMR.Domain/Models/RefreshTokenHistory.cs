using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    public class RefreshTokenHistory : Entity<Guid>
    {
        /// <summary>
        /// The token that will be used to refresh the access token
        /// </summary>
        public string Token { get; set; } = null!;

        /// <summary>
        /// The JWT ID of the access token that this refresh token is linked to
        /// </summary>
        public string JwtId { get; set; } = null!;

        /// <summary>
        /// The date and time when the refresh token was created
        /// </summary>
        public DateTime GeneratedAt { get; set; }

        /// <summary>
        /// The date and time when the refresh token expires
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// Created by IP address
        /// </summary>
        public string? CreatedByIp { get; set; }

        /// <summary>
        /// The date and time when the refresh token was revoked 
        /// </summary>
        public DateTime? RevokedAt { get; set; }

        /// <summary>
        /// Revoked by IP address
        /// </summary>
        public string? RevokedByIp { get; set; }

        /// <summary>
        /// Revoked by token
        /// </summary>
        public string? ReplacedByToken { get; set; }

        /// <summary>
        /// The reason why the refresh token was revoked
        /// </summary>
        public string ReasonRevoked { get; set; } = "Normal";

        /// <summary>
        /// The ID of the user that this refresh token belongs to
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// The user that this refresh token belongs to
        /// </summary>
        public User User { get; set; } = null!;
    }
}