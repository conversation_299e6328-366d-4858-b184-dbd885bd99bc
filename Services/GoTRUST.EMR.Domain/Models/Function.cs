﻿namespace GoTRUST.EMR.Domain.Models;

public class Function : Entity<Guid>
{
    [Required]
    [StringLength(100)]
    // Consider adding [Index(IsUnique = true)] attribute from Microsoft.EntityFrameworkCore for unique constraint
    public string Name { get; set; } = null!;
    /// <summary>
    /// Code của chức năng để phân quyền
    /// </summary>
    [MaxLength(2)]
    public string? Code { get; set; }

    [StringLength(255)]
    public string? Description { get; set; }
    public string? Path { get; set; }
    public string? IconPath { get; set; }
    public int Order { get; set; } = 99;

    // Navigation properties
    public virtual ICollection<RoleFunction>? RoleFunctions { get; set; }
}