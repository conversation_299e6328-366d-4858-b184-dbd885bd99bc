
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("TONG_HOP")]
    public class EMRTongHopWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public string SyncCode { get; set; } = string.Empty;
        public DateTime? SyncedAt { get; set; }
        public EMRTongHop? TONG_HOP { get; set; }
    }
    public class EMRTongHop
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất (dùng để liên kết giữa Bảng chỉ tiêu tổng hợp khám bệnh, chữ<PERSON> bệnh (bảng XML 1) và các bảng còn lại ban hành kèm theo Quyết định này trong một lần khám bệnh, chữa bệnh (PRIMARY KEY)).
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// <PERSON><PERSON> số thứ tự tăng từ 1 đến hết trong một lần gửi dữ liệu.
        ///</summary>
        public int STT { get; set; }

        ///<summary>
        /// Là họ và tên của người bệnh.
        ///</summary>
        [StringLength(100)]
        public string MA_BN { get; set; } = string.Empty;

        ///<summary>
        /// Là số thứ tự tăng từ 1 đến hết trong một lần gửi dữ liệu.
        ///</summary>
        [StringLength(255)]
        public string HO_TEN { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số căn cước công dân hoặc số định danh cá nhân trên thẻ căn cước hoặc giấy chứng nhận căn cước hoặc giấy khai sinh của người bệnh. 
        ///</summary>
        public string SO_CCCD { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày, tháng, năm sinh ghi trên thẻ BHYT của người bệnh, gồm 12 ký tự theo định dạng yyyymmddHHMM.
        ///</summary>
        [StringLength(12)]
        public string NGAY_SINH { get; set; } = string.Empty;

        ///<summary>
        /// Là mã giới tính của người bệnh (1: Nam; 2: Nữ; 3: Chưa xác định).
        ///</summary>
        public int GIOI_TINH { get; set; }

        [StringLength(5)]
        public string NHOM_MAU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã quốc tịch của người bệnh theo quy định tại Phụ lục 2 Thông tư số 07/2016/TT-BCA ngày 01 tháng 2 năm 2016 của Bộ trưởng Bộ Công an.
        ///</summary>
        [StringLength(3)]
        public string MA_QUOCTICH { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã dân tộc của người bệnh.
        ///</summary>
        [StringLength(2)]
        public string MA_DANTOC { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã nghề nghiệp của người bệnh.
        ///</summary>
        [StringLength(2)]
        public string MA_NGHE_NGHIEP { get; set; } = string.Empty;

        ///<summary>
        /// Ghi địa chỉ nơi cư trú hiện tại của người bệnh.
        ///</summary>
        [StringLength(1024)]
        public string DIA_CHI { get; set; } = string.Empty;

        ///<summary>
        /// Mã đơn vị hành chính cấp tỉnh nơi người bệnh đang cư trú (thường trú hoặc tạm trú).
        ///</summary>
        [StringLength(3)]
        public string MATINH_CU_TRU { get; set; } = string.Empty;

        ///<summary>
        /// Mã đơn vị hành chính cấp huyện nơi người bệnh đang cư trú (thường trú hoặc tạm trú).
        ///</summary>
        [StringLength(3)]
        public string MAHUYEN_CU_TRU { get; set; } = string.Empty;

        ///<summary>
        /// Mã đơn vị hành chính cấp xã nơi người bệnh đang cư trú (thường trú hoặc tạm trú).
        ///</summary>
        [StringLength(5)]
        public string MAXA_CU_TRU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số điện thoại liên lạc của người bệnh hoặc của thân nhân người bệnh.
        ///</summary>
        [StringLength(15)]
        public string? DIEN_THOAI { get; set; }

        ///<summary>
        /// Ghi mã thẻ BHYT của người bệnh do cơ quan BHXH cấp.
        ///</summary>
        public string? MA_THE_BHYT { get; set; }

        ///<summary>
        /// Ghi mã cơ sở KBCB nơi người bệnh đăng ký ban đầu ghi trên thẻ BHYT, gồm có 05 ký tự.
        ///</summary>
        public string? MA_DKBD { get; set; }

        ///<summary>
        /// Ghi thời điểm thẻ BHYT bắt đầu có giá trị sử dụng, gồm 08 ký tự theo định dạng yyyymmdd.
        ///</summary>
        public string? GT_THE_TU { get; set; }

        ///<summary>
        /// Ghi thời điểm thẻ BHYT hết giá trị sử dụng, gồm 08 ký tự theo định dạng yyyymmdd.
        ///</summary>
        public string? GT_THE_DEN { get; set; }

        ///<summary>
        /// Ghi thời điểm người bệnh tham gia BHYT được hưởng chế độ miễn cùng chi trả.
        ///</summary>
        [StringLength(12)]
        public string? NGAY_MIEN_CCT { get; set; }

        ///<summary>
        /// Ghi lý do người bệnh đến KBCB.
        ///</summary>
        public string? LY_DO_VV { get; set; }

        ///<summary>
        /// Ghi lý do vào nội trú, áp dụng đối với trường hợp điều trị nội trú hoặc điều trị ban ngày.
        ///</summary>
        public string? LY_DO_VNT { get; set; }

        ///<summary>
        /// Ghi mã lý do người bệnh vào điều trị nội trú theo quy định của Bộ Y tế.
        ///</summary>
        [StringLength(5)]
        public string? MA_LY_DO_VNT { get; set; }

        ///<summary>
        /// Ghi chẩn đoán sơ bộ của cơ sở KBCB ở thời điểm tiếp nhận người bệnh.
        ///</summary>
        public string? CHAN_DOAN_VAO { get; set; }

        ///<summary>
        /// Ghi đầy đủ các chẩn đoán xác định bệnh chính, bệnh kèm theo trong quá trình điều trị.
        ///</summary>
        public string? CHAN_DOAN_RV { get; set; }

        ///<summary>
        /// Ghi mã bệnh chính theo mã ICD-10 do Bộ trưởng Bộ Y tế ban hành.
        ///</summary>
        [StringLength(7)]
        public string MA_BENH_CHINH { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã các bệnh kèm theo hoặc mã của triệu chứng, hội chứng đã được xác định, phân cách bằng dấu chấm phẩy “;”.
        ///</summary>
        [StringLength(100)]
        public string? MA_BENH_KT { get; set; }

        ///<summary>
        /// Ghi mã của bệnh chính và mã của các bệnh kèm theo trong trường hợp sử dụng mã bệnh YHCT, phân cách bằng dấu chấm phẩy “;”.
        ///</summary>
        [StringLength(150)]
        public string? MA_BENH_YHCT { get; set; }

        ///<summary>
        /// Ghi mã phẫu thuật, thủ thuật quốc tế ICD-9 CM.
        ///</summary>
        [StringLength(125)]
        public string? MA_PTTT_QT { get; set; }

        ///<summary>
        /// Ghi mã đối tượng đến KBCB theo Bộ mã DMDC do Bộ trưởng Bộ Y tế ban hành.
        ///</summary>
        [StringLength(4)]
        public string MA_DOITUONG_KCB { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã cơ sở KBCB nơi chuyển người bệnh đi do cơ quan có thẩm quyền cấp.
        ///</summary>
        [StringLength(5)]
        public string? MA_NOI_DI { get; set; }

        ///<summary>
        /// Ghi mã cơ sở KBCB nơi chuyển người bệnh đến do cơ quan có thẩm quyền cấp.
        ///</summary>
        [StringLength(5)]
        public string? MA_NOI_DEN { get; set; }

        ///<summary>
        /// Ghi mã tai nạn thương tích.
        ///</summary>
        public int MA_TAI_NAN { get; set; }

        ///<summary>
        /// Ghi thời điểm người bệnh đến KBCB, gồm 12 ký tự, theo định dạng yyyymmddHHMM.
        ///</summary>
        [StringLength(12)]
        public string NGAY_VAO { get; set; } = string.Empty;

        ///<summary>
        /// Ghi thời điểm người bệnh vào điều trị nội trú hoặc điều trị ban ngày, gồm 12 ký tự, theo định dạng yyyymmddHHMM.
        ///</summary>
        [StringLength(12)]
        public string? NGAY_VAO_NOI_TRU { get; set; }

        ///<summary>
        /// Ghi thời điểm người bệnh kết thúc lần khám bệnh hoặc đợt điều trị tại cơ sở KBCB, gồm 12 ký tự theo định dạng yyyymmddHHMM.
        ///</summary>
        [StringLength(12)]
        public string NGAY_RA { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số giấy chuyển tuyến của cơ sở KBCB/Số giấy chuyển cơ sở KBCB nơi chuyển người bệnh đi hoặc số giấy hẹn khám lại (nếu có).
        ///</summary>
        [StringLength(50)]
        public string? GIAY_CHUYEN_TUYEN { get; set; }

        ///<summary>
        /// Số ngày điều trị = NGAY_RA - NGAY_VAO + 1
        ///</summary>
        public int SO_NGAY_DTRI { get; set; }

        ///<summary>
        /// Ghi phương pháp điều trị cho người bệnh như nội khoa, ngoại khoa, xạ trị, hoá trị hoặc xạ trị + nội khoa.
        ///</summary>
        public string PP_DIEU_TRI { get; set; } = string.Empty;

        ///<summary>
        /// Sửa đổi, bổ sung một số nội dung:
        /// - Mã "5": Tử vong tại cơ sở KBCB
        ///</summary>
        public int KET_QUA_DTRI { get; set; }

        ///<summary>
        /// Sửa đổi, bổ sung một số nội dung:
        /// - Mã "2": Chuyển tuyến/Chuyển cơ sở KBCB theo yêu cầu chuyên môn;
        /// - Mã "5": Chuyển tuyến/Chuyển cơ sở KBCB theo yêu cầu người bệnh.
        ///</summary>
        public int MA_LOAI_RV { get; set; }

        ///<summary>
        /// Ghi lời dặn của bác sĩ hoặc nhân viên y tế đối với người bệnh sau khi kết thúc lần KBCB.
        ///</summary>
        public string? GHI_CHU { get; set; }

        ///<summary>
        /// Ghi thời điểm người bệnh thanh toán chi phí KBCB, gồm 12 ký tự theo định dạng yyyymmddHHMM.
        ///</summary>
        [StringLength(12)]
        public string? NGAY_TTOAN { get; set; }

        ///<summary>
        /// Ghi tổng thành tiền (THANH_TIEN_BV) các khoản chi của thuốc (kể cả oxy), dịch truyền, máu và chế phẩm máu.
        ///</summary>
        public decimal T_THUOC { get; set; }

        ///<summary>
        /// Ghi tổng thành tiền của vật tư y tế trong trường thông tin THANH_TIEN_BV.
        ///</summary>
        public decimal T_VTYT { get; set; }

        ///<summary>
        /// Ghi tổng chi phí trong lần khám bệnh hoặc trong đợt điều trị.
        ///</summary>
        public decimal T_TONGCHI_BV { get; set; }

        ///<summary>
        /// Ghi tổng chi phí trong phạm vi quỹ BHYT thanh toán của lần khám bệnh hoặc đợt điều trị.
        ///</summary>
        public decimal T_TONGCHI_BH { get; set; }

        ///<summary>
        /// Ghi tổng số tiền người bệnh tự trả ngoài phạm vi chi trả của Quỹ BHYT.
        ///</summary>
        public decimal T_BNTT { get; set; }

        ///<summary>
        /// Ghi tổng số tiền người bệnh cùng chi trả trong phạm vi quyền lợi được hưởng BHYT.
        ///</summary>
        public decimal T_BNCCT { get; set; }

        ///<summary>
        /// Ghi tổng số tiền đề nghị cơ quan bảo hiểm xã hội thanh toán.
        ///</summary>
        public decimal T_BHTT { get; set; }

        ///<summary>
        /// Ghi tổng số tiền các nguồn khác chi trả ngoài phạm vi chi trả của quỹ BHYT.
        ///</summary>
        public decimal T_NGUONKHAC { get; set; }

        ///<summary>
        /// Ghi số tiền quỹ BHYT thanh toán đối với các khoản chi ngoài định suất hoặc ngoài DRG.
        ///</summary>
        public decimal T_BHTT_GDV { get; set; }

        ///<summary>
        /// Ghi năm mà cơ sở KBCB đề nghị cơ quan BHXH thanh toán.
        ///</summary>
        public int NAM_QT { get; set; }

        ///<summary>
        /// Ghi tháng mà cơ sở KBCB đề nghị cơ quan BHXH thanh toán.
        ///</summary>
        public int THANG_QT { get; set; }

        ///<summary>
        /// Ghi mã hình thức KBCB theo Bộ mã DMDC do Bộ trưởng Bộ Y tế ban hành.
        ///</summary>
        [StringLength(2)]
        public string MA_LOAI_KCB { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã khoa nơi người bệnh điều trị.
        ///</summary>
        [StringLength(50)]
        public string MA_KHOA { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã cơ sở KBCB nơi người bệnh đến khám bệnh, điều trị do cơ quan có thẩm quyền cấp.
        ///</summary>
        [StringLength(5)]
        public string MA_CSKCB { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã nơi sinh sống của người bệnh ghi trên thẻ BHYT (K1 hoặc K2 hoặc K3)
        ///</summary>
        [StringLength(2)]
        public string MA_KHUVUC { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số kilogram (kg) cân nặng của người bệnh, biểu thị đầy đủ cả số thập phân.
        ///</summary>
        [StringLength(6)]
        public string CAN_NANG { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số gram (ký hiệu là: g) cân nặng của con mới sinh. Chỉ ghi trong trường hợp sinh con.
        ///</summary>
        [StringLength(100)]
        public string? CAN_NANG_CON { get; set; }

        ///<summary>
        /// Ghi thời điểm người bệnh tham gia BHYT đủ 05 năm liên tục, gồm 08 ký tự theo định dạng yyyymmdd.
        ///</summary>
        [StringLength(8)]
        public string NAM_NAM_LIEN_TUC { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày cơ sở KBCB hẹn người bệnh tái khám tiếp theo (nếu có), gồm 08 ký tự theo định dạng yyyymmdd.
        ///</summary>
        [StringLength(50)]
        public string? NGAY_TAI_KHAM { get; set; }

        ///<summary>
        /// Ghi mã số hồ sơ bệnh án hoặc số phiếu khám ngoại trú của người bệnh do cơ sở KBCB quy định.
        ///</summary>
        [StringLength(100)]
        public string MA_HSBA { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã của người đứng đầu cơ sở KBCB hoặc người được người đứng đầu cơ sở KBCB ủy quyền được ký và đóng dấu của cơ sở KBCB đó (mã hoá theo số GPHN).
        ///</summary>
        [StringLength(255)]
        public string MA_TTDV { get; set; } = string.Empty;

        ///<summary>
        /// Ghi thông tin chiều cao của người bệnh (đơn vị cm), bắt buộc trong trường hợp sử dụng chỉ số này để chẩn đoán, điều trị.
        ///</summary>
        public string? DU_PHONG { get; set; }
    }
}
