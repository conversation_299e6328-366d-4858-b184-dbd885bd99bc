﻿namespace GoTRUST.EMR.Domain.Models;

public class NotificationTemplate : Entity<Guid>
{
    [Required]
    [StringLength(100)]
    // Consider adding [Index(IsUnique = true)] attribute from Microsoft.EntityFrameworkCore for unique constraint
    public string Name { get; set; } = null!;

    [Required]
    [StringLength(20)]
    public NotificationTemplateType Type { get; set; } = NotificationTemplateType.email; // 'email', 'sms'

    [StringLength(255)]
    public string Subject { get; set; } = string.Empty;

    [Required]
    [Column(TypeName = "text")]
    public string Body { get; set; } = string.Empty;
}
