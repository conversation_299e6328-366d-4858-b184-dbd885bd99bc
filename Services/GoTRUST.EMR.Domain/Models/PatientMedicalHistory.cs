namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Patient medical history
    /// </summary>
    public class PatientMedicalHistory : Entity<Guid>
    {
        /// <summary>
        /// Patient ID
        /// </summary>
        public Guid PatientId { get; set; }
        
        /// <summary>
        /// Type of history (e.g., Allergy, PreExistingCondition, ChronicDisease)
        /// </summary>
        public string HistoryType { get; set; } = string.Empty;
        
        /// <summary>
        /// Detailed description
        /// </summary>
        public string Description { get; set; } = string.Empty;
        
        /// <summary>
        /// Time when the history was recorded
        /// </summary>
        public DateTime RecordedAt { get; set; }
        
        /// <summary>
        /// Navigation property to the Patient
        /// </summary>
        public virtual Patient Patient { get; set; } = null!;
    }
}
