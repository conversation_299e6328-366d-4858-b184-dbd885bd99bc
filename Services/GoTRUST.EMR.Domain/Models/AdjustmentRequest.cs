namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Medical record adjustment request
    /// </summary>
    public class AdjustmentRequest : Entity<Guid>
    {
        /// <summary>
        /// Hospital ID
        /// </summary>
        public Guid HospitalId { get; set; }
        
        /// <summary>
        /// Medical record ID
        /// </summary>
        public Guid MedicalRecordId { get; set; }
        
        /// <summary>
        /// User ID who requested the adjustment (Doctor)
        /// </summary>
        public Guid RequestedByUserId { get; set; }
        
        /// <summary>
        /// Reason for adjustment
        /// </summary>
        public string Reason { get; set; } = string.Empty;
        
        /// <summary>
        /// Request date
        /// </summary>
        public DateTime RequestedDate { get; set; }
        
        /// <summary>
        /// User ID who approved the request (Hospital Tech Dept Head)
        /// </summary>
        public Guid? ApprovedByUserId { get; set; }
        
        /// <summary>
        /// Approval status (Pending approval, Approved, Rejected, Approved & Synced with HIS)
        /// </summary>
        public string ApprovalStatus { get; set; } = string.Empty;
        
        /// <summary>
        /// Reason for approval/rejection
        /// </summary>
        public string? ApprovalReason { get; set; }
        
        /// <summary>
        /// Approval date
        /// </summary>
        public DateTime? ApprovalDate { get; set; }
        
        /// <summary>
        /// Indicates if synchronized from HIS after reopening the record
        /// </summary>
        public bool IsSynchronizedFromHIS { get; set; }
        
        /// <summary>
        /// Navigation property to the Hospital
        /// </summary>
        public virtual Hospital Hospital { get; set; } = null!;
        
        /// <summary>
        /// Navigation property to the Medical Record
        /// </summary>
        public virtual MedicalRecord MedicalRecord { get; set; } = null!;
    }
}
