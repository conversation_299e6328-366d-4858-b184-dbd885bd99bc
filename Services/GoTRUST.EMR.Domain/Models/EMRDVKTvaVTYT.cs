
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIET_DVKT")]
    public class EMRDVKTvaVTYTWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRDVKTvaVTYT? CHI_TIET_DVKT { get; set; }
    }
    public class EMRDVKTvaVTYT
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất (dùng để liên kết giữa Bảng chỉ tiêu tổng hợp khám bệnh, chữa bệnh (bảng XML 1) và các bảng còn lại ban hành kèm theo Quyết định này trong một lần khám bệnh, chữa bệnh (PRIMARY KEY)).
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// <PERSON><PERSON> thứ tự tăng từ 1 đến hết trong một lần gửi dữ liệu.
        ///</summary>
        public int STT { get; set; }

        ///<summary>
        /// Ghi mã dịch vụ kỹ thuật hoặc mã tiền khám hoặc mã tiền giường bệnh theo hạng bệnh viện theo quy định tại Bộ mã DMDC do Bộ Y tế ban hành.
        ///</summary>
        [StringLength(50)]
        public string MA_DICH_VU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã dịch vụ kỹ thuật hoặc mã tiền khám hoặc mã tiền giường bệnh theo hạng bệnh viện theo quy định tại Bộ mã DMDC do Bộ Y tế ban hành.
        ///</summary>
        [StringLength(255)]
        public string MA_PTTT_QT { get; set; } = string.Empty;

        [StringLength(255)]
        public string? MA_VAT_TU { get; set; }

        ///<summary>
        /// Ghi mã nhóm theo chi phí, dùng để phân loại, sắp xếp các chi phí vào các nhóm, ghi theo Phụ lục số 3 ban hành kèm theo Quyết định số 5937/QĐ-BYT ngày 30 tháng 12 năm 2021 của Bộ trưởng Bộ Y tế.
        ///</summary>
        public int MA_NHOM { get; set; }

        ///<summary>
        /// Ghi mã gói VTYT trong một lần sử dụng dịch vụ kỹ thuật (lần thứ nhất ghi G1, lần thứ hai ghi G2,…).
        ///</summary>
        [StringLength(3)]
        public string GOI_VTYT { get; set; } = string.Empty;

        ///<summary>
        /// Ghi tên nhóm VTYT theo quy định tại Thông tư số 04/2017/TT-BYT.
        ///</summary>
        [StringLength(1024)]
        public string TEN_VAT_TU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi tên dịch vụ kỹ thuật hoặc tên dịch vụ khám bệnh hoặc tên giường bệnh đề nghị quỹ BHYT thanh toán.
        ///</summary>
        [StringLength(1024)]
        public string TEN_DICH_VU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã loại xăng, dầu để tính chi phí vận chuyển người bệnh, ghi theo Bộ mã DMDC do Bộ trưởng Bộ Y tế ban hành.
        ///</summary>
        [StringLength(20)]
        public string MA_XANG_DAU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi đơn vị tính của VTYT hoặc DVKT đề nghị thanh toán.
        ///</summary>
        [StringLength(50)]
        public string DON_VI_TINH { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã để xác định phạm vi của VTYT, dịch vụ kỹ thuật, trong đó:
        /// - Mã "1": VTYT, dịch vụ kỹ thuật được quỹ BHYT thanh toán;
        /// - Mã "2": VTYT, dịch vụ kỹ thuật do người bệnh tự trả;
        /// - Mã "3": VTYT, dịch vụ kỹ thuật được sử dụng cho các đối tượng thuộc quân đội, công an, cơ yếu theo quy định tại khoản 2 Điều 10 Nghị định 70/2015/NĐ-CP của Chính phủ.
        ///</summary>
        public int PHAM_VI { get; set; }

        ///<summary>
        /// Ghi số lượng ngày giường bệnh, công khám, dịch vụ kỹ thuật hoặc VTYT thực tế sử dụng cho người bệnh, làm tròn số đến 3 chữ số thập phân. Sử dụng dấu chấm “.” để phân cách giữa số Nguyên (hàng đơn vị) với số thập phân đầu tiên.
        ///</summary>
        public decimal SO_LUONG { get; set; }

        ///<summary>
        /// Ghi đơn giá dịch vụ kỹ thuật, tiền giường bệnh, tiền công khám bệnh theo yêu cầu (nếu có) hoặc giá dịch vụ KBCB không thuộc danh mục do BHYT thanh toán mà không phải là dịch vụ KBCB theo yêu cầu.
        ///</summary>
        public decimal DON_GIA_BV { get; set; }

        ///<summary>
        /// Ghi đơn giá dịch vụ kỹ thuật, VTYT, tiền giường bệnh, tiền công khám bệnh do quỹ BHYT thanh toán; làm tròn đến 3 (ba) chữ số thập phân. Sử dụng dấu chấm “.” để phân cách giữa số Nguyên (hàng đơn vị) với số thập phân đầu tiên.
        ///</summary>
        public decimal DON_GIA_BH { get; set; }

        ///<summary>
        /// Ghi thông tin thầu của VTYT theo thứ tự, gồm: Số quyết định phê duyệt kết quả lựa chọn nhà thầu; số gói thầu; mã nhóm thầu (theo quy định tại Phụ lục số 7 ban hành kèm theo Quyết định số 5937/QĐ-BYT ngày 30 tháng 12 năm 2021 của Bộ trưởng Bộ Y tế); năm ban hành quyết định phê duyệt kết quả lựa chọn nhà thầu. Các thông tin này cách nhau bằng dấu chấm phẩy “;”.
        ///</summary>
        [StringLength(25)]
        public string TT_THAU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi tỷ lệ thanh toán đối với một số dịch vụ kỹ thuật đặc biệt. Tỷ lệ này là số nguyên dương.
        ///</summary>
        public int TYLE_TT_DV { get; set; }

        ///<summary>
        /// Ghi tỷ lệ thanh toán BHYT đối với dịch vụ kỹ thuật hoặc VTYT có quy định tỷ lệ (%). Tỷ lệ này là số nguyên dương.
        ///</summary>
        public int TYLE_TT_BH { get; set; }

        ///<summary>
        /// Ghi số tiền thanh toán theo giá của bệnh viện. Trường thông tin này được xác định như sau: THANH_TIEN_BV = SO_LUONG * DON_GIA_BV * TYLE_TT_DV/100, làm tròn số đến 2 chữ số thập phân.
        ///</summary>
        public decimal THANH_TIEN_BV { get; set; }

        ///<summary>
        /// Ghi mức giá do quỹ BHYT thanh toán theo quy định của cơ quan có thẩm quyền. Trường thông tin này được xác định như sau:
        /// THANH_TIEN_BH = SO_LUONG * DON_GlA_BH * TYLE_TT_DV/100 * TYLE_TT_BH/100, làm tròn số đến 2 chữ số thập phân. Sử dụng dấu chấm “.” để phân cách giữa số nguyên (hàng đơn vị) với số thập phân đầu tiên.
        ///</summary>
        public decimal THANH_TIEN_BH { get; set; }

        public decimal? T_TRANTT { get; set; }

        ///<summary>
        /// Ghi mức hưởng tương ứng với từng loại chi phí, trong đó:
        /// - Trường hợp người bệnh KBCB BHYT đúng tuyến ghi mức hưởng là 80 hoặc 95 hoặc 100;
        /// - Trường hợp trái tuyến ghi mức hưởng sau khi đã nhân với tỷ lệ hưởng trái tuyến tương ứng với tuyến chuyên môn kỹ thuật của cơ sở KBCB.
        ///</summary>
        public int MUC_HUONG { get; set; }

        ///<summary>
        /// Ghi số tiền dịch vụ kỹ thuật hoặc VTYT được ngân sách nhà nước (Trung ương và/hoặc địa phương) hỗ trợ, làm tròn số đến 2 chữ số thập phân. Sử dụng dấu chấm “.” để phân cách giữa số Nguyên (hàng đơn vị) với số thập phân đầu tiên.
        ///</summary>
        public decimal T_NGUONKHAC_NSNN { get; set; }

        ///<summary>
        /// Ghi số tiền dịch vụ kỹ thuật hoặc VTYT được các tổ chức, đơn vị có trụ sở ngoài lãnh thổ Việt Nam hoặc các cá nhân đang sinh sống, học tập, lao động ngoài lãnh thổ Việt Nam hỗ trợ, làm tròn số đến 2 chữ số thập phân. Sử dụng dấu chấm “.” để phân cách giữa số Nguyên (hàng đơn vị) với số thập phân đầu tiên.
        ///</summary>
        public decimal T_NGUONKHAC_VTNN { get; set; }

        ///<summary>
        /// Ghi số tiền dịch vụ kỹ thuật hoặc VTYT được các tổ chức, cơ quan, đơn vị có trụ sở trong lãnh thổ Việt Nam hoặc các cá nhân đang sinh sống, học tập, lao động trong lãnh thổ Việt Nam hỗ trợ, làm tròn số đến 2 chữ số thập phân. Sử dụng dấu chấm “.” để phân cách giữa số Nguyên (hàng đơn vị) với số thập phân đầu tiên.
        ///</summary>
        public decimal T_NGUONKHAC_VTTN { get; set; }

        ///<summary>
        /// Ghi số tiền dịch vụ kỹ thuật hoặc VTYT được các nguồn khác còn lại (Không thuộc một trong ba nguồn của các trường thông tin trong bảng này: "T_NGUONKHAC_NSNN", "T_NGUONKHAC_VTNN", "T_NGUONKHAC_VTTN") hỗ trợ, làm tròn số đến 2 chữ số thập phân. Sử dụng dấu chấm “.” để phân cách giữa số Nguyên (hàng đơn vị) với số thập phân đầu tiên.
        ///</summary>
        public decimal T_NGUONKHAC_CL { get; set; }

        ///<summary>
        /// Là số tiền do nguồn khác chi trả.
        ///</summary>
        public decimal T_NGUONKHAC { get; set; }

        ///<summary>
        /// Ghi số tiền người bệnh tự trả ngoài phạm vi chi trả của Quỹ BHYT, làm tròn số đến 2 chữ số thập phân. Sử dụng dấu chấm “.” để phân cách giữa số Nguyên (hàng đơn vị) với số thập phân đầu tiên.
        ///</summary>
        public decimal T_BNTT { get; set; }

        ///<summary>
        /// Ghi số tiền người bệnh cùng chi trả trong phạm vi quyền lợi mức hưởng BHYT, làm tròn số đến 2 chữ số thập phân. Sử dụng dấu chấm “.” để phân cách giữa số Nguyên (hàng đơn vị) với số thập phân đầu tiên.
        ///</summary>
        public decimal T_BNCCT { get; set; }

        ///<summary>
        /// Ghi số tiền cơ sở KBCB đề nghị cơ quan BHXH thanh toán theo phạm vi quyền lợi, mức hưởng BHYT của người bệnh, làm tròn số đến 2 chữ số thập phân. Sử dụng dấu chấm “.” để phân cách giữa số Nguyên (hàng đơn vị) với số thập phân đầu tiên.
        ///</summary>
        public decimal T_BHTT { get; set; }

        [StringLength(50)]
        ///<summary>
        /// Ghi mã khoa nơi người bệnh được cung cấp DVKT, VTYT, giường bệnh. Mã khoa ghi theo Phụ lục số 5 ban hành kèm theo Quyết định số 5937/QĐ-BYT ngày 30 tháng 12 năm 2021 của Bộ trưởng Bộ Y tế.
        ///</summary>
        public string MA_KHOA { get; set; } = string.Empty;

        [StringLength(50)]
        public string? MA_GIUONG { get; set; }

        [StringLength(255)]
        ///<summary>
        /// Ghi mã người hành nghề thực hiện khám, chỉ định (mã hóa theo số GPHN).
        ///</summary>
        public string MA_BAC_SI { get; set; } = string.Empty;

        [StringLength(255)]
        ///<summary>
        /// Ghi mã nhân viên y tế thực hiện dịch vụ kỹ thuật (mã hóa theo số GPHN).Trường hợp người thực hiện DVKT không cần phải cấp GPHN theo quy định tại điểm g khoản 4 Điều 40 Nghị định số 96/2023/NĐ-CP thì ghi số CCCD hoặc số định danh cá nhân trên thẻ căn cước.
        ///</summary>
        public string NGUOI_THUC_HIEN { get; set; } = string.Empty;

        [StringLength(100)]
        public string? MA_BENH { get; set; }

        [StringLength(150)]
        public string? MA_BENH_YHCT { get; set; }

        ///<summary>
        /// Riêng ngày giường bệnh, cơ sở KBCB phải thực hiện việc ghi ngày y lệnh theo ngày bắt đầu sử dụng hoặc ngày thay đổi mã giường, loại giường, giá giường, nằm ghép, chuyển giữa các khoa.
        ///</summary>
        [StringLength(12)]
        public string NGAY_YL { get; set; } = string.Empty;

        ///<summary>
        /// Đối với giường điều trị ban ngày thì ghi theo từng ngày điều trị trong đó NGAY_TH_YL là thời điểm bắt đầu sử dụng giường bệnh.
        ///</summary>
        [StringLength(12)]
        public string NGAY_TH_YL { get; set; } = string.Empty;

        [StringLength(12)]
        public string? NGAY_KQ { get; set; }

        ///<summary>
        /// Ghi mã phương thức thanh toán, trong đó:
        /// - Mã "1": thanh toán theo phí dịch vụ;
        /// - Mã "2": thanh toán theo định suất;
        /// - Mã "3": thanh toán theo trường hợp bệnh (DRG).
        ///</summary>
        public int MA_PTTT { get; set; }

        public int? VET_THUONG_TP { get; set; }

        public int? PP_VO_CAM { get; set; }

        public int? VI_TRI_TH_DVKT { get; set; }

        [StringLength(1024)]
        public string? MA_MAY { get; set; }

        [StringLength(255)]
        public string? MA_HIEU_SP { get; set; }

        public int? TAI_SU_DUNG { get; set; }

        public string? DU_PHONG { get; set; }
    }
}
