
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHECK_IN")]
    public class EMRCheckinWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRCheckin CHECK_IN { get; set; } = default!;
    }
    public class EMRCheckin
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất (dùng để liên kết giữa Bảng chỉ tiêu tổng hợp khám bệnh, chữa bệnh (bảng XML 1) và các bảng còn lại ban hành kèm theo Quyết định này trong một lần khám bệnh, chữa bệnh (PRIMARY KEY)).
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// <PERSON><PERSON> s<PERSON> thứ tự tăng từ 1 đến hết trong một lần gửi dữ liệu.
        ///</summary>
        public int STT { get; set; }

        ///<summary>
        /// Là mã người bệnh theo quy định của cơ sở KBCB
        ///</summary>
        [StringLength(100)]
        public string MA_BN { get; set; } = string.Empty;

        ///<summary>
        /// Là họ và tên của người bệnh.
        ///</summary>
        [StringLength(255)]
        public string HO_TEN { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số căn cước công dân hoặc số định danh cá nhân trên thẻ căn cước hoặc giấy chứng nhận căn cước hoặc giấy khai sinh của người bệnh.
        ///</summary>
        public string SO_CCCD { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày, tháng, năm sinh ghi trên thẻ BHYT của người bệnh, gồm 12 ký tự, bao gồm: 04 ký tự năm + 02 ký tự tháng + 02 ký tự ngày + 02 ký tự giờ + 02 ký tự phút. Ví dụ: 201703311520
        ///</summary>
        [StringLength(12)]
        public string NGAY_SINH { get; set; } = string.Empty;

        ///<summary>
        /// Là mã giới tính của người bệnh (1: Nam; 2: Nữ; 3: Chưa xác định)
        ///</summary>
        public int GIOI_TINH { get; set; }

        [StringLength(15)]
        public string? MA_THE_BHYT { get; set; }

        [StringLength(5)]
        public string? MA_DKBD { get; set; }

        [StringLength(8)]
        public string? GT_THE_TU { get; set; }

        [StringLength(8)]
        public string? GT_THE_DEN { get; set; }

        ///<summary>
        /// Ghi mã đối tượng đến KBCB theo Bộ mã DMDC do Bộ trưởng Bộ Y tế ban hành.
        ///</summary>
        [StringLength(4)]
        public string MA_DOITUONG_KCB { get; set; } = string.Empty;

        ///<summary>
        /// Ghi thời điểm người bệnh đến KBCB, gồm 12 ký tự, trong đó: 04 ký tự năm + 02 ký tự tháng + 02 ký tự ngày + 02 ký tự giờ (định dạng theo 24 giờ) + 02 ký tự phút. Ví dụ: 201703311520
        ///</summary>
        [StringLength(12)]
        public string NGAY_VAO { get; set; } = string.Empty;

        [StringLength(12)]
        public string? NGAY_VAO_NOI_TRU { get; set; }

        public string? LY_DO_VNT { get; set; }

        [StringLength(5)]
        public string? MA_LY_DO_VNT { get; set; }

        ///<summary>
        /// Ghi mã hình thức KBCB theo Bộ mã DMDC do Bộ trưởng Bộ Y tế ban hành.
        ///</summary>
        [StringLength(2)]
        public string MA_LOAI_KCB { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã cơ sở KBCB nơi người bệnh đến khám bệnh, điều trị do cơ quan có thẩm quyền cấp.
        ///</summary>
        [StringLength(5)]
        public string MA_CSKCB { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã dịch vụ kỹ thuật hoặc mã dịch vụ khám bệnh thực hiện đối với người bệnh, theo quy định tại Bộ mã danh mục dùng chung (DMDC) do Bộ trưởng Bộ Y tế ban hành trong trường hợp phát sinh chi phí đầu tiên tại khoa điều trị nội trú hoặc khoa điều trị ban ngày hoặc khoa điều trị ngoại trú là dịch vụ kỹ thuật hoặc tiền khám bệnh.
        ///</summary>
        [StringLength(50)]
        public string MA_DICH_VU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi tên dịch vụ kỹ thuật hoặc tên dịch vụ khám bệnh.
        ///</summary>
        [StringLength(1024)]
        public string TEN_DICH_VU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã hoạt chất của thuốc theo quy định tại Bộ mã danh mục dùng chung do Bộ Y tế ban hành trong trường hợp phát sinh chi phí đầu tiên tại khoa điều trị nội trú hoặc khoa điều trị nội trú ban ngày hoặc khoa điều trị ngoại trú là thuốc.
        ///</summary>
        [StringLength(255)]
        public string MA_THUOC { get; set; } = string.Empty;

        [StringLength(1024)]
        public string TEN_THUOC { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã vật tư y tế là mã nhóm VTYT quy định tại Thông tư số 04/2017/TT-BYT.
        ///</summary>
        [StringLength(255)]
        public string MA_VAT_TU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi tên vật tư y tế là tên nhóm VTYT quy định tại Thông tư số 04/2017/TT-BYT.
        ///</summary>
        [StringLength(1024)]
        public string TEN_VAT_TU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi thời điểm ra y lệnh (gồm 12 ký tự, theo cấu trúc: yyyymmddHHmm, bao gồm: 04 ký tự năm + 02 ký tự tháng + 02 ký tự ngày + 02 ký tự giờ (24 giờ) + 02 ký tự phút). Ví dụ: 201703311520
        ///</summary>
        [StringLength(12)]
        public string NGAY_YL { get; set; } = string.Empty;

        public string? DU_PHONG { get; set; }
    }
}
