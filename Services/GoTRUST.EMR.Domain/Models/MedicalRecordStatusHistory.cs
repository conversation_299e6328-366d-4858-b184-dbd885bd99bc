namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Medical record status history
    /// </summary>
    public class MedicalRecordStatusHistory : Entity<Guid>
    {
        /// <summary>
        /// Medical record ID
        /// </summary>
        public Guid MedicalRecordId { get; set; }

        /// <summary>
        /// Status of the medical record
        /// </summary>
        public MedicalRecordHistoryStatus Status { get; set; }

        /// <summary>
        /// User ID who made the change
        /// </summary>
        public Guid? ChangedByUserId { get; set; }

        /// <summary>
        /// Reason for the change
        /// </summary>
        public string ChangeReason { get; set; } = string.Empty;

        /// <summary>
        /// Date and time of the change
        /// </summary>
        public DateTime ChangeDate { get; set; }

        /// <summary>
        /// Navigation property to the Medical Record
        /// </summary>
        public virtual MedicalRecord MedicalRecord { get; set; } = null!;

        /// <summary>
        /// Navigation property to the User who made the change
        /// </summary>
        public virtual User? ChangedByUser { get; set; } = null!;
    }
}
