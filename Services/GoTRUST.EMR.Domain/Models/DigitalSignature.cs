namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Chữ ký số
    /// </summary>
    public class DigitalSignature : Entity<Guid>
    {
        /// <summary>
        /// Mã người dùng sở hữu chữ ký số
        /// </summary>
        public Guid UserId { get; set; }
        
        /// <summary>
        /// Dữ liệu chứng chỉ chữ ký số (ví dụ: base64 encoded)
        /// </summary>
        public string CertificateData { get; set; } = string.Empty;
        
        /// <summary>
        /// Thời gian cấp
        /// </summary>
        public DateTime IssuedAt { get; set; }
        
        /// <summary>
        /// Thời gian hết hạn
        /// </summary>
        public DateTime ExpiresAt { get; set; }
        
        /// <summary>
        /// Người dùng sở hữu chữ ký số
        /// </summary>
        public virtual User User { get; set; } = null!;
    }
}
