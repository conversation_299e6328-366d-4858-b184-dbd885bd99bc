
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIEU_CHAMSOCVADIEUTRIHIVVAAIDS")]
    public class EMRHIVvaAIDSWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRHIVvaAIDS? CHI_TIEU_CHAMSOCVADIEUTRIHIVVAAIDS { get; set; }
    }
    public class EMRHIVvaAIDS
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất (dùng để liên kết giữa Bảng chỉ tiêu tổng hợp khám bệnh, chữa bệnh (bảng XML 1) và các bảng còn lại ban hành kèm theo Quyết định này trong một lần khám bệnh, chữa bệnh (PRIMARY KEY)).
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        public string? MA_THE_BHYT { get; set; }

        ///<summary>
        /// Ghi số căn cước công dân hoặc số định danh cá nhân trên thẻ căn cước.
        ///</summary>
        public string SO_CCCD { get; set; } = string.Empty;

        [StringLength(12)]
        public string NGAY_SINH { get; set; } = string.Empty;

        public int GIOI_TINH { get; set; }

        [StringLength(1024)]
        public string DIA_CHI { get; set; } = string.Empty;

        [StringLength(3)]
        public string MATINH_CU_TRU { get; set; } = string.Empty;

        [StringLength(3)]
        public string MAHUYEN_CU_TRU { get; set; } = string.Empty;

        [StringLength(5)]
        public string MAXA_CU_TRU { get; set; } = string.Empty;

        [StringLength(8)]
        public string? NGAYKD_HIV { get; set; }

        [StringLength(5)]
        public string NOI_LAY_MAU_XN { get; set; } = string.Empty;

        [StringLength(5)]
        public string NOI_XN_KD { get; set; } = string.Empty;

        [StringLength(5)]
        public string NOI_BDDT_ARV { get; set; } = string.Empty;

        ///<summary>
        /// Ghi thời điểm đầu tiên người bệnh nhận thuốc ARV; định dạng yyyymmdd.
        ///</summary>
        [StringLength(8)]
        public string BDDT_ARV { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã phác đồ điều trị HIV/AIDS khi bắt đầu điều trị ARV.
        ///</summary>
        [StringLength(200)]
        public string MA_PHAC_DO_DIEU_TRI_BD { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã bậc của phác đồ khi bắt đầu điều trị ARV.
        ///</summary>
        public int MA_BAC_PHAC_DO_BD { get; set; }

        ///<summary>
        /// Ghi mã lý do bệnh nhân đăng ký giai đoạn điều trị.
        ///</summary>
        public int MA_LYDO_DTRI { get; set; }

        ///<summary>
        /// Ghi mã loại điều trị lao.
        ///</summary>
        public int LOAI_DTRI_LAO { get; set; }

        public int SANG_LOC_LAO { get; set; }

        ///<summary>
        /// Ghi mã phác đồ điều trị lao.
        ///</summary>
        public int PHACDO_DTRI_LAO { get; set; }

        ///<summary>
        /// Ghi thời điểm bắt đầu điều trị bệnh lao hoặc lao tiềm ẩn; định dạng yyyymmdd.
        ///</summary>
        [StringLength(8)]
        public string NGAYBD_DTRI_LAO { get; set; } = string.Empty;

        [StringLength(8)]
        public string? NGAYKT_DTRI_LAO { get; set; }

        public int KQ_DTRI_LAO { get; set; }

        ///<summary>
        /// Ghi mã lý do chỉ định xét nghiệm đo tải lượng vi rút.
        ///</summary>
        public int MA_LYDO_XNTL_VR { get; set; }

        ///<summary>
        /// Ghi thời điểm lấy mẫu làm xét nghiệm tải lượng virus; định dạng yyyymmdd. Ví dụ: 20170331
        ///</summary>
        [StringLength(8)]
        public string NGAY_XN_TLVR { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã kết quả xét nghiệm tải lượng vi rút HIV.
        ///</summary>
        public int KQ_XNTL_VR { get; set; }

        ///<summary>
        /// Ghi thời điểm có kết quả xét nghiệm tải lượng virus; định dạng yyyymmdd. Ví dụ: 20170331
        ///</summary>
        [StringLength(8)]
        public string NGAY_KQ_XN_TLVR { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã đối tượng đến khám.
        ///</summary>
        public int MA_LOAI_BN { get; set; }

        public int GIAI_DOAN_LAM_SANG { get; set; }

        ///<summary>
        /// Ghi mã nhóm đối tượng.
        ///</summary>
        public int NHOM_DOI_TUONG { get; set; }

        ///<summary>
        /// Ghi mã tình trạng của đối tượng đến khám. Ví dụ: 1;2;3
        ///</summary>
        [StringLength(18)]
        public string MA_TINH_TRANG_DK { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã lần thực hiện xét nghiệm PCR.
        ///</summary>
        public int LAN_XN_PCR { get; set; }

        ///<summary>
        /// Ghi ngày thực hiện xét nghiệm PCR; định dạng yyyymmdd. Ví dụ: 20170331
        ///</summary>
        [StringLength(8)]
        public string NGAY_XN_PCR { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày có kết quả xét nghiệm PCR1; định dạng yyyymmdd. Ví dụ: 20170331
        ///</summary>
        [StringLength(8)]
        public string NGAY_KQ_XN_PCR { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã kết quả xét nghiệm PCR1.
        ///</summary>
        public int MA_KQ_XN_PCR { get; set; }

        ///<summary>
        /// Ghi thời điểm nhận thông tin mang thai; định dạng yyyymmdd. Ví dụ: 20170331
        ///</summary>
        [StringLength(8)]
        public string NGAY_NHAN_TT_MANG_THAI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi thời điểm bắt đầu điều trị Cotrimoxazol (CTX); định dạng yyyymmdd. Ví dụ: 20170331
        ///</summary>
        [StringLength(8)]
        public string NGAY_BAT_DAU_DT_CTX { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã xử trí của cơ sở y tế. Ví dụ: 1;2;3
        ///</summary>
        public string MA_XU_TRI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày bắt đầu xử trí của đợt điều trị ARV; định dạng yyyymmdd. Ví dụ: 20170331
        ///</summary>
        [StringLength(8)]
        public string NGAY_BAT_DAU_XU_TRI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày kết thúc xử trí của đợt điều trị ARV; định dạng yyyymmdd. Ví dụ: 20170331
        ///</summary>
        [StringLength(8)]
        public string NGAY_KET_THUC_XU_TRI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã phác đồ điều trị HIV/AIDS của đợt điều trị.
        ///</summary>
        [StringLength(200)]
        public string MA_PHAC_DO_DIEU_TRI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã bậc phác đồ của đợt điều trị.
        ///</summary>
        public int MA_BAC_PHAC_DO { get; set; }

        ///<summary>
        /// Ghi số ngày thuốc ARV được cấp.
        ///</summary>
        public int SO_NGAY_CAP_THUOC_ARV { get; set; }

        [StringLength(8)]
        public string NGAY_CHUYEN_PHAC_DO { get; set; } = string.Empty;

        public int LY_DO_CHUYEN_PHAC_DO { get; set; }

        [StringLength(5)]
        public string MA_CSKCB { get; set; } = string.Empty;

        public string? DU_PHONG { get; set; }
    }
}
