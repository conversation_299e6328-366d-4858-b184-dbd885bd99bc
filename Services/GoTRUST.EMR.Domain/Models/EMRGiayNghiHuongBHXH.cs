
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIEU_GIAYNGHIHUONGBHXH")]
    public class EMRGiayNghiHuongBHXHWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRGiayNghiHuongBHXH? CHI_TIEU_GIAYNGHIHUONGBHXH { get; set; }
    }
    public class EMRGiayNghiHuongBHXH
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất (dùng để liên kết giữa Bảng chỉ tiêu tổng hợp khám bệnh, chữ<PERSON> bệnh (bảng XML 1) và các bảng còn lại ban hành kèm theo Quyết định này trong một lần khám bệnh, chữ<PERSON> bệ<PERSON> (PRIMARY KEY)).
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// <PERSON><PERSON> số chứng từ, là mã lưu trữ giấy chứng nhận nghỉ việc hưởng BHXH tại cơ sở KBCB.
        ///</summary>
        [StringLength(200)]
        public string SO_CT { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số định danh chứng từ (Giấy chứng nhận nghỉ việc hưởng BHXH) của mỗi đợt điều trị theo quy định của cơ sở KBCB.
        ///</summary>
        [StringLength(200)]
        public string SO_SERI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số chứng từ phục vụ việc quản lý nội bộ của cơ sở KBCB theo Phụ lục 07 Thông tư 18/2022/TT-BYT của Bộ trưởng Bộ Y tế.
        ///</summary>
        [StringLength(200)]
        public string SO_KCB { get; set; } = string.Empty;

        ///<summary>
        /// Ghi tên đơn vị của người hưởng BHXH.
        ///</summary>
        [StringLength(1024)]
        public string DON_VI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã số BHXH của người bệnh.
        ///</summary>
        [StringLength(10)]
        public string MA_BHXH { get; set; } = string.Empty;

        public string? MA_THE_BHYT { get; set; }

        ///<summary>
        /// Đối với việc ghi chẩn đoán ra viện để phục vụ việc tạo lập giấy chứng nhận nghỉ việc hưởng bảo hiểm xã hội thì thực hiện theo hướng dẫn tại Thông tư số 18/2022/TT-BYT của Bộ trưởng Bộ Y tế.
        ///</summary>
        public string CHAN_DOAN_RV { get; set; } = string.Empty;

        ///<summary>
        /// Ghi phương pháp điều trị cho người bệnh như nội khoa, ngoại khoa, xạ trị, hoá trị hoặc xạ trị + nội khoa
        ///</summary>
        public string PP_DIEUTRI { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã "1" là đình chỉ thai nghén, mã "0" là không đình chỉ thai nghén.
        ///</summary>
        public int MA_DINH_CHI_THAI { get; set; }

        public string? NGUYENNHAN_DINHCHI { get; set; }

        public int? TUOI_THAI { get; set; }

        ///<summary>
        /// Ghi số ngày nghỉ căn cứ vào tình trạng sức khỏe của người bệnh.
        ///</summary>
        public int SO_NGAY_NGHI { get; set; }

        ///<summary>
        /// Ghi ngày bắt đầu hưởng chế độ, theo định dạng yyyymmdd và phải trùng khớp với ngày người bệnh đến khám.
        ///</summary>
        [StringLength(8)]
        public string TU_NGAY { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày kết thúc hưởng chế độ, theo định dạng yyyymmdd
        ///</summary>
        [StringLength(8)]
        public string DEN_NGAY { get; set; } = string.Empty;

        [StringLength(255)]
        public string? HO_TEN_CHA { get; set; }

        [StringLength(255)]
        public string? HO_TEN_ME { get; set; }

        ///<summary>
        /// Ghi mã của người đứng đầu cơ sở KBCB hoặc người được người đứng đầu cơ sở KBCB ủy quyền được ký để cấp giấy chứng nhận nghỉ hưởng BHXH (mã hoá theo số GPHN).
        ///</summary>
        [StringLength(255)]
        public string MA_TTDV { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã của Trưởng khoa hoặc Trưởng phòng hoặc Phó trưởng khoa hoặc Phó trưởng phòng hoặc Bác sỹ hành nghề KBCB ký tên theo quy định của Thủ trưởng cơ sở KBCB (mã hóa theo số GPHN).
        ///</summary>
        [StringLength(255)]
        public string MA_BS { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày cấp chứng từ (Giấy chứng nhận nghỉ việc hưởng BHXH), theo định dạng yyyymmdd và phải trùng với ngày người lao động đến khám bệnh. Trường hợp đợt khám bệnh kéo dài từ 2 ngày trở lên thì ngày, tháng, năm cấp phải trùng với ngày cuối cùng của đợt người lao động đến khám bệnh và cần được chỉ định nghỉ ngoại trú.
        ///</summary>
        [StringLength(8)]
        public string NGAY_CT { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã thẻ BHYT tạm thời của trẻ em sinh ra hoặc của người hiến tạng nhưng chưa được cơ quan BHXH cấp thẻ BHYT. Cơ sở KBCB sử dụng chức năng “Thông tuyến khám chữa bệnh\Tra cứu thẻ tạm của trẻ em hoặc của người hiến tạng” trên Cổng tiếp nhận dữ liệu Hệ thống thông tin giám định BHYT của BHXH Việt Nam để tra cứu mã thẻ BHYT tạm thời.
        ///</summary>
        [StringLength(15)]
        public string MA_THE_TAM { get; set; } = string.Empty;

        ///<summary>
        /// Các cơ sở KBCB sử dụng chuỗi CT07 để xác định đây là Giấy nghỉ việc hưởng bảo hiểm xã hội. Mẫu số mặc định để trống không điền thì hệ thống tự điền CT07.
        ///</summary>
        [StringLength(5)]
        public string MAU_SO { get; set; } = "CT07";

        public string? DU_PHONG { get; set; }
    }
}
