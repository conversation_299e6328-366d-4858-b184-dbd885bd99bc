namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// API Key để gọi API từ bên ngoài
    /// </summary>
    public class ApiKey : Entity<Guid>
    {
        /// <summary>
        /// Hash của API Key (thay vì lưu raw key)
        /// </summary>
        public string KeyHash { get; set; } = string.Empty;
        
        /// <summary>
        /// Mã bệnh viện sở hữu API Key
        /// </summary>
        public Guid HospitalId { get; set; }
        
        /// <summary>
        /// Mô tả API Key (phòng CNTT đặt tên)
        /// </summary>
        public string Description { get; set; } = string.Empty;
        
        /// <summary>
        /// Thời gian cấp
        /// </summary>
        public DateTime IssuedAt { get; set; }
        
        /// <summary>
        /// Thời gian hết hạn
        /// </summary>
        public DateTime ExpiresAt { get; set; }
        
        /// <summary>
        /// Trạng thái hoạt động của API Key
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// Bệnh viện sở hữu API Key
        /// </summary>
        public virtual Hospital Hospital { get; set; } = null!;
    }
}
