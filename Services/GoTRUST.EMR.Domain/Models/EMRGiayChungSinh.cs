
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIEU_GIAYCHUNGSINH")]
    public class EMRGiayChungSinhWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRGiayChungSinh? CHI_TIEU_GIAYCHUNGSINH { get; set; }
    }
    public class EMRGiayChungSinh : Entity<Guid>
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất (dùng để liên kết giữa Bảng chỉ tiêu tổng hợp khám bệnh, chữa bệnh (bảng XML 1) và các bảng còn lại ban hành kèm theo Quyết định này trong một lần khám bệnh, chữa bệnh (PRIMARY KEY)).
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        [StringLength(10)]
        public string? MA_BHXH_NND { get; set; }

        [StringLength(15)]
        public string? MA_THE_NND { get; set; }

        ///<summary>
        /// Ghi họ và tên của mẹ hoặc của người nuôi dưỡng.
        ///</summary>
        [StringLength(255)]
        public string HO_TEN_NND { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày sinh của mẹ hoặc người nuôi dưỡng, định dạng yyyymmdd
        ///</summary>
        [StringLength(8)]
        public string NGAYSINH_NND { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã dân tộc của mẹ hoặc người nuôi dưỡng theo Danh mục các dân tộc Việt Nam.
        ///</summary>
        [StringLength(2)]
        public string MA_DANTOC_NND { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số căn cước công dân hoặc số định danh cá nhân trên thẻ căn cước của mẹ hoặc người nuôi dưỡng.
        ///</summary>
        public string SO_CCCD_NND { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày cấp chứng minh nhân dân hoặc căn cước công dân hoặc hộ chiếu của mẹ hoặc người nuôi dưỡng, định dạng yyyymmdd
        ///</summary>
        [StringLength(8)]
        public string NGAYCAP_CCCD_NND { get; set; } = string.Empty;

        ///<summary>
        /// Ghi nơi cấp chứng minh nhân dân hoặc căn cước công dân hoặc hộ chiếu của mẹ hoặc người nuôi dưỡng.
        ///</summary>
        [StringLength(1024)]
        public string NOICAP_CCCD_NND { get; set; } = string.Empty;

        ///<summary>
        /// Ghi địa chỉ nơi cư trú hiện tại của mẹ hoặc người nuôi dưỡng.
        ///</summary>
        [StringLength(1024)]
        public string NOI_CU_TRU_NND { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã quốc tịch của mẹ hoặc người nuôi dưỡng theo quy định tại Phụ lục 2 Thông tư số 07/2016/TT-BCA.
        ///</summary>
        [StringLength(3)]
        public string MA_QUOCTICH { get; set; } = string.Empty;

        ///<summary>
        /// Mã đơn vị hành chính cấp tỉnh nơi cư trú hiện tại của mẹ hoặc người nuôi dưỡng (thường trú hoặc tạm trú).
        ///</summary>
        [StringLength(3)]
        public string MATINH_CU_TRU { get; set; } = string.Empty;

        ///<summary>
        /// Mã đơn vị hành chính cấp huyện nơi cư trú hiện tại của mẹ hoặc người nuôi dưỡng (thường trú hoặc tạm trú).
        ///</summary>
        [StringLength(3)]
        public string MAHUYEN_CU_TRU { get; set; } = string.Empty;

        ///<summary>
        /// Mã đơn vị hành chính cấp xã nơi cư trú hiện tại của mẹ hoặc người nuôi dưỡng (thường trú hoặc tạm trú).
        ///</summary>
        [StringLength(5)]
        public string MAXA_CU_TRU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi họ và tên cha (bố) của trẻ được cấp giấy chứng sinh.
        ///</summary>
        [StringLength(255)]
        public string HO_TEN_CHA { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã thẻ BHYT tạm thời của người con.
        ///</summary>
        [StringLength(15)]
        public string MA_THE_TAM { get; set; } = string.Empty;

        [StringLength(255)]
        public string? HO_TEN_CON { get; set; }

        ///<summary>
        /// Ghi giới tính con, trong đó: Mã "1": Nam; Mã "2": Nữ; Mã "3": Chưa xác định.
        ///</summary>
        public int GIOI_TINH_CON { get; set; }

        ///<summary>
        /// Ghi số lượng con trong lần sinh này.
        ///</summary>
        public int SO_CON { get; set; }

        ///<summary>
        /// Ghi số lần sinh con (tính cả lần sinh này).
        ///</summary>
        public int LAN_SINH { get; set; }

        ///<summary>
        /// Ghi số con hiện đang sống (tính cả trẻ sinh ra lần này).
        ///</summary>
        public int SO_CON_SONG { get; set; }

        ///<summary>
        /// Ghi số cân nặng của con, tính theo gram (ký hiệu là: g) (ví dụ: 3.6 kg = 3600g).
        ///</summary>
        public int CAN_NANG_CON { get; set; }

        ///<summary>
        /// Ghi ngày sinh con theo định dạng yyyymmddHHMM.
        ///</summary>
        [StringLength(12)]
        public string NGAY_SINH_CON { get; set; } = string.Empty;

        ///<summary>
        /// Ghi địa chỉ nơi con được sinh ra.
        ///</summary>
        [StringLength(1024)]
        public string NOI_SINH_CON { get; set; } = string.Empty;

        ///<summary>
        /// Ghi rõ tình trạng của trẻ tại thời điểm làm Giấy chứng sinh: khỏe mạnh, yếu, dị tật hoặc các biểu hiện liên quan đến sức khỏe khác (nếu có).
        ///</summary>
        public string TINH_TRANG_CON { get; set; } = string.Empty;

        ///<summary>
        /// Ghi: Mã "1": sinh con phải phẫu thuật; Mã "0": sinh con không phải phẫu thuật.
        ///</summary>
        public int SINHCON_PHAUTHUAT { get; set; }

        ///<summary>
        /// Ghi: Mã "1": sinh con dưới 32 tuần tuổi; Mã "0" là không sinh con dưới 32 tuần tuổi.
        ///</summary>
        public int SINHCON_DUOI32TUAN { get; set; }

        public string? GHI_CHU { get; set; }

        ///<summary>
        /// Ghi họ và tên người đỡ đẻ.
        ///</summary>
        [StringLength(255)]
        public string NGUOI_DO_DE { get; set; } = string.Empty;

        ///<summary>
        /// Ghi họ và tên người ghi phiếu.
        ///</summary>
        [StringLength(255)]
        public string NGUOI_GHI_PHIEU { get; set; } = string.Empty;

        ///<summary>
        /// Ghi ngày cấp chứng từ (Giấy chứng sinh), định dạng yyyymmdd, ghi theo ngày dương lịch.
        ///</summary>
        [StringLength(8)]
        public string NGAY_CT { get; set; } = string.Empty;

        ///<summary>
        /// Ghi số của chứng từ (Giấy chứng sinh) tại cơ sở KBCB.
        ///</summary>
        [StringLength(200)]
        public string SO { get; set; } = string.Empty;

        ///<summary>
        /// Ghi quyển số của chứng từ (Giấy chứng sinh) tại cơ sở KBCB.
        ///</summary>
        [StringLength(200)]
        public string QUYEN_SO { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã của người đứng đầu cơ sở KBCB hoặc người được người đứng đầu cơ sở KBCB ủy quyền được ký để cấp giấy chứng sinh (mã hoá theo số GPHN).
        ///</summary>
        [StringLength(255)]
        public string MA_TTDV { get; set; } = string.Empty;

        public string? DU_PHONG { get; set; }
    }
}
