namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Nhân viên y tế
    /// </summary>
    public class Employee : Aggregate<Guid>
    {
        /// <summary>
        /// Mã bệnh viện
        /// </summary>
        public Guid HospitalId { get; set; }

        /// <summary>
        /// Mã người dùng (nếu nhân viên là người dùng hệ thống)
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// Mã cán bộ
        /// </summary>
        public string EmployeeCode { get; set; } = string.Empty;

        /// <summary>
        /// Họ và tên
        /// </summary>
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// Ngày sinh
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// Giới tính: Nam, Nữ
        /// </summary>
        public string? Gender { get; set; }

        /// <summary>
        /// <PERSON><PERSON> cước công dân
        /// </summary>
        public string? CitizenId { get; set; }

        /// <summary>
        /// Ngày cấp CCCD
        /// </summary>
        public DateTime? CitizenIdIssueDate { get; set; }

        /// <summary>
        /// Nơi cấp CCCD
        /// </summary>
        public string? CitizenIdIssuePlace { get; set; }

        /// <summary>
        /// Email
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Tình trạng hôn nhân, nối với bảng TinhTrangHonNhan
        /// </summary>
        public string? MaritalStatusId { get; set; }

        /// <summary>
        /// Số điện thoại
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Quốc tịch, nối với bảng QuocTich
        /// </summary>
        public string? ReligionId { get; set; }

        /// <summary>
        /// Dân tộc, nối với bảng DanToc
        /// </summary>
        public string? EthnicityId { get; set; }

        /// <summary>
        /// Địa chỉ thường trú
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// Địa chỉ tạm trú
        /// </summary>
        public string? TemporaryAddress { get; set; }

        /// <summary>
        /// Mã khoa công tác
        /// </summary>
        public Guid? DepartmentId { get; set; }

        /// <summary>
        /// Số hiệu cán bộ
        /// </summary>
        public string? EmployeeNumber { get; set; }

        /// <summary>
        /// Phòng ban
        /// </summary>
        public string? OfficeUnit { get; set; }

        /// <summary>
        /// Vị trí làm việc
        /// </summary>
        public string? Position { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string? Note { get; set; }

        /// <summary>
        /// Bệnh viện
        /// </summary>
        public virtual Hospital Hospital { get; set; } = null!;

        /// <summary>
        /// Người dùng (nếu có)
        /// </summary>
        public virtual User? User { get; set; }

        /// <summary>
        /// Khoa công tác
        /// </summary>
        public virtual Department? Department { get; set; }
    }
}
