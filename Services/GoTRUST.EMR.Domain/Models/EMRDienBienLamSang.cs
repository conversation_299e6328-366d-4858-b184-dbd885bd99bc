
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIET_DIEN_BIEN_BENH")]
    public class EMRDienBienLamSangWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRDienBienLamSang? CHI_TIET_DIEN_BIEN_BENH { get; set; }
    }
    public class EMRDienBienLamSang
    {
        ///<summary>
        /// Là mã đợt điều trị duy nhất (dùng để liên kết giữa Bảng chỉ tiêu tổng hợp khám bệnh, chữa bệnh (bảng XML 1) và các bảng còn lại ban hành kèm theo Quyết định này trong một lần khám bệnh, chữa bệnh (PRIMARY KEY)).
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// <PERSON><PERSON> thứ tự tăng từ 1 đến hết trong một lần gửi dữ liệu.
        ///</summary>
        public int STT { get; set; }

        ///<summary>
        /// Ghi diễn biến lâm sàng của người bệnh trong lần khám và/hoặc ghi nội dung chăm sóc của nhân viên y tế.
        ///</summary>
        public string DIEN_BIEN_LS { get; set; } = string.Empty;

        ///<summary>
        /// Ghi thời điểm diễn biến lâm sàng, gồm 12 ký tự, theo cấu trúc: yyyymmddHHMM. Ví dụ: 201503311520
        ///</summary>
        [StringLength(12)]
        public string THOI_DIEM_DBLS { get; set; } = string.Empty;

        ///<summary>
        /// Ghi mã của nhân viên y tế thực hiện ghi chép diễn biến lâm sàng (mã hóa theo số GPHN).
        ///</summary>
        [StringLength(255)]
        public string NGUOI_THUC_HIEN { get; set; } = string.Empty;

        public string? GIAI_DOAN_BENH { get; set; }

        public string? HOI_CHAN { get; set; }

        public string? PHAU_THUAT { get; set; }

        public string? DU_PHONG { get; set; }
    }
}
