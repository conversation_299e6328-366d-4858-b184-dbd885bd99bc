﻿
namespace GoTRUST.EMR.Domain.Models
{
    public class ForgotPasswordSession : Entity<Guid>
    {
        /// <summary>
        /// otp expired time
        /// </summary>
        public DateTime ExpiresAt { get; set; }
        /// <summary>
        /// Update this field when user verify otp
        /// </summary>
        public DateTime? VerifiedAt { get; set; }

        public Guid UserId { get; set; }
        public User User { get; set; } = null!;
    }
}
