namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Medical record information
    /// </summary>
    public class MedicalRecord : Aggregate<Guid>
    {
        /// <summary>
        /// Hospital ID
        /// </summary>
        public Guid HospitalId { get; set; }

        /// <summary>
        /// Admission code (from HIS)
        /// </summary>
        public string AdmissionCode { get; set; } = string.Empty;

        /// <summary>
        /// Medical record code
        /// </summary>
        public string RecordCode { get; set; } = string.Empty;
        /// <summary>
        /// Medical record name
        /// </summary>
        public string RecordName { get; set; } = string.Empty;

        /// <summary>
        /// Storage number
        /// </summary>
        public string StorageNumber { get; set; } = string.Empty;

        /// <summary>
        /// Patient ID
        /// </summary>
        public Guid PatientId { get; set; }

        /// <summary>
        /// Patient code
        /// </summary>
        public string PatientCode { get; set; } = string.Empty;

        /// <summary>
        /// Admission date
        /// </summary>
        public DateTime AdmissionDate { get; set; }

        /// <summary>
        /// Discharge date
        /// </summary>
        public DateTime? DischargeDate { get; set; }

        /// <summary>
        /// Total film count (X-ray, RMI, CT, External films)
        /// </summary>
        public int FilmCount { get; set; }

        /// <summary>
        /// Has healthcare insurance
        /// </summary>
        public bool HasBHYT { get; set; }

        /// <summary>
        /// Treatment department ID
        /// </summary>
        public Guid DepartmentId { get; set; }

        /// <summary>
        /// Notes
        /// </summary>
        public string Note { get; set; } = string.Empty;

        /// <summary>
        /// Total number of uploaded files
        /// </summary>
        public int TotalFilesUploaded { get; set; }

        /// <summary>
        /// Status (Pending reception, Received, Processing storage, Returned to department, Returned to department, Internal borrowing, External borrowing)
        /// </summary>
        public MedicalRecordStatus Status { get; set; } = MedicalRecordStatus.Pending;

        /// <summary>
        /// Number of years stored up to present
        /// </summary>
        public int StorageYears { get; set; }

        /// <summary>
        /// Encounter type (Nội Trú, Ngoại Trú)
        /// </summary>
        public string EncounterType { get; set; } = string.Empty;

        /// <summary>
        /// Navigation property to the Hospital
        /// </summary>
        public virtual Hospital Hospital { get; set; } = null!;

        /// <summary>
        /// Navigation property to the Patient
        /// </summary>
        public virtual Patient Patient { get; set; } = null!;

        /// <summary>
        /// Navigation property to the Department
        /// </summary>
        public virtual Department Department { get; set; } = null!;

        /// <summary>
        /// Collection of film details associated with the medical record
        /// </summary>
        public virtual FilmDetail? FilmDetails { get; set; }

        /// <summary>
        /// Collection of uploaded files associated with the medical record
        /// </summary>
        public virtual ICollection<MedicalRecordFile> Files { get; set; } = [];

        /// <summary>
        /// Collection of medical record instance data
        /// </summary>
        public virtual ICollection<MedicalRecordInstanceData> InstanceData { get; set; } = [];

        /// <summary>
        /// Collection of status history records for the medical record
        /// </summary>
        public virtual ICollection<MedicalRecordStatusHistory> StatusHistory { get; set; } = [];
    }
}
