namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Patient information
    /// </summary>
    public class Patient : Aggregate<Guid>
    {
        /// <summary>
        /// Hospital ID
        /// </summary>
        public Guid HospitalId { get; set; }
        
        /// <summary>
        /// User ID (if patient is a system user)
        /// </summary>
        public Guid? UserId { get; set; }
        
        /// <summary>
        /// Patient code
        /// </summary>
        public string PatientCode { get; set; } = string.Empty;
        
        /// <summary>
        /// Full name
        /// </summary>
        public string FullName { get; set; } = string.Empty;
        
        /// <summary>
        /// Nam, Nữ, Khác
        /// </summary>
        public string Sex { get; set; } = string.Empty;
        
        /// <summary>
        /// Avatar URL
        /// </summary>
        public string AvatarUrl { get; set; } = string.Empty;
        
        /// <summary>
        /// Date of birth
        /// </summary>
        public DateTime DateOfBirth { get; set; }
        
        /// <summary>
        /// Ethnic group
        /// </summary>
        public string EthnicGroup { get; set; } = string.Empty;
        
        /// <summary>
        /// Citizen ID
        /// </summary>
        public string CitizenId { get; set; } = string.Empty;

        /// <summary>
        /// Health insurance no
        /// </summary>
        public string HealthInsuranceNo { get; set; } = string.Empty;
        
        /// <summary>
        /// Citizen ID issue date
        /// </summary>
        public DateTime? CitizenIdIssueDate { get; set; }
        
        /// <summary>
        /// Citizen ID issue place
        /// </summary>
        public string CitizenIdIssuePlace { get; set; } = string.Empty;
        
        /// <summary>
        /// Email
        /// </summary>
        public string Email { get; set; } = string.Empty;
        
        /// <summary>
        /// Phone number
        /// </summary>
        public string PhoneNumber { get; set; } = string.Empty;
        
        /// <summary>
        /// Address
        /// </summary>
        public string Address { get; set; } = string.Empty;
        
        /// <summary>
        /// Occupation
        /// </summary>
        public string Occupation { get; set; } = string.Empty;
        
        /// <summary>
        /// Marital status
        /// </summary>
        public string MaritalStatus { get; set; } = string.Empty;
        
        /// <summary>
        /// Workplace
        /// </summary>
        public string Workplace { get; set; } = string.Empty;
        
        /// <summary>
        /// Số lần khám
        /// </summary>
        public int NumberOfMedicalExaminations { get; set; } = 0;
        
        /// <summary>
        /// Số lần tái khám
        /// </summary>
        public int NumberOfReExaminations { get; set; } = 0;
        
        /// <summary>
        /// Navigation property to the Hospital
        /// </summary>
        public virtual Hospital Hospital { get; set; } = null!;
        
        /// <summary>
        /// Health metrics for this patient
        /// </summary>
        public virtual ICollection<PatientHealthMetric> HealthMetrics { get; set; } = [];
        
        /// <summary>
        /// Medical history for this patient
        /// </summary>
        public virtual ICollection<PatientMedicalHistory> MedicalHistories { get; set; } = [];
        
        /// <summary>
        /// Medical records for this patient
        /// </summary>
        public virtual ICollection<MedicalRecord> MedicalRecords { get; set; } = [];
    }
}
