
using Marten.Schema;

namespace GoTRUST.EMR.Domain.Models
{
    [DocumentAlias("CHI_TIEU_GIAYCHUYENTUYEN")]
    public class EMRGiayChuyenTuyenBHYTWrapper : Entity<Guid>
    {
        public string HospitalCode { get; set; } = string.Empty;
        public EMRGiayChuyenTuyenBHYT? CHI_TIEU_GIAYCHUYENTUYEN { get; set; }
    }
    public class EMRGiayChuyenTuyenBHYT
    {
        ///<summary>
        /// Mã đợt điều trị duy nhất.
        ///</summary>
        [StringLength(100)]
        public string MA_LK { get; set; } = string.Empty;

        ///<summary>
        /// S<PERSON> hồ sơ giấy chuyển tuyến.
        ///</summary>
        [StringLength(50)]
        public string SO_HOSO { get; set; } = string.Empty;

        ///<summary>
        /// Số của sổ chuyển tuyến.
        ///</summary>
        [StringLength(50)]
        public string SO_CHUYENTUYEN { get; set; } = string.Empty;

        ///<summary>
        /// Số giấy chuyển tuyến.
        ///</summary>
        [StringLength(50)]
        public string GIAY_CHUYEN_TUYEN { get; set; } = string.Empty;

        ///<summary>
        /// Mã cơ sở KBCB nơi cấp giấy chuyển tuyến.
        ///</summary>
        [StringLength(5)]
        public string MA_CSKCB { get; set; } = string.Empty;

        ///<summary>
        /// Mã nơi đi.
        ///</summary>
        [StringLength(100)]
        public string MA_NOI_DI { get; set; } = string.Empty;

        ///<summary>
        /// Mã nơi đến.
        ///</summary>
        [StringLength(5)]
        public string MA_NOI_DEN { get; set; } = string.Empty;

        ///<summary>
        /// Họ và tên người bệnh.
        ///</summary>
        [StringLength(255)]
        public string HO_TEN { get; set; } = string.Empty;

        ///<summary>
        /// Ngày sinh.
        ///</summary>
        [StringLength(12)]
        public string NGAY_SINH { get; set; } = string.Empty;

        ///<summary>
        /// Giới tính.
        ///</summary>
        public int GIOI_TINH { get; set; }

        ///<summary>
        /// Mã quốc tịch.
        ///</summary>
        [StringLength(3)]
        public string MA_QUOCTICH { get; set; } = string.Empty;

        ///<summary>
        /// Mã dân tộc.
        ///</summary>
        [StringLength(2)]
        public string MA_DANTOC { get; set; } = string.Empty;

        ///<summary>
        /// Mã nghề nghiệp.
        ///</summary>
        [StringLength(5)]
        public string MA_NGHE_NGHIEP { get; set; } = string.Empty;

        ///<summary>
        /// Địa chỉ.
        ///</summary>
        [StringLength(1024)]
        public string DIA_CHI { get; set; } = string.Empty;

        ///<summary>
        /// Mã thẻ BHYT.
        ///</summary>
        public string MA_THE_BHYT { get; set; } = string.Empty;

        ///<summary>
        /// Giá trị thẻ đến.
        ///</summary>
        public string GT_THE_DEN { get; set; } = string.Empty;

        ///<summary>
        /// Ngày vào.
        ///</summary>
        [StringLength(100)]
        public string NGAY_VAO { get; set; } = string.Empty;

        ///<summary>
        /// Ngày vào nội trú.
        ///</summary>
        [StringLength(12)]
        public string NGAY_VAO_NOI_TRU { get; set; } = string.Empty;

        ///<summary>
        /// Ngày ra.
        ///</summary>
        [StringLength(100)]
        public string NGAY_RA { get; set; } = string.Empty;

        ///<summary>
        /// Dấu hiệu lâm sàng.
        ///</summary>
        public string DAU_HIEU_LS { get; set; } = string.Empty;

        ///<summary>
        /// Chẩn đoán ra viện.
        ///</summary>
        public string CHAN_DOAN_RV { get; set; } = string.Empty;

        ///<summary>
        /// Quá trình bệnh lý.
        ///</summary>
        public string QT_BENHLY { get; set; } = string.Empty;

        ///<summary>
        /// Tóm tắt kết quả.
        ///</summary>
        public string TOMTAT_KQ { get; set; } = string.Empty;

        ///<summary>
        /// Phương pháp điều trị.
        ///</summary>
        public string PP_DIEUTRI { get; set; } = string.Empty;

        ///<summary>
        /// Mã bệnh chính.
        ///</summary>
        [StringLength(7)]
        public string MA_BENH_CHINH { get; set; } = string.Empty;

        ///<summary>
        /// Mã bệnh kèm theo.
        ///</summary>
        [StringLength(100)]
        public string MA_BENH_KT { get; set; } = string.Empty;

        ///<summary>
        /// Mã bệnh YHCT.
        ///</summary>
        [StringLength(255)]
        public string MA_BENH_YHCT { get; set; } = string.Empty;

        ///<summary>
        /// Tên dịch vụ kỹ thuật.
        ///</summary>
        [StringLength(1024)]
        public string TEN_DICH_VU { get; set; } = string.Empty;

        ///<summary>
        /// Tên thuốc.
        ///</summary>
        [StringLength(1024)]
        public string TEN_THUOC { get; set; } = string.Empty;

        ///<summary>
        /// Tình trạng chuyển tuyến
        ///</summary>
        public string TINH_TRANG_CT { get; set; } = string.Empty;

        ///<summary>
        /// Mã loại ra viện.
        ///</summary>
        public int MA_LOAI_RV { get; set; }

        ///<summary>
        /// Mã lý do chuyển tuyến.
        ///</summary>
        public int MA_LYDO_CT { get; set; }

        ///<summary>
        /// Hướng điều trị.
        ///</summary>
        public string HUONG_DIEU_TRI { get; set; } = string.Empty;

        ///<summary>
        /// Phương tiện vận chuyển.
        ///</summary>
        [StringLength(255)]
        public string PHUONGTIEN_VC { get; set; } = string.Empty;

        ///<summary>
        /// Họ tên người hộ tống.
        ///</summary>
        [StringLength(255)]
        public string HOTEN_NGUOI_HT { get; set; } = string.Empty;

        ///<summary>
        /// Chức danh người hộ tống.
        ///</summary>
        [StringLength(255)]
        public string CHUCDANH_NGUOI_HT { get; set; } = string.Empty;

        ///<summary>
        /// Mã bác sĩ.
        ///</summary>
        [StringLength(255)]
        public string MA_BAC_SI { get; set; } = string.Empty;

        ///<summary>
        /// Mã số định danh y tế.
        ///</summary>
        [StringLength(255)]
        public string MA_TTDV { get; set; } = string.Empty;

        public string? DU_PHONG { get; set; }
    }
}
