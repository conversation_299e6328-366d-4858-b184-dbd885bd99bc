namespace GoTRUST.EMR.Domain.Constants
{
    public static class HospitalConfigConstants
    {
        /// <summary>
        /// Khóa tài khoản nếu đăng nhập sai vượt quá số lần cho phép
        /// </summary>
        public const string LoginFailureLockThreshold = "LoginFailureLockThreshold";
        public const int LoginFailureLockThresholdDefaultValue = 5;
        /// <summary>
        /// Bắt buộc xác thực 2 lớp
        /// </summary>
        public const string RequireTwoFactorAuth = "RequireTwoFactorAuth";
        public const bool RequireTwoFactorAuthDefaultValue = true;
        /// <summary>
        /// Thông báo hoạt động người dùng bất thường
        /// </summary>
        public const string NotifyUserUnusualActivity = "NotifyUserUnusualActivity";
        public const bool NotifyUserUnusualActivityDefaultValue = true;
        /// <summary>
        /// M<PERSON>t khẩu phải chứa ít nhất 1 ký tự thường
        /// </summary>
        public const string PasswordRequireLowercase = "PasswordRequireLowercase";
        public const bool PasswordRequireLowercaseDefaultValue = true;
        /// <summary>
        /// Email admin nhận thông báo
        /// </summary>
        public const string AdminNotificationEmail = "AdminNotificationEmail";
        public const string AdminNotificationEmailDefaultValue = "";
        /// <summary>
        /// Tự động gửi link đổi mật khẩu định kỳ (ngày)
        /// </summary>
        public const string ForcePasswordChangeDays = "ForcePasswordChangeDays";
        public const int ForcePasswordChangeDaysDefaultValue = 90;
        /// <summary>
        /// Mật khẩu phải chứa ít nhất 1 ký tự in hoa
        /// </summary>
        public const string PasswordRequireUppercase = "PasswordRequireUppercase";
        public const bool PasswordRequireUppercaseDefaultValue = true;
        /// <summary>
        /// Ngôn ngữ hệ thống
        /// </summary>
        public const string SystemLanguage = "SystemLanguage";
        public const string SystemLanguageDefaultValue = "vi";
        /// <summary>
        /// Thông báo hoạt động hệ thống bất thường
        /// </summary>
        public const string NotifySystemUnusualActivity = "NotifySystemUnusualActivity";
        public const bool NotifySystemUnusualActivityDefaultValue = true;
        /// <summary>
        /// Mật khẩu phải chứa ít nhất 1 ký tự đặc biệt
        /// </summary>
        public const string PasswordRequireSpecialChar = "PasswordRequireSpecialChar";
        public const bool PasswordRequireSpecialCharDefaultValue = true;
        /// <summary>
        /// Thời gian hết phiên đăng nhập (phút)
        /// </summary>
        public const string SessionTimeoutMinutes = "SessionTimeoutMinutes";
        public const int SessionTimeoutMinutesDefaultValue = 30;
        /// <summary>
        /// Địa chỉ đơn vị
        /// </summary>
        public const string HospitalAddress = "HospitalAddress";
        public const string HospitalAddressDefaultValue = "";
        /// <summary>
        /// Tên đơn vị y tế
        /// </summary>
        public const string HospitalName = "HospitalName";
        public const string HospitalNameDefaultValue = "";
        /// <summary>
        /// Độ dài mật khẩu tối thiểu
        /// </summary>
        public const string PasswordMinLength = "PasswordMinLength";
        public const int PasswordMinLengthDefaultValue = 8;
        /// <summary>
        /// Mật khẩu phải chứa ít nhất 1 chữ số
        /// </summary>
        public const string PasswordRequireDigit = "PasswordRequireDigit";
        public const bool PasswordRequireDigitDefaultValue = true;
        /// <summary>
        /// Xử lý tự động thu hồi quyền mượn khi đến thời gian, theo giờ
        /// </summary>
        public const string BorrowRequestExpireTimeHours = "BorrowRequestExpireTimeHours";
        public const int BorrowRequestExpireTimeHoursDefaultValue = 24;
        /// <summary>
        /// URL logo bệnh viện 
        /// </summary>
        public const string LogoUrl = "LogoUrl";
        public const string LogoUrlDefaultValue = "";
        /// <summary>
        /// Email quản trị viên để gửi thông báo 
        /// </summary>
        public const string AdminEmail = "AdminEmail";
        public const string AdminEmailDefaultValue = "";
        /// <summary>
        ///  Các loại thông báo sẽ nhận (Json hoặc List of strings)
        /// </summary>
        public const string NotificationTypes = "NotificationTypes";
        public static readonly string[] NotificationTypesDefaultValue = [];

        /// <summary>
        /// Tên đăng nhập SoftDream
        /// </summary>
        public const string SoftDreamUsername = "SoftDreamUsername";
        public const string SoftDreamUsernameDefaultValue = "";

        /// <summary>
        /// Mật khẩu đăng nhập SoftDream
        /// </summary>
        public const string SoftDreamPassword = "SoftDreamPassword";
        public const string SoftDreamPasswordDefaultValue = "";

        /// <summary>
        /// Số lượng thời gian tự động thu hồi bệnh án
        /// </summary>
        public const string AutoRevokeValue = "AutoRevokeValue";
        public const int AutoRevokeValueDefaultValue = 15;

        /// <summary>
        /// Đơn vị thời gian tự động thu hồi bệnh án (Phút, Giờ, Ngày)
        /// </summary>
        public const string AutoRevokeUnit = "AutoRevokeUnit";
        public const string AutoRevokeUnitDefaultValue = "Phút";
    }
}