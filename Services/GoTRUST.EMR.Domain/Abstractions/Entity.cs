﻿namespace GoTRUST.EMR.Domain.Abstractions;
public abstract class Entity<T> : IEntity<T>
{
    public T Id { get; set; } = default!; // Default value should be set by the derived class or during instantiation
    public bool IsDeleted { get; set; } = false; // Default: false
    public DateTime? CreatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
}
