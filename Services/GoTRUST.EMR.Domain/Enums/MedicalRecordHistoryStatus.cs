namespace GoTRUST.EMR.Domain.Enums
{
    public enum MedicalRecordHistoryStatus
    {
        Draft = 0,      // Bản nháp
        Pending = 1,      // Ch<PERSON> xử lý
        Approved = 2,     // Đ<PERSON> phê duyệt
        Rejected = 3,     // Đã từ chối
        ReadBorrowed = 4,      // Đã mượn xem
        UpdateBorrowed = 5,      // Đã mượn sửa
        RejectBorrowed = 6,      // Đã từ chối mượn
        Returned = 7,     // Đã trả
        Processing = 8,     // Đang đồng bộ
        Removed = 9     // Đã xóa
    }
}