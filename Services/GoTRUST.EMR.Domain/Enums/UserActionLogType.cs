﻿namespace GoTRUST.EMR.Domain.Enums
{
    public enum UserActionLogType
    {
        Authenticate = 0,
        Create = 1,
        Update = 2,
        Delete = 3,
        Approve = 4,
        Reject = 5,
        Export = 6,
        Import = 7,
        Revoke = 8 // Added for borrow request revocation
    }
    public static class UserActionLogTypeNames
    {
        public static readonly Dictionary<UserActionLogType, string> VietnameseNames = new()
        {
            { UserActionLogType.Authenticate, "Xác thực" },
            { UserActionLogType.Create, "Tạo mới" },
            { UserActionLogType.Update, "Cập nhật" },
            { UserActionLogType.Delete, "Xoá" },
            { UserActionLogType.Approve, "Phê duyệt" },
            { UserActionLogType.Reject, "Từ chối" },
            { UserActionLogType.Export, "Xuất" },
            { UserActionLogType.Import, "Nhập" },
        };
    }
}