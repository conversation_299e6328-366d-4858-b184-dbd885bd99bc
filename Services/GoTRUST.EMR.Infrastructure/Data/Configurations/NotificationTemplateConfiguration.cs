﻿using GoTRUST.EMR.Domain.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations;
public class NotificationTemplateConfiguration : IEntityTypeConfiguration<NotificationTemplate>
{
    public void Configure(EntityTypeBuilder<NotificationTemplate> builder)
    {
        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        //filter deleted records
        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.Type)
            .HasDefaultValue(NotificationTemplateType.email)
            .HasConversion<string>();

        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
        
        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
