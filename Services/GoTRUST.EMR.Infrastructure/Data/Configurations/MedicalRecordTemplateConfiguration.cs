using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class MedicalRecordTemplateConfiguration : IEntityTypeConfiguration<MedicalRecordTemplate>
    {
        public void Configure(EntityTypeBuilder<MedicalRecordTemplate> builder)
        {
            builder.HasKey(t => t.Id);

            builder.Property(t => t.Id)
                .ValueGeneratedOnAdd();

            builder.Property(t => t.TemplateName)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(t => t.TemplateType)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(50);

            builder.Property(t => t.Template<PERSON>ontent<PERSON>)
                .IsRequired();

            builder.Property(t => t.TemplateContent)
                .IsRequired();

            builder.Property(t => t.Code)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(t => t.AdmissionType)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(50);

            // Relationships
            builder.HasOne(t => t.Hospital)
                .WithMany()
                .HasForeignKey(t => t.HospitalId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(t => t.Department)
                .WithMany()
                .HasForeignKey(t => t.DepartmentId)
                .OnDelete(DeleteBehavior.Restrict);

            // Filter deleted records
            builder.HasQueryFilter(t => !t.IsDeleted);

            // Default values
            builder.Property(t => t.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();

            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
