using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations;

public class ApiKeyConfiguration : IEntityTypeConfiguration<ApiKey>
{
    public void Configure(EntityTypeBuilder<ApiKey> builder)
    {
        builder.HasKey(a => a.Id);
        builder.HasIndex(a => a.Id)
                .IsUnique();

        builder.Property(a => a.Id)
            .ValueGeneratedOnAdd();

        builder.Property(a => a.KeyHash)
            .IsRequired()
            .HasMaxLength(500);

        builder.HasIndex(a => a.KeyHash)
            .IsUnique();

        builder.Property(a => a.Description)
            .HasMaxLength(255);

        builder.Property(a => a.IssuedAt)
            .IsRequired();

        builder.Property(a => a.ExpiresAt)
            .IsRequired();

        builder.Property(a => a.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Relationship with Hospital
        builder.HasOne(a => a.Hospital)
            .WithMany()
            .HasForeignKey(a => a.HospitalId)
            .OnDelete(DeleteBehavior.Cascade);

        // Filter deleted records
        builder.HasQueryFilter(a => !a.IsDeleted);

        builder.Property(a => a.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
                
        builder.Property(h => h.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
