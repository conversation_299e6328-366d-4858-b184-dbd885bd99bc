using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class PatientMedicalHistoryConfiguration : IEntityTypeConfiguration<PatientMedicalHistory>
    {
        public void Configure(EntityTypeBuilder<PatientMedicalHistory> builder)
        {
            builder.HasKey(h => h.Id);
            builder.HasIndex(h => h.Id)
                .IsUnique();

            builder.Property(h => h.Id)
                .ValueGeneratedOnAdd();

            builder.Property(h => h.HistoryType)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(h => h.Description)
                .IsRequired()
                .HasMaxLength(1000);

            builder.Property(h => h.RecordedAt)
                .IsRequired();

            // Relationships
            builder.HasOne(h => h.Patient)
                .WithMany(p => p.MedicalHistories)
                .HasForeignKey(h => h.PatientId)
                .OnDelete(DeleteBehavior.Cascade);

            // Filter deleted records
            builder.HasQueryFilter(h => !h.Is<PERSON>eleted);

            // Default values
            builder.Property(h => h.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();
                    
            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
