using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations;

public class DigitalSignatureConfiguration : IEntityTypeConfiguration<DigitalSignature>
{
    public void Configure(EntityTypeBuilder<DigitalSignature> builder)
    {
        builder.HasKey(d => d.Id);
        builder.HasIndex(a => a.Id)
                .IsUnique();

        builder.Property(d => d.Id)
            .ValueGeneratedOnAdd();

        builder.Property(d => d.CertificateData)
            .IsRequired();

        builder.Property(d => d.IssuedAt)
            .IsRequired();

        builder.Property(d => d.ExpiresAt)
            .IsRequired();

        // Relationship with User
        builder.HasOne(d => d.User)
            .WithMany()
            .HasForeignKey(d => d.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Filter deleted records
        builder.HasQueryFilter(d => !d.IsDeleted);

        builder.Property(d => d.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();
                
        builder.Property(h => h.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
