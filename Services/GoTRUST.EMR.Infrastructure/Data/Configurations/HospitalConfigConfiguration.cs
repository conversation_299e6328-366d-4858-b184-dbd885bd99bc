using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class HospitalConfigConfiguration : IEntityTypeConfiguration<HospitalConfig>
    {
        public void Configure(EntityTypeBuilder<HospitalConfig> builder)
        {
            builder.HasKey(hc => hc.Id);
            builder.HasIndex(hc => hc.Id)
                .IsUnique();
            builder.HasIndex(hc => hc.Key);

            builder.Property(hc => hc.Id)
                .ValueGeneratedOnAdd();

            builder.Property(hc => hc.Key)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(hc => hc.Value)
                .IsRequired();

            // Relationship with Hospital
            builder.HasOne(hc => hc.Hospital)
                .WithMany()
                .HasForeignKey(hc => hc.HospitalId)
                .OnDelete(DeleteBehavior.Cascade);

            // Filter deleted records
            builder.HasQueryFilter(hc => !hc.IsDeleted);

            // Default values
            builder.Property(hc => hc.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();

            builder.Property(hc => hc.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}