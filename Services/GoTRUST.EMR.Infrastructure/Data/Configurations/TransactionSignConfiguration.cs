using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class TransactionSignConfiguration : IEntityTypeConfiguration<TransactionSign>
    {
        public void Configure(EntityTypeBuilder<TransactionSign> builder)
        {
            builder.HasKey(ts => ts.Id);
            builder.HasIndex(ts => ts.Id)
                .IsUnique();

            builder.Property(ts => ts.Id)
                .ValueGeneratedOnAdd();

            builder.Property(ts => ts.MedicalRecordFileId)
                .IsRequired();

            builder.Property(ts => ts.ExpiredAt)
                .IsRequired();

            //filter deleted records
            builder.HasQueryFilter(ts => !ts.IsDeleted);

            builder.Property(ts => ts.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();

            builder.Property(ts => ts.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}