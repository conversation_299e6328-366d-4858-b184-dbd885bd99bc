using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class SignatureCoordinateConfiguration : IEntityTypeConfiguration<SignatureCoordinate>
    {
        public void Configure(EntityTypeBuilder<SignatureCoordinate> builder)
        {
            builder.HasKey(c => c.Id);
            builder.HasIndex(c => c.Id)
                .IsUnique();

            // Property configurations
            builder.Property(sc => sc.Id)
                .ValueGeneratedOnAdd();

            builder.Property(sc => sc.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(sc => sc.CoordinateX)
                .IsRequired();

            builder.Property(sc => sc.CoordinateY)
                .IsRequired();

            builder.Property(sc => sc.PageNumber)
                .IsRequired()
                .HasDefaultValue(1);

            builder.Property(sc => sc.Width)
                .IsRequired();

            builder.Property(sc => sc.Height)
                .IsRequired();

            builder.Property(sc => sc.AllowAutoSign)
                .IsRequired()
                .HasDefaultValue(false);

            // Relationships
            builder.HasOne(sc => sc.MedicalRecordTemplate)
                .WithMany(mrt => mrt.SignatureCoordinates)
                .HasForeignKey(sc => sc.MedicalRecordTemplateId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(sc => sc.Role)
                .WithMany(r => r.SignatureCoordinates)
                .HasForeignKey(sc => sc.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            // Filter deleted records
            builder.HasQueryFilter(sc => !sc.IsDeleted);

            // Default values for audit fields
            builder.Property(sc => sc.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();

            builder.Property(sc => sc.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
