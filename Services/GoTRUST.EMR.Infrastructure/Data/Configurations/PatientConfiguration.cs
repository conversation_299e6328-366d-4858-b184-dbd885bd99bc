using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class PatientConfiguration : IEntityTypeConfiguration<Patient>
    {
        public void Configure(EntityTypeBuilder<Patient> builder)
        {
            builder.HasKey(p => p.Id);

            builder.Property(p => p.Id)
                .ValueGeneratedOnAdd();

            builder.Property(p => p.PatientCode)
                .IsRequired()
                .HasMaxLength(50);

            builder.HasIndex(p => p.PatientCode)
                .IsUnique();

            builder.Property(p => p.FullName)
                .IsRequired()
                .HasMaxLength(100);
            
            builder.Property(p => p.Sex)
                .IsRequired()
                .HasMaxLength(10);

            builder.Property(p => p.AvatarUrl)
                .HasMaxLength(500);

            builder.Property(p => p.EthnicGroup)
                .HasMaxLength(50);

            builder.Property(p => p.CitizenId)
                .HasMaxLength(50);

            builder.Property(p => p.HealthInsuranceNo)
                .HasMaxLength(50);

            builder.Property(p => p.CitizenIdIssuePlace)
                .HasMaxLength(100);

            builder.Property(p => p.Email)
                .HasMaxLength(100);

            builder.Property(p => p.PhoneNumber)
                .HasMaxLength(20);

            builder.Property(p => p.Address)
                .HasMaxLength(500);

            builder.Property(p => p.Occupation)
                .HasMaxLength(100);

            builder.Property(p => p.MaritalStatus)
                .HasMaxLength(50);

            builder.Property(p => p.Workplace)
                .HasMaxLength(200);

            // Relationships
            builder.HasOne(p => p.Hospital)
                .WithMany()
                .HasForeignKey(p => p.HospitalId)
                .OnDelete(DeleteBehavior.Restrict);

            // Optional relationship with User
            builder.HasOne<User>()
                .WithOne()
                .HasForeignKey<Patient>(p => p.UserId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.SetNull);

            // Filter deleted records
            builder.HasQueryFilter(p => !p.IsDeleted);

            // Default values
            builder.Property(p => p.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();
                    
            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
