using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class FilmDetailConfiguration : IEntityTypeConfiguration<FilmDetail>
    {
        public void Configure(EntityTypeBuilder<FilmDetail> builder)
        {
            builder.HasKey(f => f.Id);
            builder.HasIndex(a => a.Id)
                .IsUnique();

            builder.Property(f => f.Id)
                .ValueGeneratedOnAdd();

            builder.Property(f => f.XRayCount)
                .HasDefaultValue(0);

            builder.Property(f => f.RMICount)
                .HasDefaultValue(0);

            builder.Property(f => f.CTCount)
                .HasDefaultValue(0);

            builder.Property(f => f.ExternalFilmCount)
                .HasDefaultValue(0);

            // Relationships
            builder.HasOne(f => f.MedicalRecord)
                .WithOne(m => m.FilmDetails)
                .HasForeignKey<FilmDetail>(f => f.MedicalRecordId)
                .OnDelete(DeleteBehavior.Cascade);

            // Filter deleted records
            builder.HasQueryFilter(f => !f.IsDeleted);

            // Default values
            builder.Property(f => f.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();
                    
            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
