using GoTRUST.EMR.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations;

public class NotificationRecipientConfiguration : IEntityTypeConfiguration<NotificationRecipient>
{
    public void Configure(EntityTypeBuilder<NotificationRecipient> builder)
    {
        builder.HasKey(r => new { r.NotificationId, r.UserId });

        builder.Property(r => r.IsRead)
            .IsRequired();

        builder.Property(r => r.ReadAt)
            .IsRequired(false);

        builder.HasOne(r => r.Notification)
            .WithMany(n => n.NotificationRecipients)
            .HasForeignKey(r => r.NotificationId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(r => r.User)
            .WithMany(n => n.NotificationRecipients)
            .HasForeignKey(r => r.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
