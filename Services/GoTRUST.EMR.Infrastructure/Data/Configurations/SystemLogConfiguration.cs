using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class SystemLogConfiguration : IEntityTypeConfiguration<SystemLog>
    {
        public void Configure(EntityTypeBuilder<SystemLog> builder)
        {
            builder.HasKey(l => l.Id);
            builder.HasIndex(l => l.Id)
                .IsUnique();

            builder.Property(l => l.Id)
                .ValueGeneratedOnAdd();

            builder.Property(l => l.ActionName)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(l => l.ActionDetail)
                .HasMaxLength(1000);

            builder.Property(l => l.ActionIcon)
                .HasMaxLength(50);

            builder.Property(l => l.ActionTime)
                .IsRequired();

            builder.Property(l => l.SeverityLevel)
                .IsRequired()
                .HasMaxLength(20);

            // Relationships
            builder.HasOne(l => l.Hospital)
                .WithMany()
                .HasForeignKey(l => l.HospitalId)
                .OnDelete(DeleteBehavior.Restrict);

            // Filter deleted records
            builder.HasQueryFilter(l => !l.IsDeleted);

            // Default values
            builder.Property(l => l.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();
                
            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
