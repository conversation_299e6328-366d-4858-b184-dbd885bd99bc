using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class AdjustmentRequestConfiguration : IEntityTypeConfiguration<AdjustmentRequest>
    {
        public void Configure(EntityTypeBuilder<AdjustmentRequest> builder)
        {
            builder.HasKey(a => a.Id);
            builder.HasIndex(a => a.Id)
                .IsUnique();

            builder.Property(a => a.Id)
                .ValueGeneratedOnAdd();

            builder.Property(a => a.Reason)
                .IsRequired()
                .HasMaxLength(1000);

            builder.Property(a => a.RequestedDate)
                .IsRequired();

            builder.Property(a => a.ApprovalStatus)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(a => a.ApprovalReason)
                .HasMaxLength(1000);

            builder.Property(a => a.IsSynchronizedFromHIS)
                .HasDefaultValue(false);

            // Relationships
            builder.HasOne(a => a.Hospital)
                .WithMany()
                .HasForeignKey(a => a.HospitalId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(a => a.MedicalRecord)
                .WithMany()
                .HasForeignKey(a => a.MedicalRecordId)
                .OnDelete(DeleteBehavior.Restrict);

            // Filter deleted records
            builder.HasQueryFilter(a => !a.IsDeleted);

            // Default values
            builder.Property(a => a.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();
                
            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
