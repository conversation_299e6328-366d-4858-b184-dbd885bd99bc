﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations;

public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.ToTable("Users");

        builder.HasKey(c => c.Id);
        builder.HasIndex(c => c.Id)
            .IsUnique();

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd();

        builder.Property(c => c.FullName)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(n => n.SignSerials)
            .HasConversion(
                v => v == null || !v.Any() ? string.Empty : string.Join(';', v.Select(s => $"{s.SignSerial},{s.SignPinNumber}")),
                v => string.IsNullOrEmpty(v) ? new List<SignInfo>() :
                     v.Split(';', StringSplitOptions.RemoveEmptyEntries)
                      .Select(i => new SignInfo(
                          i.Contains(',') ? i.Substring(i.IndexOf(',') + 1) : null,
                          i.Contains(',') ? i.Substring(0, i.IndexOf(',')) : i))
                      .ToList());

        //filter deleted records
        builder.Property(c => c.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();

        builder.Property(c => c.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
