using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations;

public class HospitalConfiguration : IEntityTypeConfiguration<Hospital>
{
    public void Configure(EntityTypeBuilder<Hospital> builder)
    {
        builder.<PERSON><PERSON><PERSON>(h => h.Id);
        builder.HasIndex(a => a.Id)
                .IsUnique();

        builder.Property(h => h.Id)
            .ValueGeneratedOnAdd();

        builder.Property(h => h.Code)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(h => h.Name)
            .IsRequired()
            .HasMaxLength(255);

        // Filter deleted records
        builder.HasQueryFilter(h => !h.IsDeleted);

        builder.Property(h => h.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAdd();

        builder.Property(h => h.UpdatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .ValueGeneratedOnAddOrUpdate();
    }
}
