using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class MedicalRecordStatusHistoryConfiguration : IEntityTypeConfiguration<MedicalRecordStatusHistory>
    {
        public void Configure(EntityTypeBuilder<MedicalRecordStatusHistory> builder)
        {
            builder.HasKey(h => h.Id);
            builder.HasIndex(h => h.Id)
                .IsUnique();

            builder.Property(h => h.Id)
                .ValueGeneratedOnAdd();

            builder.Property(h => h.Status)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(50);

            builder.Property(h => h.ChangeReason)
                .HasMaxLength(1000);

            builder.Property(h => h.ChangeDate)
                .IsRequired();

            // Relationships
            builder.HasOne(h => h.MedicalRecord)
                .WithMany(m => m.StatusHistory)
                .HasForeignKey(h => h.MedicalRecordId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(h => h.ChangedByUser)
                .WithMany(u => u.MedicalRecordStatusHistories)
                .HasForeignKey(h => h.ChangedByUserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Filter deleted records
            builder.HasQueryFilter(h => !h.IsDeleted);

            // Default values
            builder.Property(h => h.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();
                    
            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
