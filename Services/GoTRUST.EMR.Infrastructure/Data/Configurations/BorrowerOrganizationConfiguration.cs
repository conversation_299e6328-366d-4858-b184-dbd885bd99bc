using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class ExternalBorrowerOrganizationConfiguration : IEntityTypeConfiguration<BorrowerOrganization>
    {
        public void Configure(EntityTypeBuilder<BorrowerOrganization> builder)
        {
            builder.HasKey(o => o.Id);
            builder.HasIndex(a => a.Id)
                .IsUnique();

            builder.Property(o => o.Id)
                .ValueGeneratedOnAdd();

            builder.Property(o => o.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(o => o.ContactPerson)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(o => o.PhoneNumber)
                .HasMaxLength(20);

            builder.Property(o => o.Email)
                .HasMaxLength(100);

            builder.Property(o => o.Address)
                .HasMaxLength(500);

            // Relationships
            builder.HasOne(o => o.Hospital)
                .WithMany()
                .HasForeignKey(o => o.HospitalId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(o => o.ApiKey)
                .WithOne()
                .HasForeignKey<BorrowerOrganization>(o => o.ApiKeyId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.SetNull);

            // Filter deleted records
            builder.HasQueryFilter(o => !o.IsDeleted);

            // Default values
            builder.Property(o => o.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();

            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
