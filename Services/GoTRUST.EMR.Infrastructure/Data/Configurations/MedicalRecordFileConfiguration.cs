using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class MedicalRecordFileConfiguration : IEntityTypeConfiguration<MedicalRecordFile>
    {
        public void Configure(EntityTypeBuilder<MedicalRecordFile> builder)
        {
            builder.HasKey(f => f.Id);
            builder.HasIndex(a => a.Id)
                .IsUnique();

            builder.Property(f => f.Id)
                .ValueGeneratedOnAdd();

            builder.Property(f => f.FileName)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(f => f.FilePath)
                .IsRequired()
                .HasMaxLength(1000);

            builder.Property(f => f.FileSize)
                .IsRequired();

            builder.Property(f => f.FileType)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(f => f.Type)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(50);

            builder.Property(f => f.IsEncrypted)
                .HasDefaultValue(false);

            builder.Property(h => h.InitialVector)
                .IsRequired()
                .HasMaxLength(1000);

            builder.Property(f => f.UploadedAt)
                .IsRequired();

            builder.Property(f => f.WatermarkEnabled)
                .HasDefaultValue(false);

            // Relationships
            builder.HasOne(f => f.MedicalRecord)
                .WithMany(m => m.Files)
                .HasForeignKey(f => f.MedicalRecordId)
                .OnDelete(DeleteBehavior.Cascade);

            // Filter deleted records
            builder.HasQueryFilter(f => !f.IsDeleted);

            // Default values
            builder.Property(f => f.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();

            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
