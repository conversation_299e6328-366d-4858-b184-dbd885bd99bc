using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class PatientShareInfoConfiguration : IEntityTypeConfiguration<PatientShareInfo>
    {
        public void Configure(EntityTypeBuilder<PatientShareInfo> builder)
        {
            builder.HasKey(p => p.Id);
            builder.HasIndex(p => p.Id)
                .IsUnique();

            builder.Property(p => p.Id)
                .ValueGeneratedOnAdd();

            builder.Property(p => p.TxnId)
                .HasMaxLength(100);

            builder.HasIndex(p => p.TxnId);

            builder.Property(p => p.IdentityNo)
                .HasMaxLength(50);

            builder.HasIndex(p => p.IdentityNo);

            builder.Property(p => p.Result)
                .HasMaxLength(10);

            // Filter deleted records
            builder.HasQueryFilter(p => !p.IsDeleted);

            // Default values
            builder.Property(p => p.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();

            builder.Property(p => p.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
