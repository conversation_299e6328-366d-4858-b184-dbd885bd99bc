using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class UserFileSystemNodeConfiguration : IEntityTypeConfiguration<UserFileSystemNode>
    {
        public void Configure(EntityTypeBuilder<UserFileSystemNode> builder)
        {
            builder.HasKey(un => new { un.UserId, un.FileSystemNodeId });

            builder.HasIndex(un => un.UserId);
            builder.HasIndex(un => un.FileSystemNodeId);

            builder.Property(un => un.IsRootShare)
                .IsRequired()
                .HasDefaultValue(false);

            // Relationships
            builder.HasOne(un => un.Node)
                .WithMany(n => n.UserNodes)
                .HasForeignKey(un => un.FileSystemNodeId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(un => un.User)
                .WithMany(u => u.UserNodes)
                .HasForeignKey(un => un.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Default values
            builder.Property(f => f.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();

            builder.Property(h => h.LastAccessedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}