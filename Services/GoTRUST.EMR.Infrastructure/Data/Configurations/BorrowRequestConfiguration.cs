using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class BorrowRequestConfiguration : IEntityTypeConfiguration<BorrowRequest>
    {
        public void Configure(EntityTypeBuilder<BorrowRequest> builder)
        {
            builder.HasKey(b => b.Id);
            builder.HasIndex(a => a.Id)
                .IsUnique();

            builder.Property(b => b.Id)
                .ValueGeneratedOnAdd();

            builder.Property(b => b.Purpose)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(b => b.Note)
                .HasMaxLength(1000);

            builder.Property(b => b.RequestedDate)
                .IsRequired();

            builder.Property(b => b.ApprovalStatus)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(50);

            builder.Property(b => b.ApprovalReason)
                .HasMaxLength(1000);

            builder.Property(b => b.UserType)
                .IsRequired()
                .HasMaxLength(50);

            // Relationships
            builder.HasOne(b => b.Hospital)
                .WithMany()
                .HasForeignKey(b => b.HospitalId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(b => b.BorrowerUser)
                .WithMany()
                .HasForeignKey(b => b.BorrowerUserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(b => b.ApprovedByUser)
                .WithMany()
                .HasForeignKey(b => b.ApprovedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(b => b.MedicalRecord)
                .WithMany()
                .HasForeignKey(b => b.MedicalRecordId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(b => b.BorrowerUser)
                .WithMany()
                .HasForeignKey(b => b.BorrowerUserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(b => b.ApprovedByUser)
                .WithMany()
                .HasForeignKey(b => b.ApprovedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(b => b.Organization)
                .WithMany()
                .HasForeignKey(b => b.OrganizationId)
                .OnDelete(DeleteBehavior.Restrict);

            // Filter deleted records
            builder.HasQueryFilter(b => !b.IsDeleted);

            // Default values
            builder.Property(b => b.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();
                    
            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
