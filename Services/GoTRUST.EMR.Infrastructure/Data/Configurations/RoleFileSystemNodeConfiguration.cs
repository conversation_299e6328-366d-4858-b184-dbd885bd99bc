using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class RoleFileSystemNodeConfiguration : IEntityTypeConfiguration<RoleFileSystemNode>
    {
        public void Configure(EntityTypeBuilder<RoleFileSystemNode> builder)
        {
            builder.HasKey(un => new { un.RoleId, un.FileSystemNodeId });

            builder.HasIndex(un => un.RoleId);
            builder.HasIndex(un => un.FileSystemNodeId);

            // Relationships
            builder.HasOne(un => un.Node)
                .WithMany(n => n.RoleNodes)
                .HasForeignKey(un => un.FileSystemNodeId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(un => un.Role)
                .WithMany(u => u.RoleNodes)
                .HasForeignKey(un => un.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            // Default values
            builder.Property(f => f.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();

            builder.Property(h => h.LastAccessedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}