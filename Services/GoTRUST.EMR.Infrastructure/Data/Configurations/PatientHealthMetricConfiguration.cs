using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class PatientHealthMetricConfiguration : IEntityTypeConfiguration<PatientHealthMetric>
    {
        public void Configure(EntityTypeBuilder<PatientHealthMetric> builder)
        {
            builder.HasKey(m => m.Id);

            builder.Property(m => m.Id)
                .ValueGeneratedOnAdd();

            builder.Property(m => m.MetricName)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(m => m.Value)
                .IsRequired()
                .HasPrecision(10, 2);

            builder.Property(m => m.Unit)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(m => m.RecordedAt)
                .IsRequired();

            // Relationships
            builder.HasOne(m => m.Patient)
                .WithMany(p => p.HealthMetrics)
                .HasForeignKey(m => m.PatientId)
                .OnDelete(DeleteBehavior.Cascade);

            // Filter deleted records
            builder.HasQueryFilter(m => !m.IsDeleted);

            // Default values
            builder.Property(m => m.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();
                    
            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
