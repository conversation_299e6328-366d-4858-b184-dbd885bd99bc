using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class MedicalRecordConfiguration : IEntityTypeConfiguration<MedicalRecord>
    {
        public void Configure(EntityTypeBuilder<MedicalRecord> builder)
        {
            builder.HasKey(m => m.Id);
            builder.HasIndex(a => a.Id)
                .IsUnique();

            builder.HasIndex(h => h.StorageNumber);


            builder.Property(m => m.Id)
                .ValueGeneratedOnAdd();

            builder.Property(m => m.AdmissionCode)
                .HasMaxLength(50);

            builder.Property(m => m.RecordCode)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(m => m.StorageNumber)
                .HasMaxLength(50);

            builder.Property(m => m.AdmissionDate)
                .IsRequired();

            builder.Property(m => m.Note)
                .HasMaxLength(1000);

            builder.Property(m => m.EncounterType)
                .HasMaxLength(100);

            builder.Property(m => m.Status)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(50);

            // Relationships
            builder.HasOne(m => m.Hospital)
                .WithMany()
                .HasForeignKey(m => m.HospitalId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(m => m.Patient)
                .WithMany(p => p.MedicalRecords)
                .HasForeignKey(m => m.PatientId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(m => m.Department)
                .WithMany()
                .HasForeignKey(m => m.DepartmentId)
                .OnDelete(DeleteBehavior.Restrict);

            // Filter deleted records
            builder.HasQueryFilter(m => !m.IsDeleted);
            builder.HasQueryFilter(m => m.Patient.IsDeleted == false);

            // Default values
            builder.Property(m => m.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();

            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();

            builder.Property(m => m.TotalFilesUploaded)
                .HasDefaultValue(0);

            builder.Property(m => m.StorageYears)
                .HasDefaultValue(0);

            builder.Property(m => m.FilmCount)
                .HasDefaultValue(0);

            builder.Property(m => m.HasBHYT)
                .HasDefaultValue(false);
        }
    }
}
