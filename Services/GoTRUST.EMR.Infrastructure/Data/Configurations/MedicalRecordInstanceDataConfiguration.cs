using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
    public class MedicalRecordInstanceDataConfiguration : IEntityTypeConfiguration<MedicalRecordInstanceData>
    {
        public void Configure(EntityTypeBuilder<MedicalRecordInstanceData> builder)
        {
            builder.HasKey(i => i.Id);
            builder.HasIndex(a => a.Id)
                .IsUnique();

            builder.Property(i => i.Id)
                .ValueGeneratedOnAdd();

            builder.Property(i => i.DateRecorded)
                .IsRequired();

            builder.Property(i => i.DataJson)
                .IsRequired();

            // Relationships
            builder.HasOne(i => i.MedicalRecord)
                .WithMany(m => m.InstanceData)
                .HasForeignKey(i => i.MedicalRecordId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(i => i.MedicalRecordTemplate)
                .WithMany(t => t.InstanceData)
                .HasForeignKey(i => i.MedicalRecordTemplateId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(i => i.MedicalRecordFile)
                .WithMany(f => f.InstanceData)
                .HasForeignKey(i => i.MedicalRecordFileId)
                .OnDelete(DeleteBehavior.Restrict);

            // Filter deleted records
            builder.HasQueryFilter(i => !i.IsDeleted);

            // Default values
            builder.Property(i => i.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAdd();
                    
            builder.Property(h => h.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .ValueGeneratedOnAddOrUpdate();
        }
    }
}
