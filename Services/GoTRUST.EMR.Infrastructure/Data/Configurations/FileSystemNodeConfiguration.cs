using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace GoTRUST.EMR.Infrastructure.Data.Configurations
{
	public class FileSystemNodeConfiguration : IEntityTypeConfiguration<FileSystemNode>
	{
		public void Configure(EntityTypeBuilder<FileSystemNode> builder)
		{
			builder.HasKey(n => n.Id);
			builder.HasIndex(n => n.Id)
				.IsUnique();

			builder.Property(n => n.Id)
				.ValueGeneratedOnAdd();

			builder.Property(n => n.Path)
				.IsRequired()
				.HasMaxLength(500);

			builder.Property(n => n.Name)
				.IsRequired()
				.HasMaxLength(255);

			builder.Property(n => n.NodeType)
				.IsRequired()
				.HasConversion<string>();

			builder.Property(n => n.ShareStatus)
				.IsRequired()
				.HasConversion<string>();

			builder.Property(n => n.ParentId)
				.IsRequired(false);

			builder.Property(n => n.OwnerId)
				.IsRequired();

			builder.Property(n => n.DestinationUrl)
				.IsRequired(false);

			builder.Property(n => n.MimeType)
				.IsRequired(false);

			builder.Property(n => n.Size)
				.IsRequired(false);

			// Filter deleted records
			builder.HasQueryFilter(n => !n.IsDeleted);

			// Navigation properties
			builder.HasOne(n => n.Owner)
				.WithMany(u => u.Nodes)
				.HasForeignKey(n => n.OwnerId)
				.OnDelete(DeleteBehavior.Cascade);

			builder.HasOne(n => n.Parent)
				.WithMany(u => u.Children)
				.HasForeignKey(n => n.ParentId)
				.OnDelete(DeleteBehavior.Cascade);

			// Filter deleted records
			builder.HasQueryFilter(f => !f.IsDeleted);

			// Default values
			builder.Property(f => f.CreatedAt)
				.HasDefaultValueSql("CURRENT_TIMESTAMP")
				.ValueGeneratedOnAdd();

			builder.Property(h => h.UpdatedAt)
				.HasDefaultValueSql("CURRENT_TIMESTAMP")
				.ValueGeneratedOnAddOrUpdate();
		}
	}
}