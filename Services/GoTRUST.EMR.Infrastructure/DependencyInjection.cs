﻿using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using Marten;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Common.Interface;
using BuildingBlocks.Common.Service;

namespace GoTRUST.EMR.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructureServices
        (this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("Database");

        // Add services to the container.
        services.AddSingleton<ISaveChangesInterceptor, AuditableEntityInterceptor>();
        services.AddSingleton<ISaveChangesInterceptor, DispatchDomainEventsInterceptor>();

        services.AddDbContextPool<ApplicationDbContext>((sp, o) =>
        {
            o.AddInterceptors(sp.GetServices<ISaveChangesInterceptor>());

            var dataSource = new NpgsqlDataSourceBuilder(connectionString)
                .EnableDynamicJson()
                .Build();

            o.UseNpgsql(dataSource, c =>
            {
                c.EnableRetryOnFailure(5);
            });
            o.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTrackingWithIdentityResolution);
        });

        services.AddSingleton<IFileStorageService, FileStorageService>();

        services.AddIdentity<User, Role>()
            .AddEntityFrameworkStores<ApplicationDbContext>()
            .AddDefaultTokenProviders()
            .AddSignInManager();
        services.Configure<DataProtectionTokenProviderOptions>(opt =>
        {
            opt.TokenLifespan = TimeSpan.FromDays(1);
        });

        services.AddScoped<IApplicationDbContext, ApplicationDbContext>();
        services.AddScoped<ITokenService, TokenService>();
        services.AddMarten(options =>
                    {
                        options.Connection(configuration.GetConnectionString("Database")
                            ?? throw new NullReferenceException(nameof(connectionString)));
                    });
        return services;
    }
}
