﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddPatientShareInfo : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PatientShareInfos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TxnId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IdentityNo = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Result = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ConsentGivenAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ConsentRevokedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PatientShareInfos", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PatientShareInfos_Id",
                table: "PatientShareInfos",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PatientShareInfos_IdentityNo",
                table: "PatientShareInfos",
                column: "IdentityNo");

            migrationBuilder.CreateIndex(
                name: "IX_PatientShareInfos_TxnId",
                table: "PatientShareInfos",
                column: "TxnId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PatientShareInfos");
        }
    }
}
