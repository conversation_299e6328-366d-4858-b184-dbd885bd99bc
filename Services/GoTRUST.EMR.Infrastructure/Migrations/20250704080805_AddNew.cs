﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddNew : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "PatientCode",
                table: "MedicalRecords",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ApprovedByUserCode",
                table: "BorrowRequests",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BorrowerUserCode",
                table: "BorrowRequests",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MedicalRecordCode",
                table: "BorrowRequests",
                type: "text",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PatientCode",
                table: "MedicalRecords");

            migrationBuilder.DropColumn(
                name: "ApprovedByUserCode",
                table: "BorrowRequests");

            migrationBuilder.DropColumn(
                name: "BorrowerUserCode",
                table: "BorrowRequests");

            migrationBuilder.DropColumn(
                name: "MedicalRecordCode",
                table: "BorrowRequests");
        }
    }
}
