﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class EditHospital : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Address",
                table: "Hospitals");

            migrationBuilder.DropColumn(
                name: "AdminEmail",
                table: "Hospitals");

            migrationBuilder.DropColumn(
                name: "Language",
                table: "Hospitals");

            migrationBuilder.DropColumn(
                name: "LoginSessionTimeout",
                table: "Hospitals");

            migrationBuilder.DropColumn(
                name: "LogoUrl",
                table: "Hospitals");

            migrationBuilder.DropColumn(
                name: "MaxLoginAttempts",
                table: "Hospitals");

            migrationBuilder.DropColumn(
                name: "NotificationTypes",
                table: "Hospitals");

            migrationBuilder.DropColumn(
                name: "PasswordPolicyJson",
                table: "Hospitals");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "Hospitals",
                type: "character varying(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "AdminEmail",
                table: "Hospitals",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Language",
                table: "Hospitals",
                type: "character varying(10)",
                maxLength: 10,
                nullable: false,
                defaultValue: "vi");

            migrationBuilder.AddColumn<int>(
                name: "LoginSessionTimeout",
                table: "Hospitals",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "LogoUrl",
                table: "Hospitals",
                type: "character varying(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "MaxLoginAttempts",
                table: "Hospitals",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "NotificationTypes",
                table: "Hospitals",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PasswordPolicyJson",
                table: "Hospitals",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: false,
                defaultValue: "");
        }
    }
}
