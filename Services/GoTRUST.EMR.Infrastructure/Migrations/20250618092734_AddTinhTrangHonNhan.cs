﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddTinhTrangHonNhan : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "MaritalStatus",
                table: "Employees",
                newName: "MaritalStatusId");

            migrationBuilder.RenameColumn(
                name: "MaritalStatus",
                table: "BorrowerOrganizations",
                newName: "MaritalStatusId");

            migrationBuilder.CreateTable(
                name: "TinhTrangHon<PERSON>han<PERSON>",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TinhTrangHonNhans", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TinhTrangHonNhans_Id",
                table: "TinhTrangHonNhans",
                column: "Id",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TinhTrangHonNhans");

            migrationBuilder.RenameColumn(
                name: "MaritalStatusId",
                table: "Employees",
                newName: "MaritalStatus");

            migrationBuilder.RenameColumn(
                name: "MaritalStatusId",
                table: "BorrowerOrganizations",
                newName: "MaritalStatus");
        }
    }
}
