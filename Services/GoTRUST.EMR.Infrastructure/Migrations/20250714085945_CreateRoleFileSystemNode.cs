﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class CreateRoleFileSystemNode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserFileSystemNodes_FileSystemNodes_NodeId",
                table: "UserFileSystemNodes");

            migrationBuilder.RenameColumn(
                name: "NodeId",
                table: "UserFileSystemNodes",
                newName: "FileSystemNodeId");

            migrationBuilder.RenameIndex(
                name: "IX_UserFileSystemNodes_NodeId",
                table: "UserFileSystemNodes",
                newName: "IX_UserFileSystemNodes_FileSystemNodeId");

            migrationBuilder.AddColumn<DateTime>(
                name: "OwnerLastAccessedAt",
                table: "FileSystemNodes",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "RoleFileSystemNodes",
                columns: table => new
                {
                    FileSystemNodeId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    LastAccessedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    IsAccessible = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RoleFileSystemNodes", x => new { x.UserId, x.FileSystemNodeId });
                    table.ForeignKey(
                        name: "FK_RoleFileSystemNodes_AspNetRoles_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RoleFileSystemNodes_FileSystemNodes_FileSystemNodeId",
                        column: x => x.FileSystemNodeId,
                        principalTable: "FileSystemNodes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_RoleFileSystemNodes_FileSystemNodeId",
                table: "RoleFileSystemNodes",
                column: "FileSystemNodeId");

            migrationBuilder.CreateIndex(
                name: "IX_RoleFileSystemNodes_UserId",
                table: "RoleFileSystemNodes",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserFileSystemNodes_FileSystemNodes_FileSystemNodeId",
                table: "UserFileSystemNodes",
                column: "FileSystemNodeId",
                principalTable: "FileSystemNodes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserFileSystemNodes_FileSystemNodes_FileSystemNodeId",
                table: "UserFileSystemNodes");

            migrationBuilder.DropTable(
                name: "RoleFileSystemNodes");

            migrationBuilder.DropColumn(
                name: "OwnerLastAccessedAt",
                table: "FileSystemNodes");

            migrationBuilder.RenameColumn(
                name: "FileSystemNodeId",
                table: "UserFileSystemNodes",
                newName: "NodeId");

            migrationBuilder.RenameIndex(
                name: "IX_UserFileSystemNodes_FileSystemNodeId",
                table: "UserFileSystemNodes",
                newName: "IX_UserFileSystemNodes_NodeId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserFileSystemNodes_FileSystemNodes_NodeId",
                table: "UserFileSystemNodes",
                column: "NodeId",
                principalTable: "FileSystemNodes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
