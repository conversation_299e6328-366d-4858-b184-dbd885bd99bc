﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddHospitalIdToRole : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "HospitalId",
                table: "AspNetRoles",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AspNetRoles_HospitalId",
                table: "AspNetRoles",
                column: "HospitalId");

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetRoles_Hospitals_HospitalId",
                table: "AspNetRoles",
                column: "HospitalId",
                principalTable: "Hospitals",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AspNetRoles_Hospitals_HospitalId",
                table: "AspNetRoles");

            migrationBuilder.DropIndex(
                name: "IX_AspNetRoles_HospitalId",
                table: "AspNetRoles");

            migrationBuilder.DropColumn(
                name: "HospitalId",
                table: "AspNetRoles");
        }
    }
}
