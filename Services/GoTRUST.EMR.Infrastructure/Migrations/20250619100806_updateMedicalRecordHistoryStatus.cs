﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class updateMedicalRecordHistoryStatus : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NewBorrowStatus",
                table: "MedicalRecordStatusHistories");

            migrationBuilder.DropColumn(
                name: "NewStatus",
                table: "MedicalRecordStatusHistories");

            migrationBuilder.DropColumn(
                name: "OldBorrowStatus",
                table: "MedicalRecordStatusHistories");

            migrationBuilder.DropColumn(
                name: "OldStatus",
                table: "MedicalRecordStatusHistories");

            migrationBuilder.AddColumn<string>(
                name: "Status",
                table: "MedicalRecordStatusHistories",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_MedicalRecordStatusHistories_ChangedByUserId",
                table: "MedicalRecordStatusHistories",
                column: "ChangedByUserId");

            migrationBuilder.AddForeignKey(
                name: "FK_MedicalRecordStatusHistories_AspNetUsers_ChangedByUserId",
                table: "MedicalRecordStatusHistories",
                column: "ChangedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MedicalRecordStatusHistories_AspNetUsers_ChangedByUserId",
                table: "MedicalRecordStatusHistories");

            migrationBuilder.DropIndex(
                name: "IX_MedicalRecordStatusHistories_ChangedByUserId",
                table: "MedicalRecordStatusHistories");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "MedicalRecordStatusHistories");

            migrationBuilder.AddColumn<string>(
                name: "NewBorrowStatus",
                table: "MedicalRecordStatusHistories",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NewStatus",
                table: "MedicalRecordStatusHistories",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OldBorrowStatus",
                table: "MedicalRecordStatusHistories",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OldStatus",
                table: "MedicalRecordStatusHistories",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);
        }
    }
}
