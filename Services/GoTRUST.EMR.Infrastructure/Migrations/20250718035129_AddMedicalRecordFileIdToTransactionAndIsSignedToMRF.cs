﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddMedicalRecordFileIdToTransactionAndIsSignedToMRF : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PatientId",
                table: "TransactionSigns",
                newName: "MedicalRecordFileId");

            migrationBuilder.AddColumn<bool>(
                name: "IsSignedByPatient",
                table: "MedicalRecordFiles",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsSignedByPatient",
                table: "MedicalRecordFiles");

            migrationBuilder.RenameColumn(
                name: "MedicalRecordFileId",
                table: "TransactionSigns",
                newName: "PatientId");
        }
    }
}
