﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddMigration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "MedicalRecordId",
                table: "UserActionLogs",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserActionLogs_MedicalRecordId",
                table: "UserActionLogs",
                column: "MedicalRecordId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserActionLogs_MedicalRecords_MedicalRecordId",
                table: "UserActionLogs",
                column: "MedicalRecordId",
                principalTable: "MedicalRecords",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserActionLogs_MedicalRecords_MedicalRecordId",
                table: "UserActionLogs");

            migrationBuilder.DropIndex(
                name: "IX_UserActionLogs_MedicalRecordId",
                table: "UserActionLogs");

            migrationBuilder.DropColumn(
                name: "MedicalRecordId",
                table: "UserActionLogs");
        }
    }
}
