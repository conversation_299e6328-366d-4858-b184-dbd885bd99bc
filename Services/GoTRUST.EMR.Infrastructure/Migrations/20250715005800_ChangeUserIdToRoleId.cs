﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ChangeUserIdToRoleId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RoleFileSystemNodes_AspNetRoles_UserId",
                table: "RoleFileSystemNodes");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "RoleFileSystemNodes",
                newName: "RoleId");

            migrationBuilder.RenameIndex(
                name: "IX_RoleFileSystemNodes_UserId",
                table: "RoleFileSystemNodes",
                newName: "IX_RoleFileSystemNodes_RoleId");

            migrationBuilder.AddForeignKey(
                name: "FK_RoleFileSystemNodes_AspNetRoles_RoleId",
                table: "RoleFileSystemNodes",
                column: "RoleId",
                principalTable: "AspNetRoles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RoleFileSystemNodes_AspNetRoles_RoleId",
                table: "RoleFileSystemNodes");

            migrationBuilder.RenameColumn(
                name: "RoleId",
                table: "RoleFileSystemNodes",
                newName: "UserId");

            migrationBuilder.RenameIndex(
                name: "IX_RoleFileSystemNodes_RoleId",
                table: "RoleFileSystemNodes",
                newName: "IX_RoleFileSystemNodes_UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_RoleFileSystemNodes_AspNetRoles_UserId",
                table: "RoleFileSystemNodes",
                column: "UserId",
                principalTable: "AspNetRoles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
