﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RemoveExternalBorrowRequest : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ExternalBorrowRequests");

            migrationBuilder.DropTable(
                name: "ExternalBorrowerOrganizations");

            migrationBuilder.AlterColumn<string>(
                name: "OldStatus",
                table: "MedicalRecordStatusHistories",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AddColumn<string>(
                name: "NewBorrowStatus",
                table: "MedicalRecordStatusHistories",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "OldBorrowStatus",
                table: "MedicalRecordStatusHistories",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "BorrowerOrganizationId",
                table: "BorrowRequests",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "OrganizationId",
                table: "BorrowRequests",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UserType",
                table: "BorrowRequests",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "BorrowerOrganizations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    HospitalId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    ContactPerson = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PhoneNumber = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Email = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Address = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ApiKeyId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BorrowerOrganizations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BorrowerOrganizations_ApiKeys_ApiKeyId",
                        column: x => x.ApiKeyId,
                        principalTable: "ApiKeys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_BorrowerOrganizations_Hospitals_HospitalId",
                        column: x => x.HospitalId,
                        principalTable: "Hospitals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BorrowRequests_ApprovedByUserId",
                table: "BorrowRequests",
                column: "ApprovedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_BorrowRequests_BorrowerOrganizationId",
                table: "BorrowRequests",
                column: "BorrowerOrganizationId");

            migrationBuilder.CreateIndex(
                name: "IX_BorrowRequests_BorrowerUserId",
                table: "BorrowRequests",
                column: "BorrowerUserId");

            migrationBuilder.CreateIndex(
                name: "IX_BorrowRequests_OrganizationId",
                table: "BorrowRequests",
                column: "OrganizationId");

            migrationBuilder.CreateIndex(
                name: "IX_BorrowerOrganizations_ApiKeyId",
                table: "BorrowerOrganizations",
                column: "ApiKeyId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_BorrowerOrganizations_HospitalId",
                table: "BorrowerOrganizations",
                column: "HospitalId");

            migrationBuilder.CreateIndex(
                name: "IX_BorrowerOrganizations_Id",
                table: "BorrowerOrganizations",
                column: "Id",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_BorrowRequests_AspNetUsers_ApprovedByUserId",
                table: "BorrowRequests",
                column: "ApprovedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_BorrowRequests_AspNetUsers_BorrowerUserId",
                table: "BorrowRequests",
                column: "BorrowerUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_BorrowRequests_BorrowerOrganizations_BorrowerOrganizationId",
                table: "BorrowRequests",
                column: "BorrowerOrganizationId",
                principalTable: "BorrowerOrganizations",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BorrowRequests_BorrowerOrganizations_OrganizationId",
                table: "BorrowRequests",
                column: "OrganizationId",
                principalTable: "BorrowerOrganizations",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BorrowRequests_AspNetUsers_ApprovedByUserId",
                table: "BorrowRequests");

            migrationBuilder.DropForeignKey(
                name: "FK_BorrowRequests_AspNetUsers_BorrowerUserId",
                table: "BorrowRequests");

            migrationBuilder.DropForeignKey(
                name: "FK_BorrowRequests_BorrowerOrganizations_BorrowerOrganizationId",
                table: "BorrowRequests");

            migrationBuilder.DropForeignKey(
                name: "FK_BorrowRequests_BorrowerOrganizations_OrganizationId",
                table: "BorrowRequests");

            migrationBuilder.DropTable(
                name: "BorrowerOrganizations");

            migrationBuilder.DropIndex(
                name: "IX_BorrowRequests_ApprovedByUserId",
                table: "BorrowRequests");

            migrationBuilder.DropIndex(
                name: "IX_BorrowRequests_BorrowerOrganizationId",
                table: "BorrowRequests");

            migrationBuilder.DropIndex(
                name: "IX_BorrowRequests_BorrowerUserId",
                table: "BorrowRequests");

            migrationBuilder.DropIndex(
                name: "IX_BorrowRequests_OrganizationId",
                table: "BorrowRequests");

            migrationBuilder.DropColumn(
                name: "NewBorrowStatus",
                table: "MedicalRecordStatusHistories");

            migrationBuilder.DropColumn(
                name: "OldBorrowStatus",
                table: "MedicalRecordStatusHistories");

            migrationBuilder.DropColumn(
                name: "BorrowerOrganizationId",
                table: "BorrowRequests");

            migrationBuilder.DropColumn(
                name: "OrganizationId",
                table: "BorrowRequests");

            migrationBuilder.DropColumn(
                name: "UserType",
                table: "BorrowRequests");

            migrationBuilder.AlterColumn<string>(
                name: "OldStatus",
                table: "MedicalRecordStatusHistories",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.CreateTable(
                name: "ExternalBorrowerOrganizations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ApiKeyId = table.Column<Guid>(type: "uuid", nullable: true),
                    HospitalId = table.Column<Guid>(type: "uuid", nullable: false),
                    Address = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ContactPerson = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    Email = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    PhoneNumber = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExternalBorrowerOrganizations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ExternalBorrowerOrganizations_ApiKeys_ApiKeyId",
                        column: x => x.ApiKeyId,
                        principalTable: "ApiKeys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_ExternalBorrowerOrganizations_Hospitals_HospitalId",
                        column: x => x.HospitalId,
                        principalTable: "Hospitals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ExternalBorrowRequests",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    HospitalId = table.Column<Guid>(type: "uuid", nullable: false),
                    MedicalRecordId = table.Column<Guid>(type: "uuid", nullable: false),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    ApprovalReason = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ApprovalStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ApprovedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    BorrowedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    Note = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Purpose = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    RequestedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ReturnedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExternalBorrowRequests", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ExternalBorrowRequests_ExternalBorrowerOrganizations_Organi~",
                        column: x => x.OrganizationId,
                        principalTable: "ExternalBorrowerOrganizations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ExternalBorrowRequests_Hospitals_HospitalId",
                        column: x => x.HospitalId,
                        principalTable: "Hospitals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ExternalBorrowRequests_MedicalRecords_MedicalRecordId",
                        column: x => x.MedicalRecordId,
                        principalTable: "MedicalRecords",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ExternalBorrowerOrganizations_ApiKeyId",
                table: "ExternalBorrowerOrganizations",
                column: "ApiKeyId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ExternalBorrowerOrganizations_HospitalId",
                table: "ExternalBorrowerOrganizations",
                column: "HospitalId");

            migrationBuilder.CreateIndex(
                name: "IX_ExternalBorrowerOrganizations_Id",
                table: "ExternalBorrowerOrganizations",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ExternalBorrowRequests_HospitalId",
                table: "ExternalBorrowRequests",
                column: "HospitalId");

            migrationBuilder.CreateIndex(
                name: "IX_ExternalBorrowRequests_Id",
                table: "ExternalBorrowRequests",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ExternalBorrowRequests_MedicalRecordId",
                table: "ExternalBorrowRequests",
                column: "MedicalRecordId");

            migrationBuilder.CreateIndex(
                name: "IX_ExternalBorrowRequests_OrganizationId",
                table: "ExternalBorrowRequests",
                column: "OrganizationId");
        }
    }
}
