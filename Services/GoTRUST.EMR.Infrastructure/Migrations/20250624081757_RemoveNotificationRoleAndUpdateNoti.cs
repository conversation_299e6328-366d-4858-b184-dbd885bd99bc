﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RemoveNotificationRoleAndUpdateNoti : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "NotificationRoles");

            migrationBuilder.AddColumn<string>(
                name: "RoleFunctionIds",
                table: "Notifications",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RoleFunctionIds",
                table: "Notifications");

            migrationBuilder.CreateTable(
                name: "NotificationRoles",
                columns: table => new
                {
                    NotificationId = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NotificationRoles", x => new { x.NotificationId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_NotificationRoles_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_NotificationRoles_Notifications_NotificationId",
                        column: x => x.NotificationId,
                        principalTable: "Notifications",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_NotificationRoles_RoleId",
                table: "NotificationRoles",
                column: "RoleId");
        }
    }
}
