﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateRoleandFunction : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_RoleFunctions",
                table: "RoleFunctions");

            migrationBuilder.DropIndex(
                name: "IX_RoleFunctions_RoleId_FunctionId",
                table: "RoleFunctions");

            migrationBuilder.AlterColumn<string>(
                name: "PermissionType",
                table: "RoleFunctions",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "Functions",
                type: "character varying(2)",
                maxLength: 2,
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_RoleFunctions",
                table: "RoleFunctions",
                columns: new[] { "RoleId", "FunctionId", "PermissionType" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_RoleFunctions",
                table: "RoleFunctions");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "Functions");

            migrationBuilder.AlterColumn<string>(
                name: "PermissionType",
                table: "RoleFunctions",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddPrimaryKey(
                name: "PK_RoleFunctions",
                table: "RoleFunctions",
                columns: new[] { "RoleId", "FunctionId" });

            migrationBuilder.CreateIndex(
                name: "IX_RoleFunctions_RoleId_FunctionId",
                table: "RoleFunctions",
                columns: new[] { "RoleId", "FunctionId" },
                unique: true);
        }
    }
}
