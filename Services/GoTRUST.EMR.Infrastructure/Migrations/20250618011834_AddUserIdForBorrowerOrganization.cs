﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTRUST.EMR.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddUserIdForBorrowerOrganization : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "UserId",
                table: "BorrowerOrganizations",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_BorrowerOrganizations_UserId",
                table: "BorrowerOrganizations",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_BorrowerOrganizations_AspNetUsers_UserId",
                table: "BorrowerOrganizations",
                column: "UserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BorrowerOrganizations_AspNetUsers_UserId",
                table: "BorrowerOrganizations");

            migrationBuilder.DropIndex(
                name: "IX_BorrowerOrganizations_UserId",
                table: "BorrowerOrganizations");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "BorrowerOrganizations");
        }
    }
}
