﻿global using GoTRUST.EMR.Domain.Models;
global using BuildingBlocks.CQRS;
global using GoTRUST.EMR.Application.Data;
global using GoTRUST.EMR.Application.Exceptions;
global using MediatR;
global using Microsoft.Extensions.Logging;
// global using GoTRUST.EMR.Domain.Events;
global using Microsoft.EntityFrameworkCore;
// global using GoTRUST.EMR.Application.Extensions;
global using FluentValidation;
global using Microsoft.AspNetCore.Http;
global using System.Security.Claims;
global using BuildingBlocks.Common.Helper;