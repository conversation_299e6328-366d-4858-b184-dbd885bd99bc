using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Exceptions;

public class MedicalRecordTemplateNameAlreadyExistsException : BadRequestException
{
    public MedicalRecordTemplateNameAlreadyExistsException() : base("Tên mẫu phiếu đã tồn tại") { }

    public MedicalRecordTemplateNameAlreadyExistsException(string templateName) : base($"Tên mẫu phiếu '{templateName}' đã tồn tại") { }

    public MedicalRecordTemplateNameAlreadyExistsException(string templateName, Guid departmentId) 
        : base($"Tên mẫu phiếu '{templateName}' đã tồn tại trong khoa '{departmentId}'") { }
}
