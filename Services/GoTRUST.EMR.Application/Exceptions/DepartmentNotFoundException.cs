using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Exceptions;

public class DepartmentNotFoundException : NotFoundException
{
    public DepartmentNotFoundException() : base("Không tìm thấy khoa") { }

    public DepartmentNotFoundException(Guid id) : base($"Không tìm thấy khoa có ID '{id}'") { }

    public DepartmentNotFoundException(string code) : base($"Không tìm thấy khoa có mã '{code}'") { }
}
