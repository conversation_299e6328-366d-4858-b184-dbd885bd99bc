using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Exceptions
{
    public class BorrowRequestAsyncException : AppLogicException
    {
        public override string ErrorCode { get; } = "014";

        public BorrowRequestAsyncException() 
            : base("<PERSON><PERSON><PERSON> cầu mượn không thể được xử lý vì trạng thái hiện tại không phải là 'Đang Chờ Xử Lý'.")
        {
        }
    }
}