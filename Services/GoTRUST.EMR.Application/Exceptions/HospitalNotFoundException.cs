using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Exceptions;

public class HospitalNotFoundException : NotFoundException
{
    public HospitalNotFoundException() : base("Không tìm thấy bệnh viện") { }

    public HospitalNotFoundException(Guid id) : base($"Không tìm thấy bệnh viện có ID '{id}'") { }

    public HospitalNotFoundException(string code) : base($"Không tìm thấy bệnh viện có mã '{code}'") { }
}
