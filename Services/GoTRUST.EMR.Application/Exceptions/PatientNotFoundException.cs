using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Exceptions;

public class PatientNotFoundException : NotFoundException
{
    public PatientNotFoundException() : base("Không tìm thấy bệnh nhân") { }

    public PatientNotFoundException(Guid id) : base($"Không tìm thấy bệnh nhân có ID '{id}'") { }

    public PatientNotFoundException(string code) : base($"Không tìm thấy bệnh nhân có mã '{code}'") { }
}
