using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Exceptions;

public class HospitalConfigNotFoundException : NotFoundException
{
    public HospitalConfigNotFoundException() : base("Không tìm thấy cấu hình bệnh viện") { }

    public HospitalConfigNotFoundException(Guid id) : base($"Không tìm thấy cấu hình bệnh viện có ID '{id}'") { }

    public HospitalConfigNotFoundException(string key) : base($"Không tìm thấy cấu hình bệnh viện có key '{key}'") { }
}
