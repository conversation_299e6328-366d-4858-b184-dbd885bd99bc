using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Exceptions
{
    public class MedicalRecordHistoryInvalidStatusException : AppLogicException
    {
        public override string ErrorCode { get; } = "013";
        public MedicalRecordHistoryInvalidStatusException()
            : base("Không thể lấy lịch sử truy xuất vì trạng thái truy xuất không hợp lệ.")
        {
        }
    }
}