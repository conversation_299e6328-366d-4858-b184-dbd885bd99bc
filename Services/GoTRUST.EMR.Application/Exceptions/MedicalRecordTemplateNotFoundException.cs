using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Exceptions;

public class MedicalRecordTemplateNotFoundException : NotFoundException
{
    public MedicalRecordTemplateNotFoundException() : base("Không tìm thấy mẫu phiếu") { }

    public MedicalRecordTemplateNotFoundException(Guid id) : base($"Không tìm thấy mẫu phiếu có ID '{id}'") { }

    public MedicalRecordTemplateNotFoundException(string templateName) : base($"Không tìm thấy mẫu phiếu có tên '{templateName}'") { }
}
