using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Exceptions;

public class DepartmentCodeAlreadyExistsException : AppLogicException
{
    public override string ErrorCode { get; } = "001";

    public DepartmentCodeAlreadyExistsException(string departmentCode) : base($"Mã khoa '{departmentCode}' đã tồn tại") { }

    public DepartmentCodeAlreadyExistsException(string code, Guid hospitalId) 
        : base($"Mã khoa '{code}' đã tồn tại trong bệnh viện '{hospitalId}'") { }
}
