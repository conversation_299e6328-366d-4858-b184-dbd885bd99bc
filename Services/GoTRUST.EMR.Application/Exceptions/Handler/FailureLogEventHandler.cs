using BuildingBlocks.Notifications;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Exceptions.Handler;

public class FailureLogEventHandler(
    IApplicationDbContext dbContext,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<FailureLogEvent>
{
    private static readonly Dictionary<string, ActionInfo> ActionMap = new(StringComparer.OrdinalIgnoreCase)
    {
        ["/employees_post"] = new("Thêm nhân viên", UserActionLogConstants.CreateIcon, UserActionLogType.Create),
        ["/employees_put"] = new("Cập nhật nhân viên", UserActionLogConstants.UpdateIcon, UserActionLogType.Update),
        ["/employees_delete"] = new("Xóa nhân viên", UserActionLogConstants.DeleteIcon, UserActionLogType.Delete),

        ["/borrower-organizations_post"] = new("Thêm tài khoản đối tác", UserActionLogConstants.CreateIcon, UserActionLogType.Create),
        ["/borrower-organizations_put"] = new("Cập nhật tài khoản đối tác", UserActionLogConstants.UpdateIcon, UserActionLogType.Update),
        ["/borrower-organizations_delete"] = new("Xóa tài khoản đối tác", UserActionLogConstants.DeleteIcon, UserActionLogType.Delete),

        ["/borrow-requests_post"] = new("Tạo yêu cầu mượn", UserActionLogConstants.CreateIcon, UserActionLogType.Create),
        ["/borrow-requests/approve_put"] = new("Duyệt yêu cầu mượn", UserActionLogConstants.ApproveIcon, UserActionLogType.Approve),
        ["/borrow-requests/reject_post"] = new("Từ chối yêu cầu mượn", UserActionLogConstants.RejectIcon, UserActionLogType.Reject),

        ["/departments_post"] = new("Thêm khoa", UserActionLogConstants.CreateIcon, UserActionLogType.Create),
        ["/departments_put"] = new("Cập nhật khoa", UserActionLogConstants.UpdateIcon, UserActionLogType.Update),
        ["/departments_delete"] = new("Xóa khoa", UserActionLogConstants.DeleteIcon, UserActionLogType.Delete),

        ["/medical-records_post"] = new("Tạo bệnh án mới", UserActionLogConstants.CreateIcon, UserActionLogType.Create),
        ["/medical-records_put"] = new("Cập nhật bệnh án", UserActionLogConstants.UpdateIcon, UserActionLogType.Update),
        ["/medical-records/films_put"] = new("Cập nhật phim y tế", UserActionLogConstants.UpdateIcon, UserActionLogType.Update),
        ["/medical-records/approve_put"] = new("Chấp nhận hồ sơ bệnh án", UserActionLogConstants.ApproveIcon, UserActionLogType.Approve),
        ["/medical-records/reject_put"] = new("Từ chối hồ sơ bệnh án", UserActionLogConstants.RejectIcon, UserActionLogType.Reject),
        ["/medical-records_delete"] = new("Xóa bệnh án", UserActionLogConstants.DeleteIcon, UserActionLogType.Delete),

        ["/hospital-configs/sync_post"] = new("Đồng bộ cấu hình bệnh viện", UserActionLogConstants.UpdateIcon, UserActionLogType.Update),

        ["/patients_post"] = new("Tạo bệnh nhân mới", UserActionLogConstants.CreateIcon, UserActionLogType.Create),
        ["/patients_put"] = new("Cập nhật thông tin bệnh nhân", UserActionLogConstants.UpdateIcon, UserActionLogType.Update),
        ["/patients_delete"] = new("Xóa bệnh nhân", UserActionLogConstants.DeleteIcon, UserActionLogType.Delete),

        ["/notifications/system_post"] = new("Tạo thông báo hệ thống", UserActionLogConstants.CreateIcon, UserActionLogType.Create),

        ["/medical-record-templates_post"] = new("Tạo mẫu hồ sơ mới", UserActionLogConstants.CreateIcon, UserActionLogType.Create),
        ["/medical-record-templates_put"] = new("Cập nhật mẫu hồ sơ", UserActionLogConstants.UpdateIcon, UserActionLogType.Update),
        ["/medical-record-templates_delete"] = new("Xóa mẫu hồ sơ", UserActionLogConstants.DeleteIcon, UserActionLogType.Delete),

        ["/authenticates/login_post"] = new("Đăng nhập", UserActionLogConstants.LoginIcon, UserActionLogType.Authenticate),
        ["/authenticates/logout_post"] = new("Đăng xuất", UserActionLogConstants.LoginIcon, UserActionLogType.Authenticate),
        ["/authenticates/refresh-token_post"] = new("Làm mới token", UserActionLogConstants.LoginIcon, UserActionLogType.Authenticate),
        ["/authenticates/register_post"] = new("Đăng ký tài khoản", UserActionLogConstants.LoginIcon, UserActionLogType.Authenticate),
        ["/authenticates/approve-login_post"] = new("Phê duyệt đăng nhập", UserActionLogConstants.LoginIcon, UserActionLogType.Authenticate),
        ["/authenticates/forgot-password_post"] = new("Quên mật khẩu", UserActionLogConstants.PasswordIcon, UserActionLogType.Authenticate),
        ["/authenticates/reset-password_post"] = new("Đặt lại mật khẩu", UserActionLogConstants.PasswordIcon, UserActionLogType.Authenticate),
        ["/authenticates/set-password_post"] = new("Đổi mật khẩu", UserActionLogConstants.PasswordIcon, UserActionLogType.Authenticate),

        ["/roles_post"] = new("Tạo vai trò mới", UserActionLogConstants.CreateIcon, UserActionLogType.Create),
        ["/roles_put"] = new("Cập nhật vai trò", UserActionLogConstants.UpdateIcon, UserActionLogType.Update),
        ["/roles/functions_put"] = new("Cập nhật chức năng vai trò", UserActionLogConstants.UpdateIcon, UserActionLogType.Update),
        ["/roles_delete"] = new("Xóa vai trò", UserActionLogConstants.DeleteIcon, UserActionLogType.Delete),
    };

    private static readonly string[] KnownPrefixes = [.. ActionMap
        .Select(k => k.Key[..k.Key.LastIndexOf('_')])
        .Distinct()];

    public async Task Handle(FailureLogEvent notification, CancellationToken cancellationToken)
    {
        var method = notification.Method!.ToLowerInvariant();
        var path = notification.Path!.Split('?')[0].ToLowerInvariant();

        string? matchedKey = null;

        foreach (var prefix in KnownPrefixes)
        {
            if (path.StartsWith(prefix))
            {
                var candidateKey = $"{prefix}_{method}";
                if (ActionMap.ContainsKey(candidateKey))
                {
                    matchedKey = candidateKey;
                    break;
                }
            }
        }

        if (matchedKey is null)
            return;

        var info = ActionMap[matchedKey];

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var ipAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString()
                         ?? UserActionLogConstants.IpUnknown;
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal();

        dbContext.UserActionLogs.Add(new UserActionLog
        {
            HospitalId = hospitalId,
            ActionName = info.ActionName,
            ActionDetail = info.ActionName,
            ActionIcon = info.ActionIcon,
            ActionType = info.ActionType,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = ipAddress,
            Result = UserActionLogStatusConstants.FailureResult
        });

        await dbContext.SaveChangesAsync(cancellationToken);
    }

    private record ActionInfo(string ActionName, string ActionIcon, UserActionLogType ActionType);
}