using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Exceptions;

public class EmployeeNotFoundException : NotFoundException
{
    public EmployeeNotFoundException() : base("Không tìm thấy nhân viên") { }

    public EmployeeNotFoundException(Guid id) : base($"Không tìm thấy nhân viên có ID '{id}'") { }

    public EmployeeNotFoundException(string code) : base($"Không tìm thấy nhân viên có mã '{code}'") { }
}
