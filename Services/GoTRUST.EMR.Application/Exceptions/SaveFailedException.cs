using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Exceptions;

public class SaveFailedException : InternalServerException
{
    public SaveFailedException() : base("Thao tác lưu dữ liệu thất bại") { }

    public SaveFailedException(string entity) : base($"Lư<PERSON> {entity} thất bại") { }

    public SaveFailedException(string entity, string operation) : base($"Thao tác {operation} {entity} thất bại") { }
}
