using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;


namespace GoTRUST.EMR.Application.Hubs
{
    [Authorize]
    public class NotificationHub(ILogger<NotificationHub> logger) : Hub
    {
        private readonly ILogger<NotificationHub> _logger = logger;

        public override async Task OnConnectedAsync()
        {
            var roles = Context.User?.Claims
                .Where(c => c.Type == ClaimTypes.Role)
                .Select(c => c.Value)
                .Where(IsValidNotificationRole)
                .ToList();
            var userId = Context.UserIdentifier;
            var connectionId = Context.ConnectionId;
            
            // Tham gia nhóm
            if (roles != null && roles.Count != 0)
            {
                foreach (var role in roles)
                {
                    // Group theo role
                    await Groups.AddToGroupAsync(connectionId, role);
                    _logger.LogDebug("Added user {UserId} with connection {ConnectionId} to group {Role}", 
                        userId, connectionId, role);
                }
            }
            
            _logger.LogInformation("User {UserId} connected to NotificationHub with connection {ConnectionId} and roles: {Roles}", 
                userId, connectionId, string.Join(", ", roles ?? new List<string>()));
            
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var userId = Context.UserIdentifier;
            var connectionId = Context.ConnectionId;
            
            if (exception != null)
            {
                // Disconnect do exception (network timeout, client crash, etc.)
                _logger.LogWarning(exception, "User {UserId} disconnected from NotificationHub with connection {ConnectionId} due to exception", 
                    userId, connectionId);
            }
            else
            {
                // Disconnect bình thường (user logout, close app properly)
                _logger.LogInformation("User {UserId} disconnected from NotificationHub with connection {ConnectionId}", 
                    userId, connectionId);
            }
            
            // SignalR tự động remove khỏi tất cả groups khi disconnect
            // Nhờ có CloseTimeout, method này sẽ được gọi đúng lúc
            await base.OnDisconnectedAsync(exception);
        }
        private static bool IsValidNotificationRole(string role)
        {
            var allowedRoles = new[]
            {
                "STC", "STR", "STU", "STD", "STA",
                "EMC", "EMR", "EMU", "EMD", "EMA",
                "PMC", "PMR", "PMU", "PMD", "PMA",
                "MSC", "MSR", "MSU", "MSD", "MSA",
                "PDC", "PDR", "PDU", "PDD", "PDA",
                "TOC", "TOR", "TOU", "TOD", "TOA",
                "TRC", "TRR", "TRU", "TRD", "TRA",
                "MDC", "MDR", "MDU", "MDD", "MDA",
                "RAC", "RAR", "RAU", "RAD", "RAA",
                "DSC", "DSR", "DSU", "DSD", "DSA",
                "SEC", "SER", "SEU", "SED", "SEA",
                "FDC", "FDR", "FDU", "FDD", "FDA",
                "DMC", "DMR", "DMU", "DMD", "DMA",
                "FMC", "FMR", "FMU", "FMD", "FMA"
            };
            return allowedRoles.Contains(role);
        }
    }
}
