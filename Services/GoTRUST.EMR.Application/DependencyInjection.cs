﻿using BuildingBlocks.Behaviors;
using GoTRUST.EMR.Application.Repositories;
using Hangfire;
using Hangfire.Mongo;
using Hangfire.Mongo.Migration.Strategies;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;
using System.Reflection;
using StackExchange.Redis;
using BuildingBlocks.Common.Interface;
using BuildingBlocks.Common.Service;
using GoTRUST.EMR.Application.Services;
using GoTRUST.EMR.Application.Jobs;

namespace GoTRUST.EMR.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplicationServices
        (this IServiceCollection services, IConfiguration configuration)
    {
        var migrationOptions = new MongoMigrationOptions
        {
            MigrationStrategy = new MigrateMongoMigrationStrategy()
        };
        services.AddMediatR(config =>
        {
            config.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
            config.AddOpenBehavior(typeof(ValidationBehavior<,>));
            config.AddOpenBehavior(typeof(LoggingBehavior<,>));
        });
        services.AddHangfire(c =>
        {
            c.SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UseMongoStorage(configuration["MongoDatabaseUrl"], configuration["MongoDatabaseHangfire"], new MongoStorageOptions
            {
                MigrationOptions = migrationOptions,
                CheckQueuedJobsStrategy = CheckQueuedJobsStrategy.TailNotificationsCollection,
            });
        });
        services.AddHangfireServer();

        services.AddFeatureManagement();

        services.AddDistributedMemoryCache();
        var redisConnectionString = configuration["REDIS_CONNECTION_STRING"];
        if (!string.IsNullOrEmpty(redisConnectionString))
        {
            services.AddSingleton<IConnectionMultiplexer>(ConnectionMultiplexer.Connect(redisConnectionString));
            services.AddTransient<ICachedService, RedisCacheService>();
        }
        else
        {
            services.AddScoped<ICachedService, CachedService>();
        }

        // Register repositories
        services.AddScoped<IUserRepository, UserRepository>();

        // Register HIS services
        services.AddScoped<IHisService, HisService>();

        return services;
    }
}
