using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Helpers
{
    public static class UserManagerHelper
    {
        public static async Task<User> FindByUserNameOrEmailOrPhoneAsync(this UserManager<User> userManager, string account)
        {
            var user = await userManager.FindByNameAsync(account);
            if (user != null) return user;

            user = await userManager.FindByEmailAsync(account);
            if (user != null) return user;

            user = await userManager.Users.SingleOrDefaultAsync(x => x.PhoneNumber == account);
            if (user != null) return user;

            return null!;
        }
    }
}