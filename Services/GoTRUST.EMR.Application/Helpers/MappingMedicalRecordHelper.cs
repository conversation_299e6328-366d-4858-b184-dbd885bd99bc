using System.Text.Json;
using GoTRUST.EMR.Application.MedicalRecordTemplates;
using GoTRUST.EMR.Application.MedicalRecordTemplates.Templates.MedicalRecord;
using GoTRUST.EMR.Application.MedicalRecordTemplates.Templates.Papper;

namespace GoTRUST.EMR.Application.Helpers
{
    public static class MappingMedicalRecordHelper
    {
        public static MedicalRecordTemplateAbstract? MapToMedicalRecord(string medicalRecordTemplateCode)
        {
            return medicalRecordTemplateCode switch
            {
                "01/BV1" => new BenhAnNoiKhoa01BV1(),
                "01/BV2" => new GiayCamKetChapThuanPhauThuatThuThuatVaGayMeHoiSuc01BV2(),
                 _ => null
            };
        }
        
        public static object? DeserializeToMedicalRecord(string jsonValue, string templateCode)
        {
            var template = MapToMedicalRecord(templateCode);
            if (template == null)
                return null;

            var templateType = template.GetType();
    
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            try
            {
                return JsonSerializer.Deserialize(jsonValue, templateType, options);
            }
            catch (JsonException ex)
            {
                throw new InvalidDataException($"Dữ liệu JSON không hợp lệ: {ex.Message}", ex);
            }
        }
    }
}