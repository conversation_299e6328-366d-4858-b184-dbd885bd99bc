using System.Security.Cryptography;
using System.Text.Json;
using System.Text.RegularExpressions;
using NanoidDotNet;

namespace GoTRUST.EMR.Application.Helpers
{
    public static class StringExtensions
    {
        /// <summary>
        /// Remove spec char in string
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string RemoveSpecialCharacters(this string str)
        {
            return Regex.Replace(str, @"[^\p{L}0-9 ]", "", RegexOptions.IgnoreCase, TimeSpan.FromSeconds(0.5));
        }

        /// <summary>
        /// Check if a string is valid date of birth (dd/MM/yyyy)
        /// </summary>
        /// <param name="str"></param>
        /// <returns>true if string is a valid date of birth</returns>
        public static bool IsDateOfBirth(this string str)
        {
            return Regex.IsMatch(str, @"^((0[1-9]|[12]\d|3[01])\/(0[1-9]|1[012])\/((19|20)\d{2}))$");
        }

        public static void DeleteFolder(this string str)
        {
            if (Directory.Exists(str))
            {
                Directory.Delete(str, true);
            }
        }

        /// <summary>
        /// Check if a string is Email address
        /// </summary>
        /// <param name="str">true if string is a Email address</param>
        /// <returns></returns>
        public static bool IsEmail(this string str)
        {
            return Regex.IsMatch(str, @"^([\w\.\-]+)@([\w\-]+)((\.(\w){2,3})+)$");
        }

        /// <summary>
        /// Check if a string is Phone number
        /// </summary>
        /// <param name="str"></param>
        /// <returns>true if string is a phone number</returns>
        public static bool IsPhone(this string str)
        {
            return Regex.IsMatch(str, @"^(\+84|84|0)(\d{9})$");
        }

        /// <summary>
        /// string to camelCase
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string ToCamelCase(this string str)
        {
            if (string.IsNullOrEmpty(str))
                return str;

            if (str.Length == 1)
                return str.ToLowerInvariant();

            return char.ToLowerInvariant(str[0]) + str[1..];
        }

        /// <summary>
        /// Remove vietnamese character
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string RemoveVietnameseCharacter(this string str)
        {
            if (string.IsNullOrEmpty(str))
                return str;

            str = str.ToLowerInvariant();
            str = Regex.Replace(str, @"[àáạảãâầấậẩẫăằắặẳẵ]", "a");
            str = Regex.Replace(str, @"[èéẹẻẽêềếệểễ]", "e");
            str = Regex.Replace(str, @"[ìíịỉĩ]", "i");
            str = Regex.Replace(str, @"[òóọỏõôồốộổỗơờớợởỡ]", "o");
            str = Regex.Replace(str, @"[ùúụủũưừứựửữ]", "u");
            str = Regex.Replace(str, @"[ỳýỵỷỹ]", "y");
            str = Regex.Replace(str, @"[đ]", "d");

            str = Regex.Replace(str, @"[^a-zA-Z0-9_]", "");

            return str;
        }



        /// <summary>
        /// normalize slug with vietnamese
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string NormalizeSlug(this string str)
        {
            if (string.IsNullOrEmpty(str))
                return str;

            str = str.RemoveVietnameseCharacter();
            str = str.ToLowerInvariant();
            str = Regex.Replace(str, @"[^\w\-]", "-");
            str = Regex.Replace(str, @"-+", "-");
            str = str.Trim('-');

            return str;
        }

        /// <summary>
        /// Check if a string is valid url
        /// </summary>
        /// <param name="str"></param>
        /// <returns>true if string is a valid url</returns>
        public static bool IsUrl(this string str)
        {
            return Regex.IsMatch(str, @"^(http|https)://([\w-]+\.)+[\w-]+(/[\w- ./?%&=]*)?$");
        }

        /// <summary>
        /// Convert stringuified json to dynamic object
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static dynamic? StringifyToObject(this string str)
        {
            return JsonSerializer.Deserialize<dynamic>(str);
        }

        /// <summary>
        /// Generate random code by nanoId
        /// </summary>
        public static string GenerateRandomUppercaseCode(int length = 8)
        {
            return Nanoid.Generate("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789", length);
        }

        /// <summary>
        /// Tạo mật khẩu ngẫu nhiên mạnh, đảm bảo có ít nhất một chữ thường,
        /// một chữ in hoa, một chữ số, và một ký tự đặc biệt.
        /// </summary>
        /// <param name="length">Độ dài mật khẩu. Tối thiểu là 8.</param>
        /// <returns>Một chuỗi mật khẩu mạnh.</returns>
        public static string GenerateRandomPassword(int length = 12)
        {
            // Dòng 1-2: Kiểm tra đầu vào
            // Yêu cầu mật khẩu phải đủ dài để chứa ít nhất 4 loại ký tự bắt buộc và thêm các ký tự khác.
            // Chúng ta đặt mức tối thiểu là 8 để đảm bảo tính an toàn.
            if (length < 8)
            {
                throw new System.ArgumentException("Password length must be at least 8 characters.", nameof(length));
            }

            // Dòng 3-7: Định nghĩa các bộ ký tự
            // Chúng ta định nghĩa rõ ràng từng nhóm ký tự để có thể lấy ra một ký tự bắt buộc từ mỗi nhóm.
            const string lowercaseChars = "abcdefghijklmnopqrstuvwxyz";
            const string uppercaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string digitChars = "0123456789";
            const string specialChars = "!@#$%^&*()_+";

            // Dòng 8: Gộp tất cả các ký tự
            // Bộ ký tự này sẽ được dùng để điền vào các vị trí còn lại của mật khẩu sau khi đã đảm bảo đủ 4 loại ký tự bắt buộc.
            const string allChars = lowercaseChars + uppercaseChars + digitChars + specialChars;

            // Dòng 9: Chuẩn bị một danh sách (List)
            // Chúng ta dùng List<char> thay vì StringBuilder vì nó cho phép chúng ta dễ dàng xáo trộn (shuffle) các ký tự ở bước cuối.
            var passwordChars = new List<char>(length);

            // Dòng 10: Sử dụng bộ tạo số ngẫu nhiên an toàn
            // Luôn dùng RandomNumberGenerator cho các tác vụ liên quan đến bảo mật.
            using (var rng = RandomNumberGenerator.Create())
            {
                // Hàm trợ giúp để rút gọn code, lấy một ký tự ngẫu nhiên và an toàn từ một chuỗi cho trước.
                char GetRandomChar(string characterSet)
                {
                    byte[] uintBuffer = new byte[sizeof(uint)];
                    rng.GetBytes(uintBuffer);
                    uint num = BitConverter.ToUInt32(uintBuffer, 0);
                    return characterSet[(int)(num % (uint)characterSet.Length)];
                }

                // Dòng 11-14: Đảm bảo mỗi loại ký tự có ít nhất một
                // Đây là bước cốt lõi. Chúng ta lần lượt thêm một ký tự ngẫu nhiên từ mỗi bộ ký tự bắt buộc vào danh sách.
                passwordChars.Add(GetRandomChar(lowercaseChars));
                passwordChars.Add(GetRandomChar(uppercaseChars));
                passwordChars.Add(GetRandomChar(digitChars));
                passwordChars.Add(GetRandomChar(specialChars));

                // Dòng 15-18: Điền nốt các ký tự còn lại
                // Mật khẩu hiện đã có 4 ký tự. Vòng lặp này sẽ điền `length - 4` ký tự còn lại.
                // Các ký tự này được lấy từ bộ `allChars` để đảm bảo sự đa dạng.
                for (int i = 0; i < length - 4; i++)
                {
                    passwordChars.Add(GetRandomChar(allChars));
                }

                // Dòng 19-27: Xáo trộn toàn bộ mật khẩu
                // Đây là bước CỰC KỲ QUAN TRỌNG. Nếu không xáo trộn, 4 ký tự đầu tiên sẽ luôn là:
                // 1 chữ thường, 1 chữ hoa, 1 số, 1 ký tự đặc biệt. Điều này tạo ra một khuôn mẫu dễ đoán.
                // Thuật toán Fisher-Yates shuffle này sẽ hoán đổi vị trí các ký tự một cách ngẫu nhiên.
                for (int i = passwordChars.Count - 1; i > 0; i--)
                {
                    // Lấy một chỉ số ngẫu nhiên `j` từ 0 đến `i`.
                    byte[] uintBuffer = new byte[sizeof(uint)];
                    rng.GetBytes(uintBuffer);
                    uint num = BitConverter.ToUInt32(uintBuffer, 0);
                    int j = (int)(num % (uint)(i + 1));

                    // Hoán đổi ký tự tại vị trí `i` và `j`.
                    (passwordChars[i], passwordChars[j]) = (passwordChars[j], passwordChars[i]);
                }
            }

            // Dòng 28: Trả về kết quả cuối cùng
            // Chuyển danh sách các ký tự đã được xáo trộn thành một chuỗi mật khẩu hoàn chỉnh.
            return new string(passwordChars.ToArray());
        }

        private static readonly JsonSerializerOptions DefaultJsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        public static T? DeserializeTo<T>(this string jsonString)
        {
            if (string.IsNullOrWhiteSpace(jsonString))
                return default(T);

            try
            {
                return JsonSerializer.Deserialize<T>(jsonString, DefaultJsonOptions);
            }
            catch (JsonException ex)
            {
                throw new InvalidDataException($"Invalid JSON data: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Tạo OTP 6 chữ số ngẫu nhiên
        /// </summary>
        public static string GenerateOtp()
        {
            var random = new Random();
            return random.Next(100000, 999999).ToString();
        }
    }
}