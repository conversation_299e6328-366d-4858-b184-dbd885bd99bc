namespace GoTRUST.EMR.Application.Helpers
{
    public static class RolePermissionHelper
    {
        public class PermissionDto
        {
            public string Id { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
        }
        public static List<PermissionDto> GetPermissions()
        {
            return
            [
                new() { Id = "C", Name = "Thê<PERSON>" },
                new() { Id = "R", Name = "<PERSON>ọ<PERSON>" },
                new() { Id = "U", Name = "Sửa" },
                new() { Id = "D", Name = "Xóa" },
                new() { Id = "A", Name = "<PERSON><PERSON> du<PERSON>ệ<PERSON>" },
            ];
        }
    }
}