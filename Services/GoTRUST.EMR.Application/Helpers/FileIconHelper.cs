using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Helpers;

/// <summary>
/// Helper class để generate icon cho files/folders dựa trên type
/// </summary>
public static class FileIconHelper
{
    /// <summary>
    /// Lấy icon string dựa trên NodeType và MimeType
    /// </summary>
    /// <param name="nodeType">Loại node (File hoặc Folder)</param>
    /// <param name="mimeType">MIME type của file (chỉ áp dụng cho File)</param>
    /// <returns>Icon string hoặc null</returns>
    public static string? GetIcon(FileSystemNodeType nodeType, string? mimeType = null)
    {
        if (nodeType == FileSystemNodeType.Folder)
        {
            return "folder";
        }

        if (nodeType == FileSystemNodeType.File && !string.IsNullOrEmpty(mimeType))
        {
            return GetFileIconByMimeType(mimeType);
        }

        return "file";
    }

    /// <summary>
    /// Lấy icon dựa trên MIME type
    /// </summary>
    /// <param name="mimeType">MIME type</param>
    /// <returns>Icon string</returns>
    private static string GetFileIconByMimeType(string mimeType)
    {
        return mimeType.ToLower() switch
        {
            // PDF files
            "application/pdf" => "file-pdf",
            
            // Microsoft Office Documents
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => "file-word", // .docx
            "application/msword" => "file-word", // .doc
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" => "file-excel", // .xlsx
            "application/vnd.ms-excel" => "file-excel", // .xls
            "application/vnd.openxmlformats-officedocument.presentationml.presentation" => "file-powerpoint", // .pptx
            "application/vnd.ms-powerpoint" => "file-powerpoint", // .ppt
            
            // Images
            var mime when mime.StartsWith("image/") => "file-image",
            
            // Videos
            var mime when mime.StartsWith("video/") => "file-video",
            
            // Audio
            var mime when mime.StartsWith("audio/") => "file-audio",
            
            // Text files
            "text/plain" => "file-text",
            "text/html" => "file-code",
            "text/css" => "file-code",
            "text/javascript" => "file-code",
            "application/json" => "file-code",
            "application/xml" => "file-code",
            
            // Archives
            "application/zip" => "file-archive",
            "application/x-rar-compressed" => "file-archive",
            "application/x-7z-compressed" => "file-archive",
            "application/gzip" => "file-archive",
            
            // Medical files (DICOM, etc.)
            "application/dicom" => "file-medical",
            
            // Default
            _ => "file"
        };
    }
}
