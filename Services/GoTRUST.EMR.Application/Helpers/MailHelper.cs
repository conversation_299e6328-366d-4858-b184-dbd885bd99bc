using System.Net;
using System.Net.Mail;

namespace GoTRUST.EMR.Application.Helpers
{
    public class MailHelper
    {
        private static readonly SemaphoreSlim semaphore = new SemaphoreSlim(25); // <PERSON>i<PERSON>i hạn số luồng đồng thời

        public static async Task<string> SendAsync(
            int port,
            string host,
            string password,
            string fromMail,
            string toMail,
            string displayName,
            string subject,
            string body)
        {
            if (!await semaphore.WaitAsync(TimeSpan.FromMinutes(1)))
            {
                return "Error";
            }
            string messageError = string.Empty;
            try
            {
                MailMessage message = new();
                SmtpClient smtp = new();
                message.From = new MailAddress(fromMail, displayName);
                message.To.Add(new MailAddress(toMail));
                message.Subject = subject;
                message.IsBodyHtml = true;
                message.Body = body;
                smtp.Port = port;
                smtp.Host = host;
                smtp.EnableSsl = true;
                smtp.UseDefaultCredentials = false;
                smtp.Credentials = new NetworkCredential(fromMail, password);
                smtp.Timeout = 1000 * 300;
                await smtp.SendMailAsync(message);
                return messageError;
            }
            catch (Exception ex)
            {
                messageError = ex.Message;
                return messageError;
            }
        }
        public static async Task<(string, string)> SendWithAttachmentsAsync(
            int port,
            string host,
            string password,
            string fromMail,
            string toMail,
            string displayName,
            string subject,
            string body,
            List<string> CcMails,
            string[] attachments)
        {
            if (!await semaphore.WaitAsync(TimeSpan.FromMinutes(2)))
            {
                return ("Error", "Send mail timeout");
            }
            string messageError = "Success";
            string messageLog = "Thành công";
            Dictionary<string, Attachment> attachmentsList = new();
            try
            {
                MailMessage message = new();
                SmtpClient smtp = new();
                message.From = new MailAddress(fromMail, displayName);
                message.To.Add(new MailAddress(toMail));
                foreach (var ccMail in CcMails)
                {
                    message.CC.Add(ccMail);
                }
                message.Subject = subject;
                message.IsBodyHtml = true;
                message.Body = body;
                foreach (var attachment in attachments)
                {
                    if (File.Exists(attachment))
                    {
                        var attach = new Attachment(attachment);
                        message.Attachments.Add(attach);
                        attachmentsList.Add(attachment, attach);
                    }
                }
                smtp.Port = port;
                smtp.Host = host;
                smtp.EnableSsl = true;
                smtp.UseDefaultCredentials = false;
                smtp.Credentials = new NetworkCredential(fromMail, password);
                smtp.Timeout = 1000 * 300;
                await smtp.SendMailAsync(message);
            }
            catch (Exception ex)
            {
                Console.WriteLine("SendWithAttachmentsAsync " + ex.Message);
                messageError = "Error";
                messageLog = ex.Message;

            }
            finally
            {
                foreach (var item in attachmentsList)
                {
                    item.Value.Dispose();
                    File.Delete(item.Key);
                }
                semaphore.Release();
            }
            return (messageError, messageLog);
        }
    }
}