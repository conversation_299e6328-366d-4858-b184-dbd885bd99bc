using System.Security.Cryptography;
using System.Text.Json;
using System.Text.RegularExpressions;
using NanoidDotNet;

namespace GoTRUST.EMR.Application.Helpers
{
    public static class DateTimeExtensions
    {
        /// <summary>
        /// Remove spec char in string
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string RemoveSpecialCharacters(this string str)
        {
            return Regex.Replace(str, @"[^\p{L}0-9 ]", "", RegexOptions.IgnoreCase, TimeSpan.FromSeconds(0.5));
        }

        /// <summary>
        /// Calculate age from birth date
        /// </summary>
        /// <param name="birthDate"></param>
        /// <param name="asOf"></param>
        /// <returns></returns>
        public static int CalculateAge(this DateTime birthDate, DateTime? asOf = null)
        {
            var today = asOf ?? DateTime.Today;
            var age = today.Year - birthDate.Year;
            if (birthDate.Date > today.AddYears(-age)) age--;
            return age;
        }
    }
}