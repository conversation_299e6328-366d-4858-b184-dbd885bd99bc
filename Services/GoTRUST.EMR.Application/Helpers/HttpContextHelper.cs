namespace GoTRUST.EMR.Application.Helpers
{
    public static class HttpContextHelper
    {
        public static string? GetRequestIpAddress(this HttpContext httpContext)
        {
            if (httpContext.Request.Headers.TryGetValue("X-Forwarded-For", out Microsoft.Extensions.Primitives.StringValues value))
                return value;
            else
                return httpContext.Connection.RemoteIpAddress?.MapToIPv4().ToString();
        }
    }
}