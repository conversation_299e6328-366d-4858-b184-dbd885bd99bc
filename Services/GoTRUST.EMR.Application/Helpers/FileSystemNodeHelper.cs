using Microsoft.Extensions.Configuration;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;

namespace GoTRUST.EMR.Application.Helpers
{
    public static class FileSystemNodeHelper
    {
        /// <summary>
        /// Lấy icon cho node (file/folder) dựa v<PERSON>o lo<PERSON> node hoặc mimeType.
        /// </summary>
        public static string GetIconForNode(FileSystemNodeType nodeType, string? mimeType)
        {
            if (!string.IsNullOrEmpty(mimeType))
            {
                return mimeType.ToLower() switch
                {
                    ".pdf" => "https://s3.cloud.cmctelecom.vn/emr/file-icons/pdf.png",
                    ".doc" or ".docx" => "https://s3.cloud.cmctelecom.vn/emr/file-icons/doc.png",
                    ".xls" or ".xlsx" => "https://s3.cloud.cmctelecom.vn/emr/file-icons/xls.png",
                    ".ppt" or ".pptx" => "https://s3.cloud.cmctelecom.vn/emr/file-icons/ppt.png",
                    ".zip" or ".rar" => "https://s3.cloud.cmctelecom.vn/emr/file-icons/zip.png",
                    ".csv" => "https://s3.cloud.cmctelecom.vn/emr/file-icons/csv.png",
                    _ => "https://s3.cloud.cmctelecom.vn/emr/file-icons/unknown.png",
                };
            }
            return nodeType switch
            {
                FileSystemNodeType.Folder => "folder",
                FileSystemNodeType.File => "file",
                _ => "unknown"
            };
        }

        /// <summary>
        /// Lấy danh sách các định dạng file theo từng loại (type), gom các đuôi tương tự vào cùng một type.
        /// </summary>
        public static List<FileFormatType> GetFileFormatTypes()
        {
            return
            [
                new FileFormatType
                {
                    Type = "pdf",
                    Extensions = [".pdf"],
                    IconUrl = "https://s3.cloud.cmctelecom.vn/emr/file-icons/pdf.png"
                },
                new FileFormatType
                {
                    Type = "doc",
                    Extensions = [".doc", ".docx"],
                    IconUrl = "https://s3.cloud.cmctelecom.vn/emr/file-icons/doc.png"
                },
                new FileFormatType
                {
                    Type = "xls",
                    Extensions = [".xls", ".xlsx"],
                    IconUrl = "https://s3.cloud.cmctelecom.vn/emr/file-icons/xls.png"
                },
                new FileFormatType
                {
                    Type = "ppt",
                    Extensions = [".ppt", ".pptx"],
                    IconUrl = "https://s3.cloud.cmctelecom.vn/emr/file-icons/ppt.png"
                },
                new FileFormatType
                {
                    Type = "zip",
                    Extensions = [".zip", ".rar"],
                    IconUrl = "https://s3.cloud.cmctelecom.vn/emr/file-icons/zip.png"
                },
                new FileFormatType
                {
                    Type = "csv",
                    Extensions = [".csv"],
                    IconUrl = "https://s3.cloud.cmctelecom.vn/emr/file-icons/csv.png"
                },
                new FileFormatType
                {
                    Type = "unknown",
                    Extensions = [],
                    IconUrl = "https://s3.cloud.cmctelecom.vn/emr/file-icons/unknown.png"
                }
            ];
        }
        /// <summary>
        /// Kiểm tra node (file/folder) có accessible với user hiện tại không
        /// </summary>
        public static bool IsNodeAccessible(FileSystemNode? node, Guid hospitalId, Guid currentUserId, List<Guid> roleIds)
        {
            if (node == null)
                return false;
            if (node.NodeType == FileSystemNodeType.File && hospitalId != node.Owner?.HospitalId)
                return false;
            if (node.NodeType == FileSystemNodeType.Folder && hospitalId != node.Owner?.HospitalId)
                return false;
            var hasAccess = node.OwnerId == currentUserId
                            || node.UserNodes.Any(un => un.UserId == currentUserId)
                            || node.RoleNodes.Any(un => roleIds.Contains(un.RoleId))
                            || node.ShareStatus == FileSystemNodeShareStatus.Public;
            return hasAccess;
        }

        /// <summary>
        /// Lấy nội dung file và giải mã (dùng chung cho DownloadFile/Folder)
        /// </summary>
        public static async Task<byte[]> GetFileContentAsync(
            FileSystemNode fileNode,
            IConfiguration configuration,
            ILogger logger,
            CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(fileNode.DestinationUrl))
                return [];

            using var httpClient = new HttpClient();
            try
            {
                var response = await httpClient.GetAsync(fileNode.DestinationUrl, cancellationToken);
                response.EnsureSuccessStatusCode();
                var encryptedBytes = await response.Content.ReadAsByteArrayAsync(cancellationToken);

                var fileName = fileNode.Name + (fileNode.MimeType ?? "");
                var formFile = new FormFile(new MemoryStream(encryptedBytes), 0, encryptedBytes.Length, "file", fileName);

                var secretKey = configuration.GetValue<string>("FileSystemNode_SercretKey") ?? string.Empty;
                var watermark = string.Empty;
                var iv = fileNode.EncryptionIV ?? string.Empty;

                var decryptedStream = await PdfEncryptionHelper.DecryptPdfIFormFileAsync(
                    formFile,
                    secretKey,
                    iv,
                    watermark
                );
                if (decryptedStream == null)
                {
                    logger.LogError("Failed to decrypt file from DestinationUrl: {Url}", fileNode.DestinationUrl);
                    return [];
                }
                decryptedStream.Position = 0;
                using var ms = new MemoryStream();
                await decryptedStream.CopyToAsync(ms, cancellationToken);
                return ms.ToArray();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Không thể tải hoặc giải mã file từ DestinationUrl: {Url}", fileNode.DestinationUrl);
                return [];
            }
        }
        /// <summary>
        /// Lấy tất cả các node con (descendants) của một node dựa vào Path.
        /// </summary>
        public static async Task<List<FileSystemNode>> GetAllDescendantNodesAsync(
            IApplicationDbContext context,
            FileSystemNode parentNode,
            CancellationToken cancellationToken)
        {
            if (parentNode == null) return [];
            return await context.FileSystemNodes
                .Where(n => n.Path.StartsWith(parentNode.Path) && n.Id != parentNode.Id)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Gom node gốc và tất cả descendants (không trùng lặp).
        /// </summary>
        public static async Task<List<FileSystemNode>> GetNodeAndAllDescendantsAsync(
            IApplicationDbContext context,
            FileSystemNode node,
            CancellationToken cancellationToken)
        {
            var result = new List<FileSystemNode> { node };
            if (node.NodeType == FileSystemNodeType.Folder)
            {
                var descendants = await GetAllDescendantNodesAsync(context, node, cancellationToken);
                result.AddRange(descendants);
            }
            return [.. result.DistinctBy(n => n.Id)];
        }

        /// <summary>
        /// Kiểm tra parentId có phải là con của bất kỳ nodeId nào không (dựa vào Path của parent).
        /// </summary>
        public static bool IsParentAChildOfNodes(FileSystemNode parentNode, List<Guid> nodeIds)
        {
            if (parentNode == null) return false;
            var parentPathParts = parentNode.Path.Split('/', StringSplitOptions.RemoveEmptyEntries);
            var parentPathGuids = parentPathParts
                .Select(part => Guid.TryParse(part, out var guid) ? guid : Guid.Empty)
                .Where(guid => guid != Guid.Empty)
                .ToList();
            return nodeIds.Any(parentPathGuids.Contains);
        }
    }
}