using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.Application.Helpers
{
    public static class ClaimsPrincipalHelper
    {
        public static Guid? RetrieveUserIdFromPrincipal(this ClaimsPrincipal user)
        {
            var userId = user?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return null;
            }

            return Guid.Parse(userId);
        }

        public static string? RetrieveEmailFromPrincipal(this ClaimsPrincipal user)
        {
            return user?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.Email)?.Value;
        }

        public static string? RetrieveUserNameFromPrincipal(this ClaimsPrincipal user)
        {
            return user?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.Name)?.Value;
        }

        public static string[]? RetrieveSchoolIdFromPrincipal(this ClaimsPrincipal user)
        {
            var hospitalId = user?.Claims?.FirstOrDefault(x => x.Type == "hospital_id")?.Value;

            if (string.IsNullOrEmpty(hospitalId))
            {
                return null;
            }

            return hospitalId.Split(',');
        }
        public static string? RetrieveUserFullNameFromPrincipal(this ClaimsPrincipal user)
        {
            return user?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.GivenName)?.Value;
        }

        public static Guid? RetrieveHospitalIdFromPrincipal(this ClaimsPrincipal user)
        {
            if (user == null)
            {
                return null;
            }

            var hospitalIdClaim = user.Claims.FirstOrDefault(x => x.Type == ClaimConstants.HospitalId)?.Value;

            if (string.IsNullOrEmpty(hospitalIdClaim) || !Guid.TryParse(hospitalIdClaim, out var hospitalId))
            {
                return null;
            }

            return hospitalId;
        }

        public static List<string>? RetrieveRoleNamesFromPrincipal(this ClaimsPrincipal user)
        {
            return user?.Claims?.Where(x => x.Type == ClaimConstants.RoleNames)?.Select(x => x.Value).ToList();
        }
    }
}