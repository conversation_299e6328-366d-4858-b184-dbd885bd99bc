namespace GoTRUST.EMR.Application.Globals
{
    public static class PolicyNames
    {
        public const string REQUIRE_HOSPITAL_DIRECTOR_ROLE = "RequireHospitalDirectorRole";
        public const string REQUIRE_PLANNING_DEPARTMENT_ROLE = "RequirePlanningDepartmentRole";
        public const string REQUIRE_NURSE_DOCTOR_ROLE = "RequireNurseDoctorRole";
        public const string REQUIRE_IT_DEPARTMENT_ROLE = "RequireITDepartmentRole";
        public const string REQUIRE_PATIENT_ROLE = "RequirePatientRole";
        public const string REQUIRE_MEDICAL_RECORD_REQUESTOR_ROLE = "RequireMedicalRecordRequestorRole";
        public const string REQUIRE_ADMIN_ROLE = "RequireAdminRole";
        public const string REQUIRE_NOT_ADMIN_ROLE = "RequireNotAdminRole";
    }
}