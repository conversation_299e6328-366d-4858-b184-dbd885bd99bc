using System.Collections;
using System.Text.Json;

namespace GoTRUST.EMR.Application.MedicalRecordTemplates;

public static class BuildContentMedicalRecord
{
    public static Dictionary<string, string> BuildReplacementDictionary(object? recordData)
    {
        var replacements = new Dictionary<string, string>();

        if (recordData != null)
        {
            var jsonProperties = recordData.GetType().GetProperties();
            foreach (var prop in jsonProperties)
            {
                try
                {
                    var rawValue = prop.GetValue(recordData);

                    var value = rawValue switch
                    {
                        null => "",
                        IEnumerable when rawValue.GetType() != typeof(string) => JsonSerializer.Serialize(rawValue),
                        _ => rawValue.ToString()
                    };

                    var key = $"{{{prop.Name}}}";
                    replacements[key] = value ?? string.Empty;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Lỗi khi lấy property value: {prop.Name} - {ex.Message}");
                }
            }
        }

        return replacements;
    }

    public static string ReplacePlaceholders(string htmlContent, Dictionary<string, string> replacements)
    {
        foreach (var replacement in replacements)
        {
            try
            {
                // Simple text replacement
                htmlContent = htmlContent.Replace(replacement.Key, replacement.Value);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi xử lý placeholder: {replacement.Key} - {ex.Message}");
            }
        }

        return htmlContent;
    }
}