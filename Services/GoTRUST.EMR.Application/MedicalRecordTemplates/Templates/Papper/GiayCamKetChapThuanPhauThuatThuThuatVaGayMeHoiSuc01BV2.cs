namespace GoTRUST.EMR.Application.MedicalRecordTemplates.Templates.Papper;

public record GiayCamKetChapThuanPhauThuatThuThuatVaGayMeHoiSuc01BV2 : MedicalRecordTemplateAbstract
{
    public string? TEN_CO_QUAN_CHU_QUAN { get; set; }
    public string? TEN_CO_SO_KB_CB { get; set; }
    public bool? CAP_CUU_CAP_CUU { get; set; }
    public bool? CAP_CUU_BAN_CAP { get; set; }
    public bool? CAP_CUU_CHUONG_TRINH { get; set; }
    public string? HO_TEN_BAC_SI_PHAU_THUAT { get; set; }
    public string? CHUC_DANH_BAC_SI_PHAU_THUAT { get; set; }
    public string? KHOA_BAC_SI_PHAU_THUAT { get; set; }
    public string? HO_TEN_BAC_SI_GAY_ME { get; set; }
    public string? CHUC_DANH_BAC_SI_GAY_ME { get; set; }
    public string? HO_TEN_NGUOI_BENH { get; set; }
    public string? NAM_SINH_NGUOI_BENH { get; set; }
    public string? HO_TEN_THAN_NHAN { get; set; }
    public string? NAM_SINH_THAN_NHAN { get; set; }
    public string? QUAN_HE_VOI_NGUOI_BENH { get; set; }
    public string? CHAN_DOAN_BENH { get; set; }
    public bool? CHAN_DOAN { get; set; }
    public bool? LY_DO_PHAU_THUAT { get; set; }
    public bool? RUI_RO_NEU_KHONG_THUC_HIEN { get; set; }
    public bool? KET_QUA_SAU_PHAU_THUAT { get; set; }
    public string? KET_QUA_SAU_PHAU_THUAT_DU_KIEN { get; set; }
    public bool? PHUONG_PHAP_PHAU_THUAT_MO { get; set; }
    public bool? PHUONG_PHAP_PHAU_THUAT_NOI_SOI { get; set; }
    public bool? PHUONG_PHAP_PHAU_THUAT_THU_THUAT { get; set; }
    public bool? PHUONG_PHAP_GAY_ME_ME_NOI_KHI_QUAN { get; set; }
    public bool? PHUONG_PHAP_GAY_ME_ME_MASK_THANH_QUAN { get; set; }
    public bool? PHUONG_PHAP_GAY_ME_ME_TINH_MACH { get; set; }
    public bool? PHUONG_PHAP_GAY_ME_TE_TUY_SONG { get; set; }
    public bool? PHUONG_PHAP_GAY_ME_TE_NGOAI_MANG_CUNG { get; set; }
    public bool? PHUONG_PHAP_GAY_ME_TE_DAM_ROI_THAN_KINH { get; set; }
    public bool? PHUONG_PHAP_GAY_ME_TIEN_ME_TE_TAI_CHO { get; set; }
    public bool? PHUONG_PHAP_GAY_ME_KHAC { get; set; }
    public bool? PHUONG_PHAP_DIEU_TRI_KHAC_NGOAI_PHAU_THUAT_KHONG { get; set; }
    public bool? PHUONG_PHAP_DIEU_TRI_KHAC_NGOAI_PHAU_THUAT_CO { get; set; }
    public string? PHUONG_PHAP_DIEU_TRI_KHAC_NGOAI_PHAU_THUAT_CO_CU_THE { get; set; }
    public bool? NGUY_CO_TAI_BIEN_TRONG_VA_SAU_PHAU_THUAT_PHAN_UNG_THUOC { get; set; }
    public bool? NGUY_CO_TAI_BIEN_TRONG_VA_SAU_PHAU_THUAT_SUY_HO_HAP_TUAN_HOAN { get; set; }
    public bool? NGUY_CO_TAI_BIEN_TRONG_VA_SAU_PHAU_THUAT_CHAY_MAU { get; set; }
    public bool? NGUY_CO_TAI_BIEN_TRONG_VA_SAU_PHAU_THUAT_NHIEM_TRUNG { get; set; }
    public bool? NGUY_CO_TAI_BIEN_TRONG_VA_SAU_PHAU_THUAT_TU_VONG { get; set; }
    public bool? NGUY_CO_TAI_BIEN_TRONG_VA_SAU_PHAU_THUAT_NGUY_CO_RUI_RO { get; set; }
    public string? NGUY_CO_THUONG_GAP_CO_THE_XAY_RA { get; set; }
    public string? QUYET_DINH_CUA_BENH_NHAN { get; set; }
    public string? TEN_CO_SO_KHAM_CHUA_BENH { get; set; }
    public string? NGAY_LAP_GIAY { get; set; }
    public string? THANG_LAP_GIAY { get; set; }
    public string? NAM_LAP_GIAY { get; set; }
    public string? HO_TEN_BAC_SY_THUC_HIEN { get; set; }
    public string? HO_TEN_BAC_SY_GAY_ME { get; set; }
}

