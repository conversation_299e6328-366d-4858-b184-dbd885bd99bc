namespace GoTRUST.EMR.Application.MedicalRecordTemplates.Templates.MedicalRecord;

/// <summary>
/// Đ<PERSON>i diện cho toàn bộ thông tin của một bệnh án nội khoa.
/// </summary>
public record BenhAnNoiKhoa01BV1 : MedicalRecordTemplateAbstract
{
    // THÔNG TIN HỒ SƠ

    #region Thông tin hồ sơ

    /// <summary>
    /// Khoa
    /// </summary>
    public string? KHOA { get; set; }

    /// <summary>
    /// Giường
    /// </summary>
    public string? GIUONG { get; set; }

    /// <summary>
    /// Số lưu trữ
    /// </summary>
    public string? SO_LUU_TRU { get; set; }

    /// <summary>
    /// Mã YT ô 1
    /// </summary>
    public string? MA_YT_1 { get; set; }

    /// <summary>
    /// Mã YT ô 2
    /// </summary>
    public string? MA_YT_2 { get; set; }

    /// <summary>
    /// Mã YT ô 3
    /// </summary>
    public string? MA_YT_3 { get; set; }

    /// <summary>
    /// Mã YT ô 4
    /// </summary>
    public string? MA_YT_4 { get; set; }

    #endregion

    // HÀNH CHÍNH (HC)

    #region Hành chính

    /// <summary>
    /// Họ và tên bệnh nhân.
    /// Ghi chú: In hoa
    /// </summary>
    public string? HO_TEN { get; set; }

    /// <summary>
    /// Ngày sinh ô 1
    /// </summary>
    public string? SINH_NGAY_1 { get; set; }

    /// <summary>
    /// Ngày sinh ô 2
    /// </summary>
    public string? SINH_NGAY_2 { get; set; }

    /// <summary>
    /// Ngày sinh ô 3
    /// </summary>
    public string? SINH_NGAY_3 { get; set; }

    /// <summary>
    /// Ngày sinh ô 4
    /// </summary>
    public string? SINH_NGAY_4 { get; set; }

    /// <summary>
    /// Ngày sinh ô 5
    /// </summary>
    public string? SINH_NGAY_5 { get; set; }

    /// <summary>
    /// Ngày sinh ô 6
    /// </summary>
    public string? SINH_NGAY_6 { get; set; }

    /// <summary>
    /// Ngày sinh ô 7
    /// </summary>
    public string? SINH_NGAY_7 { get; set; }

    /// <summary>
    /// Ngày sinh ô 8
    /// </summary>
    public string? SINH_NGAY_8 { get; set; }

    /// <summary>
    /// Tuổi ô 1
    /// </summary>
    public string? TUOI_1 { get; set; }

    /// <summary>
    /// Tuổi ô 2
    /// </summary>
    public string? TUOI_2 { get; set; }

    /// <summary>
    /// Giới tính ô 1.
    /// Ghi chú: 1. Nam
    /// </summary>
    public bool? GIOI_TINH_NAM { get; set; }

    /// <summary>
    /// Giới tính ô 2
    /// Ghi chú: 2. Nữ
    /// </summary>
    public bool? GIOI_TINH_NU { get; set; }

    /// <summary>
    /// Nghề nghiệp
    /// </summary>
    public string? NGHE_NGHIEP { get; set; }

    /// <summary>
    /// Mã nghề nghiệp ô 1
    /// </summary>
    public string? MA_NGHE_NGHIEP_1 { get; set; }

    /// <summary>
    /// Mã nghề nghiệp ô 2
    /// </summary>
    public string? MA_NGHE_NGHIEP_2 { get; set; }

    /// <summary>
    /// Dân tộc
    /// </summary>
    public string? DAN_TOC { get; set; }

    /// <summary>
    /// Mã dân tộc ô 1
    /// </summary>
    public string? MA_DAN_TOC_1 { get; set; }

    /// <summary>
    /// Mã dân tộc ô 2
    /// </summary>
    public string? MA_DAN_TOC_2 { get; set; }

    /// <summary>
    /// Ngoại kiều
    /// </summary>
    public string? NGOAI_KIEU { get; set; }

    /// <summary>
    /// Mã ngoại kiều ô 1
    /// </summary>
    public string? MA_NGOAI_KIEU_1 { get; set; }

    /// <summary>
    /// Mã ngoại kiều ô 2
    /// </summary>
    public string? MA_NGOAI_KIEU_2 { get; set; }

    /// <summary>
    /// Số nhà trong địa chỉ
    /// </summary>
    public string? DIA_CHI_SO_NHA { get; set; }

    /// <summary>
    /// Thôn/phố trong địa chỉ
    /// </summary>
    public string? DIA_CHI_THON_PHO { get; set; }

    /// <summary>
    /// Xã/phường trong địa chỉ
    /// </summary>
    public string? DIA_CHI_XA_PHUONG { get; set; }

    /// <summary>
    /// Quận/huyện trong địa chỉ
    /// </summary>
    public string? DIA_CHI_HUYEN { get; set; }

    /// <summary>
    /// Mã quận/huyện trong địa chỉ ô 1
    /// </summary>
    public string? MA_DIA_CHI_HUYEN_1 { get; set; }

    /// <summary>
    /// Mã quận/huyện trong địa chỉ ô 2
    /// </summary>
    public string? MA_DIA_CHI_HUYEN_2 { get; set; }

    /// <summary>
    /// Tỉnh/thành phố trong địa chỉ
    /// </summary>
    public string? DIA_CHI_TINH_THANH { get; set; }

    /// <summary>
    /// Mã tỉnh/thành phố trong địa chỉ ô 1
    /// </summary>
    public string? MA_DIA_CHI_TINH_THANH_1 { get; set; }

    /// <summary>
    /// Mã tỉnh/thành phố trong địa chỉ ô 2
    /// </summary>
    public string? MA_DIA_CHI_TINH_THANH_2 { get; set; }

    /// <summary>
    /// Nơi làm việc
    /// </summary>
    public string? NOI_LAM_VIEC { get; set; }

    /// <summary>
    /// Đối tượng BHYT.
    /// Ghi chú: 1. BHYT, 2. Thu phí, 3. Miễn, 4. Khác
    /// </summary>
    public bool? DOI_TUONG_BHYT { get; set; }

    /// <summary>
    /// Đối tượng thu phí BHYT
    /// Ghi chú: 2. Thu phí
    /// </summary>
    public bool? DOI_TUONG_BHYT_THU_PHI { get; set; }

    /// <summary>
    /// Đối tượng miễn BHYT
    /// Ghi chú: 3. Miễn
    /// </summary>
    public bool? DOI_TUONG_BHYT_MIEN { get; set; }

    /// <summary>
    /// Đối tượng khác
    /// Ghi chú: 4. Khác
    /// </summary>
    public bool? DOI_TUONG_BHYT_KHAC { get; set; }

    /// <summary>
    /// Giá trị thẻ BHYT đến ngày
    /// </summary>
    public string? BHYT_GIA_TRI_DEN_NGAY { get; set; }

    /// <summary>
    /// Giá trị thẻ BHYT đến tháng
    /// </summary>
    public string? BHYT_GIA_TRI_DEN_THANG { get; set; }

    /// <summary>
    /// Giá trị thẻ BHYT đến năm
    /// </summary>
    public string? BHYT_GIA_TRI_DEN_NAM { get; set; }

    /// <summary>
    /// Số thẻ BHYT ô 1
    /// </summary>
    public string? SO_THE_BHYT_1 { get; set; }

    /// <summary>
    /// Số thẻ BHYT ô 2
    /// </summary>
    public string? SO_THE_BHYT_2 { get; set; }

    /// <summary>
    /// Số thẻ BHYT ô 3
    /// </summary>
    public string? SO_THE_BHYT_3 { get; set; }

    /// <summary>
    /// Số thẻ BHYT ô 4
    /// </summary>
    public string? SO_THE_BHYT_4 { get; set; }

    /// <summary>
    /// Số thẻ BHYT ô 5
    /// </summary>
    public string? SO_THE_BHYT_5 { get; set; }

    /// <summary>
    /// Họ tên người báo tin
    /// </summary>
    public string? HO_TEN_NGUOI_BAO_TIN { get; set; }

    /// <summary>
    /// Địa chỉ người báo tin
    /// </summary>
    public string? DIA_CHI_NGUOI_BAO_TIN { get; set; }

    /// <summary>
    /// Số điện thoại
    /// </summary>
    public string? SO_DIEN_THOAI { get; set; }

    #endregion

    // QUẢN LÝ NGƯỜI BỆNH (QL)

    #region Quản lý người bệnh

    /// <summary>
    /// Giờ vào viện
    /// </summary>
    public string? GIO_VAO_VIEN { get; set; }

    /// <summary>
    /// Phút vào viện
    /// </summary>
    public string? PHU_VAO_VIEN { get; set; }

    /// <summary>
    /// Ngày vào viện ô 1
    /// </summary>
    public string? NGAY_VAO_VIEN_1 { get; set; }

    /// <summary>
    /// Ngày vào viện ô 2
    /// </summary>
    public string? NGAY_VAO_VIEN_2 { get; set; }

    /// <summary>
    /// Ngày vào viện ô 3
    /// </summary>
    public string? NGAY_VAO_VIEN_3 { get; set; }

    /// <summary>
    /// Hình thức vào viện / trực tiếp vào.
    /// Ghi chú: 1. Cấp cứu, 2. KKB, 3. Khoa điều trị
    /// </summary>
    public bool? HINH_THUC_VAO_VIEN_CAP_CUU { get; set; }

    /// <summary>
    /// Mã hình thức vào viện / trực tiếp vào.
    /// Ghi chú: 1. Cấp cứu, 2. KKB, 3. Khoa điều trị
    /// </summary>
    public bool? HINH_THUC_VAO_VIEN_KKB { get; set; }

    /// <summary>
    /// Hình thức vào viện / trực tiếp vào.
    /// Ghi chú: 1. Cấp cứu, 2. KKB, 3. Khoa điều trị
    /// </summary>
    public bool? HINH_THUC_VAO_VIEN_KHOA_DIEU_TRI { get; set; }

    /// <summary>
    /// Nơi giới thiệu.
    /// Ghi chú: 1. Cơ quan y tế, 2. Tự đến, 3. Khác
    /// </summary>
    public bool? NOI_GIOI_THIEU_CO_QUAN_Y_TE { get; set; }

    /// <summary>
    /// Nơi giới thiệu.
    /// Ghi chú: 2. Tự đến
    /// </summary>
    public bool? NOI_GIOI_THIEU_TU_DEN { get; set; }

    /// <summary>
    /// Nơi giới thiệu.
    /// Ghi chú: 3. Khác
    /// </summary>
    public bool? NOI_GIOI_THIEU_KHAC { get; set; }

    /// <summary>
    /// Vào viện do bệnh này lần thứ
    /// </summary>
    public string? VAO_VIEN_LAN_THU { get; set; }

    /// <summary>
    /// Tên khoa vào
    /// </summary>
    public string? TEN_KHOA_VAO { get; set; }

    /// <summary>
    /// Giờ vào khoa
    /// </summary>
    public string? GIO_VAO_KHOA { get; set; }

    /// <summary>
    /// Phút vào khoa
    /// </summary>
    public string? PHU_VAO_KHOA { get; set; }

    /// <summary>
    /// Ngày vào khoa ô 1
    /// </summary>
    public string? NGAY_VAO_KHOA_1 { get; set; }

    /// <summary>
    /// Ngày vào khoa ô 2
    /// </summary>
    public string? NGAY_VAO_KHOA_2 { get; set; }

    /// <summary>
    /// Ngày vào khoa ô 3
    /// </summary>
    public string? NGAY_VAO_KHOA_3 { get; set; }

    /// <summary>
    /// Mã khoa vào ô 1
    /// </summary>
    public string? MA_KHOA_VAO_1 { get; set; }

    /// <summary>
    /// Mã khoa vào ô 2
    /// </summary>
    public string? MA_KHOA_VAO_2 { get; set; }

    /// <summary>
    /// Số ngày điều trị ở khoa vào ô 1
    /// </summary>
    public string? SO_NGAY_DIEU_TRI_O_KHOA_VAO_1 { get; set; }

    /// <summary>
    /// Số ngày điều trị ở khoa vào ô 2
    /// </summary>
    public string? SO_NGAY_DIEU_TRI_O_KHOA_VAO_2 { get; set; }

    /// <summary>
    /// Chuyên khoa dòng 1
    /// </summary>
    public string? CHUYEN_KHOA_DONG_1 { get; set; }

    /// <summary>
    /// Chuyên khoa dòng 2
    /// </summary>
    public string? CHUYEN_KHOA_DONG_2 { get; set; }

    /// <summary>
    /// Chuyên khoa dòng 3
    /// </summary>
    public string? CHUYEN_KHOA_DONG_3 { get; set; }

    /// <summary>
    /// Giờ chuyên khoa dòng 1
    /// </summary>
    public string? GIO_CHUYEN_KHOA_DONG_1 { get; set; }

    /// <summary>
    /// Phút chuyên khoa dòng 1
    /// </summary>
    public string? PHU_CHUYEN_KHOA_DONG_1 { get; set; }

    /// <summary>
    /// Ngày chuyên khoa dòng 1 ô 1
    /// </summary>
    public string? NGAY_CHUYEN_KHOA_DONG_1_1 { get; set; }

    /// <summary>
    /// Ngày chuyên khoa dòng 1 ô 2
    /// </summary>
    public string? NGAY_CHUYEN_KHOA_DONG_1_2 { get; set; }

    /// <summary>
    /// Ngày chuyên khoa dòng 1 ô 3
    /// </summary>
    public string? NGAY_CHUYEN_KHOA_DONG_1_3 { get; set; }


    /// <summary>
    /// Số ngày điều trị chuyên khoa dòng 1 ô 1
    /// </summary>
    public string? SO_NGAY_DIEU_TRI_CHUYEN_KHOA_DONG_1_1 { get; set; }

    /// <summary>
    /// Số ngày điều trị chuyên khoa dòng 1 ô 2
    /// </summary>
    public string? SO_NGAY_DIEU_TRI_CHUYEN_KHOA_DONG_1_2 { get; set; }

    /// <summary>
    /// Giờ chuyên khoa dòng 2
    /// </summary>
    public string? GIO_CHUYEN_KHOA_DONG_2 { get; set; }

    /// <summary>
    /// Phút chuyên khoa dòng 2
    /// </summary>
    public string? PHU_CHUYEN_KHOA_DONG_2 { get; set; }

    /// <summary>
    /// Ngày chuyên khoa dòng 2 ô 1
    /// </summary>
    public string? NGAY_CHUYEN_KHOA_DONG_2_1 { get; set; }

    /// <summary>
    /// Ngày chuyên khoa dòng 2 ô 2
    /// </summary>
    public string? NGAY_CHUYEN_KHOA_DONG_2_2 { get; set; }

    /// <summary>
    /// Ngày chuyên khoa dòng 2 ô 3
    /// </summary>
    public string? NGAY_CHUYEN_KHOA_DONG_2_3 { get; set; }

    /// <summary>
    /// Số ngày điều trị chuyên khoa dòng 2 ô 1
    /// </summary>
    public string? SO_NGAY_DIEU_TRI_CHUYEN_KHOA_DONG_2_1 { get; set; }

    /// <summary>
    /// Số ngày điều trị chuyên khoa dòng 2 ô 2
    /// </summary>
    public string? SO_NGAY_DIEU_TRI_CHUYEN_KHOA_DONG_2_2 { get; set; }

    /// <summary>
    /// Giờ chuyên khoa dòng 3
    /// </summary>
    public string? GIO_CHUYEN_KHOA_DONG_3 { get; set; }

    /// <summary>
    /// Phút chuyên khoa dòng 3
    /// </summary>
    public string? PHU_CHUYEN_KHOA_DONG_3 { get; set; }

    /// <summary>
    /// Ngày chuyên khoa dòng 3 ô 1
    /// </summary>
    public string? NGAY_CHUYEN_KHOA_DONG_3_1 { get; set; }

    /// <summary>
    /// Ngày chuyên khoa dòng 3 ô 2
    /// </summary>
    public string? NGAY_CHUYEN_KHOA_DONG_3_2 { get; set; }

    /// <summary>
    /// Ngày chuyên khoa dòng 3 ô 3
    /// </summary>
    public string? NGAY_CHUYEN_KHOA_DONG_3_3 { get; set; }

    /// <summary>
    /// Số ngày điều trị chuyên khoa dòng 3 ô 1
    /// </summary>
    public string? SO_NGAY_DIEU_TRI_CHUYEN_KHOA_DONG_3_1 { get; set; }

    /// <summary>
    /// Số ngày điều trị chuyên khoa dòng 3 ô 2
    /// </summary>
    public string? SO_NGAY_DIEU_TRI_CHUYEN_KHOA_DONG_3_2 { get; set; }

    /// <summary>
    /// Chuyển viện.
    /// Ghi chú: 1. Tuyến trên, 2. Tuyến dưới, 3. CK
    /// </summary>
    public bool? CHUYEN_VIEN_TUYEN_TREN { get; set; }

    /// <summary>
    /// Chuyển viện.
    /// Ghi chú: 1. Tuyến trên, 2. Tuyến dưới, 3. CK
    /// </summary>
    public bool? CHUYEN_VIEN_TUYEN_DUOI { get; set; }

    /// <summary>
    /// Chuyển viện.
    /// Ghi chú: 1. Tuyến trên, 2. Tuyến dưới, 3. CK
    /// </summary>
    public bool? CHUYEN_VIEN_CK { get; set; }

    /// <summary>
    /// Nơi chuyển đến
    /// </summary>
    public string? NOI_CHUYEN_DEN_QL { get; set; }

    /// <summary>
    /// Giờ ra viện
    /// </summary>
    public string? GIO_RA_VIEN { get; set; }

    /// <summary>
    /// Phút ra viện
    /// </summary>
    public string? PHU_RA_VIEN { get; set; }

    /// <summary>
    /// Ngày ra viện ô 1
    /// </summary>
    public string? NGAY_RA_VIEN_1 { get; set; }

    /// <summary>
    /// Ngày ra viện ô 2
    /// </summary>
    public string? NGAY_RA_VIEN_2 { get; set; }

    /// <summary>
    /// Ngày ra viện ô 3
    /// </summary>
    public string? NGAY_RA_VIEN_3 { get; set; }

    /// <summary>
    /// Hình thức ra viện.
    /// Ghi chú: 1. Ra viện, 2. Xin về, 3. Bỏ về, 4. Đưa về
    /// </summary>
    public bool? HINH_THUC_RA_VIEN_RA_VIEN { get; set; }

    /// <summary>
    /// Hình thức ra viện.
    /// Ghi chú: 1. Ra viện, 2. Xin về, 3. Bỏ về, 4. Đưa về
    /// </summary>
    public bool? HINH_THUC_RA_VIEN_XIN_VE { get; set; }

    /// <summary>
    /// Hình thức ra viện.
    /// Ghi chú: 1. Ra viện, 2. Xin về, 3. Bỏ về, 4. Đưa về
    /// </summary>
    public bool? HINH_THUC_RA_VIEN_BO_VE { get; set; }

    /// <summary>
    /// Hình thức ra viện.
    /// Ghi chú: 1. Ra viện, 2. Xin về, 3. Bỏ về, 4. Đưa về
    /// </summary>
    public bool? HINH_THUC_RA_VIEN_DUA_VE { get; set; }

    /// <summary>
    /// Tổng số ngày điều trị
    /// </summary>
    public string? TONG_SO_NGAY_DIEU_TRI { get; set; }

    /// <summary>
    /// Mã tổng số ngày điều trị ô 1
    /// </summary>
    public string? MA_TONG_SO_NGAY_DIEU_TRI_1 { get; set; }

    /// <summary>
    /// Mã tổng số ngày điều trị ô 2
    /// </summary>
    public string? MA_TONG_SO_NGAY_DIEU_TRI_2 { get; set; }

    /// <summary>
    /// Mã tổng số ngày điều trị ô 3
    /// </summary>
    public string? MA_TONG_SO_NGAY_DIEU_TRI_3 { get; set; }

    #endregion

    // CHẨN ĐOÁN (CD)

    #region Chẩn đoán

    /// <summary>
    /// Nơi chuyển đến (Chẩn đoán)
    /// </summary>
    public string? NOI_CHUYEN_DEN_CD { get; set; }

    /// <summary>
    /// Mã nơi chuyển đến ô 1
    /// </summary>
    public string? MA_NOI_CHUYEN_DEN_CD_1 { get; set; }

    /// <summary>
    /// Mã nơi chuyển đến ô 2
    /// </summary>
    public string? MA_NOI_CHUYEN_DEN_CD_2 { get; set; }

    /// <summary>
    /// Mã nơi chuyển đến ô 3
    /// </summary>
    public string? MA_NOI_CHUYEN_DEN_CD_3 { get; set; }

    /// <summary>
    /// Mã nơi chuyển đến ô 4
    /// </summary>
    public string? MA_NOI_CHUYEN_DEN_CD_4 { get; set; }

    /// <summary>
    /// Chẩn đoán tại KKB/Cấp cứu
    /// </summary>
    public string? KKB_CAP_CUU { get; set; }

    /// <summary>
    /// Mã chẩn đoán tại KKB/Cấp cứu ô 1
    /// </summary>
    public string? MA_KKB_CAP_CUU_1 { get; set; }

    /// <summary>
    /// Mã chẩn đoán tại KKB/Cấp cứu ô 2
    /// </summary>
    public string? MA_KKB_CAP_CUU_2 { get; set; }

    /// <summary>
    /// Mã chẩn đoán tại KKB/Cấp cứu ô 3
    /// </summary>
    public string? MA_KKB_CAP_CUU_3 { get; set; }

    /// <summary>
    /// Mã chẩn đoán tại KKB/Cấp cứu ô 4
    /// </summary>
    public string? MA_KKB_CAP_CUU_4 { get; set; }

    /// <summary>
    /// Chẩn đoán khi vào khoa điều trị
    /// </summary>
    public string? KHI_VAO_KHOA_DIEU_TRI { get; set; }

    /// <summary>
    /// Mã chẩn đoán khi vào khoa điều trị ô 1
    /// </summary>
    public string? MA_KHI_VAO_KHOA_DIEU_TRI_1 { get; set; }

    /// <summary>
    /// Mã chẩn đoán khi vào khoa điều trị ô 2
    /// </summary>
    public string? MA_KHI_VAO_KHOA_DIEU_TRI_2 { get; set; }

    /// <summary>
    /// Mã chẩn đoán khi vào khoa điều trị ô 3
    /// </summary>
    public string? MA_KHI_VAO_KHOA_DIEU_TRI_3 { get; set; }

    /// <summary>
    /// Mã chẩn đoán khi vào khoa điều trị ô 4
    /// </summary>
    public string? MA_KHI_VAO_KHOA_DIEU_TRI_4 { get; set; }

    /// <summary>
    /// Thủ thuật
    /// </summary>
    public bool? THU_THUAT { get; set; }

    /// <summary>
    /// Phẫu thuật
    /// </summary>
    public bool? PHAU_THUAT { get; set; }

    /// <summary>
    /// Bệnh chính khi ra viện
    /// </summary>
    public string? BENH_CHINH { get; set; }

    /// <summary>
    /// Mã bệnh chính ô 1
    /// </summary>
    public string? MA_BENH_CHINH_1 { get; set; }

    /// <summary>
    /// Mã bệnh chính ô 2
    /// </summary>
    public string? MA_BENH_CHINH_2 { get; set; }

    /// <summary>
    /// Mã bệnh chính ô 3
    /// </summary>
    public string? MA_BENH_CHINH_3 { get; set; }

    /// <summary>
    /// Mã bệnh chính ô 4
    /// </summary>
    public string? MA_BENH_CHINH_4 { get; set; }

    /// <summary>
    /// Bệnh kèm theo khi ra viện
    /// </summary>
    public string? BENH_KEM_THEO { get; set; }

    /// <summary>
    /// Mã bệnh kèm theo khi ra viện ô 1
    /// </summary>
    public string? MA_BENH_KEM_THEO_1 { get; set; }

    /// <summary>
    /// Mã bệnh kèm theo khi ra viện ô 2
    /// </summary>
    public string? MA_BENH_KEM_THEO_2 { get; set; }

    /// <summary>
    /// Mã bệnh kèm theo khi ra viện ô 3
    /// </summary>
    public string? MA_BENH_KEM_THEO_3 { get; set; }

    /// <summary>
    /// Mã bệnh kèm theo khi ra viện ô 4
    /// </summary>
    public string? MA_BENH_KEM_THEO_4 { get; set; }

    /// <summary>
    /// Tai biến
    /// </summary>
    public bool? TAI_BIEN { get; set; }

    /// <summary>
    /// Biến chứng
    /// </summary>
    public bool? BIEN_CHUNG { get; set; }

    #endregion

    // TÌNH TRẠNG RA VIỆN (RV)

    #region Tình trạng ra viện

    /// <summary>
    /// Kết quả điều trị.
    /// Ghi chú: 1. Khỏi, 2. Đỡ/giảm, 3. Không thay đổi, 4. Nặng hơn, 5. Tử vong
    /// </summary>
    public bool? KET_QUA_DIEU_TRI_KHOI { get; set; }

    /// <summary>
    /// Kết quả điều trị.
    /// Ghi chú: 1. Khỏi, 2. Đỡ/giảm, 3. Không thay đổi, 4. Nặng hơn, 5. Tử vong
    /// </summary>
    public bool? KET_QUA_DIEU_TRI_DO_GIAM { get; set; }

    /// <summary>
    /// Kết quả điều trị.
    /// Ghi chú: 1. Khỏi, 2. Đỡ/giảm, 3. Không thay đổi, 4. Nặng hơn, 5. Tử vong
    /// </summary>
    public bool? KET_QUA_DIEU_TRI_KHONG_THAY_DOI { get; set; }

    /// <summary>
    /// Kết quả điều trị.
    /// Ghi chú: 1. Khỏi, 2. Đỡ/giảm, 3. Không thay đổi, 4. Nặng hơn, 5. Tử vong
    /// </summary>
    public bool? KET_QUA_DIEU_TRI_NANG_HON { get; set; }

    /// <summary>
    /// Kết quả điều trị.
    /// Ghi chú: 1. Khỏi, 2. Đỡ/giảm, 3. Không thay đổi, 4. Nặng hơn, 5. Tử vong
    /// </summary>
    public bool? KET_QUA_DIEU_TRI_TU_VONG { get; set; }

    /// <summary>
    /// Giải phẫu bệnh (sinh thiết).
    /// Ghi chú: 1. Lành tính, 2. Nghi ngờ, 3. Ác tính
    /// </summary>
    public bool? GIAI_PHAU_BENH_LANH_TINH { get; set; }

    /// <summary>
    /// Giải phẫu bệnh (sinh thiết).
    /// Ghi chú: 1. Lành tính, 2. Nghi ngờ, 3. Ác tính
    /// </summary>
    public bool? GIAI_PHAU_BENH_NGHI_NGO { get; set; }

    /// <summary>
    /// Giải phẫu bệnh (sinh thiết).
    /// Ghi chú: 1. Lành tính, 2. Nghi ngờ, 3. Ác tính
    /// </summary>
    public bool? GIAI_PHAU_BENH_AC_TINH { get; set; }

    /// <summary>
    /// Giờ tử vong
    /// </summary>
    public string? GIO_TU_VONG { get; set; }

    /// <summary>
    /// Phút tử vong
    /// </summary>
    public string? PHU_TU_VONG { get; set; }

    /// <summary>
    /// Ngày tử vong
    /// </summary>
    public string? NGAY_TU_VONG { get; set; }

    /// <summary>
    /// Tháng tử vong
    /// </summary>
    public string? THANG_TU_VONG { get; set; }

    /// <summary>
    /// Năm tử vong
    /// </summary>
    public string? NAM_TU_VONG { get; set; }

    /// <summary>
    /// Nguyên nhân tử vong: Do bệnh, Do tai biến điều trị, Khác, Trong 24 giờ vào viện, Sau 24 giờ vào viện
    /// </summary>
    public bool? NGUYEN_NHAN_TU_VONG_DO_BENH { get; set; }

    /// <summary>
    /// Nguyên nhân tử vong: Do bệnh, Do tai biến điều trị, Khác, Trong 24 giờ vào viện, Sau 24 giờ vào viện
    /// </summary>
    public bool? NGUYEN_NHAN_TU_VONG_DO_TAI_BIEN_DIEU_TRI { get; set; }

    /// <summary>
    /// Nguyên nhân tử vong: Do bệnh, Do tai biến điều trị, Khác, Trong 24 giờ vào viện, Sau 24 giờ vào viện
    /// </summary>
    public bool? NGUYEN_NHAN_TU_VONG_KHAC { get; set; }

    /// <summary>
    /// Nguyên nhân tử vong: Do bệnh, Do tai biến điều trị, Khác, Trong 24 giờ vào viện, Sau 24 giờ vào viện
    /// </summary>
    public bool? NGUYEN_NHAN_TU_VONG_TRONG_24_GIO_VAO_VIEN { get; set; }

    /// <summary>
    /// Nguyên nhân tử vong: Do bệnh, Do tai biến điều trị, Khác, Trong 24 giờ vào viện, Sau 24 giờ vào viện
    /// </summary>
    public bool? NGUYEN_NHAN_TU_VONG_SAU_24_GIO_VAO_VIEN { get; set; }

    /// <summary>
    /// Nguyên nhân chính gây tử vong
    /// </summary>
    public string? NGUYEN_NHAN_CHINH_GAY_TU_VONG { get; set; }

    /// <summary>
    /// Mã nguyên nhân chính gây tử vong ô 1
    /// </summary>
    public string? MA_NGUYEN_NHAN_CHINH_GAY_TU_VONG_1 { get; set; }

    /// <summary>
    /// Mã nguyên nhân chính gây tử vong ô 2
    /// </summary>
    public string? MA_NGUYEN_NHAN_CHINH_GAY_TU_VONG_2 { get; set; }

    /// <summary>
    /// Mã nguyên nhân chính gây tử vong ô 3
    /// </summary>
    public string? MA_NGUYEN_NHAN_CHINH_GAY_TU_VONG_3 { get; set; }

    /// <summary>
    /// Mã nguyên nhân chính gây tử vong ô 4
    /// </summary>
    public string? MA_NGUYEN_NHAN_CHINH_GAY_TU_VONG_4 { get; set; }

    /// <summary>
    /// Khám nghiệm tử thi
    /// </summary>
    public bool? KHAM_NGHIEM_TU_THI { get; set; }

    /// <summary>
    /// Chẩn đoán giải phẫu tử thi
    /// </summary>
    public string? CHAN_DOAN_TU_THI { get; set; }

    /// <summary>
    /// Mã chẩn đoán giải phẫu tử thi ô 1
    /// </summary>
    public string? MA_CHAN_DOAN_TU_THI_1 { get; set; }

    /// <summary>
    /// Mã chẩn đoán giải phẫu tử thi ô 2
    /// </summary>
    public string? MA_CHAN_DOAN_TU_THI_2 { get; set; }

    /// <summary>
    /// Mã chẩn đoán giải phẫu tử thi ô 3
    /// </summary>
    public string? MA_CHAN_DOAN_TU_THI_3 { get; set; }

    /// <summary>
    /// Mã chẩn đoán giải phẫu tử thi ô 4
    /// </summary>
    public string? MA_CHAN_DOAN_TU_THI_4 { get; set; }

    #endregion

    // KÝ CỦA GIÁM ĐỐC VÀ TRƯỞNG KHOA (KY)

    #region Ký của Giám đốc và Trưởng khoa

    /// <summary>
    /// Họ và tên giám đốc bệnh viện ở mục ký
    /// </summary>
    public string? KY_HO_TEN_GIAM_DOC_BENH_VIEN { get; set; }

    /// <summary>
    /// Ngày tháng năm ở mục ký của trưởng khoa
    /// </summary>
    public string? KY_NGAY_TRUONG_KHOA { get; set; }

    /// <summary>
    /// Tháng trưởng khoa
    /// </summary>
    public string? KY_THANG_TRUONG_KHOA { get; set; }

    /// <summary>
    /// Năm trưởng khoa
    /// </summary>
    public string? KY_NAM_TRUONG_KHOA { get; set; }

    /// <summary>
    /// Họ và tên trưởng khoa ở mục ký
    /// </summary>
    public string? KY_HO_TEN_TRUONG_KHOA { get; set; }

    #endregion

    // BỆNH ÁN (BA)

    #region Bệnh án

    /// <summary>
    /// Lý do vào viện
    /// </summary>
    public string? LY_DO_VAO_VIEN { get; set; }

    /// <summary>
    /// Vào ngày thứ mấy của bệnh
    /// </summary>
    public string? SO_NGAY_BENH_TRUOC_VAO_VIEN { get; set; }

    /// <summary>
    /// Quá trình bệnh lý
    /// </summary>
    public string? QUA_TRINH_BENH_LY { get; set; }

    /// <summary>
    /// Tiền sử bản thân
    /// </summary>
    public string? TIEN_SU_BAN_THAN { get; set; }

    /// <summary>
    /// Ký hiệu dị ứng ô 1
    /// </summary>
    public string? KY_HIEU_DI_UNG_1 { get; set; }

    /// <summary>
    /// Ký hiệu ma túy ô 1
    /// </summary>
    public string? KY_HIEU_MA_TUY_1 { get; set; }

    /// <summary>
    /// Ký hiệu ma túy ô 2
    /// </summary>
    public string? KY_HIEU_MA_TUY_2 { get; set; }

    /// <summary>
    /// Thời gian tính theo tháng của yếu tố ma tuý
    /// </summary>
    public string? THOI_GIAN_MA_TUY { get; set; }

    /// <summary>
    /// Ký hiệu rượu bia ô 1
    /// </summary>
    public string? KY_HIEU_RUOU_BIA_1 { get; set; }

    /// <summary>
    /// Ký hiệu rượu bia ô 2
    /// </summary>
    public string? KY_HIEU_RUOU_BIA_2 { get; set; }

    /// <summary>
    /// Thời gian tính theo tháng của yếu tố rượu bia
    /// </summary>
    public string? THOI_GIAN_RUOU_BIA { get; set; }

    /// <summary>
    /// Ký hiệu thuốc lá ô 1
    /// </summary>
    public string? KY_HIEU_THUOC_LA_1 { get; set; }

    /// <summary>
    /// Thời gian tính theo tháng của yếu tố thuốc lá
    /// </summary>
    public string? THOI_GIAN_THUOC_LA { get; set; }

    /// <summary>
    /// Ký hiệu thuốc lào ô 1
    /// </summary>
    public string? KY_HIEU_THUOC_LAO_1 { get; set; }

    /// <summary>
    /// Ký hiệu thuốc lào ô 2
    /// </summary>
    public string? KY_HIEU_THUOC_LAO_2 { get; set; }

    /// <summary>
    /// Thời gian tính theo tháng của yếu tố thuốc lào
    /// </summary>
    public string? THOI_GIAN_THUOC_LAO { get; set; }

    /// <summary>
    /// Ký hiệu khác ô 1
    /// </summary>
    public string? KY_HIEU_KHAC_1 { get; set; }

    /// <summary>
    /// Ký hiệu khác ô 2
    /// </summary>
    public string? KY_HIEU_KHAC_2 { get; set; }

    /// <summary>
    /// Thời gian tính theo tháng của yếu tố khác
    /// </summary>
    public string? THOI_GIAN_KHAC { get; set; }

    /// <summary>
    /// Tiền sử gia đình
    /// </summary>
    public string? TIEN_SU_GIA_DINH { get; set; }

    /// <summary>
    /// Khám toàn thân
    /// </summary>
    public string? KHAM_TOAN_THAN { get; set; }

    /// <summary>
    /// Mạch.
    /// Ghi chú: Số lần/phút
    /// </summary>
    public string? MACH { get; set; }

    /// <summary>
    /// Nhiệt độ.
    /// Ghi chú: Đơn vị °C
    /// </summary>
    public string? NHIET_DO { get; set; }

    /// <summary>
    /// Huyết áp.trước dấu "/"
    /// Ghi chú: Đơn vị mmHg
    /// </summary>
    public string? HUYET_AP_1 { get; set; }

    /// <summary>
    /// Huyết áp.sau dấu "/"
    /// Ghi chú: Đơn vị mmHg
    /// </summary>
    public string? HUYET_AP_2 { get; set; }

    /// <summary>
    /// Nhịp thở.
    /// Ghi chú: Số lần/phút
    /// </summary>
    public string? NHIP_THO { get; set; }

    /// <summary>
    /// Cân nặng.
    /// Ghi chú: Đơn vị kg
    /// </summary>
    public string? CAN_NANG { get; set; }

    /// <summary>
    /// Khám tuần hoàn
    /// </summary>
    public string? KHAM_TUAN_HOAN { get; set; }

    /// <summary>
    /// Khám hô hấp
    /// </summary>
    public string? KHAM_HO_HAP { get; set; }

    /// <summary>
    /// Khám tiêu hoá
    /// </summary>
    public string? KHAM_TIEU_HOA { get; set; }

    /// <summary>
    /// Khám thận - tiết niệu - sinh dục
    /// </summary>
    public string? KHAM_THAN_TIETNIEU_SINHDUC { get; set; }

    /// <summary>
    /// Khám thần kinh
    /// </summary>
    public string? KHAM_THAN_KINH { get; set; }

    /// <summary>
    /// Khám cơ - xương - khớp
    /// </summary>
    public string? KHAM_CO_XUONG_KHOP { get; set; }

    /// <summary>
    /// Khám tai - mũi - họng
    /// </summary>
    public string? KHAM_TAI_MUI_HONG { get; set; }

    /// <summary>
    /// Khám răng - hàm - mặt
    /// </summary>
    public string? KHAM_RANG_HAM_MAT { get; set; }

    /// <summary>
    /// Khám mắt
    /// </summary>
    public string? KHAM_MAT { get; set; }

    /// <summary>
    /// Khám nội tiết và dinh dưỡng và các bệnh lý khác
    /// </summary>
    public string? KHAM_NOI_TIET_DINH_DUONG_VA_BENH_LY_KHAC { get; set; }

    /// <summary>
    /// Các xét nghiệm cận lâm sàng
    /// </summary>
    public string? XET_NGHIEM_CAN_LAM_SANG { get; set; }

    /// <summary>
    /// Tóm tắt bệnh án
    /// </summary>
    public string? TOM_TAT_BENH_AN { get; set; }

    /// <summary>
    /// Chẩn đoán bệnh chính
    /// </summary>
    public string? CHAN_DOAN_BENH_CHINH { get; set; }

    /// <summary>
    /// Chẩn đoán kèm theo
    /// </summary>
    public string? CHAN_DOAN_KEM_THEO { get; set; }

    /// <summary>
    /// Chẩn đoán phân biệt
    /// </summary>
    public string? CHAN_DOAN_PHAN_BIET { get; set; }

    /// <summary>
    /// Tiên lượng
    /// </summary>
    public string? TIEN_LUONG { get; set; }

    /// <summary>
    /// Hướng điều trị
    /// </summary>
    public string? HUONG_DIEU_TRI { get; set; }

    #endregion

    // KÝ CỦA BÁC SỸ LÀM BỆNH ÁN (KY)

    #region Ký của Bác sỹ làm bệnh án

    /// <summary>
    /// Ngày tháng năm khi ký của bác sỹ làm bệnh án
    /// </summary>
    public string? KY_NGAY_BAC_SY_LAM_BENH_AN { get; set; }

    /// <summary>
    /// Tháng khi ký của bác sỹ làm bệnh án
    /// </summary>
    public string? KY_THANG_BAC_SY_LAM_BENH_AN { get; set; }

    /// <summary>
    /// Năm khi ký của bác sỹ làm bệnh án
    /// </summary>
    public string? KY_NAM_BAC_SY_LAM_BENH_AN { get; set; }

    /// <summary>
    /// Họ và tên bác sỹ làm bệnh án
    /// </summary>
    public string? KY_HO_TEN_BAC_SY_LAM_BENH_AN { get; set; }

    #endregion

    // TỔNG KẾT BỆNH ÁN (TK)

    #region Tổng kết bệnh án

    /// <summary>
    /// Quá trình bệnh lý và diễn biến lâm sàng
    /// </summary>
    public string? QUA_TRINH_DIEN_BIEN { get; set; }

    /// <summary>
    /// Tóm tắt kết quả xét nghiệm
    /// </summary>
    public string? KET_QUA_XET_NGHIEM { get; set; }

    /// <summary>
    /// Phương pháp điều trị
    /// </summary>
    public string? PHUONG_PHAP_DIEU_TRI { get; set; }

    /// <summary>
    /// Tình trạng người bệnh ra viện
    /// </summary>
    public string? TINH_TRANG_RA_VIEN { get; set; }

    /// <summary>
    /// Hướng điều trị và chế độ tiếp theo
    /// </summary>
    public string? HUONG_DIEU_TRI_TIEP { get; set; }

    /// <summary>
    /// Ngày tháng năm ký của bác sỹ điều trị
    /// </summary>
    public string? KY_NGAY_CUA_BAC_SY_DIEU_TRI { get; set; }

    /// <summary>
    /// Tháng ký của bác sỹ điều trị
    /// </summary>
    public string? KY_THANG_CUA_BAC_SY_DIEU_TRI { get; set; }

    /// <summary>
    /// Năm ký của bác sỹ điều trị
    /// </summary>
    public string? KY_NAM_CUA_BAC_SY_DIEU_TRI { get; set; }

    /// <summary>
    /// Họ tên bác sỹ điều trị
    /// </summary>
    public string? KY_HO_TEN_BAC_SY_DIEU_TRI { get; set; }

    /// <summary>
    /// Họ tên người giao hồ sơ
    /// </summary>
    public string? HO_TEN_NGUOI_GIAO_HO_SO { get; set; }

    /// <summary>
    /// Họ tên người nhận hồ sơ
    /// </summary>
    public string? HO_TEN_NGUOI_NHAN_HO_SO { get; set; }

    /// <summary>
    /// Số tờ X-quang
    /// </summary>
    public string? SO_TO_XQUANG { get; set; }

    /// <summary>
    /// Số tờ CT Scanner
    /// </summary>
    public string? SO_TO_CT_SCANNER { get; set; }

    /// <summary>
    /// Số tờ siêu âm
    /// </summary>
    public string? SO_TO_SIEU_AM { get; set; }

    /// <summary>
    /// Số tờ xét nghiệm
    /// </summary>
    public string? SO_TO_XET_NGHIEM { get; set; }

    /// <summary>
    /// Số tờ khác
    /// </summary>
    public string? SO_TO_KHAC { get; set; }

    /// <summary>
    /// Tổng số tờ hồ sơ
    /// </summary>
    public string? SO_TO_TOAN_BO { get; set; }

    #endregion
}