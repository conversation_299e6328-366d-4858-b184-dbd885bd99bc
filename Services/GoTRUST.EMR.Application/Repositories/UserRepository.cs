using Amazon.Runtime.Internal.Util;
using BuildingBlocks.Common.Interface;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Domain.Constants;
using JasperFx.Core.Reflection;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Caching.Memory;

namespace GoTRUST.EMR.Application.Repositories;

public class UserRepository : IUserRepository
{
    private readonly UserManager<User> _userManager;
    private readonly IApplicationDbContext _dbContext;
    private readonly ICachedService _cache;

    public UserRepository(UserManager<User> userManager, IApplicationDbContext dbContext, ICachedService cache)
    {
        _userManager = userManager;
        _dbContext = dbContext;
        _cache = cache;
    }

    private async Task<Dictionary<Guid, UserCodeInfo>> LoadUserCodesAsync(CancellationToken cancellationToken = default)
    {
        string cacheKey = "all_user_codes";
        
        // Try to get from cache first
        var cachedUserCodes = await _cache.GetAsync<Dictionary<Guid, UserCodeInfo>>(cacheKey, cancellationToken);
        if (cachedUserCodes != null)
        {
            return cachedUserCodes;
        }

        // Load all user codes from database
        cachedUserCodes = new Dictionary<Guid, UserCodeInfo>();

        // Get all users
        var users = _userManager.Users.ToList();

        foreach (var user in users)
        {
            if (string.IsNullOrEmpty(user.UserType)) continue;

            var userInfo = new UserCodeInfo
            {
                UserType = user.UserType
            };

            try
            {
                if (user.UserType == UserTypeConstants.Internal)
                {
                    var emp = await _dbContext.Employees
                        .AsNoTracking()
                        .FirstOrDefaultAsync(e => e.UserId == user.Id, cancellationToken);
                    
                    userInfo.UserCode = emp?.EmployeeCode;
                }
                else if (user.UserType == UserTypeConstants.External)
                {
                    var organization = await _dbContext.BorrowerOrganizations
                        .AsNoTracking()
                        .FirstOrDefaultAsync(c => c.UserId == user.Id, cancellationToken);
                    
                    userInfo.UserCode = organization?.Code;
                }
            }
            catch
            {
                // If error occurs, UserCode will remain null
                userInfo.UserCode = null;
            }

            // Always add to cache, even if UserCode is null
            cachedUserCodes[user.Id] = userInfo;
        }

        // Cache the result for 12 hours (43200 seconds)
        await _cache.SetAsync(cacheKey, cachedUserCodes, cancellationToken, 43200);

        return cachedUserCodes;
    }

    public async Task<string> GetUserCodeAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var cachedUserCodes = await LoadUserCodesAsync(cancellationToken);

        if (cachedUserCodes.TryGetValue(userId, out var userCodeInfo))
        {
            return userCodeInfo.UserCode ?? string.Empty;
        }

        return string.Empty;
    }

    public async Task<string> GetUserCodeEmployeeAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var cachedUserCodes = await LoadUserCodesAsync(cancellationToken);

        if (cachedUserCodes.TryGetValue(userId, out var userCodeInfo))
        {
            if (userCodeInfo.UserType == UserTypeConstants.Internal)
            {
                return userCodeInfo.UserCode ?? string.Empty;
            }
        }

        return string.Empty;
    }

    private class UserCodeInfo
    {
        public string UserType { get; set; } = string.Empty;
        public string? UserCode { get; set; }
    }
}
