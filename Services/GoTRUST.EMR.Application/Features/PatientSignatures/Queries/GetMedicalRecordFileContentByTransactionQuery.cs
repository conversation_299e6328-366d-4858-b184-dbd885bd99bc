using BuildingBlocks.Exceptions;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.Upload.Commands;
using Amazon.S3;
using Amazon.S3.Model;
namespace GoTRUST.EMR.Application.Features.PatientSignatures.Queries;

public record GetMedicalRecordFileContentByTransactionQuery(Guid? TransactionSignId) : IQuery<Response<MedicalRecordFileContentDto>>;

public record MedicalRecordFileContentDto
{
    public byte[] FileContent { get; init; } = [];
    public string FileName { get; init; } = string.Empty;
    public string FileType { get; init; } = string.Empty;
}

public class GetMedicalRecordFileContentByTransactionQueryValidator : AbstractValidator<GetMedicalRecordFileContentByTransactionQuery>
{
    public GetMedicalRecordFileContentByTransactionQueryValidator()
    {
        RuleFor(x => x.TransactionSignId).NotEmpty().WithMessage("Medical record file ID is required.");
    }
}

public class GetMedicalRecordFileContentQueryHandler(
    IApplicationDbContext dbContext,
    IMediator mediator,
    IHttpClientFactory httpClientFactory
) : IQueryHandler<GetMedicalRecordFileContentByTransactionQuery, Response<MedicalRecordFileContentDto>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;
    private readonly IMediator _mediator = mediator;
    private readonly IHttpClientFactory _httpClientFactory = httpClientFactory;

    public async Task<Response<MedicalRecordFileContentDto>> Handle(GetMedicalRecordFileContentByTransactionQuery request, CancellationToken cancellationToken)
    {
        var transactionSign = await _dbContext.TransactionSigns
            .FindAsync([request.TransactionSignId], cancellationToken)
            ?? throw new NotFoundException("Không tìm thấy phiên ký số.");

        if (transactionSign.ExpiredAt < DateTime.UtcNow)
            throw new BadRequestException("Phiên ký số đã hết hạn.");

        var medicalRecordFile = await _dbContext.MedicalRecordFiles
            .FindAsync([transactionSign.MedicalRecordFileId], cancellationToken)
            ?? throw new NotFoundException("Không tìm thấy tệp hồ sơ bệnh án.");

        byte[] fileContent;

        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            var response = await httpClient.GetAsync(medicalRecordFile.FilePath, cancellationToken);

            if (!response.IsSuccessStatusCode)
                throw new NotFoundException($"Không thể tải tệp từ URL: {response.ReasonPhrase}");

            fileContent = await response.Content.ReadAsByteArrayAsync(cancellationToken);

            if (medicalRecordFile.IsEncrypted)
            {
                using var memoryStream = new MemoryStream(fileContent);
                var formFile = new FormFile(memoryStream, 0, fileContent.Length, medicalRecordFile.FileName, medicalRecordFile.FileName)
                {
                    Headers = new HeaderDictionary(),
                    ContentType = medicalRecordFile.FileType
                };

                var decryptCommand = new DecryptFileCommand
                {
                    File = formFile,
                    IV = medicalRecordFile.InitialVector
                };

                var decryptResult = await _mediator.Send(decryptCommand, cancellationToken);

                using var decryptedStream = new FileStream(decryptResult.Data!, FileMode.Open, FileAccess.Read);
                using var decryptedMemoryStream = new MemoryStream();
                await decryptedStream.CopyToAsync(decryptedMemoryStream, cancellationToken);
                fileContent = decryptedMemoryStream.ToArray();
            }

            var result = new MedicalRecordFileContentDto
            {
                FileContent = fileContent,
                FileName = medicalRecordFile.FileName,
                FileType = medicalRecordFile.FileType
            };

            return new Response<MedicalRecordFileContentDto>(result);
        }
        catch (HttpRequestException ex)
        {
            throw new NotFoundException($"Không thể tải tệp từ URL: {ex.Message}");
        }
    }
}
