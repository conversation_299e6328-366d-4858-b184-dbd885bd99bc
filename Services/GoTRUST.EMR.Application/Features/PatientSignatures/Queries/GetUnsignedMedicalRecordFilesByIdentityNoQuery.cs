using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Mapster;

namespace GoTRUST.EMR.Application.Features.PatientSignatures.Queries;

public record GetUnsignedMedicalRecordFilesByIdentityNoQuery(string? IdentityNo) : IQuery<Response<List<UnsignedMedicalRecordFileDto>>>;

public record UnsignedMedicalRecordFileDto(
    Guid Id,
    string FileName,
    string PatientId,
    string PatientName,
    string HospitalId,
    string HospitalName,
    string FileType,
    string UploadedAt
);

public class GetUnsignedMedicalRecordFilesByIdentityNoQueryValidator : AbstractValidator<GetUnsignedMedicalRecordFilesByIdentityNoQuery>
{
    public GetUnsignedMedicalRecordFilesByIdentityNoQueryValidator()
    {
        RuleFor(x => x.IdentityNo).NotEmpty();
    }
}

public class GetUnsignedMedicalRecordFilesByIdentityNoQueryHandler(IApplicationDbContext dbContext) : IQueryHandler<GetUnsignedMedicalRecordFilesByIdentityNoQuery, Response<List<UnsignedMedicalRecordFileDto>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<UnsignedMedicalRecordFileDto>>> Handle(GetUnsignedMedicalRecordFilesByIdentityNoQuery req, CancellationToken cancellationToken)
    {
        var patient = await _dbContext.Patients.AsNoTracking()
            .FirstOrDefaultAsync(p => p.CitizenId == req.IdentityNo, cancellationToken) ?? throw new NotFoundException("Không tìm thấy bệnh nhân với mã số định danh này.");

        var result = await _dbContext.MedicalRecordFiles.AsNoTracking()
            .Include(f => f.MedicalRecord)
            .ThenInclude(f => f.Patient)
            .ThenInclude(f => f.Hospital)
            .Where(f => !f.IsSignedByPatient && f.MedicalRecord.PatientId == patient!.Id)
            .Select(f => new UnsignedMedicalRecordFileDto(
                f.Id,
                f.FileName,
                f.MedicalRecord.PatientId.ToString(),
                f.MedicalRecord.Patient.FullName,
                f.MedicalRecord.HospitalId.ToString(),
                f.MedicalRecord.Hospital.Name,
                f.FileType,
                f.UploadedAt.ToString("dd/MM/yyyy")
            ))
            .ToListAsync(cancellationToken);

        return new Response<List<UnsignedMedicalRecordFileDto>>(result);
    }
}
