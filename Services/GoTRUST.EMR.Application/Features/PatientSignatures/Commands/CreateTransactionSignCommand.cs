using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.Application.Features.PatientSignatures.Commands;

public class CreateTransactionSignCommand : ICommand<Response<CreateTransactionSignResponse>>
{
    public Guid MedicalRecordFileId { get; init; }
}

public record CreateTransactionSignResponse(Guid TransactionId);

public class CreateTransactionSignCommandValidator : AbstractValidator<CreateTransactionSignCommand>
{
    public CreateTransactionSignCommandValidator()
    {
    }
}

public class CreateTransactionSignCommandHandler(
    IApplicationDbContext _dbContext
) : ICommandHandler<CreateTransactionSignCommand, Response<CreateTransactionSignResponse>>
{
    public async Task<Response<CreateTransactionSignResponse>> Handle(CreateTransactionSignCommand req, CancellationToken cancellationToken)
    {
        var transactionSign = new TransactionSign
        {
            MedicalRecordFileId = req.MedicalRecordFileId,
            ExpiredAt = DateTime.UtcNow.AddMinutes(30)
        };

        _dbContext.TransactionSigns.Add(transactionSign);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return new Response<CreateTransactionSignResponse>(
            new CreateTransactionSignResponse(transactionSign.Id)
        );
    }
}
