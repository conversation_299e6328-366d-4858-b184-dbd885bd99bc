using System.Text.Json.Serialization;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using Mapster;

namespace GoTRUST.EMR.Application.Features.SignatureCoordinates.Queries;

public record GetSignatureCoordinatesQuery(Guid MedicalRecordTemplateId)
    : IQuery<Response<List<SignatureCoordinateDto>>>;

public record SignatureCoordinateDto(
    Guid Id,
    Guid MedicalRecordTemplateId,
    Guid RoleId,
    string Name,
    int CoordinateX,
    int CoordinateY,
    int PageNumber,
    int Width,
    int Height,
    bool AllowAutoSign,
    [property: JsonPropertyName("templateName")]
    string? MedicalRecordTemplateTemplateName
);

public class GetSignatureCoordinatesQueryHandler(
    IApplicationDbContext context,
    IHttpContextAccessor httpContextAccessor,
    ILogger<GetSignatureCoordinatesQueryHandler> logger)
    : IQueryHandler<GetSignatureCoordinatesQuery, Response<List<SignatureCoordinateDto>>>
{
    public async Task<Response<List<SignatureCoordinateDto>>> Handle(
        GetSignatureCoordinatesQuery request,
        CancellationToken cancellationToken)
    {
        // Get role from claims
        var roleNames = httpContextAccessor.HttpContext?.User?.RetrieveRoleNamesFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin người dùng. Vui lòng đăng nhập lại.");

        // Query signature coordinates
        var signatureCoordinates = await context.SignatureCoordinates
            .AsNoTracking()
            .Include(sc => sc.MedicalRecordTemplate)
            .Include(sc => sc.Role)
            .Where(sc => sc.MedicalRecordTemplateId == request.MedicalRecordTemplateId
                        && roleNames.Contains(sc.Role.Name ?? string.Empty))
            .OrderBy(sc => sc.Name)
            .ProjectToType<SignatureCoordinateDto>()
            .ToListAsync(cancellationToken);

        logger.LogInformation("Found {Count} signature coordinates for template {TemplateId}",
            signatureCoordinates.Count, request.MedicalRecordTemplateId);

        return new Response<List<SignatureCoordinateDto>>(signatureCoordinates);
    }
}
