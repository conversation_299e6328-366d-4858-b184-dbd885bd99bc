


using GoTRUST.EMR.Application.Helpers;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.Application.Features.Employees.Queries;

public record GetAllEmployeesQuery : IQuery<Response<List<GetAllEmployeesResponse>>>;

public record GetAllEmployeesResponse(
    Guid Id,
    string FullName,
    string AvatarUrl
);

public class GetAllEmployeesQueryHandler(
    IApplicationDbContext dbContext,
    IHttpContextAccessor httpContextAccessor,
    UserManager<User> userManager
) : IQueryHandler<GetAllEmployeesQuery, Response<List<GetAllEmployeesResponse>>>
{
    public async Task<Response<List<GetAllEmployeesResponse>>> Handle(GetAllEmployeesQuery request, CancellationToken cancellationToken)
    {
        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await userManager.FindByIdAsync(userId.ToString() ?? string.Empty) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var employees = await dbContext.Employees
            .Include(e => e.User)
            .Where(e => e.HospitalId == hospitalId)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        var result = employees.Select(e => new GetAllEmployeesResponse(
            e.Id,
            e.FullName,
            e.User?.AvatarUrl ?? string.Empty
        )).ToList();
        return new Response<List<GetAllEmployeesResponse>>(result);
    }
}
