﻿using Mapster;
using Microsoft.AspNetCore.Identity;
using GoTRUST.EMR.Application.Helpers;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.Application.Features.Employees.Queries;

public record GetEmployeeActionLogQuery : PaginationRequest, IQuery<PaginationResponse<GetEmployeeActionLogResponse>>
{
    public Guid EmployeeId { get; set; }
    public string? SearchTerm { get; set; }
    public DateTime? SearchDate { get; set; }
}

public record GetEmployeeActionLogResponse(
    Guid Id,
    string ActionName,
    string ActionDetail,
    string ActionIcon,
    DateTime ActionTime,
    string Result
);

public class GetEmployeeActionLogQueryHandler(
    IApplicationDbContext dbContext,
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager
) : IQueryHandler<GetEmployeeActionLogQuery, PaginationResponse<GetEmployeeActionLogResponse>>
{
    public async Task<PaginationResponse<GetEmployeeActionLogResponse>> Handle(GetEmployeeActionLogQuery request, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await _userManager.FindByIdAsync(userId.ToString()) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var query = dbContext.Employees
            .Where(e => e.Id == request.EmployeeId && e.HospitalId == hospitalId)
            .SelectMany(e => e.User!.UserActionLogs);

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var term = request.SearchTerm.Trim().ToLower();
            query = query.Where(log => log.ActionName.ToLower().Contains(term));
        }

        if (request.SearchDate.HasValue)
        {
            var day = request.SearchDate.Value.Date;
            query = query.Where(log => log.ActionTime.Date == day);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var result = await query
            .AsNoTracking()
            .OrderByDescending(log => log.ActionTime)
            .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
            .Take(request.PageSize!.Value)
            .ProjectToType<GetEmployeeActionLogResponse>()
            .ToListAsync(cancellationToken);

        return new PaginationResponse<GetEmployeeActionLogResponse>(
            request.PageIndex,
            request.PageSize,
            totalCount,
            result
        );
    }
}