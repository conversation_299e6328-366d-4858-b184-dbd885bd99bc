﻿using Mapster;
using Microsoft.AspNetCore.Identity;
using GoTRUST.EMR.Application.Helpers;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.Employees.Queries;

public record GetEmployeeDetailQuery(Guid EmployeeId) : IQuery<Response<GetEmployeeDetailResponse>>;

public record GetEmployeeDetailResponse(
    Guid Id,
    Guid HospitalId,
    Guid? UserId,
    string FullName,
    string EmployeeCode,
    string? AvatarUrl,
    DateTime? DateOfBirth,
    string? Age,
    string? Gender,
    string? CitizenId,
    string? Email,
    bool IsActive,
    DateTime? CitizenIdIssueDate,
    string? MaritalStatusId,
    string? MaritalStatusName,
    string? PhoneNumber,
    string? ReligionId,
    string? ReligionName,
    string? EthnicityId,
    string? EthnicityName,
    string? Address,
    string? TemporaryAddress,
    string? EmployeeNumber,
    string? OfficeUnit,
    string? Position,
    string? Note,
    Guid RoleId,
    string RoleName,
    string UserType
);

public class GetEmployeeDetailQueryHandler(
    IApplicationDbContext dbContext,
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager
) : IQueryHandler<GetEmployeeDetailQuery, Response<GetEmployeeDetailResponse>>
{
    public async Task<Response<GetEmployeeDetailResponse>> Handle(GetEmployeeDetailQuery request, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var user = await _userManager.FindByIdAsync(userId.ToString() ?? string.Empty) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var employee = await dbContext.Employees
            .AsNoTracking()
            .Include(e => e.User)
            .Where(e => e.Id == request.EmployeeId && e.HospitalId == hospitalId)
            .FirstOrDefaultAsync(cancellationToken) ?? throw new NotFoundException("Không tìm thấy tài khoản nhân viên.");
        Guid roleId = Guid.Empty;
        string roleName = "";

        if (employee.User != null)
        {
            var roles = await _userManager.GetRolesAsync(employee.User);
            if (roles.Count > 0)
            {
                roleName = roles[0];
                var role = await _roleManager.FindByNameAsync(roleName);
                if (role != null)
                    roleId = role.Id;
            }
        }

        string? maritalName = null;
        if (!string.IsNullOrEmpty(employee.ReligionId))
        {
            maritalName = await dbContext.TinhTrangHonNhans
                .Where(r => r.Id == employee.MaritalStatusId)
                .Select(r => r.Name)
                .FirstOrDefaultAsync(cancellationToken);
        }
        string? religionName = null;
        if (!string.IsNullOrEmpty(employee.ReligionId))
        {
            religionName = await dbContext.TonGiaos
                .Where(r => r.Id == employee.ReligionId)
                .Select(r => r.Name)
                .FirstOrDefaultAsync(cancellationToken);
        }
        string? ethnicityName = null;
        if (!string.IsNullOrEmpty(employee.EthnicityId))
        {
            ethnicityName = await dbContext.DanTocs
                .Where(e => e.Id == employee.EthnicityId)
                .Select(e => e.Name)
                .FirstOrDefaultAsync(cancellationToken);
        }

        var response = employee.Adapt<GetEmployeeDetailResponse>() with
        {
            RoleId = roleId,
            RoleName = roleName,
            Email = employee.User?.Email ?? string.Empty,
            IsActive = employee.User?.IsActive ?? false,
            UserType = employee.User?.UserType! ?? string.Empty,
            AvatarUrl = employee.User?.AvatarUrl!,
            Age = employee.DateOfBirth?.CalculateAge().ToString() ?? null,
            ReligionName = religionName,
            EthnicityName = ethnicityName,
            MaritalStatusName = maritalName
        };

        return new Response<GetEmployeeDetailResponse>(response);
    }
}