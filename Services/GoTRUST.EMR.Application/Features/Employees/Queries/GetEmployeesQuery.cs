﻿using GoTRUST.EMR.Application.Helpers;
using Mapster;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.Application.Features.Employees.Queries;

public record GetEmployeesQuery : PaginationRequest, IQuery<PaginationResponse<GetEmployeesResponse>>
{
    public string? SearchTerm { get; set; }
}

public record GetEmployeesResponse(
    Guid Id,
    string EmployeeCode,
    string FullName,
    string AvatarUrl,
    string Email,
    string ReligionId,
    string EthnicityId,
    Guid RoleId,
    string RoleName,
    string UserType,
    bool IsActive
);

public class GetEmployeesQueryHandler(
    IApplicationDbContext dbContext,
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager
) : IQueryHandler<GetEmployeesQuery, PaginationResponse<GetEmployeesResponse>>
{
    public async Task<PaginationResponse<GetEmployeesResponse>> Handle(GetEmployeesQuery request, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await _userManager.FindByIdAsync(userId.ToString() ?? string.Empty) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var query = dbContext.Employees
            .Include(e => e.User)
            .Where(e => e.HospitalId == hospitalId);
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var term = request.SearchTerm.Trim();
            query = query.Where(e =>
                (!string.IsNullOrEmpty(e.EmployeeCode) && e.EmployeeCode.ToLower().Contains(term.ToLower())) ||
                (!string.IsNullOrEmpty(e.FullName) && e.FullName.ToLower().Contains(term.ToLower())) ||
                (!string.IsNullOrEmpty(e.Email) && e.Email.ToLower().Contains(term.ToLower()))
            );
        }

        var employees = await query
            .AsNoTracking()
            .Include(e => e.User)
            .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
            .Take(request.PageSize!.Value)
            .ToListAsync(cancellationToken);
        var employeeCount = await query.CountAsync(cancellationToken);

        var enriched = new List<object>();

        foreach (var employee in employees)
        {
            Guid roleId = Guid.Empty;
            string roleName = "";
            var avatarUrl = "";
            if (employee.User != null)
            {
                avatarUrl = employee.User.AvatarUrl;
                var roles = await _userManager.GetRolesAsync(employee.User);
                if (roles.Count > 0)
                {
                    roleName = roles[0];
                    var role = await _roleManager.FindByNameAsync(roleName);
                    if (role != null)
                        roleId = role.Id;
                }
            }

            enriched.Add(new
            {
                employee.Id,
                employee.EmployeeCode,
                employee.FullName,
                AvatarUrl = avatarUrl,
                employee.ReligionId,
                employee.EthnicityId,
                employee.Email,
                RoleId = roleId,
                RoleName = roleName,
                UserType = employee.User?.UserType! ?? string.Empty,
                IsActive = employee.User?.IsActive ?? false
            });
        }

        var employeeList = enriched.Adapt<List<GetEmployeesResponse>>();

        return new PaginationResponse<GetEmployeesResponse>(request.PageIndex, request.PageSize, employeeCount, employeeList);
    }
}