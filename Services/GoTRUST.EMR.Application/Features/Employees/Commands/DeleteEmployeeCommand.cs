﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Domain.Events;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.Employees.Commands;

public record DeleteEmployeeRequest(
    Guid Id
) : ICommand<Response<DeleteEmployeeResponse>>;

public record DeleteEmployeeResponse(
    Guid Id,
    bool IsSuccess
);

public class DeleteEmployeeRequestValidator : AbstractValidator<DeleteEmployeeRequest>
{
    public DeleteEmployeeRequestValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteEmployeeHandler(
    IApplicationDbContext _dbContext,
    UserManager<User> _userManager
) : ICommandHandler<DeleteEmployeeRequest, Response<DeleteEmployeeResponse>>
{
    public async Task<Response<DeleteEmployeeResponse>> Handle(DeleteEmployeeRequest req, CancellationToken cancellationToken)
    {
        var employee = await _dbContext.Employees.FindAsync([req.Id], cancellationToken)
            ?? throw new EmployeeNotFoundException(req.Id);

        var user = await _userManager.FindByIdAsync(employee.UserId.ToString()!) ?? throw new NotFoundException($"Không tìm thấy User với Id: {employee.UserId}");

        _dbContext.Employees.Remove(employee);
        employee.AddDomainEvent(new EmployeeDeleteEvent(employee));
        await _dbContext.SaveChangesAsync(cancellationToken);

        var result = await _userManager.DeleteAsync(user);
        if (!result.Succeeded)
            throw new EmployeeDeleteFailedException();

        var response = new DeleteEmployeeResponse(employee.Id, true);

        return new Response<DeleteEmployeeResponse>(response);
    }
}