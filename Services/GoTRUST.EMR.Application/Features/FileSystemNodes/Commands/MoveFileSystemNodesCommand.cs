
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using JasperFx.Core;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;

public record MoveFileSystemNodesRequest(
    List<Guid> NodeIds,
    Guid NewParentId
) : ICommand<Response<MoveFileSystemNodesResponse>>;

public record MoveFileSystemNodesResponse(
    int TotalNodesMoved,
    int TotalDescendantsUpdated,
    List<string> Messages
);

public class MoveFileSystemNodesRequestValidator : AbstractValidator<MoveFileSystemNodesRequest>
{
    public MoveFileSystemNodesRequestValidator()
    {
        RuleFor(x => x.NodeIds)
            .NotEmpty()
            .WithMessage("Danh sách Node ID không được để trống")
            .Must(x => x.All(id => id != Guid.Empty))
            .WithMessage("Node ID không hợp lệ");

        RuleFor(x => x.NewParentId)
            .NotEmpty()
            .WithMessage("ParentId không được để trống");
    }
}

public class MoveFileSystemNodesHandler(
    IApplicationDbContext context,
    ILogger<MoveFileSystemNodesHandler> logger,
    IHttpContextAccessor httpContextAccessor,
    UserManager<User> userManager,
    RoleManager<Role> roleManager) : ICommandHandler<MoveFileSystemNodesRequest, Response<MoveFileSystemNodesResponse>>
{
    public async Task<Response<MoveFileSystemNodesResponse>> Handle(MoveFileSystemNodesRequest request, CancellationToken cancellationToken)
    {
        var currentUserId = httpContextAccessor.HttpContext?.User?.RetrieveUserIdFromPrincipal()
            ?? throw new UserNotFoundException();

        // Lấy user và roleIds giống GetRecentNodeQueryHandler
        var user = await userManager.FindByIdAsync(currentUserId.ToString())
            ?? throw new UserNotFoundException();
        var roles = await userManager.GetRolesAsync(user);
        var roleIds = await roleManager.Roles
            .Where(r => roles.Contains(r.Name ?? string.Empty))
            .Select(r => r.Id)
            .ToListAsync(cancellationToken);

        var messages = new List<string>();
        int totalDescendantsUpdated = 0;

        // 1. Kiểm tra parentId không nằm trong danh sách nodeIds cần move
        if (request.NodeIds.Contains(request.NewParentId))
            throw new BadRequestException("Thư mục cha không được nằm trong danh sách node cần move.");

        // 1.1 Kiểm tra parentId mới không là con của bất kỳ node cần move (dùng helper)
        var parentNode = await context.FileSystemNodes
            .AsNoTracking()
            .FirstOrDefaultAsync(n => n.Id == request.NewParentId, cancellationToken);
        if (parentNode != null && FileSystemNodeHelper.IsParentAChildOfNodes(parentNode, request.NodeIds))
            throw new BadRequestException("Thư mục cha mới không được là con của node cần move.");

        // 2. Lấy các node cần move
        var nodes = await context.FileSystemNodes
            .Where(n => request.NodeIds.Contains(n.Id)
                && (
                    n.OwnerId == currentUserId
                    || n.UserNodes.Any(x => x.UserId == currentUserId)
                    || n.RoleNodes.Any(x => roleIds.Contains(x.RoleId))
                    || n.ShareStatus == FileSystemNodeShareStatus.Public
                ))
            .ToListAsync(cancellationToken);

        if (nodes.Count != request.NodeIds.Count)
            throw new BadRequestException("Không tìm thấy hoặc không có quyền với một số node.");

        // 3. Lấy node parent mới
        var newParent = await context.FileSystemNodes
            .FirstOrDefaultAsync(n => n.Id == request.NewParentId, cancellationToken);

        if (newParent == null)
            throw new NotFoundException("Không tìm thấy thư mục cha mới.");

        foreach (var node in nodes)
        {
            var oldPath = node.Path;
            var newPath = $"{newParent.Path}{node.Id}/";
            node.ParentId = newParent.Id;

            // Nếu là folder, cập nhật path cho tất cả descendants
            if (node.NodeType == FileSystemNodeType.Folder)
            {
                var descendants = await context.FileSystemNodes
                    .Where(n => n.Path.StartsWith($"{oldPath}") && n.Id != node.Id)
                    .ToListAsync(cancellationToken);
                descendants.Add(node); // Thêm node chính vào danh sách descendants để cập nhật path
                foreach (var desc in descendants)
                {
                    desc.Path = desc.Path.Replace($"{oldPath}", $"{newPath}");
                    // Cập nhật root share cho UserFileSystemNode và RoleFileSystemNode
                    await UpdateRootShareStatus(desc, cancellationToken);
                }
                totalDescendantsUpdated += descendants.Count;
                context.FileSystemNodes.UpdateRange(descendants);
            }
            else
            {
                node.Path = newPath;
                // Cập nhật root share cho UserFileSystemNode và RoleFileSystemNode
                await UpdateRootShareStatus(node, cancellationToken);
                context.FileSystemNodes.Update(node);
            }
        }
        await context.SaveChangesAsync(cancellationToken);

        logger.LogInformation("User {UserId} đã move {NodeCount} nodes tới parent {ParentId}", currentUserId, nodes.Count, newParent.Id);

        var response = new MoveFileSystemNodesResponse(
            TotalNodesMoved: nodes.Count,
            TotalDescendantsUpdated: totalDescendantsUpdated,
            Messages: messages
        );
        return new Response<MoveFileSystemNodesResponse>(response);
    }

    private async Task UpdateRootShareStatus(FileSystemNode node, CancellationToken cancellationToken)
    {
        if (node == null) return;

        var pathParts = node.Path.Split('/', StringSplitOptions.RemoveEmptyEntries);
        var parentIds = pathParts
            .Remove(node.Id.ToString())
            .Select(part => Guid.TryParse(part, out var guid) ? guid : Guid.Empty)
            .Where(guid => guid != Guid.Empty)
            .ToList();

        // UserFileSystemNode
        var userShares = await context.UserFileSystemNodes
            .Where(x => x.FileSystemNodeId == node.Id)
            .ToListAsync(cancellationToken);

        foreach (var share in userShares)
        {
            share.IsRootShare = !await context.UserFileSystemNodes
                .AnyAsync(x => parentIds.Contains(x.FileSystemNodeId) && x.UserId == share.UserId && x.IsAccessible, cancellationToken);
        }

        context.UserFileSystemNodes.UpdateRange(userShares);

        // RoleFileSystemNode
        var roleShares = await context.RoleFileSystemNodes
            .Where(x => x.FileSystemNodeId == node.Id)
            .ToListAsync(cancellationToken);

        foreach (var share in roleShares)
        {
            share.IsRootShare = !await context.RoleFileSystemNodes
                .AnyAsync(x => parentIds.Contains(x.FileSystemNodeId) && x.RoleId == share.RoleId && x.IsAccessible, cancellationToken);
        }

        context.RoleFileSystemNodes.UpdateRange(roleShares);
    }
}
