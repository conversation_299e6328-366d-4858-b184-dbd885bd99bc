using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Mvc;
using System.IO.Compression;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Commands
{
    public class DownloadFolderCommand : ICommand<Response<FileStreamResult>>
    {
        public List<Guid> FolderNodeIds { get; init; } = new();
    }

    public class DownloadFolderCommandValidator : AbstractValidator<DownloadFolderCommand>
    {
        public DownloadFolderCommandValidator()
        {
            RuleFor(x => x.FolderNodeIds)
                .NotEmpty()
                .WithMessage("FolderNodeIds is required.");
        }
    }

    public class DownloadFolderCommandHandler(
        IApplicationDbContext dbContext,
        IHttpContextAccessor httpContextAccessor,
        ILogger<DownloadFolderCommandHandler> logger,
        UserManager<User> userManager,
        RoleManager<Role> roleManager,
        IConfiguration configuration) : ICommandHandler<DownloadFolderCommand, Response<FileStreamResult>>
    {
        private readonly IApplicationDbContext _dbContext = dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        private readonly ILogger<DownloadFolderCommandHandler> _logger = logger;
        private readonly UserManager<User> _userManager = userManager;
        private readonly RoleManager<Role> _roleManager = roleManager;
        private readonly IConfiguration _configuration = configuration;

        public async Task<Response<FileStreamResult>> Handle(DownloadFolderCommand request, CancellationToken cancellationToken)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            var hospitalId = httpContext?.User?.RetrieveHospitalIdFromPrincipal() ?? throw new NoAccessHospitalException();
            var currentUserId = httpContext?.User?.RetrieveUserIdFromPrincipal() ?? throw new UnauthorizedAccessException("Không thể xác định thông tin người dùng. Vui lòng đăng nhập lại.");
            var currentUser = await _userManager.FindByIdAsync(currentUserId.ToString()) ?? throw new UserNotFoundException();
            var roles = await _userManager.GetRolesAsync(currentUser);
            if (roles == null || !roles.Any())
            {
                _logger.LogWarning("User {UserId} has no roles assigned.", currentUserId);
                throw new UnauthorizedAccessException("Người dùng không có quyền truy cập.");
            }
            var roleIds = await _roleManager.Roles
                .Where(r => roles.Contains(r.Name ?? string.Empty))
                .Select(r => r.Id)
                .ToListAsync(cancellationToken);

            // Lấy tất cả các node liên quan (folders + files) từ DB một lần
            var allNodes = await _dbContext.FileSystemNodes
                .AsNoTracking()
                .Include(x => x.Owner)
                .Include(x => x.UserNodes)
                .Include(x => x.RoleNodes)
                .ToListAsync(cancellationToken);

            // Sử dụng stack lưu cả node và path hiện tại
            var stack = new Stack<(FileSystemNode node, string path)>();
            var visited = new HashSet<Guid>();
            var filesToDownload = new List<(FileSystemNode file, string relativePath)>();

            // Đảm bảo tên duy nhất cho các folder gốc (top-level)
            var topLevelFolderNames = new Dictionary<string, int>();
            var topLevelFoldersWithUniqueNames = new List<(FileSystemNode folder, string uniqueName)>();
            foreach (var folderId in request.FolderNodeIds)
            {
                var rootFolder = allNodes.FirstOrDefault(x => x.Id == folderId && x.NodeType == FileSystemNodeType.Folder);
                if (!FileSystemNodeHelper.IsNodeAccessible(rootFolder, hospitalId, currentUserId, roleIds))
                {
                    throw new NotFoundException($"Folder với ID {folderId} không hợp lệ hoặc không có quyền truy cập.");
                }
                var folderName = rootFolder!.Name;
                int suffix = topLevelFolderNames.TryGetValue(folderName, out int value) ? value + 1 : 0;
                var uniqueFolderName = folderName;
                if (suffix > 0)
                    uniqueFolderName = $"{folderName} ({suffix})";
                topLevelFolderNames[folderName] = suffix;
                stack.Push((rootFolder!, uniqueFolderName));
                topLevelFoldersWithUniqueNames.Add((rootFolder!, uniqueFolderName));
            }

            while (stack.Count > 0)
            {
                var (node, currentPath) = stack.Pop();
                if (node == null || visited.Contains(node.Id)) continue;
                visited.Add(node.Id);

                if (node.NodeType == FileSystemNodeType.File)
                {
                    if (!FileSystemNodeHelper.IsNodeAccessible(node, hospitalId, currentUserId, roleIds))
                        continue;
                    filesToDownload.Add((node, currentPath));
                }
                else if (node.NodeType == FileSystemNodeType.Folder)
                {
                    var children = allNodes.Where(x => x.ParentId == node.Id).ToList();
                    foreach (var child in children)
                    {
                        var childPath = child.NodeType == FileSystemNodeType.Folder
                            ? $"{currentPath}/{child.Name}"
                            : currentPath; // file giữ nguyên path cha
                        stack.Push((child, childPath));
                    }
                }
            }

            if (filesToDownload.Count == 0)
            {
                throw new NotFoundException("Không tìm thấy file nào trong các thư mục được chọn hoặc không có quyền truy cập.");
            }

            using var ms = new MemoryStream();
            using (var zip = new ZipArchive(ms, ZipArchiveMode.Create, true))
            {
                // Đảm bảo tên duy nhất cho folder rỗng
                var usedFolderNamesByPath = new Dictionary<string, Dictionary<string, int>>();
                var folderIds = request.FolderNodeIds;
                var allFolderNodes = allNodes.Where(x => x.NodeType == FileSystemNodeType.Folder && folderIds.Contains(x.Id)).ToList();
                var allChildFolderNodes = allNodes.Where(x => x.NodeType == FileSystemNodeType.Folder).ToList();
                var allFileNodes = allNodes.Where(x => x.NodeType == FileSystemNodeType.File).ToList();

                // Sử dụng unique top-level folder names cho foldersToCheck
                var foldersToCheck = new Stack<(FileSystemNode folder, string parentZipPath, string folderName)>();
                foreach (var (folder, uniqueName) in topLevelFoldersWithUniqueNames)
                {
                    foldersToCheck.Push((folder, "", uniqueName));
                }
                while (foldersToCheck.Count > 0)
                {
                    var (folder, parentZipPath, folderName) = foldersToCheck.Pop();
                    var childFolders = allChildFolderNodes.Where(x => x.ParentId == folder.Id).ToList();
                    var childFiles = allFileNodes.Where(x => x.ParentId == folder.Id).ToList();
                    // Đảm bảo tên duy nhất cho folder trong parentZipPath
                    if (!usedFolderNamesByPath.ContainsKey(parentZipPath))
                        usedFolderNamesByPath[parentZipPath] = new Dictionary<string, int>();
                    var uniqueFolderName = folderName;
                    int suffix = usedFolderNamesByPath[parentZipPath].TryGetValue(folderName, out int value) ? value + 1 : 0;
                    if (suffix > 0)
                        uniqueFolderName = $"{folderName} ({suffix})";
                    usedFolderNamesByPath[parentZipPath][folderName] = suffix;
                    var entryFolderPath = string.IsNullOrWhiteSpace(parentZipPath) ? uniqueFolderName + "/" : parentZipPath + "/" + uniqueFolderName + "/";
                    if (childFolders.Count == 0 && childFiles.Count == 0)
                    {
                        zip.CreateEntry(entryFolderPath);
                    }
                    foreach (var childFolder in childFolders)
                    {
                        foldersToCheck.Push((childFolder, entryFolderPath.TrimEnd('/'), childFolder.Name));
                    }
                }

                // Đảm bảo tên duy nhất cho file trong từng folder
                var usedFileNamesByPath = new Dictionary<string, Dictionary<string, int>>();
                foreach (var (file, relativePath) in filesToDownload)
                {
                    var fileBytes = await FileSystemNodeHelper.GetFileContentAsync(file, _configuration, _logger, cancellationToken);
                    // Lấy tên file đầy đủ bao gồm cả phần mở rộng
                    string fileNameWithExt = !string.IsNullOrWhiteSpace(file.MimeType) && !file.Name.EndsWith(file.MimeType)
                        ? file.Name + file.MimeType
                        : file.Name;
                    var pathKey = relativePath ?? "";
                    if (!usedFileNamesByPath.ContainsKey(pathKey))
                        usedFileNamesByPath[pathKey] = new Dictionary<string, int>();
                    int suffix = usedFileNamesByPath[pathKey].ContainsKey(fileNameWithExt) ? usedFileNamesByPath[pathKey][fileNameWithExt] + 1 : 0;
                    string entryFileName = fileNameWithExt;
                    if (suffix > 0)
                        entryFileName = $"{Path.GetFileNameWithoutExtension(fileNameWithExt)} ({suffix}){Path.GetExtension(fileNameWithExt)}";
                    usedFileNamesByPath[pathKey][fileNameWithExt] = suffix;
                    var entryPath = string.IsNullOrWhiteSpace(relativePath) ? entryFileName : $"{relativePath}/{entryFileName}";
                    var entry = zip.CreateEntry(entryPath, CompressionLevel.Fastest);
                    using var entryStream = entry.Open();
                    await entryStream.WriteAsync(fileBytes, cancellationToken);
                }
            }
            // Copy sang MemoryStream mới để tránh lỗi stream bị đóng
            var zipBytes = ms.ToArray();
            var outStream = new MemoryStream(zipBytes)
            {
                Position = 0
            };
            string fileName;
            if (request.FolderNodeIds.Count == 1)
            {
                var folderId = request.FolderNodeIds.First();
                var folder = allNodes.FirstOrDefault(x => x.Id == folderId && x.NodeType == FileSystemNodeType.Folder);
                fileName = folder != null ? $"{folder.Name}.zip" : "folder.zip";
            }
            else
            {
                fileName = "folders.zip";
            }
            var fileResult = new FileStreamResult(outStream, "application/zip")
            {
                FileDownloadName = fileName
            };
            return new Response<FileStreamResult>(fileResult);
        }
    }
}