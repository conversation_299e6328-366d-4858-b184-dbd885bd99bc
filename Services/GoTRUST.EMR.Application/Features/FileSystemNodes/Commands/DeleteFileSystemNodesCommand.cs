﻿using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;

public record DeleteFileSystemNodeFilesCommand(List<Guid> FileSystemNodeIds)
 : ICommand<Response<DeleteFileSystemNodeFilesResponse>>;

public record DeleteFileSystemNodeFilesResponse(
    bool IsSuccess
);

public class DeleteFileSystemNodeFilesHandler(
    IApplicationDbContext _dbContext
) : ICommandHandler<DeleteFileSystemNodeFilesCommand, Response<DeleteFileSystemNodeFilesResponse>>
{
    public async Task<Response<DeleteFileSystemNodeFilesResponse>> Handle(DeleteFileSystemNodeFilesCommand req, CancellationToken cancellationToken)
    {
        var nodes = await _dbContext.FileSystemNodes
            .Where(x => req.FileSystemNodeIds.Contains(x.Id))
            .ToListAsync(cancellationToken);

        var folderIds = new List<Guid>();

        foreach (var node in nodes)
        {
            node.IsDeleted = true;

            if (node.NodeType == FileSystemNodeType.Folder)
            {
                folderIds.Add(node.Id);
            }
        }

        if (folderIds.Count > 0)
        {
            var childFiles = await _dbContext.FileSystemNodes
                .Where(x => folderIds.Contains(x.ParentId!.Value) && x.NodeType == FileSystemNodeType.File)
                .ToListAsync(cancellationToken);

            foreach (var file in childFiles)
            {
                file.IsDeleted = true;
            }

            _dbContext.FileSystemNodes.UpdateRange(childFiles);
        }

        _dbContext.FileSystemNodes.UpdateRange(nodes);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return new Response<DeleteFileSystemNodeFilesResponse>(new DeleteFileSystemNodeFilesResponse(true));
    }
}