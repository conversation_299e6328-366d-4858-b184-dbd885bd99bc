

using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;

public record ShareFileSystemNodeAllRequest(
    List<Guid> NodeIds,
    bool IsSharing = true
) : ICommand<Response<ShareFileSystemNodeAllResponse>>;

public record ShareFileSystemNodeAllResponse(
    int TotalNodesProcessed,
    int TotalPublicUpdated,
    int TotalPublicRemoved,
    List<string> Messages
);

public class ShareFileSystemNodeAllRequestValidator : AbstractValidator<ShareFileSystemNodeAllRequest>
{
    public ShareFileSystemNodeAllRequestValidator()
    {
        RuleFor(x => x.NodeIds)
            .NotEmpty()
            .WithMessage("Danh sách Node ID không được để trống")
            .Must(x => x.All(id => id != Guid.Empty))
            .WithMessage("Node ID không hợp lệ");
    }
}


public class ShareFileSystemNodeAllHandler(
    IApplicationDbContext context,
    ILogger<ShareFileSystemNodeAllHandler> logger,
    IHttpContextAccessor httpContextAccessor,
    UserManager<User> userManager,
    RoleManager<Role> roleManager) : ICommandHandler<ShareFileSystemNodeAllRequest, Response<ShareFileSystemNodeAllResponse>>
{
    public async Task<Response<ShareFileSystemNodeAllResponse>> Handle(ShareFileSystemNodeAllRequest request, CancellationToken cancellationToken)
    {
        var currentUserId = httpContextAccessor.HttpContext?.User?.RetrieveUserIdFromPrincipal()
            ?? throw new UserNotFoundException();

        var messages = new List<string>();
        int totalPublicUpdated = 0;
        int totalPublicRemoved = 0;

        // Lấy user và roleIds giống MoveFileSystemNodesHandler
        var user = await userManager.FindByIdAsync(currentUserId.ToString())
            ?? throw new UserNotFoundException();
        var roles = await userManager.GetRolesAsync(user);
        var roleIds = await roleManager.Roles
            .Where(r => roles.Contains(r.Name ?? string.Empty))
            .Select(r => r.Id)
            .ToListAsync(cancellationToken);

        // Validate nodes tồn tại và user có quyền chia sẻ (owner, user share, role share, hoặc public)
        var nodes = await context.FileSystemNodes
            .Where(n => request.NodeIds.Contains(n.Id)
                && (
                    n.OwnerId == currentUserId
                    || n.UserNodes.Any(x => x.UserId == currentUserId)
                    || n.RoleNodes.Any(x => roleIds.Contains(x.RoleId))
                    || n.ShareStatus == FileSystemNodeShareStatus.Public
                ))
            .ToListAsync(cancellationToken);

        if (nodes.Count != request.NodeIds.Count)
        {
            var missingNodeIds = request.NodeIds.Except(nodes.Select(n => n.Id)).ToList();
            throw new BadRequestException($"Không tìm thấy hoặc không có quyền truy cập vào các node: {string.Join(", ", missingNodeIds)}");
        }

        // Lấy tất cả node (bao gồm node con) cần xử lý bằng helper
        var allNodesToProcess = new List<FileSystemNode>();
        foreach (var node in nodes)
        {
            var nodeAndDescendants = await FileSystemNodeHelper.GetNodeAndAllDescendantsAsync(context, node, cancellationToken);
            allNodesToProcess.AddRange(nodeAndDescendants);
        }
        allNodesToProcess = allNodesToProcess.DistinctBy(n => n.Id).ToList();

        var nodesToUpdate = new List<FileSystemNode>();
        foreach (var n in allNodesToProcess)
        {
            if (request.IsSharing)
            {
                if (n.ShareStatus != FileSystemNodeShareStatus.Public)
                {
                    n.ShareStatus = FileSystemNodeShareStatus.Public;
                    nodesToUpdate.Add(n);
                    totalPublicUpdated++;
                }
            }
            else
            {
                // Tắt toàn bộ quyền user/role share (IsAccessible = false)
                var userShares = await context.UserFileSystemNodes.Where(x => x.FileSystemNodeId == n.Id && x.IsAccessible).ToListAsync(cancellationToken);
                foreach (var us in userShares)
                {
                    us.IsAccessible = false;
                }
                if (userShares.Count > 0)
                    context.UserFileSystemNodes.UpdateRange(userShares);

                var roleShares = await context.RoleFileSystemNodes.Where(x => x.FileSystemNodeId == n.Id && x.IsAccessible).ToListAsync(cancellationToken);
                foreach (var rs in roleShares)
                {
                    rs.IsAccessible = false;
                }
                if (roleShares.Count > 0)
                    context.RoleFileSystemNodes.UpdateRange(roleShares);

                n.ShareStatus = FileSystemNodeShareStatus.Private;
                nodesToUpdate.Add(n);
                totalPublicRemoved++;
            }
        }
        if (nodesToUpdate.Count > 0)
        {
            context.FileSystemNodes.UpdateRange(nodesToUpdate);
        }

        await context.SaveChangesAsync(cancellationToken);

        string action = request.IsSharing ? "công khai" : "hủy công khai";
        logger.LogInformation("User {CurrentUserId} đã {Action} {NodeCount} nodes thành public. Updated: {Updated}, Removed: {Removed}",
            currentUserId, action, request.NodeIds.Count, totalPublicUpdated, totalPublicRemoved);

        var response = new ShareFileSystemNodeAllResponse(
            TotalNodesProcessed: request.NodeIds.Count,
            TotalPublicUpdated: totalPublicUpdated,
            TotalPublicRemoved: totalPublicRemoved,
            Messages: messages
        );

        return new Response<ShareFileSystemNodeAllResponse>(response);
    }

    // Đã dùng helper chung, không cần hàm này nữa
}