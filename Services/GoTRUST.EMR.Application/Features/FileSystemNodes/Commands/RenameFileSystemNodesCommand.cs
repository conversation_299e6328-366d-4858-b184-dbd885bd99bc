﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;

public record RenameFileSystemNodesCommand(Guid FileSystemNodeId, string Name)
    : ICommand<Response<RenameFileSystemNodesResponse>>;

public record RenameFileSystemNodesResponse(
    bool IsSuccess
);

public class RenameFileSystemNodesHandler(
    IApplicationDbContext _dbContext
) : ICommandHandler<RenameFileSystemNodesCommand, Response<RenameFileSystemNodesResponse>>
{
    public async Task<Response<RenameFileSystemNodesResponse>> Handle(RenameFileSystemNodesCommand req, CancellationToken cancellationToken)
    {
        var node = await _dbContext.FileSystemNodes
            .FirstOrDefaultAsync(x => x.Id == req.FileSystemNodeId, cancellationToken) ?? throw new NotFoundException($"FileSystemNode with Id {req.FileSystemNodeId} not found.");
        node.Name = req.Name;
        _dbContext.FileSystemNodes.Update(node);

        await _dbContext.SaveChangesAsync(cancellationToken);

        return new Response<RenameFileSystemNodesResponse>(new RenameFileSystemNodesResponse(true));
    }
}