using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using Mapster;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;

/// <summary>
/// Command để tạo thư mục mới
/// </summary>
public record CreateFolderCommand(
    string Name,
    Guid? ParentId = null
) : ICommand<Response<CreateFolderResponse>>;

/// <summary>
/// Validator cho CreateFolderCommand
/// </summary>
public class CreateFolderCommandValidator : AbstractValidator<CreateFolderCommand>
{
    public CreateFolderCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Tên thư mục không được để trống")
            .MaximumLength(255)
            .WithMessage("Tên thư mục không được vượt quá 255 ký tự")
            .Matches(@"^[^<>:""/\\|?*]+$")
            .WithMessage("Tên thư mục chứa ký tự không hợp lệ");

        RuleFor(x => x.ParentId)
            .NotEqual(Guid.Empty)
            .When(x => x.ParentId.HasValue)
            .WithMessage("ParentId không hợp lệ");
    }
}

/// <summary>
/// Handler cho CreateFolderCommand
/// </summary>
public class CreateFolderCommandHandler(
    IApplicationDbContext context,
    ILogger<CreateFolderCommandHandler> logger,
    IHttpContextAccessor httpContextAccessor,
    UserManager<User> userManager)
    : ICommandHandler<CreateFolderCommand, Response<CreateFolderResponse>>
{
    public async Task<Response<CreateFolderResponse>> Handle(
        CreateFolderCommand request, 
        CancellationToken cancellationToken)
    {
        // 1. Lấy thông tin user hiện tại
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";
        var currentUserId = httpContextAccessor.HttpContext?.User?.RetrieveUserIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin người dùng. Vui lòng đăng nhập lại.");

        var hospitalId = httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
            ?? throw new NoAccessHospitalException();

        logger.LogInformation("Creating folder '{FolderName}' for user {UserId} in hospital {HospitalId}", 
            request.Name, currentUserId, hospitalId);

        // 2. Kiểm tra user có tồn tại và thuộc hospital không
        var user = await userManager.FindByIdAsync(currentUserId.ToString())
            ?? throw new UnauthorizedAccessException("Người dùng không tồn tại.");

        if (user.HospitalId != hospitalId)
        {
            throw new UnauthorizedAccessException("Người dùng không thuộc bệnh viện này.");
        }

        // 3. Xử lý parent folder nếu có
        FileSystemNode? parentFolder = null;
        string folderPath = "/";

        if (request.ParentId.HasValue)
        {
            // Kiểm tra parent folder có tồn tại không
            parentFolder = await context.FileSystemNodes
                .AsNoTracking()
                .FirstOrDefaultAsync(f => f.Id == request.ParentId.Value && 
                                         f.NodeType == FileSystemNodeType.Folder && 
                                         !f.IsDeleted, cancellationToken)
                ?? throw new ArgumentException($"Thư mục cha với ID {request.ParentId} không tồn tại.");

            // Kiểm tra user có quyền truy cập parent folder không
            var hasAccess = await HasAccessToFolder(currentUserId, request.ParentId.Value, cancellationToken);
            if (!hasAccess)
            {
                throw new UnauthorizedAccessException("Bạn không có quyền tạo thư mục trong thư mục này.");
            }

            // Tạo path dựa trên parent
            folderPath = $"{parentFolder.Path.TrimEnd('/')}/";
        }

        // 4. Kiểm tra trùng tên trong cùng parent
        var existingFolder = await context.FileSystemNodes
            .AsNoTracking()
            .FirstOrDefaultAsync(f => f.Name == request.Name && 
                                     f.ParentId == request.ParentId && 
                                     f.NodeType == FileSystemNodeType.Folder && 
                                     !f.IsDeleted, cancellationToken);

        if (existingFolder != null)
        {
            throw new InvalidOperationException($"Thư mục với tên '{request.Name}' đã tồn tại trong thư mục này.");
        }

        // 5. Tạo folder mới
        var newFolder = new FileSystemNode
        {
            Name = request.Name,
            NodeType = FileSystemNodeType.Folder,
            ParentId = request.ParentId,
            OwnerId = currentUserId,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = currentUser,
            UpdatedAt = DateTime.UtcNow,
            UpdatedBy = currentUser
        };
        // 6. Lưu vào database
        context.FileSystemNodes.Add(newFolder);
        // Cập nhật path cho folder mới
        newFolder.Path = $"{folderPath}{newFolder.Id}/";
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
        {
            throw new InvalidOperationException("Không thể tạo thư mục. Vui lòng thử lại.");
        }

        logger.LogInformation("Successfully created folder '{FolderName}' with ID {FolderId} for user {UserId}", 
            request.Name, newFolder.Id, currentUserId);

        // 7. Tạo response
        var response = new CreateFolderResponse
        {
            Id = newFolder.Id,
            Name = newFolder.Name,
            Path = newFolder.Path,
            ParentId = newFolder.ParentId,
            OwnerId = newFolder.OwnerId,
            CreatedAt = newFolder.CreatedAt ?? DateTime.UtcNow
        };

        return new Response<CreateFolderResponse>(response);
    }

    /// <summary>
    /// Kiểm tra user có quyền truy cập folder không
    /// </summary>
    private async Task<bool> HasAccessToFolder(Guid userId, Guid folderId, CancellationToken cancellationToken)
    {
        // User có quyền nếu:
        // 1. User là owner của folder
        var isOwner = await context.FileSystemNodes
            .AsNoTracking()
            .AnyAsync(f => f.Id == folderId && f.OwnerId == userId && !f.IsDeleted, cancellationToken);

        if (isOwner) return true;

        // 2. User được chia sẻ quyền truy cập folder
        var hasSharedAccess = await context.UserFileSystemNodes
            .AsNoTracking()
            .AnyAsync(ufn => ufn.UserId == userId &&
                            ufn.FileSystemNodeId == folderId &&
                            ufn.IsAccessible, cancellationToken);

        return hasSharedAccess;
    }
}
