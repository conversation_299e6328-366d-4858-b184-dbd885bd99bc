using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Mvc;
using System.IO.Compression;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Commands
{
public class DownloadFileCommand : ICommand<Response<FileStreamResult>>
{
    public List<Guid> FileSystemNodeIds { get; init; } = new();
}

public class DownloadFileCommandValidator : AbstractValidator<DownloadFileCommand>
{
    public DownloadFileCommandValidator()
    {
        RuleFor(x => x.FileSystemNodeIds)
            .NotEmpty()
            .WithMessage("FileSystemNodeIds is required.");
    }
}

public class DownloadFileCommandHandler : ICommandHandler<DownloadFileCommand, Response<FileStreamResult>>
{
    private readonly IApplicationDbContext _dbContext;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<DownloadFileCommandHandler> _logger;
    private readonly UserManager<User> _userManager;
    private readonly RoleManager<Role> _roleManager;
    private readonly IConfiguration _configuration;

    public DownloadFileCommandHandler(IApplicationDbContext dbContext, IHttpContextAccessor httpContextAccessor, ILogger<DownloadFileCommandHandler> logger, UserManager<User> userManager, RoleManager<Role> roleManager, IConfiguration configuration)
    {
        _dbContext = dbContext;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
        _userManager = userManager;
        _roleManager = roleManager;
        _configuration = configuration;
    }

    public async Task<Response<FileStreamResult>> Handle(DownloadFileCommand request, CancellationToken cancellationToken)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        var hospitalId = httpContext?.User?.RetrieveHospitalIdFromPrincipal() ?? throw new NoAccessHospitalException();
        var currentUserId = httpContext?.User?.RetrieveUserIdFromPrincipal() ?? throw new UnauthorizedAccessException("Không thể xác định thông tin người dùng. Vui lòng đăng nhập lại.");
        var currentUser = await _userManager.FindByIdAsync(currentUserId.ToString()) ?? throw new UserNotFoundException();
        var roles = await _userManager.GetRolesAsync(currentUser);
        if (roles == null || !roles.Any())
        {
            _logger.LogWarning("User {UserId} has no roles assigned.", currentUserId);
            throw new UnauthorizedAccessException("Người dùng không có quyền truy cập.");
        }
        var roleIds = await _roleManager.Roles
            .Where(r => roles.Contains(r.Name ?? string.Empty))
            .Select(r => r.Id)
            .ToListAsync(cancellationToken);

        // Lấy tất cả các node từ DB một lần
        var fileNodes = await _dbContext.FileSystemNodes
            .AsNoTracking()
            .Include(x => x.Owner)
            .Include(x => x.UserNodes)
            .Include(x => x.RoleNodes)
            .Where(x => request.FileSystemNodeIds.Contains(x.Id))
            .ToListAsync(cancellationToken);

        // Kiểm tra từng file: nếu có file nào không hợp lệ thì trả lỗi luôn (dùng helper)
        var validFiles = new List<FileSystemNode>();
        foreach (var id in request.FileSystemNodeIds)
        {
            var fileNode = fileNodes.FirstOrDefault(x => x.Id == id);
            if (!FileSystemNodeHelper.IsNodeAccessible(fileNode, hospitalId, currentUserId, roleIds) || fileNode?.NodeType != FileSystemNodeType.File)
            {
                throw new NotFoundException($"File với ID {id} không hợp lệ hoặc không có quyền truy cập.");
            }
            validFiles.Add(fileNode!);
        }

        if (validFiles.Count == 1)
        {
            var file = validFiles.First();
            var fileBytes = await FileSystemNodeHelper.GetFileContentAsync(file, _configuration, _logger, cancellationToken);
            var stream = new MemoryStream(fileBytes);
            // MimeType là đuôi mở rộng (ví dụ: .pdf, .txt)
            string fileName = file.Name;
            if (!string.IsNullOrWhiteSpace(file.MimeType) && !fileName.EndsWith(file.MimeType))
            {
                fileName += file.MimeType;
            }
            // Xác định contentType từ đuôi mở rộng
            string contentType = "application/octet-stream";
            switch (file.MimeType)
            {
                case ".pdf":
                    contentType = "application/pdf";
                    break;
                case ".txt":
                    contentType = "text/plain";
                    break;
                case ".doc":
                case ".docx":
                    contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                    break;
                case ".xls":
                case ".xlsx":
                    contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    break;
                // Thêm các loại khác nếu cần
            }
            var fileResult = new FileStreamResult(stream, contentType)
            {
                FileDownloadName = fileName
            };
            return new Response<FileStreamResult>(fileResult);
        }
        else
        {
            using var ms = new MemoryStream();
            using (var zip = new ZipArchive(ms, ZipArchiveMode.Create, true))
            {
                var usedNames = new Dictionary<string, int>();
                foreach (var file in validFiles)
                {
                    var fileBytes = await FileSystemNodeHelper.GetFileContentAsync(file, _configuration, _logger, cancellationToken);
                    // Lấy tên file đầy đủ bao gồm cả phần mở rộng
                    string fileNameWithExt = !string.IsNullOrWhiteSpace(file.MimeType) && !file.Name.EndsWith(file.MimeType)
                        ? file.Name + file.MimeType
                        : file.Name;
                    string fileName = fileNameWithExt;
                    if (usedNames.TryGetValue(fileNameWithExt, out int value))
                    {
                        usedNames[fileNameWithExt] = ++value;
                        fileName = $"{Path.GetFileNameWithoutExtension(fileNameWithExt)} ({value}){Path.GetExtension(fileNameWithExt)}";
                    }
                    else
                    {
                        usedNames[fileNameWithExt] = 0;
                    }
                    var entry = zip.CreateEntry(fileName, CompressionLevel.Fastest);
                    using var entryStream = entry.Open();
                    await entryStream.WriteAsync(fileBytes, cancellationToken);
                }
            }
            // Copy sang MemoryStream mới để tránh lỗi stream bị đóng
            var zipBytes = ms.ToArray();
            var outStream = new MemoryStream(zipBytes);
            outStream.Position = 0;
            var fileResult = new FileStreamResult(outStream, "application/zip")
            {
                FileDownloadName = "files.zip"
            };
            return new Response<FileStreamResult>(fileResult);
        }
    }
}
}