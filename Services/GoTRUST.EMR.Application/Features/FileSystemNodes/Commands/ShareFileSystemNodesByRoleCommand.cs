using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Commands;

public record ShareFileSystemNodesByRoleRequest(
    List<Guid> NodeIds,
    List<Guid> RoleIds,
    bool IsSharing = true
) : ICommand<Response<ShareFileSystemNodesByRoleResponse>>;

public record ShareFileSystemNodesByRoleResponse(
    int TotalNodesProcessed,
    int TotalRolesProcessed,
    int TotalShareRecordsCreated,
    int TotalShareRecordsRemoved,
    List<string> Messages
);

public class ShareFileSystemNodesByRoleRequestValidator : AbstractValidator<ShareFileSystemNodesByRoleRequest>
{
    public ShareFileSystemNodesByRoleRequestValidator()
    {
        RuleFor(x => x.NodeIds)
            .NotEmpty()
            .WithMessage("Danh sách Node ID không được để trống")
            .Must(x => x.All(id => id != Guid.Empty))
            .WithMessage("Node ID không hợp lệ");

        RuleFor(x => x.RoleIds)
            .NotEmpty()
            .WithMessage("Danh sách Role ID không được để trống")
            .Must(x => x.All(id => id != Guid.Empty))
            .WithMessage("Role ID không hợp lệ");
    }
}

public class ShareFileSystemNodesByRoleHandler(
    IApplicationDbContext context,
    ILogger<ShareFileSystemNodesByRoleHandler> logger,
    IHttpContextAccessor httpContextAccessor,
    UserManager<User> userManager,
    RoleManager<Role> roleManager) : ICommandHandler<ShareFileSystemNodesByRoleRequest, Response<ShareFileSystemNodesByRoleResponse>>
{
    public async Task<Response<ShareFileSystemNodesByRoleResponse>> Handle(ShareFileSystemNodesByRoleRequest request, CancellationToken cancellationToken)
    {
        // 1. Lấy thông tin user hiện tại và HospitalId từ claims
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";
        var currentUserId = httpContextAccessor.HttpContext?.User?.RetrieveUserIdFromPrincipal()
            ?? throw new UserNotFoundException();

        var hospitalId = httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
            ?? throw new UserNotFoundException();

        var messages = new List<string>();
        int totalShareRecordsCreated = 0;
        int totalShareRecordsRemoved = 0;
        var nodesToUpdate = new List<FileSystemNode>();

        // 2. Validate nodes tồn tại và user có quyền chia sẻ (owner, user share, role share, hoặc public)
        var user = await userManager.FindByIdAsync(currentUserId.ToString()) ?? throw new UserNotFoundException();
        var roles = await userManager.GetRolesAsync(user);
        var roleIds = await roleManager.Roles.Where(r => roles.Contains(r.Name ?? string.Empty)).Select(r => r.Id).ToListAsync(cancellationToken);

        var nodes = await context.FileSystemNodes
            .Include(n => n.Children)
            .Where(n => request.NodeIds.Contains(n.Id)
                && (
                    n.OwnerId == currentUserId
                    || n.UserNodes.Any(x => x.UserId == currentUserId)
                    || n.RoleNodes.Any(x => roleIds.Contains(x.RoleId))
                    || n.ShareStatus == FileSystemNodeShareStatus.Public
                ))
            .ToListAsync(cancellationToken);

        if (nodes.Count != request.NodeIds.Count)
        {
            var missingNodeIds = request.NodeIds.Except(nodes.Select(n => n.Id)).ToList();
            throw new BadRequestException($"Không tìm thấy hoặc không có quyền truy cập vào các node: {string.Join(", ", missingNodeIds)}");
        }

        // 3. Validate tất cả roles tồn tại và thuộc cùng hospital
        foreach (var roleId in request.RoleIds)
        {
            var role = await roleManager.FindByIdAsync(roleId.ToString());
            if (role == null || role.HospitalId != hospitalId)
            {
                throw new BadRequestException($"Không tìm thấy hoặc vai trò không hợp lệ: {roleId}");
            }
        }

        // 4. Xử lý từng node
        foreach (var node in nodes)
        {
            var allNodesToProcess = await FileSystemNodeHelper.GetNodeAndAllDescendantsAsync(context, node, cancellationToken);
            foreach (var targetRoleId in request.RoleIds)
            {
                // Lấy các record chia sẻ hiện tại
                var shareRecords = await context.RoleFileSystemNodes
                    .Where(r => allNodesToProcess.Select(n => n.Id).Contains(r.FileSystemNodeId) && r.RoleId == targetRoleId)
                    .ToListAsync(cancellationToken);

                foreach (var n in allNodesToProcess)
                {
                    var shareRecord = shareRecords.FirstOrDefault(r => r.FileSystemNodeId == n.Id);
                    if (request.IsSharing)
                    {
                        if (shareRecord != null)
                        {
                            if (!shareRecord.IsAccessible)
                            {
                                shareRecord.IsAccessible = true;
                                context.RoleFileSystemNodes.Update(shareRecord);
                            }
                        }
                        else
                        {
                            var newShare = new RoleFileSystemNode
                            {
                                FileSystemNodeId = n.Id,
                                RoleId = targetRoleId,
                                IsRootShare = true,
                                CreatedAt = DateTime.UtcNow,
                                IsAccessible = true
                            };
                            context.RoleFileSystemNodes.Add(newShare);
                        }
                        // Nếu trạng thái đang Private thì chuyển thành Shared
                        if (n.ShareStatus == FileSystemNodeShareStatus.Private)
                        {
                            n.ShareStatus = FileSystemNodeShareStatus.Shared;
                            nodesToUpdate.Add(n);
                        }
                    }
                    else
                    {
                        if (shareRecord != null && shareRecord.IsAccessible)
                        {
                            shareRecord.IsAccessible = false;
                            context.RoleFileSystemNodes.Update(shareRecord);
                        }
                        // Nếu không còn ai được share thì chuyển về Private
                        var isSharedForUser = await context.UserFileSystemNodes.AnyAsync(x => x.FileSystemNodeId == n.Id && x.IsAccessible, cancellationToken: cancellationToken);
                        var isSharedForRole = await context.RoleFileSystemNodes.AnyAsync(x => x.FileSystemNodeId == n.Id && x.IsAccessible, cancellationToken: cancellationToken);
                        if (!isSharedForUser && !isSharedForRole)
                        {
                            n.ShareStatus = FileSystemNodeShareStatus.Private;
                            nodesToUpdate.Add(n);
                        }
                    }
                }
            }
        }

        // Cập nhật trạng thái share cho các node
        if (nodesToUpdate.Count > 0)
        {
            context.FileSystemNodes.UpdateRange(nodesToUpdate);
        }
        // 6. Lưu changes
        await context.SaveChangesAsync(cancellationToken);

        // 7. Log thành công
        string action = request.IsSharing ? "chia sẻ" : "hủy chia sẻ";
        logger.LogInformation("User {CurrentUser} đã {Action} {NodeCount} nodes cho {UserCount} roles. " +
                            "Created: {Created}, Removed: {Removed}",
            currentUser, action, request.NodeIds.Count, request.RoleIds.Count,
            totalShareRecordsCreated, totalShareRecordsRemoved);

        // 8. Trả về response
        var response = new ShareFileSystemNodesByRoleResponse(
            TotalNodesProcessed: request.NodeIds.Count,
            TotalRolesProcessed: request.RoleIds.Count,
            TotalShareRecordsCreated: totalShareRecordsCreated,
            TotalShareRecordsRemoved: totalShareRecordsRemoved,
            Messages: messages
        );

        return new Response<ShareFileSystemNodesByRoleResponse>(response);
    }

    /// <summary>
    // Đã dùng helper chung, không cần hàm này nữa
}
