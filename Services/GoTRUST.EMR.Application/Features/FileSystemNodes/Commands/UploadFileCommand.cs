using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.Interface;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using Microsoft.Extensions.Configuration;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Commands
{
    public record UploadFileResult(string FileUrl, string IV, string FileName);

    public class UploadFileCommand : ICommand<Response<List<UploadFileResult>>>
    {
        public List<IFormFile> Files { get; init; } = [];
        public Guid? ParentId { get; init; }
    }

    public class UploadFileCommandValidator : AbstractValidator<UploadFileCommand>
    {
        public UploadFileCommandValidator()
        {
            RuleFor(x => x.Files)
                .NotNull()
                .WithMessage("Files are required.")
                .Must(files => files != null && files.Count > 0)
                .WithMessage("At least one file must be provided.");
            RuleForEach(x => x.Files)
                .Must(file => file.Length > 0)
                .WithMessage("File cannot be empty.");
        }
    }



    public class UploadFileCommandHandler(
        ILogger<UploadFileCommandHandler> logger,
        IFileStorageService fileStorageService,
        IApplicationDbContext context,
        IConfiguration configuration,
        IHttpContextAccessor httpContextAccessor
    ) : ICommandHandler<UploadFileCommand, Response<List<UploadFileResult>>>
    {
        public async Task<Response<List<UploadFileResult>>> Handle(UploadFileCommand request, CancellationToken cancellationToken)
        {
            var results = new List<UploadFileResult>();
            var secretKey = configuration.GetValue<string>("FileSystemNode_SercretKey") ?? string.Empty;

            // Lấy userId hiện tại từ claims
            var currentUserId = httpContextAccessor.HttpContext?.User?.RetrieveUserIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin người dùng. Vui lòng đăng nhập lại.");
            if (currentUserId == Guid.Empty)
            {
                logger.LogError("Không tìm thấy thông tin user hiện tại trong claims.");
                throw new BadRequestException("Không tìm thấy thông tin user hiện tại.");
            }

            // 1. Encrypt all files in parallel
            var encryptTasks = request.Files.Select(file => PdfEncryptionHelper.EncryptPdfIFormFileAsync(file, secretKey)).ToList();
            var encryptionResults = await Task.WhenAll(encryptTasks);

            // 2. Upload sequentially
            var nodes = new List<FileSystemNode>();
            string parentPath = "/";
            if (request.ParentId != null)
            {
                var parentNode = await context.FileSystemNodes.FirstOrDefaultAsync(node => node.Id == request.ParentId && node.NodeType == FileSystemNodeType.Folder && node.OwnerId == currentUserId, cancellationToken);
                if (parentNode != null && !string.IsNullOrEmpty(parentNode.Path))
                {
                    parentPath = parentNode.Path;
                }
                else
                {
                    logger.LogWarning("Parent node with ID {ParentId} not found.", request.ParentId);
                    throw new BadRequestException($"Thư mục cha không tồn tại hoặc không hợp lệ");
                }
            }

            for (int i = 0; i < request.Files.Count; i++)
            {
                var file = request.Files[i];
                var encryptionResult = encryptionResults[i];
                try
                {
                    if (encryptionResult?.EncryptedData == null)
                    {
                        logger.LogError("Failed to encrypt file: {FileName}", file.FileName);
                        throw new EncryptFileErrorException();
                    }
                    var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                    var fileNameWithoutExt = Path.GetFileNameWithoutExtension(file.FileName);
                    var extension = Path.GetExtension(file.FileName);
                    var key = $"{fileNameWithoutExt}_{timestamp}{extension}";
                    var (success, message, fileUrl) = await fileStorageService.UploadFileAsync(encryptionResult.EncryptedData, key);
                    if (!success)
                    {
                        throw new UploadS3FailedException(message);
                    }
                    var node = new FileSystemNode
                    {
                        Name = fileNameWithoutExt,
                        DestinationUrl = fileUrl,
                        EncryptionIV = encryptionResult.IV,
                        CreatedAt = DateTime.UtcNow,
                        MimeType = extension,
                        Size = file.Length,
                        NodeType = FileSystemNodeType.File,
                        OwnerId = currentUserId,
                        ParentId = request.ParentId
                    };
                    nodes.Add(node);
                    results.Add(new UploadFileResult(fileUrl, encryptionResult.IV, key));
                    logger.LogInformation("File uploaded and ready to save: {FileUrl}", fileUrl);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error processing file upload: {FileName}", file.FileName);
                    throw new BadRequestException($"Có lỗi xảy ra khi upload file {file.FileName}.", ex.Message);
                }
            }
            if (nodes.Count > 0)
            {
                context.FileSystemNodes.AddRange(nodes);
                nodes.ForEach(node => node.Path = $"{parentPath}{node.Id}/");
                await context.SaveChangesAsync(cancellationToken);
            }
            return new Response<List<UploadFileResult>>(results, "Files encrypted, uploaded and saved successfully", "000");
        }
    }
}
