using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Models
{
    public class FileSystemNodeDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public Guid OwnerId { get; set; }
        public string OwnerName { get; set; } = "Unknown User"; // Default owner
        public string OwnerAvatar { get; set; } = string.Empty; // Default avatar if not set
        public DateTime LastModified { get; set; }
        public Guid? ParentId { get; set; }
        public string? ParentName { get; set; }
        public FileSystemNodeType Type { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Size { get; set; } = string.Empty; // Assuming Size is a string for display purposes
        public string MimeType { get; set; } = string.Empty; // Default MIME type if not set
        public int Count { get; set; }
        public string Icon { get; set; } = string.Empty; // Default icon if not set
    }
}
