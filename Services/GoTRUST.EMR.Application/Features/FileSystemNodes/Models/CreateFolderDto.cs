namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Models;

/// <summary>
/// Request model cho việc tạo thư mục mới
/// </summary>
public class CreateFolderRequest
{
    /// <summary>
    /// Tên thư mục
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// ID của thư mục cha (null nếu tạo ở root)
    /// </summary>
    public Guid? ParentId { get; set; }
}

/// <summary>
/// Response model cho việc tạo thư mục mới
/// </summary>
public class CreateFolderResponse
{
    /// <summary>
    /// ID của thư mục vừa tạo
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Tên thư mục
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Đ<PERSON>ờng dẫn đầy đủ
    /// </summary>
    public string Path { get; set; } = string.Empty;

    /// <summary>
    /// ID của thư mục cha
    /// </summary>
    public Guid? ParentId { get; set; }

    /// <summary>
    /// ID của người tạo
    /// </summary>
    public Guid OwnerId { get; set; }

    /// <summary>
    /// Thời gian tạo
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
