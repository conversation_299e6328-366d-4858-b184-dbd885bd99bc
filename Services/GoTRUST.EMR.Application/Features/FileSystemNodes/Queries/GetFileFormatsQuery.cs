using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;
using GoTRUST.EMR.Application.Helpers;
using BuildingBlocks.Abstractions;


namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Queries
{
    public record GetFileFormatsQuery : IQuery<Response<List<FileFormatType>>>;

    public class GetFileFormatsQueryHandler : IQueryHandler<GetFileFormatsQuery, Response<List<FileFormatType>>>
    {
        public async Task<Response<List<FileFormatType>>> Handle(GetFileFormatsQuery request, CancellationToken cancellationToken)
        {
            var formats = FileSystemNodeHelper.GetFileFormatTypes();
            return await Task.FromResult(new Response<List<FileFormatType>>(formats));
        }
    }
}
