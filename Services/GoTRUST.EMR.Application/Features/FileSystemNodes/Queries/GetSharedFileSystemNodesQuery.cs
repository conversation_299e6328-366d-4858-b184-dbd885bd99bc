using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Queries;

public record GetSharedFileSystemNodesQuery : PaginationRequest, IQuery<PaginationResponse<FileSystemNodeDto>>
{
    public string? SearchTerm { get; set; }
    public string? NodeType { get; set; }
    public string? SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
    public Guid? OwnerId { get; set; }
}


public class GetSharedFileSystemNodesQueryHandler : IQueryHandler<GetSharedFileSystemNodesQuery, PaginationResponse<FileSystemNodeDto>>
{
    private readonly IApplicationDbContext _dbContext;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<GetSharedFileSystemNodesQueryHandler> _logger;
    private readonly UserManager<User> _userManager;
    private readonly RoleManager<Role> _roleManager;

    public GetSharedFileSystemNodesQueryHandler(
        IApplicationDbContext dbContext,
        IHttpContextAccessor httpContextAccessor,
        ILogger<GetSharedFileSystemNodesQueryHandler> logger,
        UserManager<User> userManager,
        RoleManager<Role> roleManager)
    {
        _dbContext = dbContext;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
        _userManager = userManager;
        _roleManager = roleManager;
    }

    public async Task<PaginationResponse<FileSystemNodeDto>> Handle(GetSharedFileSystemNodesQuery request, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User?.RetrieveUserIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin người dùng. Vui lòng đăng nhập lại.");
        var hospitalId = _httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
            ?? throw new Exception("Không thể xác định bệnh viện của người dùng.");

        // Get user and roleIds (for role-based sharing)
        var user = await _userManager.FindByIdAsync(userId.ToString())
            ?? throw new Exception("User not found");
        var roles = await _userManager.GetRolesAsync(user);
        var roleIds = await _roleManager.Roles
            .Where(r => roles.Contains(r.Name ?? string.Empty))
            .Select(r => r.Id)
            .ToListAsync(cancellationToken);

        _logger.LogInformation("Getting shared file system nodes for user {UserId} in hospital {HospitalId}", userId, hospitalId);

        // Query nodes NOT owned by current user but shared with them (user, role, or public)
        var query = _dbContext.FileSystemNodes
            .AsNoTracking()
            .Include(node => node.Parent)
            .Include(node => node.Owner)
            .Include(node => node.UserNodes.Where(un => un.UserId == userId))
            .Include(node => node.RoleNodes.Where(rn => roleIds.Contains(rn.RoleId)))
            .Where(node => node.OwnerId != userId
                && node.Owner != null && node.Owner.HospitalId == hospitalId
                && (
                    node.UserNodes.Any(un => un.UserId == userId && un.IsAccessible && un.IsRootShare)
                    || node.RoleNodes.Any(rn => roleIds.Contains(rn.RoleId) && rn.IsAccessible && rn.IsRootShare)
                    || node.ShareStatus == FileSystemNodeShareStatus.Public
                )
            );

        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            query = query.Where(node => node.Name.ToLower().Contains(request.SearchTerm.ToLower()));
        }

        if (!string.IsNullOrEmpty(request.NodeType) && Enum.TryParse<FileSystemNodeType>(request.NodeType, out var nodeType))
        {
            query = query.Where(node => node.NodeType == nodeType);
        }

        if (request.OwnerId.HasValue)
        {
            query = query.Where(node => node.OwnerId == request.OwnerId.Value);
        }

        query = request.SortBy?.ToLower() switch
        {
            "name" => request.SortDescending
                ? query.OrderByDescending(node => node.Name)
                : query.OrderBy(node => node.Name),
            "lastaccessedat" => request.SortDescending
                ? query.OrderByDescending(node => node.UserNodes.Where(un => un.UserId == userId).Select(un => un.LastAccessedAt).FirstOrDefault() ?? node.CreatedAt)
                : query.OrderBy(node => node.UserNodes.Where(un => un.UserId == userId).Select(un => un.LastAccessedAt).FirstOrDefault() ?? node.CreatedAt),
            "size" => request.SortDescending
                ? query.OrderByDescending(node => node.Size ?? 0)
                : query.OrderBy(node => node.Size ?? 0),
            _ => request.SortDescending
                ? query.OrderByDescending(node => node.CreatedAt)
                : query.OrderBy(node => node.CreatedAt)
        };

        var totalCount = await query.CountAsync(cancellationToken);

        var nodes = await query
            .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
            .Take(request.PageSize!.Value)
            .ToListAsync(cancellationToken);

        var nodeIds = nodes.Select(n => n.Id).ToList();
        var childrenCounts = await _dbContext.FileSystemNodes
            .AsNoTracking()
            .Where(n => n.ParentId != null && nodeIds.Contains(n.ParentId.Value))
            .GroupBy(n => n.ParentId)
            .Select(g => new { ParentId = g.Key, Count = g.Count() })
            .ToDictionaryAsync(g => g.ParentId!.Value, g => g.Count, cancellationToken);


        var result = nodes.Select(node => new FileSystemNodeDto
        {
            Id = node.Id,
            Name = node.Name,
            OwnerId = node.OwnerId,
            OwnerName = node.Owner?.FullName ?? "Người dùng không xác định",
            OwnerAvatar = node.Owner?.AvatarUrl ?? string.Empty,
            LastModified = node.UserNodes.Select(un => un.LastAccessedAt).FirstOrDefault() ?? node.CreatedAt ?? DateTime.MinValue,
            ParentId = node.ParentId,
            ParentName = node.Parent?.Name ?? "Tài liệu chia sẻ",
            Type = node.NodeType,
            CreatedAt = node.CreatedAt ?? DateTime.MinValue,
            MimeType = node.MimeType ?? string.Empty,
            Size = node.Size.HasValue ? $"{node.Size.Value / 1024.0 / 1024.0:F2} MB" : "N/A",
            Count = node.NodeType == FileSystemNodeType.Folder && childrenCounts.TryGetValue(node.Id, out var count) ? count : 0,
            Icon = FileSystemNodeHelper.GetIconForNode(node.NodeType, node.MimeType)
        }).ToList();

        _logger.LogInformation("Found {Count} shared file system nodes for user {UserId}", result.Count, userId);

        return new PaginationResponse<FileSystemNodeDto>(
            request.PageIndex!.Value,
            request.PageSize!.Value,
            totalCount,
            result);
    }
}
