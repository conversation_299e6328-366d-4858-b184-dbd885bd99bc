using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Queries
{
    public record GetPersonalNodeQuery : PaginationWithSortRequest, IQuery<PaginationResponse<FileSystemNodeDto>>
    {
        public string? SearchTerm { get; set; }
        public string? Type { get; set; }
        public Guid? ParentId { get; set; }
    }

    public class GetPersonalNodeQueryHandler(IApplicationDbContext dbContext, IHttpContextAccessor httpContextAccessor, UserManager<User> userManager, ILogger<GetPersonalNodeQueryHandler> logger) : IQueryHandler<GetPersonalNodeQuery, PaginationResponse<FileSystemNodeDto>>
    {
        private readonly IApplicationDbContext _dbContext = dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        private readonly UserManager<User> _userManager = userManager;
        private readonly ILogger<GetPersonalNodeQueryHandler> _logger = logger;

        public async Task<PaginationResponse<FileSystemNodeDto>> Handle(GetPersonalNodeQuery request, CancellationToken cancellationToken)
        {
            var userId = _httpContextAccessor.HttpContext?.User?.RetrieveUserIdFromPrincipal()
                ?? throw new UnauthorizedAccessException("Không thể xác định thông tin người dùng. Vui lòng đăng nhập lại.");
            var user = await _userManager.FindByIdAsync(userId.ToString())
                ?? throw new UserNotFoundException();

            var query = _dbContext.FileSystemNodes
                .AsNoTracking()
                .Include(node => node.Parent)
                .Include(node => node.Owner)
                .Where(node => node.OwnerId == userId
                    && node.ShareStatus == FileSystemNodeShareStatus.Private
                    && (
                        request.ParentId == null
                            ? node.ParentId == null
                            : node.ParentId == request.ParentId
                    )
                    && (string.IsNullOrEmpty(request.SearchTerm) || node.Name.ToLower().Contains(request.SearchTerm.ToLower()))
                );

            // Lọc theo Type (Type là type của FileFormatType, không phải đuôi mimeType)
            if (!string.IsNullOrEmpty(request.Type))
            {
                var fileFormatTypes = FileSystemNodeHelper.GetFileFormatTypes();
                var selectedType = fileFormatTypes.FirstOrDefault(f => f.Type == request.Type);
                if (selectedType != null && selectedType.Extensions.Count != 0)
                {
                    query = query.Where(node => node.MimeType != null && selectedType.Extensions.Contains(node.MimeType));
                }
                else if (selectedType != null && selectedType.Extensions.Count == 0)
                {
                    // Nếu là unknown thì lấy các file không khớp bất kỳ extension nào đã định nghĩa
                    var allKnownExtensions = fileFormatTypes.SelectMany(f => f.Extensions).ToList();
                    query = query.Where(node => node.MimeType == null || !allKnownExtensions.Contains(node.MimeType));
                }
                else
                {
                    // Nếu không tìm thấy type, trả về rỗng
                    query = query.Where(node => false);
                }
            }

            // Apply sorting
            query = request.SortBy?.ToLower() switch
            {
                "parent" => request.SortDescending == true
                    ? query.OrderByDescending(node => node.Parent != null ? node.Parent.Name : "Tài liệu cá nhân")
                    : query.OrderBy(node => node.Parent != null ? node.Parent.Name : "Tài liệu cá nhân"),
                "name" => request.SortDescending == true
                    ? query.OrderByDescending(node => node.Name)
                    : query.OrderBy(node => node.Name),
                "type" => request.SortDescending == true
                    ? query.OrderByDescending(node => node.NodeType)
                    : query.OrderBy(node => node.NodeType),
                "size" => request.SortDescending == true
                    ? query.OrderByDescending(node => node.Size ?? 0)
                    : query.OrderBy(node => node.Size ?? 0),
                _ => query.OrderByDescending(node => node.UpdatedAt)
            };

            var nodes = await query
                .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
                .Take(request.PageSize!.Value)
                .ToListAsync(cancellationToken);

            var nodeIds = nodes.Select(n => n.Id).ToList();
            var childrenCounts = _dbContext.FileSystemNodes
                .AsNoTracking()
                .Where(n => n.ParentId != null && nodeIds.Contains(n.ParentId.Value))
                .GroupBy(n => n.ParentId)
                .Select(g => new { ParentId = g.Key, Count = g.Count() })
                .ToDictionary(g => g.ParentId!.Value, g => g.Count);

            return new PaginationResponse<FileSystemNodeDto>(
                request.PageIndex!.Value,
                request.PageSize!.Value,
                await query.CountAsync(cancellationToken),
                nodes.Select(node => new FileSystemNodeDto
                {
                    Id = node.Id,
                    Name = node.Name,
                    OwnerId = node.OwnerId,
                    OwnerName = node.Owner?.FullName ?? "Người dùng không xác định",
                    OwnerAvatar = node.Owner?.AvatarUrl ?? string.Empty,
                    LastModified = node.UpdatedAt ?? node.CreatedAt ?? DateTime.MinValue,
                    ParentId = node.ParentId,
                    ParentName = node.Parent?.Name ?? "Tài liệu cá nhân",
                    Type = node.NodeType,
                    CreatedAt = node.CreatedAt ?? DateTime.MinValue,
                    MimeType = node.MimeType ?? string.Empty,
                    Size = node.Size.HasValue ? $"{node.Size.Value / 1024.0 / 1024.0:F2} MB" : "N/A",
                    Count = childrenCounts.TryGetValue(node.Id, out var count) ? count : 0,
                    Icon = FileSystemNodeHelper.GetIconForNode(node.NodeType, node.MimeType)
                }));
        }
    }
}
