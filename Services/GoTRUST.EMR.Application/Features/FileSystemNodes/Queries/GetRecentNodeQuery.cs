using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Queries
{
    public record GetRecentNodeQuery : PaginationWithSortRequest, IQuery<PaginationResponse<FileSystemNodeDto>>
    {
        public string? SearchTerm { get; set; }
        public string? Type { get; set; }
        public Guid? OwnerId { get; set; }
        public Guid? ParentId { get; set; }
        public FileSystemNodeType? NodeType { get; set; } // null = get both folder and file, specific type = get only that type
        public bool IsRecent { get; set; } = true; // Default to true to get recent nodes
    }


    public class GetRecentNodeQueryHandler : IQueryHandler<GetRecentNodeQuery, PaginationResponse<FileSystemNodeDto>>
    {
        private readonly IApplicationDbContext _dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<Role> _roleManager;
        private readonly ILogger<GetRecentNodeQueryHandler> _logger;
        public GetRecentNodeQueryHandler(IApplicationDbContext dbContext, IHttpContextAccessor httpContextAccessor, UserManager<User> userManager, RoleManager<Role> roleManager, ILogger<GetRecentNodeQueryHandler> logger)
        {
            _dbContext = dbContext;
            _httpContextAccessor = httpContextAccessor;
            _userManager = userManager;
            _roleManager = roleManager;
            _logger = logger;
        }

        public async Task<PaginationResponse<FileSystemNodeDto>> Handle(GetRecentNodeQuery request, CancellationToken cancellationToken)
        {
            // Get current user id from HttpContext
            var userId = _httpContextAccessor.HttpContext?.User?.RetrieveUserIdFromPrincipal()
                ?? throw new UnauthorizedAccessException("Không thể xác định thông tin người dùng. Vui lòng đăng nhập lại.");
            var user = await _userManager.FindByIdAsync(userId.ToString())
                ?? throw new UserNotFoundException();
            var roles = await _userManager.GetRolesAsync(user);
            if (roles == null || !roles.Any())
            {
                _logger.LogWarning("User {UserId} has no roles assigned.", userId);
                throw new UnauthorizedAccessException("Người dùng không có quyền truy cập.");
            }
            var roleIds = await _roleManager.Roles
                .Where(r => roles.Contains(r.Name ?? string.Empty))
                .Select(r => r.Id)
                .ToListAsync(cancellationToken);
            IQueryable<FileSystemNode> query;
            // Special case: NodeType là file và ParentId null
            if (request.NodeType == FileSystemNodeType.File && request.ParentId == null)
            {
                query = _dbContext.FileSystemNodes
                    .AsNoTracking()
                    .Include(node => node.Parent)
                    .Include(node => node.UserNodes.Where(un => un.UserId == userId))
                    .Include(node => node.Owner)
                    .Include(node => node.RoleNodes.Where(un => roleIds.Contains(un.RoleId)))
                    .Where(node =>
                        node.NodeType == FileSystemNodeType.File &&
                        (
                            node.OwnerId == userId
                            || node.UserNodes.Any(x => x.UserId == userId && x.IsAccessible)
                            || node.RoleNodes.Any(x => roleIds.Contains(x.RoleId) && x.IsAccessible)
                        )
                        && (string.IsNullOrEmpty(request.SearchTerm) || node.Name.ToLower().Contains(request.SearchTerm.ToLower()))
                        && (request.OwnerId == null || node.OwnerId == request.OwnerId)
                    );
            }
            else
            {
                query = _dbContext.FileSystemNodes
                    .AsNoTracking()
                    .Include(node => node.Parent)
                    .Include(node => node.UserNodes.Where(un => un.UserId == userId))
                    .Include(node => node.Owner)
                    .Include(node => node.RoleNodes.Where(un => roleIds.Contains(un.RoleId)))
                    .Where(node => (node.OwnerId == userId
                            || node.UserNodes.Count(x => x.UserId == userId) > 0
                            || node.RoleNodes.Count(x => roleIds.Contains(x.RoleId)) > 0
                            || node.ShareStatus == FileSystemNodeShareStatus.Public)
                        && (request.NodeType == null || node.NodeType == request.NodeType)
                        && (
                            request.ParentId == null
                                ? node.ParentId == null
                                : node.ParentId == request.ParentId
                        )
                        && (string.IsNullOrEmpty(request.SearchTerm) || node.Name.ToLower().Contains(request.SearchTerm.ToLower()))
                        && (request.OwnerId == null || node.OwnerId == request.OwnerId)
                    );
            }

            // Lọc theo Type (Type là type của FileFormatType, không phải đuôi mimeType)
            if (!string.IsNullOrEmpty(request.Type))
            {
                var fileFormatTypes = FileSystemNodeHelper.GetFileFormatTypes();
                var selectedType = fileFormatTypes.FirstOrDefault(f => f.Type == request.Type);
                if (selectedType != null && selectedType.Extensions.Count != 0)
                {
                    query = query.Where(node => node.MimeType != null && selectedType.Extensions.Contains(node.MimeType));
                }
                else if (selectedType != null && selectedType.Extensions.Count == 0)
                {
                    // Nếu là unknown thì lấy các file không khớp bất kỳ extension nào đã định nghĩa
                    var allKnownExtensions = fileFormatTypes.SelectMany(f => f.Extensions).ToList();
                    query = query.Where(node => node.MimeType == null || !allKnownExtensions.Contains(node.MimeType));
                }
                else
                {
                    // Nếu không tìm thấy type, trả về rỗng
                    query = query.Where(node => false);
                }
            }

            // Apply sorting based on parameters
            query = request.SortBy?.ToLower() switch
            {
                "parent" => request.SortDescending == true
                    ? query.OrderByDescending(node => node.Parent != null ? node.Parent.Name :
                        (node.OwnerId == userId ? "Tài liệu cá nhân" : "Tài liệu chia sẻ"))
                    : query.OrderBy(node => node.Parent != null ? node.Parent.Name :
                        (node.OwnerId == userId ? "Tài liệu cá nhân" : "Tài liệu chia sẻ")),
                "name" => request.SortDescending == true
                    ? query.OrderByDescending(node => node.Name)
                    : query.OrderBy(node => node.Name),
                "type" => request.SortDescending == true
                    ? query.OrderByDescending(node => node.NodeType)
                    : query.OrderBy(node => node.NodeType),
                "size" => request.SortDescending == true
                    ? query.OrderByDescending(node => node.Size ?? 0)
                    : query.OrderBy(node => node.Size ?? 0),
                _ => request.IsRecent
                    ? query.OrderByDescending(node =>
                        // Sort by access time when IsRecent = true
                        node.OwnerId == userId
                            ? node.OwnerLastAccessedAt ?? node.CreatedAt
                            : node.UserNodes.Where(un => un.UserId == userId)
                                            .Select(un => un.LastAccessedAt)
                                            .FirstOrDefault() ?? node.CreatedAt
                    )
                    : query.OrderByDescending(node => node.UpdatedAt) // Default sort by last modified when IsRecent = false
            };

            var nodes = await query
                .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
                .Take(request.PageSize!.Value)
                .ToListAsync(cancellationToken);

            // Get all node IDs to count children in one query
            var nodeIds = nodes.Select(n => n.Id).ToList();
            var childrenCounts = _dbContext.FileSystemNodes
                .AsNoTracking()
                .Where(n => n.ParentId != null && nodeIds.Contains(n.ParentId.Value))
                .GroupBy(n => n.ParentId)
                .Select(g => new { ParentId = g.Key, Count = g.Count() })
                .ToDictionary(g => g.ParentId!.Value, g => g.Count);

            return new PaginationResponse<FileSystemNodeDto>(
                request.PageIndex!.Value,
                request.PageSize!.Value,
                await query.CountAsync(cancellationToken),
                nodes.Select(node => new FileSystemNodeDto
                {
                    Id = node.Id,
                    Name = node.Name,
                    OwnerId = node.OwnerId,
                    OwnerName = node.Owner?.FullName ?? "Người dùng không xác định", // Default owner name if not set
                    OwnerAvatar = node.Owner?.AvatarUrl ?? string.Empty, // Default avatar if not set
                    LastModified = node.OwnerId == userId
                        ? node.OwnerLastAccessedAt ?? node.CreatedAt ?? DateTime.MinValue
                        : node.UserNodes.Select(un => un.LastAccessedAt)
                                        .FirstOrDefault() ?? node.CreatedAt ?? DateTime.MinValue,
                    ParentId = node.ParentId,
                    ParentName = node.Parent?.Name ?? (node.OwnerId == userId ? "Tài liệu cá nhân" : "Tài liệu chia sẻ"),
                    Type = node.NodeType,
                    CreatedAt = node.CreatedAt ?? DateTime.MinValue,
                    MimeType = node.MimeType ?? string.Empty, // Default MIME type if not set
                    Size = node.Size.HasValue ? $"{node.Size.Value / 1024.0 / 1024.0:F2} MB" : "N/A",
                    Count = childrenCounts.TryGetValue(node.Id, out var count) ? count : 0,
                    Icon = FileSystemNodeHelper.GetIconForNode(node.NodeType, node.MimeType)
                }));
        }
    }
}