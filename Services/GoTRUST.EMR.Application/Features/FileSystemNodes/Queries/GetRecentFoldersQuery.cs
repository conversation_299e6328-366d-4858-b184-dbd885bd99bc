using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.FileSystemNodes.Models;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace GoTRUST.EMR.Application.Features.FileSystemNodes.Queries
{
    public record GetRecentFoldersQuery : IQuery<Response<List<FileSystemNodeSimpleDto>>>
    {
        public Guid? ParentId { get; set; }
    }

    public class GetRecentFoldersQueryHandler(
        IApplicationDbContext dbContext,
        IHttpContextAccessor httpContextAccessor,
        UserManager<User> userManager,
        RoleManager<Role> roleManager,
        ILogger<GetRecentFoldersQueryHandler> logger) : IQueryHandler<GetRecentFoldersQuery, Response<List<FileSystemNodeSimpleDto>>>
    {
        private readonly IApplicationDbContext _dbContext = dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        private readonly UserManager<User> _userManager = userManager;
        private readonly RoleManager<Role> _roleManager = roleManager;
        private readonly ILogger<GetRecentFoldersQueryHandler> _logger = logger;

        public async Task<Response<List<FileSystemNodeSimpleDto>>> Handle(GetRecentFoldersQuery request, CancellationToken cancellationToken)
        {
            var userId = _httpContextAccessor.HttpContext?.User?.RetrieveUserIdFromPrincipal()
                ?? throw new UnauthorizedAccessException("Không thể xác định thông tin người dùng. Vui lòng đăng nhập lại.");
            var user = await _userManager.FindByIdAsync(userId.ToString())
                ?? throw new UserNotFoundException();
            var roles = await _userManager.GetRolesAsync(user);
            if (roles == null || !roles.Any())
            {
                _logger.LogWarning("User {UserId} has no roles assigned.", userId);
                throw new UnauthorizedAccessException("Người dùng không có quyền truy cập.");
            }
            var roleIds = await _roleManager.Roles
                .Where(r => roles.Contains(r.Name ?? string.Empty))
                .Select(r => r.Id)
                .ToListAsync(cancellationToken);

            var query = _dbContext.FileSystemNodes
                .AsNoTracking()
                .Include(node => node.Parent)
                .Include(node => node.UserNodes.Where(un => un.UserId == userId))
                .Include(node => node.Owner)
                .Include(node => node.RoleNodes.Where(un => roleIds.Contains(un.RoleId)))
                .Where(node => node.NodeType == FileSystemNodeType.Folder
                    && (node.OwnerId == userId
                        || node.UserNodes.Any(x => x.UserId == userId)
                        || node.RoleNodes.Any(x => roleIds.Contains(x.RoleId))
                        || node.ShareStatus == FileSystemNodeShareStatus.Public)
                    && (
                        request.ParentId == null
                            ? (
                                // Nếu là thư mục user sở hữu thì lấy cấp cao nhất (ParentId == null)
                                node.OwnerId == userId
                                    ? node.ParentId == null
                                    : (node.UserNodes.Any(un => un.UserId == userId && un.IsRootShare)
                                        || node.RoleNodes.Any(un => roleIds.Contains(un.RoleId) && un.IsRootShare))
                                
                            )
                            : node.ParentId == request.ParentId
                    )
                )
                .OrderByDescending(node => node.CreatedAt);

            var nodes = await query.ToListAsync(cancellationToken);

            var nodeIds = nodes.Select(n => n.Id).ToList();
            var childrenCounts = _dbContext.FileSystemNodes
                .AsNoTracking()
                .Where(n => n.ParentId != null && nodeIds.Contains(n.ParentId.Value))
                .GroupBy(n => n.ParentId)
                .Select(g => new { ParentId = g.Key, Count = g.Count() })
                .ToDictionary(g => g.ParentId!.Value, g => g.Count);

            var result = nodes.Select(node => new FileSystemNodeSimpleDto
            {
                Id = node.Id,
                Name = node.Name,
                ParentId = node.ParentId
            }).ToList();
            return new Response<List<FileSystemNodeSimpleDto>>(result);
        }
    }
}
