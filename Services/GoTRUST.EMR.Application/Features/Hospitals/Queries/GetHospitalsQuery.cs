﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Mapster;

namespace GoTRUST.EMR.Application.Features.Hospitals.Queries;

public record GetHospitalsRequest() : IQuery<Response<List<GetHospitalsResponse>>>;

public record GetHospitalsResponse(
    Guid Id,
    string Code,
    string Name,
    string Address,
    string Language,
    string AdminEmail,
    string NotificationTypes,
    int LoginSessionTimeout,
    int MaxLoginAttempts,
    string PasswordPolicyJson
);

public class GetHospitalsRequestValidator : AbstractValidator<GetHospitalsRequest>
{
    public GetHospitalsRequestValidator()
    {
    }
}

public class GetHospitalsHandler(
    IApplicationDbContext _dbContext
) : IQueryHandler<GetHospitalsRequest, Response<List<GetHospitalsResponse>>>
{
    public async Task<Response<List<GetHospitalsResponse>>> Handle(GetHospitalsRequest req, CancellationToken cancellationToken)
    {
        var query = _dbContext.Hospitals.AsQueryable();

        var hospitals = await query
            .ProjectToType<GetHospitalsResponse>()
            .ToListAsync(cancellationToken);

        return new Response<List<GetHospitalsResponse>>(hospitals);
    }
}