﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Mapster;
using GoTRUST.EMR.Application.Exceptions;

namespace GoTRUST.EMR.Application.Features.Hospitals.Commands;

public record DeleteHospitalRequest(
    Guid Id
) : ICommand<Response<DeleteHospitalResponse>>;

public record DeleteHospitalResponse(
    Guid Id,
    bool IsSuccess
);

public class DeleteHospitalRequestValidator : AbstractValidator<DeleteHospitalRequest>
{
    public DeleteHospitalRequestValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteHospitalHandler(
    IApplicationDbContext _dbContext
) : ICommandHandler<DeleteHospitalRequest, Response<DeleteHospitalResponse>>
{
    public async Task<Response<DeleteHospitalResponse>> Handle(DeleteHospitalRequest req, CancellationToken cancellationToken)
    {
        var hospital = await _dbContext.Hospitals.FindAsync([req.Id], cancellationToken)
            ?? throw new HospitalNotFoundException(req.Id);

        hospital.IsDeleted = true;
        _dbContext.Hospitals.Update(hospital);

        var affected = await _dbContext.SaveChangesAsync(cancellationToken);
        if (affected == 0)
        {
            throw new HospitalDeleteFailedException();
        }

        var response = new DeleteHospitalResponse(hospital.Id, true);

        return new Response<DeleteHospitalResponse>(response);
    }
}