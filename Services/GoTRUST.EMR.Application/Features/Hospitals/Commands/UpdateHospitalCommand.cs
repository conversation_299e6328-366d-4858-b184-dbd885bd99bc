﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Mapster;
using Microsoft.AspNetCore.Http;
using GoTRUST.EMR.Application.Exceptions;

namespace GoTRUST.EMR.Application.Features.Hospitals.Commands;

public record UpdateHospitalRequest(
    Guid Id,
    string Code,
    string Name,
    string Address,
    string Language,
    string AdminEmail,
    string NotificationTypes,
    int LoginSessionTimeout,
    int MaxLoginAttempts,
    string PasswordPolicyJson
) : ICommand<Response<UpdateHospitalResponse>>;

public record UpdateHospitalResponse(
    Guid Id,
    string Code,
    string Name,
    string Address,
    string Language,
    string AdminEmail,
    string NotificationTypes,
    int LoginSessionTimeout,
    int MaxLoginAttempts,
    string PasswordPolicyJson
);

public class UpdateHospitalRequestValidator : AbstractValidator<UpdateHospitalRequest>
{
    public UpdateHospitalRequestValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.Code).NotEmpty();
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.Address).NotEmpty();
        RuleFor(x => x.Language).NotEmpty();
        RuleFor(x => x.AdminEmail).NotEmpty().EmailAddress();
        RuleFor(x => x.NotificationTypes).NotEmpty();
        RuleFor(x => x.LoginSessionTimeout).GreaterThan(0);
        RuleFor(x => x.MaxLoginAttempts).GreaterThan(0);
        RuleFor(x => x.PasswordPolicyJson).NotEmpty();
    }
}

public class UpdateHospitalHandler(
    IApplicationDbContext _dbContext
) : ICommandHandler<UpdateHospitalRequest, Response<UpdateHospitalResponse>>
{
    public async Task<Response<UpdateHospitalResponse>> Handle(UpdateHospitalRequest req, CancellationToken cancellationToken)
    {
        var hospital = await _dbContext.Hospitals.FindAsync([req.Id], cancellationToken)
            ?? throw new HospitalNotFoundException(req.Id);
        if (await _dbContext.Hospitals.AnyAsync(h => h.Code == req.Code && h.Id != req.Id, cancellationToken))
            throw new HospitalCodeAlreadyExistsException(req.Code);

        req.Adapt(hospital);

        _dbContext.Hospitals.Update(hospital);
        var affected = await _dbContext.SaveChangesAsync(cancellationToken);
        if (affected == 0)
        {
            throw new HospitalUpdateFailedException();
        }


        var response = hospital.Adapt<UpdateHospitalResponse>();

        return new Response<UpdateHospitalResponse>(response);
    }
}