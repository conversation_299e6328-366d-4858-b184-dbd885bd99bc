﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Mapster;
using GoTRUST.EMR.Application.Exceptions;

namespace GoTRUST.EMR.Application.Features.Hospitals.Commands;

public record CreateHospitalRequest(
    string Code,
    string Name,
    string Address,
    string Language,
    string AdminEmail,
    string NotificationTypes,
    int LoginSessionTimeout,
    int MaxLoginAttempts,
    string PasswordPolicyJson
) : ICommand<Response<CreateHospitalResponse>>;

public record CreateHospitalResponse(
    Guid Id,
    string Code,
    string Name,
    string Address,
    string Language,
    string AdminEmail,
    string NotificationTypes,
    int LoginSessionTimeout,
    int MaxLoginAttempts,
    string PasswordPolicyJson
);

public class CreateHospitalRequestValidator : AbstractValidator<CreateHospitalRequest>
{
    public CreateHospitalRequestValidator()
    {
        RuleFor(x => x.Code).NotEmpty();
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.Address).NotEmpty();
        RuleFor(x => x.Language).NotEmpty();
        RuleFor(x => x.AdminEmail).NotEmpty().EmailAddress();
        RuleFor(x => x.NotificationTypes).NotEmpty();
        RuleFor(x => x.LoginSessionTimeout).GreaterThan(0);
        RuleFor(x => x.MaxLoginAttempts).GreaterThan(0);
        RuleFor(x => x.PasswordPolicyJson).NotEmpty();
    }
}

public class CreateHospitalHandler(
    IApplicationDbContext _dbContext
) : ICommandHandler<CreateHospitalRequest, Response<CreateHospitalResponse>>
{
    public async Task<Response<CreateHospitalResponse>> Handle(CreateHospitalRequest req, CancellationToken cancellationToken)
    {
        if (await _dbContext.Hospitals.AnyAsync(h => h.Code == req.Code))
            throw new HospitalCodeAlreadyExistsException(req.Code);

        var hospital = req.Adapt<Hospital>();

        _dbContext.Hospitals.Add(hospital);
        await _dbContext.SaveChangesAsync(cancellationToken);

        var response = hospital.Adapt<CreateHospitalResponse>();

        return new Response<CreateHospitalResponse>(response);
    }
}