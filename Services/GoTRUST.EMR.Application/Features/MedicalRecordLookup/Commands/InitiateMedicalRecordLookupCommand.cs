using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.Interface;
using BuildingBlocks.Common.PushNotification.Services;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.Extensions.Configuration;

namespace GoTRUST.EMR.Application.Features.MedicalRecordLookup.Commands;

/// <summary>
/// Request để khởi tạo phiên tra cứu bệnh án
/// </summary>
public record InitiateMedicalRecordLookupRequest(
    string CitizenId
) : ICommand<Response<InitiateMedicalRecordLookupResponse>>;

/// <summary>
/// Response cho việc khởi tạo phiên tra cứu bệnh án
/// </summary>
public record InitiateMedicalRecordLookupResponse(
    Guid SessionId
);

/// <summary>
/// Model để lưu thông tin phiên tra cứu trong cache
/// </summary>
public record MedicalRecordLookupSession(
    Guid SessionId,
    string CitizenId,
    string Otp,
    DateTime CreatedAt
);

/// <summary>
/// Validator cho InitiateMedicalRecordLookupRequest
/// </summary>
public class InitiateMedicalRecordLookupRequestValidator : AbstractValidator<InitiateMedicalRecordLookupRequest>
{
    public InitiateMedicalRecordLookupRequestValidator()
    {
        RuleFor(x => x.CitizenId)
            .NotEmpty()
            .WithMessage("Số CCCD không được để trống")
            .Length(9, 12)
            .WithMessage("Số CCCD phải có độ dài từ 9 đến 12 ký tự")
            .Matches(@"^\d+$")
            .WithMessage("Số CCCD chỉ được chứa các chữ số");
    }
}

/// <summary>
/// Command handler cho việc khởi tạo phiên tra cứu bệnh án
/// </summary>
public class InitiateMedicalRecordLookupCommandHandler(
    ICachedService cachedService,
    PushNotificationService pushNotificationService,
    IConfiguration configuration,
    ILogger<InitiateMedicalRecordLookupCommandHandler> logger)
    : ICommandHandler<InitiateMedicalRecordLookupRequest, Response<InitiateMedicalRecordLookupResponse>>
{
    private readonly int SESSION_EXPIRE_MINUTES = configuration.GetValue<int>("PushNotificationSessionExpireInMinutes");

    public async Task<Response<InitiateMedicalRecordLookupResponse>> Handle(
        InitiateMedicalRecordLookupRequest request,
        CancellationToken cancellationToken)
    {
        logger.LogInformation("Initiating medical record lookup for CitizenId {CitizenId}",
            request.CitizenId);

        // 2. Tạo sessionId và OTP
        var sessionId = Guid.NewGuid();
        var otp = StringExtensions.GenerateOtp();

        // 3. Tạo session object
        var session = new MedicalRecordLookupSession(
            SessionId: sessionId,
            CitizenId: request.CitizenId,
            Otp: otp,
            CreatedAt: DateTime.UtcNow
        );

        // 4. Lưu vào cache với key là sessionId
        var cacheKey = $"medical_record_lookup_session_{sessionId}";
        await cachedService.SetAsync(cacheKey, session, cancellationToken, SESSION_EXPIRE_MINUTES * 60);

        logger.LogInformation("Created medical record lookup session {SessionId} for CitizenId {CitizenId} with OTP {OTP}",
            sessionId, request.CitizenId, otp);

        // 5. Gửi OTP đến mobile app
        var customerId = request.CitizenId;
        var pushResult = await pushNotificationService.SendOtpToMobileAsync(customerId, otp, sessionId);

        if (!pushResult)
        {
            logger.LogWarning("Failed to send OTP push notification for session {SessionId}, but session was created successfully", sessionId);
        }
        else
        {
            logger.LogInformation("Successfully sent OTP push notification for session {SessionId}", sessionId);
        }

        var response = new InitiateMedicalRecordLookupResponse(
            SessionId: sessionId
        );

        return new Response<InitiateMedicalRecordLookupResponse>(response);
    }
}
