using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.Interface;
using BuildingBlocks.CQRS;
using FluentValidation;
using GoTRUST.EMR.Application.Data;
using GoTRUST.EMR.Application.Features.MedicalRecordLookup.Commands;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GoTRUST.EMR.Application.Features.MedicalRecordLookup.Queries;

/// <summary>
/// Request để xác thực OTP và tra cứu lịch sử khám chữa bệnh
/// </summary>
public record VerifyAndLookupMedicalRecordsRequest(
    Guid SessionId,
    string Otp,
    DateTime DateFrom,
    DateTime DateTo
) : IQuery<Response<VerifyAndLookupMedicalRecordsResponse>>;

/// <summary>
/// Response cho việc xác thực và tra cứu lịch sử khám chữa bệnh
/// </summary>
public record VerifyAndLookupMedicalRecordsResponse(
    PatientInfoDto Patient,
    List<MedicalExaminationHistoryDto> MedicalExaminationHistory
);

/// <summary>
/// DTO cho thông tin bệnh nhân
/// </summary>
public record PatientInfoDto(
    Guid Id,
    string PatientCode,
    string FullName,
    string Sex,
    int Age,
    DateTime DateOfBirth,
    string CitizenId,
    string HealthInsuranceNo,
    string PhoneNumber,
    string Address,
    string HospitalName
);

/// <summary>
/// DTO cho lịch sử khám chữa bệnh từ PatientMedicalHistory
/// </summary>
public record MedicalExaminationHistoryDto(
    Guid Id,
    string HistoryType,
    string Description,
    DateTime RecordedAt,
    string PatientName,
    string PatientCode
);

/// <summary>
/// Validator cho VerifyAndLookupMedicalRecordsRequest
/// </summary>
public class VerifyAndLookupMedicalRecordsRequestValidator : AbstractValidator<VerifyAndLookupMedicalRecordsRequest>
{
    public VerifyAndLookupMedicalRecordsRequestValidator()
    {
        RuleFor(x => x.SessionId)
            .NotEmpty()
            .WithMessage("SessionId không được để trống");

        RuleFor(x => x.Otp)
            .NotEmpty()
            .WithMessage("OTP không được để trống")
            .Length(6)
            .WithMessage("OTP phải có độ dài 6 ký tự")
            .Matches(@"^\d{6}$")
            .WithMessage("OTP chỉ được chứa 6 chữ số");

        RuleFor(x => x.DateFrom)
            .NotEmpty()
            .WithMessage("Ngày bắt đầu không được để trống")
            .LessThanOrEqualTo(x => x.DateTo)
            .WithMessage("Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc");

        RuleFor(x => x.DateTo)
            .NotEmpty()
            .WithMessage("Ngày kết thúc không được để trống")
            .LessThanOrEqualTo(DateTime.Now.Date.AddDays(1))
            .WithMessage("Ngày kết thúc không được lớn hơn ngày hiện tại");
    }
}

/// <summary>
/// Query handler cho việc xác thực OTP và tra cứu bệnh án
/// </summary>
public class VerifyAndLookupMedicalRecordsQueryHandler(
    ICachedService cachedService,
    IApplicationDbContext context,
    ILogger<VerifyAndLookupMedicalRecordsQueryHandler> logger)
    : IQueryHandler<VerifyAndLookupMedicalRecordsRequest, Response<VerifyAndLookupMedicalRecordsResponse>>
{
    public async Task<Response<VerifyAndLookupMedicalRecordsResponse>> Handle(
        VerifyAndLookupMedicalRecordsRequest request,
        CancellationToken cancellationToken)
    {
        logger.LogInformation("Verifying OTP and looking up medical records for session {SessionId}",
            request.SessionId);

        // 2. Lấy thông tin session từ cache
        var cacheKey = $"medical_record_lookup_session_{request.SessionId}";
        var session = await cachedService.GetAsync<MedicalRecordLookupSession>(cacheKey, cancellationToken);

        if (session == null)
        {
            logger.LogWarning("Session {SessionId} not found in cache", request.SessionId);
            throw new InvalidOperationException("Phiên tra cứu không tồn tại hoặc đã hết hạn. Vui lòng khởi tạo lại.");
        }

        // 3. Xác thực OTP
        if (session.Otp != request.Otp)
        {
            logger.LogWarning("Invalid OTP for session {SessionId}. Expected: {ExpectedOtp}, Provided: {ProvidedOtp}",
                request.SessionId, session.Otp, request.Otp);
            throw new InvalidOperationException("Mã OTP không chính xác. Vui lòng kiểm tra lại.");
        }

        logger.LogInformation("OTP verified successfully for session {SessionId}, looking up patient with CitizenId {CitizenId}",
            request.SessionId, session.CitizenId);

        // 4. Tìm bệnh nhân theo CCCD
        var patient = await context.Patients.AsNoTracking()
            .Include(p => p.Hospital)
            .Include(p => p.MedicalHistories.Where(mh => mh.RecordedAt.Date >= request.DateFrom.Date && mh.RecordedAt.Date <= request.DateTo.Date))
            .FirstOrDefaultAsync(p => p.CitizenId == session.CitizenId, cancellationToken);

        if (patient == null)
        {
            logger.LogWarning("Patient with CitizenId {CitizenId} not found",
                session.CitizenId);
            throw new InvalidOperationException("Không tìm thấy thông tin bệnh nhân với số CCCD này trong hệ thống.");
        }

        // 6. Tạo response
        var patientInfo = new PatientInfoDto(
            Id: patient.Id,
            PatientCode: patient.PatientCode,
            FullName: patient.FullName,
            Sex: patient.Sex,
            Age: patient.DateOfBirth.CalculateAge(),
            DateOfBirth: patient.DateOfBirth,
            CitizenId: patient.CitizenId,
            HealthInsuranceNo: patient.HealthInsuranceNo,
            PhoneNumber: patient.PhoneNumber,
            Address: patient.Address,
            HospitalName: patient.Hospital.Name
        );

        // 7. Tạo danh sách lịch sử khám chữa bệnh từ PatientMedicalHistory trong khoảng thời gian được filter
        var medicalExaminationHistory = patient.MedicalHistories.Select(mh => new MedicalExaminationHistoryDto(
            Id: mh.Id,
            HistoryType: mh.HistoryType,
            Description: mh.Description,
            RecordedAt: mh.RecordedAt,
            PatientName: patient.FullName,
            PatientCode: patient.PatientCode
        )).OrderByDescending(mh => mh.RecordedAt).ToList();

        logger.LogInformation("Successfully retrieved patient {PatientId} with {MedicalHistoryCount} medical history records from {DateFrom} to {DateTo} for session {SessionId}",
            patient.Id, medicalExaminationHistory.Count, request.DateFrom.Date, request.DateTo.Date, request.SessionId);

        // 8. Xóa session khỏi cache sau khi sử dụng thành công
        await cachedService.RemoveAsync(cacheKey, cancellationToken);

        var response = new VerifyAndLookupMedicalRecordsResponse(
            Patient: patientInfo,
            MedicalExaminationHistory: medicalExaminationHistory
        );

        return new Response<VerifyAndLookupMedicalRecordsResponse>(response);
    }
}
