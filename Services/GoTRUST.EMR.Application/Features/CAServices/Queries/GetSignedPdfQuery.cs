using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.CAService.Services;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.CAServices.Queries;

public record GetSignedPdfQuery : IQuery<Response<GetSignedPdfResponse>>
{
    public Guid? SignedPdfId { get; init; }
}

public record GetSignedPdfResponse(
    bool IsSuccess,
    Guid? SignedPdfId,
    string FileName,
    string Status,
    string DownloadUrl
);

public class GetSignedPdfValidator : AbstractValidator<GetSignedPdfQuery>
{
    public GetSignedPdfValidator()
    {
        RuleFor(x => x.SignedPdfId).NotNull().WithMessage("ID của PDF đã ký là bắt buộc");
    }
}

public class GetSignedPdfHandler(
    CAService _caService,
    ILogger<GetSignedPdfHandler> _logger
) : IQueryHandler<GetSignedPdfQuery, Response<GetSignedPdfResponse>>
{
    public async Task<Response<GetSignedPdfResponse>> Handle(GetSignedPdfQuery request, CancellationToken cancellationToken)
    {
        try
        {
            if (!request.SignedPdfId.HasValue)
                throw new BadRequestException("ID của PDF đã ký là bắt buộc");

            // Get signed PDF info from CA Service
            var response = await _caService.GetSignedPdfAsync(request.SignedPdfId.Value);

            if (!CAService.IsSuccess(response))
            {
                var errorMessage = CAService.GetErrorMessage(response);
                _logger.LogError("Không thể lấy thông tin PDF đã ký: {Error}", errorMessage);
                throw new BadRequestException($"Không thể lấy PDF đã ký: {errorMessage}");
            }

            var pdfInfo = response.Data!;

            return new Response<GetSignedPdfResponse>(
                new GetSignedPdfResponse(
                    true,
                    pdfInfo.Id,
                    pdfInfo.FileName,
                    pdfInfo.Status,
                    pdfInfo.DownloadUrl
                )
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi xảy ra khi lấy PDF đã ký: {SignedPdfId}", request.SignedPdfId);
            throw new BadRequestException("Đã xảy ra lỗi khi lấy PDF đã ký", ex.Message);
        }
    }
}
