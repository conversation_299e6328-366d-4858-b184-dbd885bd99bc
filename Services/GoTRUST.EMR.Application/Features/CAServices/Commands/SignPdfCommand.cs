using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.CAService.Services;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Features.Upload.Commands;

namespace GoTRUST.EMR.Application.Features.CAServices.Commands;

public record SignPdfCommand : ICommand<Response<SignPdfResponse>>
{
    public Guid? SignedPdfId { get; init; }
    public Guid MedicalRecordFileId { get; init; }
    public string KeyBase64 { get; init; } = string.Empty;
    public int X { get; init; } = 100;
    public int Y { get; init; } = 200;
    public int Width { get; init; } = 150;
    public int Height { get; init; } = 50;
    public int PageNumber { get; init; } = 1;
}

public record SignPdfResponse(
    bool IsSuccess,
    Guid? SignedPdfId
);

public class SignPdfValidator : AbstractValidator<SignPdfCommand>
{
    public SignPdfValidator()
    {
        RuleFor(x => x.SignedPdfId).NotNull().WithMessage("Signed PDF ID is required");
        RuleFor(x => x.MedicalRecordFileId).NotEmpty().WithMessage("Medical Record File ID is required");
        RuleFor(x => x.KeyBase64).NotEmpty().WithMessage("Key is required");
        RuleFor(x => x.X).GreaterThanOrEqualTo(0);
        RuleFor(x => x.Y).GreaterThanOrEqualTo(0);
        RuleFor(x => x.Width).GreaterThan(0);
        RuleFor(x => x.Height).GreaterThan(0);
        RuleFor(x => x.PageNumber).GreaterThan(0);
    }
}

public class SignPdfHandler(
    CAService _caService,
    ILogger<SignPdfHandler> _logger,
    IApplicationDbContext _context,
    ISender _mediator,
    HttpClient _httpClient
) : ICommandHandler<SignPdfCommand, Response<SignPdfResponse>>
{
    public async Task<Response<SignPdfResponse>> Handle(SignPdfCommand request, CancellationToken cancellationToken)
    {
        try
        {
            if (!request.SignedPdfId.HasValue)
                throw new BadRequestException("Signed PDF ID is required");

            // Get the medical record file from database
            var medicalRecordFile = await _context.MedicalRecordFiles
                .FirstOrDefaultAsync(x => x.Id == request.MedicalRecordFileId, cancellationToken);

            if (medicalRecordFile == null)
                throw new NotFoundException($"Medical record file not found: {request.MedicalRecordFileId}");

            // Download the file from S3 URL
            var fileBytes = await _httpClient.GetByteArrayAsync(medicalRecordFile.FilePath, cancellationToken);

            // Create IFormFile from downloaded bytes
            var stream = new MemoryStream(fileBytes);
            var formFile = new FormFile(stream, 0, fileBytes.Length, "file", medicalRecordFile.FileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = medicalRecordFile.FileType
            };

            // Decrypt the file using MediatR
            var decryptCommand = new DecryptFileCommand
            {
                File = formFile,
                IV = medicalRecordFile.InitialVector
            };
            var decryptResult = await _mediator.Send(decryptCommand, cancellationToken);

            if (decryptResult.Code != "000" || string.IsNullOrEmpty(decryptResult.Data))
                throw new BadRequestException("Failed to decrypt PDF file");

            // Read the decrypted PDF file
            var decryptedBytes = await File.ReadAllBytesAsync(decryptResult.Data, cancellationToken);
            var fileBase64 = Convert.ToBase64String(decryptedBytes);

            // Sign PDF
            var signResponse = await _caService.SignPdfAsync(
                request.SignedPdfId.Value,
                fileBase64,
                request.KeyBase64,
                request.X,
                request.Y,
                request.Width,
                request.Height,
                request.PageNumber);

            if (CAService.IsSuccess(signResponse))
            {
                _logger.LogInformation("PDF signed successfully: {SignedPdfId}", signResponse.Data!.SignedPDFId);

                // Get the signed PDF content
                var signedPdfResult = await _caService.GetSignedPdfAsync(signResponse.Data.SignedPDFId!.Value);

                if (!CAService.IsSuccess(signedPdfResult) || signedPdfResult.Data == null)
                {
                    var getSignedPdfErrorMessage = CAService.GetErrorMessage(signedPdfResult);
                    _logger.LogError("Failed to get signed PDF: {Error}", getSignedPdfErrorMessage);
                    throw new BadRequestException($"Failed to get signed PDF: {getSignedPdfErrorMessage}");
                }

                // Download the signed PDF content
                var signedPdfBytes = await _httpClient.GetByteArrayAsync(signedPdfResult.Data.DownloadUrl, cancellationToken);
                var signedStream = new MemoryStream(signedPdfBytes);
                var signedFormFile = new FormFile(signedStream, 0, signedPdfBytes.Length, "file", medicalRecordFile.FileName)
                {
                    Headers = new HeaderDictionary(),
                    ContentType = medicalRecordFile.FileType
                };

                // Encrypt the signed PDF using MediatR
                var encryptCommand = new EncryptFileCommand(signedFormFile);
                var encryptResult = await _mediator.Send(encryptCommand, cancellationToken);

                if (encryptResult.Code != "000" || encryptResult.Data == null)
                    throw new BadRequestException("Failed to encrypt signed PDF file");

                if (File.Exists(decryptResult.Data))
                    File.Delete(decryptResult.Data);

                return new Response<SignPdfResponse>(
                    new SignPdfResponse(
                        true,
                        signResponse.Data.SignedPDFId
                    )
                );
            }

            var errorMessage = CAService.GetErrorMessage(signResponse);
            _logger.LogError("Failed to sign PDF: {Error}", errorMessage);
            throw new BadRequestException($"Failed to sign PDF: {errorMessage}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while signing PDF");
            throw new BadRequestException("An error occurred while signing PDF", ex.Message);
        }
    }
}
