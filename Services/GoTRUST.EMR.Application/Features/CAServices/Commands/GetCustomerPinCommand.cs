using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.CAService.Services;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.CAServices.Commands;

public record GetCustomerPinCommand : ICommand<Response<GetCustomerPinResponse>>
{
    public IFormFile? FaceImage { get; init; }
    public Guid? CustomerId { get; init; }
}

public record GetCustomerPinResponse(
    bool IsSuccess,
    Guid? CustomerId,
    string Pin,
    string Message
);

public class GetCustomerPinValidator : AbstractValidator<GetCustomerPinCommand>
{
    public GetCustomerPinValidator()
    {
        RuleFor(x => x.FaceImage).NotNull().WithMessage("Face image is required");
        RuleFor(x => x.CustomerId).NotNull().WithMessage("Customer ID is required");
    }
}

public class GetCustomerPinHandler(
    CAService _caService,
    ILogger<GetCustomerPinHandler> _logger
) : ICommandHandler<GetCustomerPinCommand, Response<GetCustomerPinResponse>>
{
    public async Task<Response<GetCustomerPinResponse>> Handle(GetCustomerPinCommand request, CancellationToken cancellationToken)
    {
        try
        {
            if (request.FaceImage == null || request.FaceImage.Length == 0)
                throw new BadRequestException("Face image is required");

            if (!request.CustomerId.HasValue)
                throw new BadRequestException("Customer ID is required");

            // Convert face image to base64
            string faceImgBase64;
            using (var memoryStream = new MemoryStream())
            {
                await request.FaceImage.CopyToAsync(memoryStream, cancellationToken);
                faceImgBase64 = Convert.ToBase64String(memoryStream.ToArray());
            }

            var response = await _caService.GetCustomerPinAsync(faceImgBase64, request.CustomerId.Value);

            if (CAService.IsSuccess(response))
            {
                _logger.LogInformation("Customer PIN retrieved successfully: {CustomerId}", response.Data!.CustomerId);

                return new Response<GetCustomerPinResponse>(
                    new GetCustomerPinResponse(
                        true,
                        response.Data.CustomerId,
                        response.Data.Pin,
                        "Customer PIN retrieved successfully"
                    )
                );
            }

            var errorMessage = BuildingBlocks.Common.CAService.Services.CAService.GetErrorMessage(response);
            _logger.LogError("Failed to get customer PIN: {Error}", errorMessage);
            throw new BadRequestException($"Failed to get customer PIN: {errorMessage}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting customer PIN");
            throw new BadRequestException("An error occurred while getting customer PIN", ex.Message);
        }
    }
}
