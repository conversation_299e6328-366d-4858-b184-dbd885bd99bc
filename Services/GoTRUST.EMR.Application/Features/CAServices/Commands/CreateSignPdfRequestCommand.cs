using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.CAServices.Commands;

public record CreateSignPdfRequestCommand : ICommand<Response<CreateSignPdfRequestResponse>>
{
    public string FileName { get; init; } = string.Empty;
    public Guid? CustomerId { get; init; }
}

public record CreateSignPdfRequestResponse(
    bool IsSuccess,
    Guid? SignedPdfId,
    string Message
);

public class CreateSignPdfRequestValidator : AbstractValidator<CreateSignPdfRequestCommand>
{
    public CreateSignPdfRequestValidator()
    {
        RuleFor(x => x.FileName).NotEmpty().MaximumLength(255);
        RuleFor(x => x.CustomerId).NotNull().WithMessage("Customer ID is required");
    }
}

public class CreateSignPdfRequestHandler(
    BuildingBlocks.Common.CAService.Services.CAService _caService,
    ILogger<CreateSignPdfRequestHandler> _logger
) : ICommandHandler<CreateSignPdfRequestCommand, Response<CreateSignPdfRequestResponse>>
{
    public async Task<Response<CreateSignPdfRequestResponse>> Handle(CreateSignPdfRequestCommand request, CancellationToken cancellationToken)
    {
        try
        {
            if (!request.CustomerId.HasValue)
                throw new BadRequestException("Customer ID is required");

            var response = await _caService.CreateSignPdfRequestAsync(
                request.FileName,
                request.CustomerId.Value);

            if (BuildingBlocks.Common.CAService.Services.CAService.IsSuccess(response))
            {
                _logger.LogInformation("Sign PDF request created successfully: {SignedPdfId}", response.Data!.SignedPDFId);

                return new Response<CreateSignPdfRequestResponse>(
                    new CreateSignPdfRequestResponse(
                        true,
                        response.Data.SignedPDFId,
                        "Sign PDF request created successfully"
                    )
                );
            }

            var errorMessage = BuildingBlocks.Common.CAService.Services.CAService.GetErrorMessage(response);
            _logger.LogError("Failed to create sign PDF request: {Error}", errorMessage);
            throw new BadRequestException($"Failed to create sign PDF request: {errorMessage}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating sign PDF request");
            throw new BadRequestException("An error occurred while creating sign PDF request", ex.Message);
        }
    }
}
