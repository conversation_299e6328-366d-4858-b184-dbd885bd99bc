using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.CAService.Services;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.CAServices.Commands;

public record CreateCustomerCommand : ICommand<Response<CreateCustomerResponse>>
{
    public string IdentityNo { get; init; } = string.Empty;
    public string Email { get; init; } = string.Empty;
    public string PhoneNumber { get; init; } = string.Empty;
    public string FullName { get; init; } = string.Empty;
    public IFormFile? FaceImage { get; init; }
}

public record CreateCustomerResponse(
    bool IsSuccess,
    Guid? CustomerId,
    string Message
);

public class CreateCustomerValidator : AbstractValidator<CreateCustomerCommand>
{
    public CreateCustomerValidator()
    {
        RuleFor(x => x.IdentityNo).NotEmpty().MaximumLength(20);
        RuleFor(x => x.Email).NotEmpty().MaximumLength(255).EmailAddress();
        RuleFor(x => x.PhoneNumber).NotEmpty().MaximumLength(15);
        RuleFor(x => x.FullName).NotEmpty().MaximumLength(255);
        RuleFor(x => x.FaceImage).NotNull().WithMessage("Face image is required");
    }
}

public class CreateCustomerHandler(
    CAService _caService,
    ILogger<CreateCustomerHandler> _logger
) : ICommandHandler<CreateCustomerCommand, Response<CreateCustomerResponse>>
{
    public async Task<Response<CreateCustomerResponse>> Handle(CreateCustomerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            if (request.FaceImage == null || request.FaceImage.Length == 0)
                throw new BadRequestException("Face image is required");

            // Convert face image to base64
            string faceImgBase64;
            using (var memoryStream = new MemoryStream())
            {
                await request.FaceImage.CopyToAsync(memoryStream, cancellationToken);
                faceImgBase64 = Convert.ToBase64String(memoryStream.ToArray());
            }

            var response = await _caService.CreateCustomerAsync(
                request.IdentityNo,
                request.Email,
                request.PhoneNumber,
                request.FullName,
                faceImgBase64);

            if (CAService.IsSuccess(response))
            {
                _logger.LogInformation("Customer created successfully: {CustomerId}", response.Data!.CustomerId);

                return new Response<CreateCustomerResponse>(
                    new CreateCustomerResponse(
                        true,
                        response.Data.CustomerId,
                        "Customer created successfully"
                    )
                );
            }

            var errorMessage = CAService.GetErrorMessage(response);
            _logger.LogError("Failed to create customer: {Error}", errorMessage);
            throw new BadRequestException($"Failed to create customer: {errorMessage}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating customer");
            throw new BadRequestException("An error occurred while creating customer", ex.Message);
        }
    }
}
