using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.CAService.Models;
using BuildingBlocks.Common.CAService.Services;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.CAServices.Commands;

public record FindCustomerByFaceCommand : ICommand<Response<FindCustomerByFaceResponse>>
{
    public IFormFile? FaceImage { get; init; }
}

public record FindCustomerByFaceResponse(
    bool IsSuccess,
    List<CustomerFaceMatch> Customers,
    int Count,
    string Message
);

public class FindCustomerByFaceValidator : AbstractValidator<FindCustomerByFaceCommand>
{
    public FindCustomerByFaceValidator()
    {
        RuleFor(x => x.FaceImage).NotNull().WithMessage("Face image is required");
    }
}

public class FindCustomerByFaceHandler(
    CAService _caService,
    ILogger<FindCustomerByFaceHandler> _logger
) : ICommandHandler<FindCustomerByFaceCommand, Response<FindCustomerByFaceResponse>>
{
    public async Task<Response<FindCustomerByFaceResponse>> Handle(FindCustomerByFaceCommand request, CancellationToken cancellationToken)
    {
        try
        {
            if (request.FaceImage == null || request.FaceImage.Length == 0)
                throw new BadRequestException("Face image is required");

            // Convert face image to base64
            string faceImgBase64;
            using (var memoryStream = new MemoryStream())
            {
                await request.FaceImage.CopyToAsync(memoryStream, cancellationToken);
                faceImgBase64 = Convert.ToBase64String(memoryStream.ToArray());
            }

            var response = await _caService.FindCustomersByFaceAsync(faceImgBase64);

            if (CAService.IsSuccess(response))
            {
                _logger.LogInformation("Found {Count} matching customers", response.Data!.Faces.Count);

                return new Response<FindCustomerByFaceResponse>(
                    new FindCustomerByFaceResponse(
                        true,
                        response.Data.Faces,
                        response.Data.Faces.Count,
                        $"Found {response.Data.Faces.Count} matching customers"
                    )
                );
            }

            var errorMessage = CAService.GetErrorMessage(response);
            _logger.LogError("Failed to find customers by face: {Error}", errorMessage);
            throw new BadRequestException($"Failed to find customers: {errorMessage}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while finding customers by face");
            throw new BadRequestException("An error occurred while finding customers", ex.Message);
        }
    }
}
