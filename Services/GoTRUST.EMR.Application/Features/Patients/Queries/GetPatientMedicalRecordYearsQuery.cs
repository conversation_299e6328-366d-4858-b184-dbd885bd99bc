using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.EntityFrameworkCore;

namespace GoTRUST.EMR.Application.Features.Patients.Queries;

public record GetPatientMedicalRecordYearsRequest(
    Guid PatientId
) : IRequest<Response<List<int>>>;

public class GetPatientMedicalRecordYearsHandler(
    IApplicationDbContext context,
    ILogger<GetPatientMedicalRecordYearsHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<GetPatientMedicalRecordYearsRequest, Response<List<int>>>
{
    public async Task<Response<List<int>>> Handle(
        GetPatientMedicalRecordYearsRequest request, 
        CancellationToken cancellationToken)
    {
        // 1. Lấy HospitalId từ claims
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");

        // 2. Kiểm tra bệnh nhân có tồn tại và thuộc bệnh viện hiện tại không
        var patient = await context.Patients.AsNoTracking()
            .FirstOrDefaultAsync(p => p.Id == request.PatientId && p.HospitalId == hospitalId, cancellationToken)
            ?? throw new PatientNotFoundException(request.PatientId);

        // 3. Lấy danh sách các năm từ medical records của bệnh nhân
        var years = await context.MedicalRecords
            .AsNoTracking()
            .Where(mr => mr.PatientId == request.PatientId && mr.HospitalId == hospitalId)
            .Select(mr => mr.AdmissionDate.Year)
            .Distinct()
            .OrderByDescending(year => year)
            .ToListAsync(cancellationToken);

        // 4. Log thông tin
        logger.LogInformation(
            "Retrieved {Count} distinct years for patient {PatientId} in hospital {HospitalId}",
            years.Count, 
            request.PatientId, 
            hospitalId);

        return new Response<List<int>>(years);
    }
}
