using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using Mapster;

namespace GoTRUST.EMR.Application.Features.Patients.Queries;

public record GetPatientMedicalExaminationsRequest(
    Guid PatientId,
    int? Year = null
) : IRequest<Response<List<PatientMedicalExaminationDto>>>;

public record PatientMedicalExaminationDto(
    string DepartmentName,
    DateTime AdmissionDate,
    string AdmissionCode,
    string RecordCode,
    string Id
)
{
    public string DepartmentName { get; set; } = string.Empty;
};

public class GetPatientMedicalExaminationsHandler(
    IApplicationDbContext context,
    ILogger<GetPatientMedicalExaminationsHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<GetPatientMedicalExaminationsRequest, Response<List<PatientMedicalExaminationDto>>>
{
    public async Task<Response<List<PatientMedicalExaminationDto>>> Handle(
        GetPatientMedicalExaminationsRequest request,
        CancellationToken cancellationToken)
    {
        // 1. <PERSON><PERSON>y HospitalId từ claims
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");

        // 2. Kiểm tra bệnh nhân có tồn tại và thuộc bệnh viện hiện tại không
        var patient = await context.Patients.AsNoTracking()
            .FirstOrDefaultAsync(p => p.Id == request.PatientId && p.HospitalId == hospitalId, cancellationToken)
            ?? throw new PatientNotFoundException(request.PatientId);

        // 3. Tạo query để lấy danh sách medical records
        IQueryable<MedicalRecord> query = context.MedicalRecords
            .AsNoTracking()
            .Where(mr => mr.PatientId == request.PatientId && mr.HospitalId == hospitalId)
            .Include(mr => mr.Department);

        // 4. Lọc theo năm nếu được cung cấp
        if (request.Year.HasValue)
        {
            query = query.Where(mr => mr.AdmissionDate.Year == request.Year.Value);
        }

        // 5. Lấy dữ liệu và sắp xếp theo ngày tiếp nhận mới nhất
        var medicalRecords = await query
            .OrderByDescending(mr => mr.AdmissionDate)
            .ToListAsync(cancellationToken);

        var result = medicalRecords.Select(mr =>
        {
            var dto = mr.Adapt<PatientMedicalExaminationDto>();
            dto.DepartmentName = mr.Department.Name;
            return dto;
        }).ToList();

        // 7. Log thông tin
        logger.LogInformation(
            "Retrieved {Count} medical examinations for patient {PatientId} in hospital {HospitalId}" +
            (request.Year.HasValue ? " for year {Year}" : ""),
            result.Count,
            request.PatientId,
            hospitalId,
            request.Year);

        return new Response<List<PatientMedicalExaminationDto>>(result);
    }
}
