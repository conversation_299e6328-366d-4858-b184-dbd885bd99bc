using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using Mapster;

namespace GoTRUST.EMR.Application.Features.Patients.Queries;

public record GetPatientByCodeAndCitizenIdRequest(string? SearchTerm) : IRequest<Response<GetPatientByCodeAndCitizenIdResponse>>;

public record GetPatientByCodeAndCitizenIdResponse(
    Guid Id,
    string PatientCode,
    string FullName,
    string AvatarUrl,
    string Sex,
    int Age,
    DateTime DateOfBirth,
    string EthnicGroup,
    string CitizenId,
    string HealthInsuranceNo,
    DateTime? CitizenIdIssueDate,
    string CitizenIdIssuePlace,
    string Email,
    string PhoneNumber,
    string Address,
    string Occupation,
    string MaritalStatus,
    string Workplace,
    int NumberOfMedicalExaminations,
    int NumberOfReExaminations,
    Guid HospitalId,
    string HospitalName,
    Guid? UserId,
    DateTime? CreatedAt,
    string? CreatedBy,
    DateTime? UpdatedAt,
    string? UpdatedBy
);

public class GetPatientByCodeAndCitizenIdHandler(
    IApplicationDbContext context,
    ILogger<GetPatientByCodeAndCitizenIdHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<GetPatientByCodeAndCitizenIdRequest, Response<GetPatientByCodeAndCitizenIdResponse>>
{
    public async Task<Response<GetPatientByCodeAndCitizenIdResponse>> Handle(GetPatientByCodeAndCitizenIdRequest request, CancellationToken cancellationToken)
    {
        // 1. Lấy HospitalId từ claims
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal()
                         ?? throw new UndefineHospitalInfoException();
        
        var patient = await context.Patients.AsNoTracking()
            .Include(p => p.Hospital)
            .FirstOrDefaultAsync(p => (p.PatientCode == request.SearchTerm || p.CitizenId == request.SearchTerm) 
                                      && p.HospitalId == hospitalId, cancellationToken)
            ?? throw new PatientNotFoundException($"Không tìm thấy bệnh nhân với search term: {request.SearchTerm}");

        logger.LogInformation("Retrieved patient {PatientId} with code {PatientCode} and CitizenId {CitizenId}", 
            patient.Id, patient.PatientCode, patient.CitizenId);

        var response = patient.Adapt<GetPatientByCodeAndCitizenIdResponse>() with
        {
            Age = patient.DateOfBirth.CalculateAge()
        };

        return new Response<GetPatientByCodeAndCitizenIdResponse>(response);
    }
}
