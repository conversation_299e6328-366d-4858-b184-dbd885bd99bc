using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using Mapster;

namespace GoTRUST.EMR.Application.Features.Patients.Queries;

public record GetAllPatientsRequest : PaginationWithSortRequest, IRequest<PaginationResponse<PatientDto>>
{
    public string? Search { get; init; }
    public DateTime? CreatedDate { get; init; }
    public int? FromAge { get; init; }
    public int? ToAge { get; init; }
    public string[]? SelectedSex { get; init; }
}

public record PatientDto(
    Guid Id,
    string PatientCode,
    string FullName,
    string Sex,
    int Age,
    string AvatarUrl,
    DateTime DateOfBirth,
    string EthnicGroup,
    string CitizenId,
    string HealthInsuranceNo,
    string Email,
    string PhoneNumber,
    string Address,
    string Occupation,
    string MaritalStatus,
    string Workplace,
    Guid HospitalId,
    string HospitalName,
    Guid? UserId,
    DateTime? CreatedAt,
    string? CreatedBy
)
{
    public int Age
    {
        get => DateTime.UtcNow.Year - DateOfBirth.Year - (DateTime.UtcNow < DateOfBirth.AddYears(DateTime.UtcNow.Year - DateOfBirth.Year) ? 1 : 0);
    }
}

public class GetAllPatientsHandler(
    IApplicationDbContext context,
    ILogger<GetAllPatientsHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<GetAllPatientsRequest, PaginationResponse<PatientDto>>
{
    public async Task<PaginationResponse<PatientDto>> Handle(GetAllPatientsRequest request, CancellationToken cancellationToken)
    {
        // 1. Lấy HospitalId từ claims
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");
        
        // 2. Base query - lấy danh sách bệnh nhân của hospital hiện tại
        IQueryable<Patient> query = context.Patients
            .AsNoTracking()
            .Where(p => p.HospitalId == hospitalId)
            .Include(p => p.Hospital)
            .Include(p => p.MedicalRecords);

        // 3. Search filter
        if (!string.IsNullOrEmpty(request.Search))
        {
            var searchLower = request.Search.ToLower();
            query = query.Where(p => 
                p.PatientCode.ToLower().Contains(searchLower) ||
                p.FullName.ToLower().Contains(searchLower) ||
                p.Email.ToLower().Contains(searchLower) ||
                p.PhoneNumber.Contains(searchLower) ||
                p.CitizenId.Contains(searchLower)
            );
        }
        
        if (request.CreatedDate.HasValue)
        {
            // Lấy ngày tháng năm, bỏ qua phần thời gian.
            var targetDate = request.CreatedDate.Value.Date;

            // Chỉ định rằng ngày này là một ngày theo giờ UTC.
            // startOfDayUtc sẽ là, ví dụ: 2025-06-23 00:00:00 UTC
            var startOfDayUtc = DateTime.SpecifyKind(targetDate, DateTimeKind.Utc);

            // Lấy thời điểm bắt đầu của ngày hôm sau.
            // endOfDayUtc sẽ là, ví dụ: 2025-06-24 00:00:00 UTC
            var startOfNextDayUtc = startOfDayUtc.AddDays(1);

            // Tìm tất cả các bản ghi có CreatedAt lớn hơn hoặc bằng đầu ngày
            // VÀ nhỏ hơn đầu ngày hôm sau.
            // Điều này đảm bảo lấy tất cả bản ghi trong ngày targetDate.
            query = query.Where(p => p.CreatedAt >= startOfDayUtc && p.CreatedAt < startOfNextDayUtc);
        }
        
        // 3.2. Filter by Age range
        if (request.FromAge.HasValue || request.ToAge.HasValue)
        {
            var today = DateTime.UtcNow;
            if (request.FromAge.HasValue)
            {
                var fromDate = today.AddYears(-request.FromAge.Value);
                query = query.Where(p => p.DateOfBirth <= fromDate);
            }
            if (request.ToAge.HasValue)
            {
                var toDate = today.AddYears(-request.ToAge.Value);
                query = query.Where(p => p.DateOfBirth >= toDate);
            }
        }

        // 3.3. Filter by DepartmentId
        if (request.SelectedSex != null && request.SelectedSex.Length > 0)
        {
            query = query.Where(p => request.SelectedSex.Contains(p.Sex));
        }

        // 4. Sorting
        query = request.SortBy?.ToLower() switch
        {
            "patientcode" => request.SortDescending == true
                ? query.OrderByDescending(p => p.PatientCode)
                : query.OrderBy(p => p.PatientCode),
            "fullname" => request.SortDescending == true
                ? query.OrderByDescending(p => p.FullName)
                : query.OrderBy(p => p.FullName),
            "dateofbirth" => request.SortDescending == true
                ? query.OrderByDescending(p => p.DateOfBirth)
                : query.OrderBy(p => p.DateOfBirth),
            "email" => request.SortDescending == true
                ? query.OrderByDescending(p => p.Email)
                : query.OrderBy(p => p.Email),
            "createdate" => request.SortDescending == true
                ? query.OrderByDescending(p => p.CreatedAt)
                : query.OrderBy(p => p.CreatedAt),
            _ => query.OrderByDescending(p => p.CreatedAt) 
        };

        // 5. Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // 6. Apply pagination
        var patients = await query
            .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
            .Take(request.PageSize!.Value)
            .ProjectToType<PatientDto>()
            .ToListAsync(cancellationToken);

        // 7. Create PaginationResponse
        var paginationResponse = new PaginationResponse<PatientDto>(
            request.PageIndex,
            request.PageSize,
            totalCount,
            patients
        );

        logger.LogInformation("Retrieved {Count} patients (page {PageIndex}) for hospital {HospitalId}", 
            patients.Count, request.PageIndex, hospitalId);
        
        return paginationResponse;
    }
}
