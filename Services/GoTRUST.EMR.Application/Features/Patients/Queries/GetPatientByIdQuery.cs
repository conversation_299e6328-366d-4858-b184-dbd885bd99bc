using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.Patients.Models;
using GoTRUST.EMR.Application.Helpers;
using Mapster;

namespace GoTRUST.EMR.Application.Features.Patients.Queries;

public record GetPatientByIdRequest(Guid Id) : IRequest<Response<GetPatientByIdResponse>>;

public record MedicalRecordDto(
    Guid Id,
    string AdmissionCode,
    string RecordCode,
    string StorageNumber,
    DateTime AdmissionDate,
    DateTime? DischargeDate,
    int FilmCount,
    bool HasBHYT,
    Guid DepartmentId,
    string DepartmentName,
    string Note,
    int TotalFilesUploaded,
    string Status,
    int StorageYears,
    DateTime? CreatedAt,
    string? CreatedBy
);

public record PatientMedicalHistoryDto(
    Guid Id,
    string HistoryType,
    string Description,
    DateTime RecordedAt,
    DateTime? CreatedAt,
    string? CreatedBy
);

public record PatientHealthMetricDto(
    Guid Id,
    string MetricName,
    decimal Value,
    string Unit,
    DateTime RecordedAt,
    DateTime? CreatedAt,
    string? CreatedBy
);

public record GetPatientByIdResponse(
    Guid Id,
    string PatientCode,
    string FullName,
    string AvatarUrl,
    string Sex,
    int Age,
    DateTime DateOfBirth,
    string EthnicGroup,
    string CitizenId,
    string HealthInsuranceNo,
    DateTime? CitizenIdIssueDate,
    string CitizenIdIssuePlace,
    string Email,
    string PhoneNumber,
    string Address,
    string Occupation,
    string MaritalStatus,
    string Workplace,
    int NumberOfMedicalExaminations,
    int NumberOfReExaminations,
    Guid HospitalId,
    string HospitalName,
    Guid? UserId,
    DateTime? CreatedAt,
    string? CreatedBy,
    DateTime? UpdatedAt,
    string? UpdatedBy,
    ICollection<MedicalRecordDto> MedicalRecords,
    ICollection<PatientHealthMetricDto> HealthMetrics,
    PersonalHistory PersonalHistory,
    List<FamilyHistory> FamilyHistory
)
{
    public int Age
    {
        get => DateTime.UtcNow.Year - DateOfBirth.Year - (DateTime.UtcNow < DateOfBirth.AddYears(DateTime.UtcNow.Year - DateOfBirth.Year) ? 1 : 0);
    }

    public PersonalHistory PersonalHistory { get; set; } = new PersonalHistory();
    
    public List<FamilyHistory> FamilyHistory { get; set; } = [];
}

public class GetPatientByIdHandler(
    IApplicationDbContext context,
    ILogger<GetPatientByIdHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<GetPatientByIdRequest, Response<GetPatientByIdResponse>>
{
    public async Task<Response<GetPatientByIdResponse>> Handle(GetPatientByIdRequest request, CancellationToken cancellationToken)
    {
        // 1. Lấy HospitalId từ claims
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại."); 
        
        var patient = await context.Patients.AsNoTracking()
            .Include(p => p.Hospital)
            .Include(p => p.MedicalRecords)
            .ThenInclude(d => d.Department)
            .Include(p => p.MedicalHistories)
            .Include(p => p.HealthMetrics)
            .FirstOrDefaultAsync(p => p.Id == request.Id && p.HospitalId == hospitalId, cancellationToken)
            ?? throw new PatientNotFoundException(request.Id);

        logger.LogInformation("Retrieved patient {PatientId} with code {PatientCode} and {MedicalRecordsCount} medical records, {MedicalHistoriesCount} medical histories, {HealthMetricsCount} health metrics", 
            patient.Id, patient.PatientCode, patient.MedicalRecords.Count, patient.MedicalHistories.Count, patient.HealthMetrics.Count);
        
        var response = patient.Adapt<GetPatientByIdResponse>();

        // Tiền sử bản thân
        string tienSuBanThan = patient.MedicalHistories.Where(mh => mh.HistoryType == "PersonalHistory")
            .Select(mh => mh.Description)
            .FirstOrDefault() ?? string.Empty;

        // Tiền sử gia đình
        string tienSuGiaDinh = patient.MedicalHistories.Where(mh => mh.HistoryType == "FamilyHistory")
            .Select(mh => mh.Description)
            .FirstOrDefault() ?? string.Empty;

        // Convert tiền sử bản thân từ string mang sang PersonalHistory model
        var personalHistory = StringExtensions.DeserializeTo<PersonalHistory>(tienSuBanThan) ?? new PersonalHistory();

        // Convert tiền sử gia đình từ string mang sang FamilyHistory model
        var familyHistory = StringExtensions.DeserializeTo<List<FamilyHistory>>(tienSuGiaDinh) ?? [];

        response.PersonalHistory = personalHistory;
        response.FamilyHistory = familyHistory;

        return new Response<GetPatientByIdResponse>(response);
    }
}
