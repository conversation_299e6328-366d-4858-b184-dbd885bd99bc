using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Patients.Commands;

public record DeletePatientRequest(Guid Id, Guid ApproveId) : ICommand<Response<DeletePatientResponse>>;

public record DeletePatientResponse(
    Guid Id,
    string PatientCode,
    string FullName,
    DateTime? DeletedAt,
    string? DeletedBy
);

public class DeletePatientRequestValidator : AbstractValidator<DeletePatientRequest>
{
    public DeletePatientRequestValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID bệnh nhân không được để trống");
    }
}

public class DeletePatientHandler(
    IApplicationDbContext context,
    ILogger<DeletePatientHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<DeletePatientRequest, Response<DeletePatientResponse>>
{
    public async Task<Response<DeletePatientResponse>> Handle(DeletePatientRequest request, CancellationToken cancellationToken)
    {
        // 1. Lấy thông tin user hiện tại và HospitalId từ claims
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        var hospitalId = httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");

        // 2. Find existing patient
        var patient = await context.Patients
            .FirstOrDefaultAsync(p => p.Id == request.Id
                          && p.HospitalId == hospitalId, cancellationToken)
            ?? throw new PatientNotFoundException(request.Id);

        // 3. Soft delete related records if they exist
        var currentTime = DateTime.UtcNow;

        // 3.1. Soft delete PatientHealthMetrics
        var healthMetrics = await context.PatientHealthMetrics
            .Where(phm => phm.PatientId == request.Id)
            .ToListAsync(cancellationToken);

        foreach (var metric in healthMetrics)
        {
            metric.IsDeleted = true;
            metric.UpdatedAt = currentTime;
            metric.UpdatedBy = currentUser;
        }

        // 3.2. Soft delete PatientMedicalHistories
        var medicalHistories = await context.PatientMedicalHistories
            .Where(pmh => pmh.PatientId == request.Id)
            .ToListAsync(cancellationToken);

        foreach (var history in medicalHistories)
        {
            history.IsDeleted = true;
            history.UpdatedAt = currentTime;
            history.UpdatedBy = currentUser;
        }

        // 3.3. Soft delete MedicalRecords
        var medicalRecords = await context.MedicalRecords
            .Where(mr => mr.PatientId == request.Id)
            .ToListAsync(cancellationToken);

        foreach (var record in medicalRecords)
        {
            record.IsDeleted = true;
            record.UpdatedAt = currentTime;
            record.UpdatedBy = currentUser;
        }

        // 4. Soft delete Patient 
        patient.IsDeleted = true;
        patient.UpdatedAt = currentTime;
        patient.UpdatedBy = currentUser;

        // 5. Save to database
        context.Patients.Update(patient);
        context.PatientHealthMetrics.UpdateRange(healthMetrics);
        context.PatientMedicalHistories.UpdateRange(medicalHistories);
        context.MedicalRecords.UpdateRange(medicalRecords);
        patient.AddDomainEvent(new PatientDeleteEvent(patient));
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
            throw new PatientSaveFailedException("xóa");

        // 6. Log success
        logger.LogInformation("Deleted Patient with Id {PatientId} and Code {PatientCode} by {User}. " +
            "Also deleted {HealthMetricsCount} health metrics, {MedicalHistoriesCount} medical histories, " +
            "and {MedicalRecordsCount} medical records",
            patient.Id, patient.PatientCode, currentUser,
            healthMetrics.Count, medicalHistories.Count, medicalRecords.Count);

        // 7. Return response
        var response = new DeletePatientResponse(
            patient.Id,
            patient.PatientCode,
            patient.FullName,
            patient.UpdatedAt,
            patient.UpdatedBy
        );

        return new Response<DeletePatientResponse>(response);
    }
}
