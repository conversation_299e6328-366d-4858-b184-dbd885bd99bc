using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.AspNetCore.Identity;
using Mapster;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Patients.Commands;

public record CreatePatientRequest(
    Guid? UserId,
    string PatientCode,
    string FullName,
    string AvatarUrl,
    DateTime DateOfBirth,
    string EthnicGroup,
    string CitizenId,
    string HealthInsuranceNo,
    DateTime? CitizenIdIssueDate,
    string CitizenIdIssuePlace,
    string Email,
    string PhoneNumber,
    string Address,
    string Occupation,
    string MaritalStatus,
    string Workplace
) : ICommand<Response<CreatePatientResponse>>;

public record CreatePatientResponse(
    Guid Id,
    string PatientCode,
    string FullName,
    Guid HospitalId,
    Guid? UserId,
    DateTime DateOfBirth,
    string Email,
    string PhoneNumber,
    DateTime? CreatedAt,
    string? CreatedBy
);

public class CreatePatientRequestValidator : AbstractValidator<CreatePatientRequest>
{
    public CreatePatientRequestValidator()
    {
        RuleFor(x => x.PatientCode)
            .NotEmpty()
            .WithMessage("Mã bệnh nhân không được để trống")
            .MaximumLength(50)
            .WithMessage("Mã bệnh nhân không được vượt quá 50 ký tự");

        RuleFor(x => x.FullName)
            .NotEmpty()
            .WithMessage("Họ tên bệnh nhân không được để trống")
            .MaximumLength(255)
            .WithMessage("Họ tên bệnh nhân không được vượt quá 255 ký tự");

        RuleFor(x => x.AvatarUrl)
            .MaximumLength(500)
            .WithMessage("URL ảnh đại diện không được vượt quá 500 ký tự");

        RuleFor(x => x.DateOfBirth)
            .NotEmpty()
            .WithMessage("Ngày sinh không được để trống")
            .LessThan(DateTime.Now)
            .WithMessage("Ngày sinh phải nhỏ hơn ngày hiện tại");

        RuleFor(x => x.EthnicGroup)
            .MaximumLength(100)
            .WithMessage("Dân tộc không được vượt quá 100 ký tự");

        RuleFor(x => x.CitizenId)
            .MaximumLength(20)
            .WithMessage("Số CCCD không được vượt quá 20 ký tự");

        RuleFor(x => x.HealthInsuranceNo)
            .MaximumLength(20)
            .WithMessage("Số thẻ bảo hiểm không được vượt quá 20 ký tự");

        RuleFor(x => x.CitizenIdIssuePlace)
            .MaximumLength(255)
            .WithMessage("Nơi cấp CCCD không được vượt quá 255 ký tự");

        RuleFor(x => x.Email)
            .EmailAddress()
            .WithMessage("Email không hợp lệ")
            .MaximumLength(255)
            .WithMessage("Email không được vượt quá 255 ký tự")
            .When(x => !string.IsNullOrEmpty(x.Email));

        RuleFor(x => x.PhoneNumber)
            .MaximumLength(20)
            .WithMessage("Số điện thoại không được vượt quá 20 ký tự");

        RuleFor(x => x.Address)
            .MaximumLength(500)
            .WithMessage("Địa chỉ không được vượt quá 500 ký tự");

        RuleFor(x => x.Occupation)
            .MaximumLength(255)
            .WithMessage("Nghề nghiệp không được vượt quá 255 ký tự");

        RuleFor(x => x.MaritalStatus)
            .MaximumLength(50)
            .WithMessage("Tình trạng hôn nhân không được vượt quá 50 ký tự");

        RuleFor(x => x.Workplace)
            .MaximumLength(255)
            .WithMessage("Nơi làm việc không được vượt quá 255 ký tự");
    }
}


public class CreatePatientHandler(
    IApplicationDbContext context,
    ILogger<CreatePatientHandler> logger,
    IHttpContextAccessor httpContextAccessor,
    UserManager<User> userManager)
    : ICommandHandler<CreatePatientRequest, Response<CreatePatientResponse>>
{
    public async Task<Response<CreatePatientResponse>> Handle(CreatePatientRequest request, CancellationToken cancellationToken)
    {
        // 1. Lấy thông tin user hiện tại và HospitalId từ claims
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        var hospitalId = (httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal())
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");

        // 2. Validate Hospital exists
        var hospital = await context.Hospitals.AsNoTracking()
            .FirstOrDefaultAsync(h => h.Id == hospitalId, cancellationToken)
            ?? throw new HospitalNotFoundException(hospitalId);

        // 3. Validate User exists (if UserId is provided)
        if (request.UserId.HasValue)
        {
            var user = await userManager.FindByIdAsync(request.UserId.Value.ToString())
                ?? throw new BadRequestException($"Không tìm thấy người dùng có ID '{request.UserId.Value}'");

            // Check if user belongs to the same hospital
            if (user.HospitalId != hospitalId)
                throw new BadRequestException("Người dùng không thuộc cùng bệnh viện");

            var existingPatientWithUserId = await context.Patients
                .AsNoTracking()
                .FirstOrDefaultAsync(p => p.UserId == request.UserId.Value, cancellationToken);

            if (existingPatientWithUserId != null)
                throw new BadRequestException($"Người dùng ID '{request.UserId.Value}' đã được liên kết với bệnh nhân khác");
        }

        // 4. Check unique constraint - PatientCode in same Hospital
        var existingPatient = await context.Patients
            .AsNoTracking()
            .FirstOrDefaultAsync(p => p.PatientCode == request.PatientCode
                          && p.HospitalId == hospitalId, cancellationToken);

        if (existingPatient != null)
            throw new PatientCodeAlreadyExistsException(request.PatientCode, hospitalId);

        var patient = request.Adapt<Patient>();
        patient.Id = Guid.NewGuid();
        patient.HospitalId = hospitalId;
        patient.CreatedAt = DateTime.UtcNow;
        patient.CreatedBy = currentUser;

        // 6. Save to database
        context.Patients.Add(patient);
        patient.AddDomainEvent(new PatientCreateEvent(patient));
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
            throw new PatientSaveFailedException("tạo");

        // 7. Log success
        logger.LogInformation("Created Patient with Id {PatientId} and Code {PatientCode} by {User}",
            patient.Id, patient.PatientCode, currentUser);

        // 8. Return response
        var response = patient.Adapt<CreatePatientResponse>();
        return new Response<CreatePatientResponse>(response);
    }
}
