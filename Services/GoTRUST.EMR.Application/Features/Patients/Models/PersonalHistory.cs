namespace GoTRUST.EMR.Application.Features.Patients.Models
{
    public class DiUng
    {
        public string? LoaiDiUng { get; set; }
        public string? TenDiUng { get; set; }
        public string? CapDo { get; set; }
    }

    public class TienSuBenh
    {
        public string? TenBenh { get; set; }
        public string? NamMac { get; set; }
        public string? MaICD { get; set; }
        public string? DieuTri { get; set; }
    }

    public class BenhManTinh
    {
        public string? TenBenh { get; set; }
        public string? NamPhatHien { get; set; }
        public string? MaICD { get; set; }
        public string? DieuTri { get; set; }
    }

    public class PersonalHistory
    {
        public List<DiUng> DanhSachDiUng { get; set; } = [];
        public List<TienSuBenh> DanhSachTienSuBenh { get; set; } = [];
        public List<BenhManTinh> DanhSachBenhManTinh { get; set; } = [];
    }
}