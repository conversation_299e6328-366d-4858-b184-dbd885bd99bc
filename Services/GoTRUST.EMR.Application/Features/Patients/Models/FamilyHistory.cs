using System;
using System.Collections.Generic;

namespace GoTRUST.EMR.Application.Features.Patients.Models
{
    public class FamilyHistory
    {
        public string? MoiQuanHe { get; set; } // M<PERSON>i quan hệ (ví dụ: Cha, Mẹ, <PERSON>h, <PERSON><PERSON>, Em)
        public string? TenThanhVien { get; set; } // Tên thành viên gia đình
        public string? TenBenh { get; set; } // Tên bệnh
        public string? NamMac { get; set; } // Năm mắc bệnh
        public string? MaICD { get; set; } // Mã ICD (nếu có)
        public string? DieuTri { get; set; } 
    }
}