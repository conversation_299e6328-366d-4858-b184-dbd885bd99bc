﻿using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Patients.EventHandlers;
public class MedicalRecordCreatedEventHandler(ILogger<MedicalRecordCreatedEventHandler> logger)
    : INotificationHandler<MedicalRecordCreatedEvent>
{
    public Task Handle(MedicalRecordCreatedEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);
        return Task.CompletedTask;
    }
}