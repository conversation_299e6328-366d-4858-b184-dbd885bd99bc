﻿using BuildingBlocks.Abstractions;
using Mapster;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.Extensions.Configuration;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Authenticates.Commands;

public record ApproveLoginRequest(Guid SessionId, string Code) : ICommand<Response<ApproveLoginResponse>>;

public record ApproveLoginResponse(
    string Username,
    string Phone,
    string Email,
    string FullName,
    string Token,
    string RefreshToken,
    string RoleName,
    DateTime? TokenExpiresAt
);

public class ApproveLoginRequestValidator : AbstractValidator<ApproveLoginRequest>
{
    public ApproveLoginRequestValidator()
    {
        RuleFor(x => x.SessionId)
            .NotEmpty();
        RuleFor(x => x.Code)
            .NotEmpty()
            .MaximumLength(255);
    }
}

public class ApproveLoginHandler(
    IConfiguration _config,
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> userManager,
    SignInManager<User> signInManager,
    ITokenService tokenService,
    IApplicationDbContext dbContext,
    RoleManager<Role> roleManager)
    : ICommandHandler<ApproveLoginRequest, Response<ApproveLoginResponse>>
{
    public readonly UserManager<User> _userManager = userManager;
    public readonly SignInManager<User> _signInManager = signInManager;
    public readonly ITokenService _tokenService = tokenService;
    public readonly RoleManager<Role> _roleManager = roleManager;

    public async Task<Response<ApproveLoginResponse>> Handle(ApproveLoginRequest request, CancellationToken cancellationToken)
    {
        var twoFactorCode = await dbContext.TwoFactorCodes
            .Include(x => x.User)
            .FirstOrDefaultAsync(x => x.Id == request.SessionId && !x.Consumed, cancellationToken);

        if (twoFactorCode == null || twoFactorCode.Code != request.Code)
            throw new BadRequestException("Mã xác thực không đúng hoặc đã hết hạn");

        if (twoFactorCode.ExpiresAt < DateTime.UtcNow)
            throw new BadRequestException("Mã xác thực đã hết hạn");

        twoFactorCode.Consumed = true;
        twoFactorCode.ConsumedAt = DateTime.UtcNow;
        dbContext.TwoFactorCodes.Update(twoFactorCode);
        var user = twoFactorCode.User ?? throw new BadRequestException("Người dùng không tồn tại");
        user.AddDomainEvent(new ApproveLoginEvent(user));
        await dbContext.SaveChangesAsync(cancellationToken);

        // Fetch the hospital entity for the user
        var hospital = await dbContext.Hospitals
            .FirstOrDefaultAsync(h => h.Id == user.HospitalId, cancellationToken);

        int? loginSessionTimeout = null;
        if (hospital != null)
        {
            var sessionTimeoutConfig = await dbContext.HospitalConfigs
                .FirstOrDefaultAsync(x => x.HospitalId == hospital.Id && x.Key == HospitalConfigConstants.SessionTimeoutMinutes, cancellationToken);

            if (sessionTimeoutConfig != null && int.TryParse(sessionTimeoutConfig.Value, out var timeoutMinutes))
            {
                loginSessionTimeout = timeoutMinutes;
            }
        }

        // Use hospital's LoginSessionTimeout for token expiration if available
        string token = await _tokenService.CreateToken(user, loginSessionTimeout);
        string refreshToken = _tokenService.CreateRefreshToken();

        var refreshTokenObj = new RefreshToken
        {
            Token = refreshToken,
            JwtId = token,
            GeneratedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.AddMinutes(int.TryParse(_config["JwtRefreshTokenExpireInMinutes"], out var minutes) ? minutes : 43200),
            CreatedByIp = _httpContextAccessor.HttpContext!.GetRequestIpAddress(),
            UserId = user.Id
        };

        user.RefreshTokens.Add(refreshTokenObj);

        var userDto = user.Adapt<ApproveLoginResponse>();

        // Get user's role name
        var roles = await _userManager.GetRolesAsync(user);
        string? roleName = null;
        if (roles.Count > 0)
        {
            var role = await _roleManager.FindByNameAsync(roles[0]);
            roleName = role?.Name;
        }

        var updatedUserDto = userDto with
        {
            Token = token,
            RefreshToken = refreshToken,
            TokenExpiresAt = refreshTokenObj.ExpiresAt,
            RoleName = roleName!
        };

        await _userManager.UpdateAsync(user);

        return new Response<ApproveLoginResponse>(updatedUserDto);
    }
}