﻿using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;

namespace GoTRUST.EMR.Application.Features.Authenticates.Commands;

public record ResendTwoFactorCodeRequest(Guid SessionId) : ICommand<Response<ResendTwoFactorCodeResponse>>;

public record ResendTwoFactorCodeResponse(
    bool IsSuccess,
    Guid SessionId
);

public class ResendTwoFactorCodeRequestValidator : AbstractValidator<ResendTwoFactorCodeRequest>
{
    public ResendTwoFactorCodeRequestValidator()
    {
        RuleFor(x => x.SessionId)
            .NotEmpty();
    }
}

public class ResendTwoFactorCodeCommandHandler(
    IConfiguration _config,
    IWebHostEnvironment env,
    IApplicationDbContext dbContext)
    : ICommandHandler<ResendTwoFactorCodeRequest, Response<ResendTwoFactorCodeResponse>>
{
    public async Task<Response<ResendTwoFactorCodeResponse>> Handle(ResendTwoFactorCodeRequest request, CancellationToken cancellationToken)
    {
        var twoFactorCode = await dbContext.TwoFactorCodes
            .Include(x => x.User)
            .FirstOrDefaultAsync(x => x.Id == request.SessionId, cancellationToken) ?? throw new BadRequestException("Session không hợp lệ");
        if (twoFactorCode.Consumed || twoFactorCode.ExpiresAt < DateTime.UtcNow)
            throw new BadRequestException("Mã xác thực đã hết hạn hoặc đã được sử dụng");

        var user = twoFactorCode.User ?? throw new BadRequestException("Người dùng không tồn tại");
        var templatePath = Path.Combine(env.WebRootPath, "Templates", "Mails", "EMRApproveLogin.html");
        var template = await File.ReadAllTextAsync(templatePath, cancellationToken);

        template = template.Replace("{{AccountName}}", user.FullName ?? user.UserName);
        template = template.Replace("{{TwoFactorCode}}", twoFactorCode.Code);

        _ = MailHelper.SendAsync(
            int.TryParse(_config["EmailPort"], out int port) ? port : 587,
            _config["EmailHost"]!,
            _config["EmailPassword"]!,
            _config["EmailFromMail"]!,
            user.Email!,
            _config["EmailDisplayName"]!,
            _config["2FAEmailSubject"]!,
            template
        );

        return new Response<ResendTwoFactorCodeResponse>(
            new ResendTwoFactorCodeResponse(true, request.SessionId)
        );
    }
}