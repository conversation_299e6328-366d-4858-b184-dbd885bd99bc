﻿using BuildingBlocks.Abstractions;
using Mapster;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Authenticates.Commands;

public record RefreshTokenRequest(string RefreshToken) : ICommand<Response<RefreshTokenResponse>>;

public record RefreshTokenResponse(
    string Code,
    string Username,
    string Phone,
    string Email,
    string FullName,
    string DateOfBirth,
    int? Gender,
    string AvatarUrl,
    string Status,
    string UserType,
    DateTime CreatedAt,
    DateTime LastActive,
    string Token,
    string RefreshToken,
    DateTime? TokenExpiresAt
);

public class RefreshTokenRequestValidator : AbstractValidator<RefreshTokenRequest>
{
    public RefreshTokenRequestValidator()
    {
        RuleFor(x => x.RefreshToken)
            .NotEmpty();
    }
}

public class RefreshTokenHandler(
    IConfiguration _config,
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> userManager,
    ITokenService tokenService,
    IApplicationDbContext dbContext)
    : ICommandHandler<RefreshTokenRequest, Response<RefreshTokenResponse>>
{
    public readonly UserManager<User> _userManager = userManager;
    public readonly ITokenService _tokenService = tokenService;
    public readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<RefreshTokenResponse>> Handle(RefreshTokenRequest request, CancellationToken cancellationToken)
    {
        var refreshToken = await _dbContext.RefreshTokens
            .Include(rt => rt.User)
            .FirstOrDefaultAsync(rt => rt.Token == request.RefreshToken, cancellationToken);

        if (refreshToken == null)
            throw new NotFoundException("Refresh token không hợp lệ");

        if (refreshToken.ExpiresAt < DateTime.UtcNow)
            throw new BadRequestException("Refresh token đã hết hạn");

        var user = await _userManager.FindByIdAsync(refreshToken.UserId.ToString()) ?? throw new NotFoundException("User không tồn tại");
        user.AddDomainEvent(new RefreshTokenEvent(user));
        // Create new token and refresh token
        var newToken = await _tokenService.CreateToken(user);
        var newRefreshToken = _tokenService.CreateRefreshToken();
        var newRefreshTokenObj = new RefreshToken
        {
            Token = newRefreshToken,
            JwtId = newToken,
            GeneratedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.AddMinutes(int.TryParse(_config["JwtRefreshTokenExpireInMinutes"], out var minutes) ? minutes : 43200),
            CreatedByIp = _httpContextAccessor.HttpContext?.GetRequestIpAddress()!,
            UserId = refreshToken.UserId,
        };

        user.RefreshTokens.Remove(refreshToken);
        user.RefreshTokens.Add(newRefreshTokenObj);

        // Create history of refresh token
        var historyRefreshToken = new RefreshTokenHistory
        {
            JwtId = refreshToken.JwtId,
            Token = refreshToken.Token,
            RevokedAt = DateTime.UtcNow,
            RevokedByIp = _httpContextAccessor.HttpContext?.GetRequestIpAddress()!,
            ReplacedByToken = newRefreshToken,
            GeneratedAt = refreshToken.GeneratedAt,
            ExpiresAt = refreshToken.ExpiresAt,
            CreatedByIp = refreshToken.CreatedByIp,
            UserId = refreshToken.UserId,
        };

        user.RefreshTokenHistories.Add(historyRefreshToken);

        await _userManager.UpdateAsync(user);

        var userDto = user.Adapt<RefreshTokenResponse>() with
        {
            Token = newToken,
            RefreshToken = newRefreshToken,
            TokenExpiresAt = newRefreshTokenObj.ExpiresAt
        };

        return new Response<RefreshTokenResponse>(userDto);
    }
}