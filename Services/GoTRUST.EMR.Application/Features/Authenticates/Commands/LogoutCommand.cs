﻿using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.AspNetCore.Http;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Authenticates.Commands;

public record LogoutRequest(string AccessToken) : ICommand<Response<LogoutResponse>>;

public record LogoutResponse(
    bool IsSuccess
);

public class LogoutRequestValidator : AbstractValidator<LogoutRequest>
{
    public LogoutRequestValidator()
    {
    }
}

public class LogoutHandler(
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> userManager,
    SignInManager<User> signInManager,
    ITokenService tokenService,
    IApplicationDbContext dbContext)
    : ICommandHandler<LogoutRequest, Response<LogoutResponse>>
{
    public readonly UserManager<User> _userManager = userManager;
    public readonly SignInManager<User> _signInManager = signInManager;
    public readonly ITokenService _tokenService = tokenService;
    public readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<LogoutResponse>> Handle(LogoutRequest request, CancellationToken cancellationToken)
    {
        var refreshToken = await _dbContext.RefreshTokens
            .FirstOrDefaultAsync(x => x.JwtId == request.AccessToken, cancellationToken) ?? throw new BadRequestException("Token không hợp lệ hoặc đã đăng xuất.");

        _dbContext.RefreshTokens.Remove(refreshToken);

        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await _userManager.FindByIdAsync(userId.ToString());
        if (user != null)
        {
            user.AddDomainEvent(new LogoutEvent(user));
        }

        var historyRefreshToken = new RefreshTokenHistory
        {
            JwtId = refreshToken.JwtId,
            Token = refreshToken.Token,
            RevokedAt = DateTime.UtcNow,
            RevokedByIp = _httpContextAccessor.HttpContext.GetRequestIpAddress(),
            ReasonRevoked = "Logout",
            ReplacedByToken = null!,
            GeneratedAt = refreshToken.GeneratedAt,
            ExpiresAt = refreshToken.ExpiresAt,
            CreatedByIp = refreshToken.CreatedByIp,
            UserId = refreshToken.UserId,
            UpdatedBy = userId.ToString(),
        };

        _dbContext.RefreshTokenHistories.Add(historyRefreshToken);

        var result = await _dbContext.SaveChangesAsync(cancellationToken);
        if (result <= 0)
            throw new BadRequestException("Lỗi khi xóa dữ liệu");

        var response = new LogoutResponse(true);

        return new Response<LogoutResponse>(response);
    }
}