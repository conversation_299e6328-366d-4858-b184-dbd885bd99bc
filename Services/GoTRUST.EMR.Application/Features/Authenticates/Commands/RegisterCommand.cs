﻿using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Authenticates.Commands;

public record RegisterRequest(string Email, string FullName, string Phone, string Password, string Role
        ) : ICommand<Response<RegisterResponse>>;

public record RegisterResponse(
    bool IsSuccess
);

public class RegisterRequestValidator : AbstractValidator<RegisterRequest>
{
    public RegisterRequestValidator()
    {
        RuleFor(x => x.Email)
            .NotEmpty()
            .MaximumLength(255)
            .EmailAddress();
        RuleFor(x => x.FullName)
            .NotEmpty()
            .MaximumLength(255);
        RuleFor(x => x.Role)
            .NotEmpty()
            .MaximumLength(100);
    }
}

public class RegisterHandler(
    IApplicationDbContext _unitOfWork,
    IConfiguration _config,
    UserManager<User> userManager,
    IWebHostEnvironment env,
    RoleManager<Role> roleManager)
    : ICommandHandler<RegisterRequest, Response<RegisterResponse>>
{
    private readonly UserManager<User> _userManager = userManager;
    private readonly IWebHostEnvironment _env = env;
    private readonly RoleManager<Role> _roleManager = roleManager;
    public async Task<Response<RegisterResponse>> Handle(RegisterRequest req, CancellationToken cancellationToken)
    {
        // check if user is existed
        var user = await _userManager.FindByUserNameOrEmailOrPhoneAsync(req.Email);
        if (user != null)
            throw new BadRequestException("Email đã tồn tại");

        // create user
        user = new User
        {
            UserName = req.Email,
            Email = req.Email,
            PhoneNumber = req.Phone,
            FullName = req.FullName,
        };
        user.AddDomainEvent(new RegisterEvent(user));
        // generate password
        var generatedPassword = StringExtensions.GenerateRandomPassword();

        var result = await _userManager.CreateAsync(user, req.Password ?? generatedPassword);

        if (!result.Succeeded)
            throw new BadRequestException("Tạo tài khoản thất bại: " + result.Errors.FirstOrDefault()!.Description);

        // find role
        var role = await _roleManager.FindByNameAsync(req.Role);
        if (role == null)
            throw new NotFoundException($"Không tìm thấy quyền: {req.Role}");

        // add role for user
        result = await _userManager.AddToRoleAsync(user, req.Role);

        if (!result.Succeeded)
            throw new BadRequestException("Không thể thêm quyền cho tài khoản");

        // Send password link through email
        var expiresAt = DateTime.UtcNow.AddMinutes(int.TryParse(_config["UpdatePasswordExpireInMinutes"], out var minutes) ? minutes : 10080);

        var session = new ForgotPasswordSession
        {
            UserId = user.Id,
            ExpiresAt = expiresAt
        };

        _unitOfWork.ForgotPasswordSessions.Add(session);

        if (await _unitOfWork.SaveChangesAsync(cancellationToken) <= 0)
        {
            throw new BadRequestException("Lưu dữ liệu thất bại");
        }

        //generate reset link
        var resetLink = $"{_config["ClientUrl"]}/tao-mat-khau?session={session.Id}&user={user.UserName}";

        // get email template path from wwwroot path
        var templatePath = Path.Combine(_env.WebRootPath, "Templates", "Mails", "EMRForgotPassword.html");
        var template = await File.ReadAllTextAsync(templatePath, cancellationToken);

        // replace placeholders in template
        template = template.Replace("{{AccountName}}", user.FullName ?? user.UserName);
        template = template.Replace("{{CreatePassword}}", resetLink);

        //send email
        _ = MailHelper.SendAsync(
            int.TryParse(_config["EmailPort"], out int port) ? port : 587,
            _config["EmailHost"]!,
            _config["EmailPassword"]!,
            _config["EmailFromMail"]!,
            user.Email,
            _config["EmailDisplayName"]!,
            _config["UpdatePasswordEmailSubject"]!,
            template
            );

        return new Response<RegisterResponse>(new RegisterResponse(true));
    }
}