﻿using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;
using System.Security.Cryptography;
using GoTRUST.EMR.Domain.Constants;
using Mapster;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Authenticates.Commands;

public record LoginRequest(string Account, string Password) : ICommand<Response<LoginResponse>>;

public record LoginUserInfoResponse(
    string Username,
    string Phone,
    string Email,
    string FullName,
    string Token,
    string RefreshToken,
    string RoleName,
    DateTime? TokenExpiresAt
);

public record LoginResponse(
    bool IsSuccess,
    string? Email,
    Guid? SessionId,
    LoginUserInfoResponse? UserInfo,
    bool IsTwoFactorRequired
);

public class LoginRequestValidator : AbstractValidator<LoginRequest>
{
    public LoginRequestValidator()
    {
        RuleFor(x => x.Account)
            .NotEmpty()
            .MaximumLength(255);
        RuleFor(x => x.Password)
            .NotEmpty()
            .MaximumLength(255);
    }
}

public class LoginHandler(
    IConfiguration _config,
    UserManager<User> _userManager,
    SignInManager<User> _signInManager,
    IWebHostEnvironment env,
    IApplicationDbContext dbContext,
    ITokenService tokenService,
    IHttpContextAccessor httpContextAccessor,
    RoleManager<Role> roleManager)
    : ICommandHandler<LoginRequest, Response<LoginResponse>>
{
    public async Task<Response<LoginResponse>> Handle(LoginRequest request, CancellationToken cancellationToken)
    {
        var user = await _userManager.FindByUserNameOrEmailOrPhoneAsync(request.Account) ?? throw new BadRequestException("Tên đăng nhập hoặc mật khẩu không đúng");

        var result = await _signInManager.CheckPasswordSignInAsync(user, request.Password, false);

        var hospital = await dbContext.Hospitals
            .FirstOrDefaultAsync(h => h.Id == user.HospitalId, cancellationToken) ?? throw new HospitalNotFoundException(user.HospitalId ?? Guid.Empty);

        var configKeys = new[]
        {
            HospitalConfigConstants.LoginFailureLockThreshold,
            HospitalConfigConstants.ForcePasswordChangeDays,
            HospitalConfigConstants.RequireTwoFactorAuth,
            HospitalConfigConstants.SessionTimeoutMinutes
        };

        var configs = await dbContext.HospitalConfigs
            .Where(x => x.HospitalId == hospital.Id && configKeys.Contains(x.Key))
            .ToListAsync(cancellationToken);

        var configDict = configs.ToDictionary(x => x.Key, x => x.Value);

        int? lockThreshold = null;
        if (configDict.TryGetValue(HospitalConfigConstants.LoginFailureLockThreshold, out var lockThresholdValue) && int.TryParse(lockThresholdValue, out var threshold))
        {
            lockThreshold = threshold;
        }

        if (!result.Succeeded)
        {
            if (lockThreshold.HasValue)
            {
                // Tăng số lần đăng nhập sai
                await _userManager.AccessFailedAsync(user);

                var failedCount = await _userManager.GetAccessFailedCountAsync(user);

                if (failedCount >= lockThreshold.Value)
                {
                    await _userManager.SetLockoutEndDateAsync(user, DateTimeOffset.UtcNow.AddMinutes(15));
                    throw new BadRequestException("Tài khoản đã bị khóa do đăng nhập sai quá số lần cho phép.");
                }
            }
            throw new BadRequestException("Tên đăng nhập hoặc mật khẩu không đúng");
        }

        if (lockThreshold.HasValue)
        {
            await _userManager.ResetAccessFailedCountAsync(user);
        }

        // Password expiration check
        if (configDict.TryGetValue(HospitalConfigConstants.ForcePasswordChangeDays, out var forceChangeDaysStr) && int.TryParse(forceChangeDaysStr, out var forceChangeDays) && forceChangeDays > 0)
        {
            var lastChange = user.LastPasswordChangeAt;
            if (!lastChange.HasValue || lastChange.Value.AddDays(forceChangeDays) < DateTime.UtcNow)
            {
                throw new BadRequestException("Mật khẩu đã hết hạn, vui lòng đổi mật khẩu mới để tiếp tục đăng nhập.");
            }
        }

        var require2FA = true;
        if (configDict.TryGetValue(HospitalConfigConstants.RequireTwoFactorAuth, out var require2FAValueStr) && bool.TryParse(require2FAValueStr, out var require2FAValue))
        {
            require2FA = require2FAValue;
        }

        if (require2FA)
        {
            var code = Generate2FACode();
            var sessionId = Guid.NewGuid();

            var oldCodes = await dbContext.TwoFactorCodes
                .Where(x => x.UserId == user.Id && !x.Consumed)
                .ToListAsync(cancellationToken);
            foreach (var old in oldCodes)
            {
                old.Consumed = true;
                old.ConsumedAt = DateTime.UtcNow;
            }
            dbContext.TwoFactorCodes.UpdateRange(oldCodes);
            var twoFactorExpireTime = int.TryParse(_config["TwoFactorExpireTime"], out var expireSeconds) ? expireSeconds : 600;
            var twoFactorCode = new TwoFactorCode
            {
                Id = sessionId,
                UserId = user.Id,
                Code = code,
                Consumed = false,
                ExpiresAt = DateTime.UtcNow.AddSeconds(twoFactorExpireTime)
            };
            dbContext.TwoFactorCodes.Add(twoFactorCode);
            user.AddDomainEvent(new LoginEvent(user));
            await dbContext.SaveChangesAsync(cancellationToken);

            // Send code to user's email
            var templatePath = Path.Combine(env.WebRootPath, "Templates", "Mails", "EMRApproveLogin.html");
            var template = await File.ReadAllTextAsync(templatePath, cancellationToken);

            // replace placeholders in template
            template = template.Replace("{{ExpiringTime}}", Math.Floor((double)twoFactorExpireTime / 60) + " phút");
            template = template.Replace("{{AccountName}}", user.FullName ?? user.UserName);
            template = template.Replace("{{TwoFactorCode}}", code);

            //send email
            _ = MailHelper.SendAsync(
                int.TryParse(_config["EmailPort"], out int port) ? port : 587,
                _config["EmailHost"]!,
                _config["EmailPassword"]!,
                _config["EmailFromMail"]!,
                user.Email!,
                _config["EmailDisplayName"]!,
                _config["2FAEmailSubject"]!,
                template
            );

            return new Response<LoginResponse>(new LoginResponse(true, user.Email!, sessionId, null, true)); // <-- Set IsTwoFactorRequired = true
        }
        // Direct login, return full user info and tokens (like ApproveLoginHandler)
        int? loginSessionTimeout = null;
        if (configDict.TryGetValue(HospitalConfigConstants.SessionTimeoutMinutes, out var timeoutMinutesStr) && int.TryParse(timeoutMinutesStr, out var timeoutMinutes))
        {
            loginSessionTimeout = timeoutMinutes;
        }

        string token = await tokenService.CreateToken(user, loginSessionTimeout);
        string refreshToken = tokenService.CreateRefreshToken();

        var refreshTokenObj = new RefreshToken
        {
            Token = refreshToken,
            JwtId = token,
            GeneratedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.AddMinutes(int.TryParse(_config["JwtRefreshTokenExpireInMinutes"], out var minutes) ? minutes : 43200),
            CreatedByIp = httpContextAccessor.HttpContext!.GetRequestIpAddress(),
            UserId = user.Id
        };

        user.RefreshTokens.Add(refreshTokenObj);

        var userInfo = user.Adapt<LoginUserInfoResponse>();

        // Get user's role name
        var roles = await _userManager.GetRolesAsync(user);
        string? roleName = null;
        if (roles.Count > 0)
        {
            var role = await roleManager.FindByNameAsync(roles[0]);
            roleName = role?.Name;
        }

        var updatedUserDto = userInfo with
        {
            Token = token,
            RefreshToken = refreshToken,
            TokenExpiresAt = refreshTokenObj.ExpiresAt,
            RoleName = roleName!
        };
        await _userManager.UpdateAsync(user);

        return new Response<LoginResponse>(new LoginResponse(true, user.Email!, null, updatedUserDto, false));
    }
    private static string Generate2FACode()
    {
        var bytes = new byte[4];
        RandomNumberGenerator.Fill(bytes);
        int code = (int)(BitConverter.ToUInt32(bytes, 0) % 1000000);
        return code.ToString("D6");
    }
}