﻿using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Authenticates.Commands;

public record SetPasswordRequest(string OldPassword, string NewPassword) : ICommand<Response<SetPasswordResponse>>;

public record SetPasswordResponse(
    bool IsSuccess
);

public class SetPasswordRequestValidator : AbstractValidator<SetPasswordRequest>
{
    public SetPasswordRequestValidator()
    {
    }
}

public class SetPasswordHandler(
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> userManager,
    IApplicationDbContext _dbContext)
    : ICommandHandler<SetPasswordRequest, Response<SetPasswordResponse>>
{
    private readonly UserManager<User> _userManager = userManager;
    private readonly IHttpContextAccessor _contextAccessor = _httpContextAccessor;
    public async Task<Response<SetPasswordResponse>> Handle(SetPasswordRequest request, CancellationToken cancellationToken)
    {
        var email = _contextAccessor.HttpContext!.User.RetrieveEmailFromPrincipal() ?? string.Empty;

        var user = await _userManager.FindByUserNameOrEmailOrPhoneAsync(email) ?? throw new BadRequestException("Người dùng không tồn tại hoặc đã bị xóa.");
        user.AddDomainEvent(new SetPasswordEvent(user));
        var hospital = await _dbContext.Hospitals
            .FirstOrDefaultAsync(h => h.Id == user.HospitalId, cancellationToken)
            ?? throw new HospitalNotFoundException(user.HospitalId ?? Guid.Empty);

        var configKeys = new[]
        {
            HospitalConfigConstants.PasswordMinLength,
            HospitalConfigConstants.PasswordRequireDigit,
            HospitalConfigConstants.PasswordRequireLowercase,
            HospitalConfigConstants.PasswordRequireUppercase,
            HospitalConfigConstants.PasswordRequireSpecialChar,
            HospitalConfigConstants.ForcePasswordChangeDays
        };

        var configs = await _dbContext.HospitalConfigs
            .Where(x => x.HospitalId == hospital.Id && configKeys.Contains(x.Key))
            .ToListAsync(cancellationToken);

        var password = request.NewPassword;
        int minLength = int.TryParse(configs.FirstOrDefault(x => x.Key == HospitalConfigConstants.PasswordMinLength)?.Value, out var l) ? l : 6;
        bool requireDigit = bool.TryParse(configs.FirstOrDefault(x => x.Key == HospitalConfigConstants.PasswordRequireDigit)?.Value, out var d) && d;
        bool requireLower = bool.TryParse(configs.FirstOrDefault(x => x.Key == HospitalConfigConstants.PasswordRequireLowercase)?.Value, out var lo) && lo;
        bool requireUpper = bool.TryParse(configs.FirstOrDefault(x => x.Key == HospitalConfigConstants.PasswordRequireUppercase)?.Value, out var up) && up;
        bool requireSpecial = bool.TryParse(configs.FirstOrDefault(x => x.Key == HospitalConfigConstants.PasswordRequireSpecialChar)?.Value, out var sp) && sp;
        int forceChangeDays = int.TryParse(configs.FirstOrDefault(x => x.Key == HospitalConfigConstants.ForcePasswordChangeDays)?.Value, out var fcd) ? fcd : 0;

        if (password.Length < minLength)
            throw new BadRequestException($"Mật khẩu phải có ít nhất {minLength} ký tự");

        if (requireDigit && !password.Any(char.IsDigit))
            throw new BadRequestException("Mật khẩu phải chứa ít nhất 1 chữ số");

        if (requireLower && !password.Any(char.IsLower))
            throw new BadRequestException("Mật khẩu phải chứa ít nhất 1 ký tự thường");

        if (requireUpper && !password.Any(char.IsUpper))
            throw new BadRequestException("Mật khẩu phải chứa ít nhất 1 ký tự in hoa");

        if (requireSpecial && !password.Any(ch => !char.IsLetterOrDigit(ch)))
            throw new BadRequestException("Mật khẩu phải chứa ít nhất 1 ký tự đặc biệt");

        var passwordHasher = new PasswordHasher<User>();
        var verifyResult = passwordHasher.VerifyHashedPassword(user, user.PasswordHash!, request.NewPassword);
        if (verifyResult == PasswordVerificationResult.Success)
            throw new BadRequestException("Mật khẩu mới không được trùng với mật khẩu cũ.");

        var result = await _userManager.ChangePasswordAsync(user, request.OldPassword, request.NewPassword);
        if (!result.Succeeded)
            throw new BadRequestException("Đổi mật khẩu thất bại. Error: " + result.Errors.FirstOrDefault()?.Description);

        user.LastPasswordChangeAt = DateTime.UtcNow;
        var updateResult = await _userManager.UpdateAsync(user);
        if (!updateResult.Succeeded)
            throw new BadRequestException("Không thể cập nhật thông tin người dùng. Error: " + updateResult.Errors.FirstOrDefault()?.Description);

        var response = new SetPasswordResponse(true);

        return new Response<SetPasswordResponse>(response);
    }
}