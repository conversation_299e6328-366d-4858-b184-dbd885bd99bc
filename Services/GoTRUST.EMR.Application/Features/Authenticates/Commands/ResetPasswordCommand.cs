﻿using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Authenticates.Commands;

public record ResetPasswordRequest(string Account, string Password, Guid Session) : ICommand<Response<ResetPasswordResponse>>;

public record ResetPasswordResponse(
    bool IsSuccess
);

public class ResetPasswordRequestValidator : AbstractValidator<ResetPasswordRequest>
{
    public ResetPasswordRequestValidator()
    {
        RuleFor(x => x.Account).NotEmpty();
        RuleFor(x => x.Password).NotEmpty();
        RuleFor(x => x.Session).NotEmpty();
    }
}

public class ResetPasswordHandler(
    UserManager<User> _userManager,
    IApplicationDbContext _dbContext)
    : ICommandHandler<ResetPasswordRequest, Response<ResetPasswordResponse>>
{
    public async Task<Response<ResetPasswordResponse>> Handle(ResetPasswordRequest request, CancellationToken cancellationToken)
    {
        var foundUser = await _userManager.FindByUserNameOrEmailOrPhoneAsync(request.Account) ?? throw new BadRequestException("Tài khoản không tồn tại");
        // Get user's hospital
        var hospital = await _dbContext.Hospitals
            .FirstOrDefaultAsync(h => h.Id == foundUser.HospitalId, cancellationToken)
            ?? throw new HospitalNotFoundException(foundUser.HospitalId ?? Guid.Empty);

        var configKeys = new[]
        {
            HospitalConfigConstants.PasswordMinLength,
            HospitalConfigConstants.PasswordRequireDigit,
            HospitalConfigConstants.PasswordRequireLowercase,
            HospitalConfigConstants.PasswordRequireUppercase,
            HospitalConfigConstants.PasswordRequireSpecialChar,
            HospitalConfigConstants.ForcePasswordChangeDays
        };

        var configs = await _dbContext.HospitalConfigs
            .Where(x => x.HospitalId == hospital.Id && configKeys.Contains(x.Key))
            .ToListAsync(cancellationToken);

        var configDict = configs.ToDictionary(x => x.Key, x => x.Value);

        // Validate password
        var password = request.Password;
        int minLength = int.TryParse(configDict.GetValueOrDefault(HospitalConfigConstants.PasswordMinLength), out var l) ? l : 6;
        bool requireDigit = bool.TryParse(configDict.GetValueOrDefault(HospitalConfigConstants.PasswordRequireDigit), out var d) && d;
        bool requireLower = bool.TryParse(configDict.GetValueOrDefault(HospitalConfigConstants.PasswordRequireLowercase), out var lo) && lo;
        bool requireUpper = bool.TryParse(configDict.GetValueOrDefault(HospitalConfigConstants.PasswordRequireUppercase), out var up) && up;
        bool requireSpecial = bool.TryParse(configDict.GetValueOrDefault(HospitalConfigConstants.PasswordRequireSpecialChar), out var sp) && sp;

        if (password.Length < minLength)
            throw new BadRequestException($"Mật khẩu phải có ít nhất {minLength} ký tự");

        if (requireDigit && !password.Any(char.IsDigit))
            throw new BadRequestException("Mật khẩu phải chứa ít nhất 1 chữ số");

        if (requireLower && !password.Any(char.IsLower))
            throw new BadRequestException("Mật khẩu phải chứa ít nhất 1 ký tự thường");

        if (requireUpper && !password.Any(char.IsUpper))
            throw new BadRequestException("Mật khẩu phải chứa ít nhất 1 ký tự in hoa");

        if (requireSpecial && !password.Any(ch => !char.IsLetterOrDigit(ch)))
            throw new BadRequestException("Mật khẩu phải chứa ít nhất 1 ký tự đặc biệt");

        var session = await _dbContext.ForgotPasswordSessions
            .Include(x => x.User)
            .FirstOrDefaultAsync(x => x.Id == request.Session && x.UserId == foundUser.Id, cancellationToken) ?? throw new BadRequestException("Session không tồn tại");
        if (session.ExpiresAt < DateTime.UtcNow)
        {
            throw new BadRequestException("Session đã hết hạn");
        }

        if (session.VerifiedAt.HasValue)
        {
            throw new BadRequestException("Session đã được sử dụng");
        }

        var user = session.User;
        user.AddDomainEvent(new ResetPasswordEvent(user));
        var lastChange = user.LastPasswordChangeAt;
        int days = 0;
        if (user.HospitalId.HasValue)
        {
            if (configDict.TryGetValue(HospitalConfigConstants.ForcePasswordChangeDays, out var daysValue) && int.TryParse(daysValue, out var configDays))
                days = configDays;
        }
        if (days > 0 && (!lastChange.HasValue || lastChange.Value.AddDays(days) < DateTime.UtcNow))
        {
            var passwordHasher = new PasswordHasher<User>();
            var verifyResult = passwordHasher.VerifyHashedPassword(user, user.PasswordHash!, request.Password);
            if (verifyResult == PasswordVerificationResult.Success)
                throw new BadRequestException("Mật khẩu mới không được trùng với mật khẩu cũ.");
        }

        session.VerifiedAt = DateTime.UtcNow;
        _dbContext.ForgotPasswordSessions.Update(session);

        await _dbContext.SaveChangesAsync(cancellationToken);

        var token = await _userManager.GeneratePasswordResetTokenAsync(session.User);
        var result = await _userManager.ResetPasswordAsync(session.User, token, request.Password);

        if (!result.Succeeded)
        {
            throw new BadRequestException("Cập nhật mật khẩu thất bại");
        }

        // Update LastPasswordChangeAt
        session.User.LastPasswordChangeAt = DateTime.UtcNow;
        var updateResult = await _userManager.UpdateAsync(session.User);
        if (!updateResult.Succeeded)
            throw new BadRequestException("Không thể cập nhật thông tin người dùng. Error: " + updateResult.Errors.FirstOrDefault()?.Description);

        return new Response<ResetPasswordResponse>(new ResetPasswordResponse(true));
    }
}