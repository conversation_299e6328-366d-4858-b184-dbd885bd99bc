﻿using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Identity;
using GoTRUST.EMR.Application.Helpers;

namespace GoTRUST.EMR.Application.Features.Authenticates.Queries;

public record GetUserRoleFunctionsQuery : IQuery<Response<List<GetUserRoleFunctionRes>>>;

public record GetUserRoleFunctionRes(
    string Name,
    string? Path,
    string? IconPath,
    List<string>? Permissions
);

public class GetUserRoleFunctionsHandler(
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager,
    IApplicationDbContext _dbContext)
    : IQueryHandler<GetUserRoleFunctionsQuery, Response<List<GetUserRoleFunctionRes>>>
{

    public async Task<Response<List<GetUserRoleFunctionRes>>> Handle(GetUserRoleFunctionsQuery request, CancellationToken cancellationToken)
    {
        var email = _httpContextAccessor.HttpContext?.User.RetrieveEmailFromPrincipal() ?? string.Empty;
        var user = await _userManager.FindByUserNameOrEmailOrPhoneAsync(email)
            ?? throw new UserNotFoundException();

        var userRoles = await _userManager.GetRolesAsync(user);

        var functions = new List<GetUserRoleFunctionRes>();

        var firstRoleName = userRoles.FirstOrDefault();

        if (firstRoleName != null)
        {
            var role = await _roleManager.FindByNameAsync(firstRoleName);
            if (role != null)
            {
                var groupedFunctions = await _dbContext.RoleFunctions
                    .AsNoTracking()
                    .Include(rf => rf.Function)
                    .Where(rf => rf.RoleId == role.Id && rf.Function != null)
                    .GroupBy(rf => new { rf.Function.Name, rf.Function.Path, rf.Function.IconPath, rf.Function.Order })
                    .OrderBy(g => g.Key.Order)
                    .Select(g => new GetUserRoleFunctionRes(
                        g.Key.Name,
                        g.Key.Path,
                        g.Key.IconPath,
                        g.Where(rf => rf.PermissionType != null)
                         .Select(rf => rf.PermissionType!)
                         .Distinct()
                         .ToList()
                    ))
                    .ToListAsync(cancellationToken);

                functions = groupedFunctions;
            }
        }

        return new Response<List<GetUserRoleFunctionRes>>(functions);
    }
}