﻿using Mapster;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.Interface;
using Microsoft.AspNetCore.Identity;
using GoTRUST.EMR.Application.Helpers;

namespace GoTRUST.EMR.Application.Features.Signatures.Queries;

public record GetUserSignaturesQuery() : IQuery<Response<List<GetUserSignaturesResponse>>>;

public record GetUserSignaturesResponse(
    int Id,
    int UserId,
    string? CreatedBy,
    DateTime? CreatedDate,
    string? CoreParser,
    string? FullName,
    int Width,
    int Height,
    string? HtmlTemplate,
    bool Transparency,
    string? Thumbnail,
    string? TemplateName,
    int? Type
);

public class GetUserSignaturesQueryHandler(
    ISignService signService,
    IHttpContextAccessor httpContextAccessor,
    UserManager<User> userManager
) : IQueryHandler<GetUserSignaturesQuery, Response<List<GetUserSignaturesResponse>>>
{
    public async Task<Response<List<GetUserSignaturesResponse>>> Handle(GetUserSignaturesQuery request, CancellationToken cancellationToken)
    {
        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await userManager.FindByIdAsync(userId.ToString() ?? string.Empty) ?? throw new UserNotFoundException();

        var signUsername = user.SignUsername;
        if (string.IsNullOrEmpty(signUsername))
            return new Response<List<GetUserSignaturesResponse>>([]);

        var templates = await signService.GetSignatureTemplatesByUsernameAsync(signUsername);

        var result = templates.Data?.Adapt<List<GetUserSignaturesResponse>>() ?? [];

        return new Response<List<GetUserSignaturesResponse>>(result);
    }
}