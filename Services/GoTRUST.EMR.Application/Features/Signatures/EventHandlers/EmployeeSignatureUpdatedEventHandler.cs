using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Events;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;
using System.Text;

namespace GoTRUST.EMR.Application.Features.Signatures.EventHandlers;

public class EmployeeSignatureUpdatedEventHandler(
    ILogger<EmployeeSignatureUpdatedEventHandler> logger,
    IConfiguration config,
    IWebHostEnvironment env)
    : INotificationHandler<EmployeeSignatureUpdatedEvent>
{
  public async Task Handle(EmployeeSignatureUpdatedEvent notification, CancellationToken cancellationToken)
  {
    logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

    try
    {
      // Check if user has email
      if (string.IsNullOrEmpty(notification.User.Email))
      {
        logger.LogWarning("User {UserId} does not have email address, skipping email notification", notification.User.Id);
        return;
      }

      // Prepare serial/pin information for email
      var serialPinInfo = new StringBuilder();
      if (notification.SignSerials != null && notification.SignSerials.Count > 0)
      {
        serialPinInfo.AppendLine("<h3>Thông tin Serial/PIN chữ ký số:</h3>");
        serialPinInfo.AppendLine("<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>");
        serialPinInfo.AppendLine("<tr style='background-color: #f2f2f2;'>");
        serialPinInfo.AppendLine("<th>Serial</th>");
        serialPinInfo.AppendLine("<th>PIN</th>");
        serialPinInfo.AppendLine("</tr>");

        foreach (var signInfo in notification.SignSerials)
        {
          serialPinInfo.AppendLine("<tr>");
          serialPinInfo.AppendLine($"<td>{signInfo.SignSerial ?? "N/A"}</td>");
          serialPinInfo.AppendLine($"<td>{signInfo.SignPinNumber ?? "N/A"}</td>");
          serialPinInfo.AppendLine("</tr>");
        }
        serialPinInfo.AppendLine("</table>");
      }

      // Get email template path
      var templatePath = Path.Combine(env.WebRootPath, "Templates", "Mails", "EmployeeSignatureUpdated.html");

      // Create template if it doesn't exist
      if (!File.Exists(templatePath))
      {
        await CreateEmailTemplate(templatePath);
      }

      var template = await File.ReadAllTextAsync(templatePath, cancellationToken);

      // Replace placeholders in template
      template = template.Replace("{{AccountName}}", notification.User.FullName ?? notification.User.UserName);
      template = template.Replace("{{SignUsername}}", notification.User.SignUsername ?? "N/A");
      template = template.Replace("{{SerialPinInfo}}", serialPinInfo.ToString());

      // Send email
      _ = MailHelper.SendAsync(
          int.TryParse(config["EmailPort"], out int port) ? port : 587,
          config["EmailHost"]!,
          config["EmailPassword"]!,
          config["EmailFromMail"]!,
          notification.User.Email,
          config["EmailDisplayName"]!,
          "Cập nhật thông tin chữ ký số",
          template
      );

      logger.LogInformation("Email notification sent to {Email} for signature update", notification.User.Email);
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "Error sending signature update email to {Email}", notification.User.Email);
    }
  }

  private async Task CreateEmailTemplate(string templatePath)
  {
    var directory = Path.GetDirectoryName(templatePath);
    if (!Directory.Exists(directory))
    {
      Directory.CreateDirectory(directory!);
    }

    var templateContent = @"<table width=""100%"" cellpadding=""0"" cellspacing=""0"" border=""0"" bgcolor=""#f3f4f6""
  style=""font-family:'Inter', Arial, sans-serif; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; padding:32px 0;"">
  <tr>
    <td align=""center"">
      <table width=""600"" cellpadding=""0"" cellspacing=""0"" border=""0"" bgcolor=""#ffffff""
        style=""border:1px solid #e5e7eb; border-radius:12px; box-shadow:0 1px 2px rgba(0,0,0,0.05); overflow:hidden;"">
        <tr>
          <td align=""center"" style=""padding:24px; border-bottom:1px solid #e5e7eb;"">
            <a href=""#"" target=""_blank"">
              <img src=""https://s3.cloud.cmctelecom.vn/gotrust-mobile-assess/Logo.png"" alt=""GoTRUST Logo""
                style=""height:40px; display:block;"">
            </a>
          </td>
        </tr>
        <tr>
          <td style=""padding:40px 32px;"">
            <h1 style=""font-size:28px; font-weight:700; color:#111827; margin-bottom:16px;"">Cập nhật thông tin chữ ký số
            </h1>
            <p style=""font-size:16px; color:#4b5563; margin-bottom:24px;"">
              Xin chào <strong>{{AccountName}}</strong>,<br>
              Thông tin chữ ký số của bạn đã được cập nhật trong hệ thống quản lý hồ sơ bệnh án EMR.
            </p>
            <p style=""font-size:16px; color:#4b5563; margin-bottom:24px;"">
              <strong>Tên đăng nhập chữ ký:</strong> {{SignUsername}}
            </p>
            <div style=""margin:24px 0;"">
              {{SerialPinInfo}}
            </div>
            <p style=""font-size:16px; color:#4b5563; margin-bottom:24px;"">
              Xin lưu ý rằng thông tin chữ ký số là thông tin quan trọng và không nên chia sẻ với người khác. Hãy giữ thông tin này riêng tư để đảm bảo tính an toàn cho tài khoản.
            </p>
            <p style=""font-size:14px; color:#6b7280; border-top:1px solid #e5e7eb; padding-top:24px; margin-top:32px;"">
              Nếu bạn không thực hiện thay đổi này, vui lòng liên hệ với quản trị viên hệ thống ngay lập tức.
            </p>
          </td>
        </tr>
        <tr>
          <td align=""center"" style=""background-color:#f9fafb; border-top:1px solid #e5e7eb; padding:24px 32px;"">
            <p style=""font-size:12px; color:#6b7280;"">Bạn nhận được email này như một phần của quy trình cập nhật thông tin chữ ký số tại GoTRUST.</p>
            <p style=""font-size:12px; color:#6b7280; margin-top:8px;"">&copy; 2025 GoTRUST Inc. All Rights Reserved.<br>5B Phổ Quang, Phường 2, Quận Tân Bình, TP. Hồ Chí Minh, Việt Nam</p>
            <p style=""margin-top:16px;"">
              <a href=""mailto:<EMAIL>"" style=""color:#9ca3af; text-decoration:none;"">Email: <EMAIL></a> |
              <a href=""tel:+19003426"" style=""color:#9ca3af; text-decoration:none;"">SĐT: 19003426</a>
            </p>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>";

    await File.WriteAllTextAsync(templatePath, templateContent);
  }
}
