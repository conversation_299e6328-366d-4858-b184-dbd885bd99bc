using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Microsoft.AspNetCore.Identity;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Signatures.Commands;

public class UpdateEmployeeSignatureRequest : ICommand<Response<UpdateEmployeeSignatureResponse>>
{
    public Guid EmployeeId { get; init; }
    public string SignUsername { get; init; } = string.Empty;
    public List<SignInfo>? SignSerials { get; init; }
}

public record UpdateEmployeeSignatureResponse(
    bool IsSuccess,
    Guid EmployeeId,
    Guid UserId
);

public class UpdateEmployeeSignatureRequestValidator : AbstractValidator<UpdateEmployeeSignatureRequest>
{
    public UpdateEmployeeSignatureRequestValidator()
    {
        RuleFor(x => x.EmployeeId).NotEmpty();
        RuleFor(x => x.SignUsername).NotEmpty().MaximumLength(255);
    }
}

public class UpdateEmployeeSignatureHandler(
    IApplicationDbContext _dbContext,
    UserManager<User> _userManager
) : ICommandHandler<UpdateEmployeeSignatureRequest, Response<UpdateEmployeeSignatureResponse>>
{
    public async Task<Response<UpdateEmployeeSignatureResponse>> Handle(UpdateEmployeeSignatureRequest req, CancellationToken cancellationToken)
    {
        var employee = await _dbContext.Employees
            .FirstOrDefaultAsync(e => e.Id == req.EmployeeId, cancellationToken)
            ?? throw new EmployeeNotFoundException(req.EmployeeId);

        var user = await _userManager.FindByIdAsync(employee.UserId.ToString()!)
            ?? throw new NotFoundException($"Không tìm thấy User với Id: {employee.UserId}");

        user.SignUsername = req.SignUsername;
        if (req.SignSerials != null && req.SignSerials.Count > 0)
        {
            user.SignSerials = req.SignSerials;
            // Add domain event to send email notification
            user.AddDomainEvent(new EmployeeSignatureUpdatedEvent(user, req.SignSerials));
        }

        var updateUserResult = await _userManager.UpdateAsync(user);
        if (!updateUserResult.Succeeded)
            throw new BadRequestException("Cập nhật SignUsername/SignSerials thất bại: " + updateUserResult.Errors.FirstOrDefault()!.Description);

        // Save changes to trigger domain events
        await _dbContext.SaveChangesAsync(cancellationToken);

        return new Response<UpdateEmployeeSignatureResponse>(
            new UpdateEmployeeSignatureResponse(
                true,
                employee.Id,
                user.Id
            )
        );
    }
}