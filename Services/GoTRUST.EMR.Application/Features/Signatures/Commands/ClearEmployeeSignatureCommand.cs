using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.Signatures.Commands;

public class ClearEmployeeSignatureCommand : ICommand<Response<ClearEmployeeSignatureResponse>>
{
    public Guid EmployeeId { get; init; }
}

public record ClearEmployeeSignatureResponse(
    bool IsSuccess,
    Guid EmployeeId,
    Guid UserId
);

public class ClearEmployeeSignatureCommandValidator : AbstractValidator<ClearEmployeeSignatureCommand>
{
    public ClearEmployeeSignatureCommandValidator()
    {
        RuleFor(x => x.EmployeeId).NotEmpty();
    }
}

public class ClearEmployeeSignatureCommandHandler(
    IApplicationDbContext _dbContext,
    UserManager<User> _userManager
) : ICommandHandler<ClearEmployeeSignatureCommand, Response<ClearEmployeeSignatureResponse>>
{
    public async Task<Response<ClearEmployeeSignatureResponse>> Handle(ClearEmployeeSignatureCommand req, CancellationToken cancellationToken)
    {
        var employee = await _dbContext.Employees
            .FirstOrDefaultAsync(e => e.Id == req.EmployeeId, cancellationToken)
            ?? throw new EmployeeNotFoundException(req.EmployeeId);

        var user = await _userManager.FindByIdAsync(employee.UserId.ToString()!)
            ?? throw new NotFoundException($"Không tìm thấy User với Id: {employee.UserId}");

        user.SignUsername = string.Empty;
        user.SignSerials = [];

        var updateUserResult = await _userManager.UpdateAsync(user);
        if (!updateUserResult.Succeeded)
            throw new BadRequestException("Xóa chữ ký số thất bại: " + updateUserResult.Errors.FirstOrDefault()!.Description);

        return new Response<ClearEmployeeSignatureResponse>(
            new ClearEmployeeSignatureResponse(
                true,
                employee.Id,
                user.Id
            )
        );
    }
}