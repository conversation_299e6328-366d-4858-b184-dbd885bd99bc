﻿using BuildingBlocks.Abstractions;
using Mapster;

namespace GoTRUST.EMR.Application.Features.TinhTrangHonNhans.Queries;

public record GetTinhTrangHonNhansRequest() : IQuery<Response<List<GetTinhTrangHonNhansResponse>>>;

public record GetTinhTrangHonNhansResponse(
    string Id,
    string? Name
);

public class GetTinhTrangHonNhansRequestValidator : AbstractValidator<GetTinhTrangHonNhansRequest>
{
    public GetTinhTrangHonNhansRequestValidator()
    {
    }
}

public class GetTinhTrangHonNhansHandler(
    IApplicationDbContext _dbContext
) : IQueryHandler<GetTinhTrangHonNhansRequest, Response<List<GetTinhTrangHonNhansResponse>>>
{
    public async Task<Response<List<GetTinhTrangHonNhansResponse>>> Handle(GetTinhTrangHonNhansRequest req, CancellationToken cancellationToken)
    {
        var query = _dbContext.TinhTrangHonNhans.AsQueryable();

        var TinhTrangHonNhans = await query
            .ProjectToType<GetTinhTrangHonNhansResponse>()
            .ToListAsync(cancellationToken);

        return new Response<List<GetTinhTrangHonNhansResponse>>(TinhTrangHonNhans);
    }
}