using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.EidCAGtel.Interface;
using BuildingBlocks.Exceptions;
using FluentValidation;

namespace GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Queries;

public record CheckDocumentSigningStatusQuery(
    string TransactionCode,
    string TokenSign
) : IQuery<Response<CheckDocumentSigningStatusResponse>>;

public record CheckDocumentSigningStatusResponse(
    string Status,
    DocumentCertificateInfo? Certificate
);

public record DocumentCertificateInfo(
    string Serial,
    DateTime IssuedAt,
    DateTime ExpiredAt,
    string Subject,
    string Issuer
);

public class CheckDocumentSigningStatusQueryValidator : AbstractValidator<CheckDocumentSigningStatusQuery>
{
    public CheckDocumentSigningStatusQueryValidator()
    {
        RuleFor(x => x.TransactionCode)
            .NotEmpty()
            .WithMessage("Transaction code is required");
            
        RuleFor(x => x.TokenSign)
            .NotEmpty()
            .WithMessage("Token sign is required");
    }
}

public class CheckDocumentSigningStatusQueryHandler(
    IEidcaService eidcaService
) : IQueryHandler<CheckDocumentSigningStatusQuery, Response<CheckDocumentSigningStatusResponse>>
{
    public async Task<Response<CheckDocumentSigningStatusResponse>> Handle(CheckDocumentSigningStatusQuery request, CancellationToken cancellationToken)
    {
        var result = await eidcaService.CheckDocumentSigningStatusAsync(request.TransactionCode, request.TokenSign, cancellationToken);

        if (!result.Success || result.Data == null)
        {
            throw new BadRequestException(result.Error?.Message ?? "Failed to check document signing status");
        }

        DocumentCertificateInfo? certificateInfo = null;
        if (result.Data.Certificate != null)
        {
            certificateInfo = new DocumentCertificateInfo(
                result.Data.Certificate.Serial,
                result.Data.Certificate.Issued_At,
                result.Data.Certificate.Expired_At,
                result.Data.Certificate.Subject,
                result.Data.Certificate.Issuer
            );
        }

        var response = new CheckDocumentSigningStatusResponse(
            result.Data.Status,
            certificateInfo
        );

        return new Response<CheckDocumentSigningStatusResponse>(response);
    }
}
