using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.EidCAGtel.Interface;
using BuildingBlocks.Exceptions;
using FluentValidation;

namespace GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Queries;

public record DownloadSignedFileQuery(
    string DocId,
    string TransactionCode,
    string TokenSign,
    string OsType
) : IQuery<Response<DownloadSignedFileResponse>>;

public record DownloadSignedFileResponse(
    byte[] FileContent,
    string FileName,
    string ContentType
);

public class DownloadSignedFileQueryValidator : AbstractValidator<DownloadSignedFileQuery>
{
    public DownloadSignedFileQueryValidator()
    {
        RuleFor(x => x.DocId)
            .NotEmpty()
            .WithMessage("Document ID is required");
            
        RuleFor(x => x.TransactionCode)
            .NotEmpty()
            .WithMessage("Transaction code is required");
            
        RuleFor(x => x.TokenSign)
            .NotEmpty()
            .WithMessage("Token sign is required");
            
        RuleFor(x => x.OsType)
            .NotEmpty()
            .WithMessage("OS type is required");
    }
}

public class DownloadSignedFileQueryHandler(
    IEidcaService eidcaService
) : IQueryHandler<DownloadSignedFileQuery, Response<DownloadSignedFileResponse>>
{
    public async Task<Response<DownloadSignedFileResponse>> Handle(DownloadSignedFileQuery request, CancellationToken cancellationToken)
    {
        var stream = await eidcaService.DownloadSignedFileAsync(
            request.DocId,
            request.TransactionCode,
            request.TokenSign,
            request.OsType,
            cancellationToken);

        if (stream == null)
        {
            throw new BadRequestException("Failed to download signed file");
        }

        using var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream, cancellationToken);
        var fileContent = memoryStream.ToArray();

        var fileName = $"signed_document_{request.DocId}_{DateTime.Now:yyyyMMddHHmmss}.pdf";

        var response = new DownloadSignedFileResponse(
            fileContent,
            fileName,
            "application/pdf"
        );

        return new Response<DownloadSignedFileResponse>(response);
    }
}
