using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.EidCAGtel.Interface;
using BuildingBlocks.Common.EidCAGtel.DTOs;
using BuildingBlocks.Exceptions;
using FluentValidation;

namespace GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Queries;

public record CheckPersonalStatusQuery(
    string TransactionCode,
    string TokenSignature
) : IQuery<Response<CheckPersonalStatusResponse>>;

public record CheckPersonalStatusResponse(
         string? TransactionCode,
         string? Status,
         CertInfo? CertInfo
);

public record CertInfo
(
    string? SerialNumber,
    string? IdNumber,
    string? FullName,
    string? Phone,
    string? Email,
    string? Cert,
    DateTime? DateIssue,
    DateTime? DateExpire
);

public class CheckPersonalStatusQueryValidator : AbstractValidator<CheckPersonalStatusQuery>
{
    public CheckPersonalStatusQueryValidator()
    {
        RuleFor(x => x.TransactionCode)
            .NotEmpty()
            .WithMessage("Transaction code is required");

        RuleFor(x => x.TokenSignature)
            .NotEmpty()
            .WithMessage("Token signature is required");
    }
}

public class CheckPersonalStatusQueryHandler(
    IEidcaService eidcaService
) : IQueryHandler<CheckPersonalStatusQuery, Response<CheckPersonalStatusResponse>>
{
    public async Task<Response<CheckPersonalStatusResponse>> Handle(CheckPersonalStatusQuery request, CancellationToken cancellationToken)
    {
        var result = await eidcaService.CheckPersonalStatusAsync(request.TransactionCode, request.TokenSignature, cancellationToken);

        if (!result.Success || result.Data == null)
        {
            throw new BadRequestException(result.Error?.Message ?? "Failed to check personal status");
        }

        var response = new CheckPersonalStatusResponse(
            result.Data.TransactionCode,
            result.Data.Status,
            new CertInfo(
                result.Data.CertInfo?.SerialNumber,
                result.Data.CertInfo?.IdNumber,
                result.Data.CertInfo?.FullName,
                result.Data.CertInfo?.Phone,
                result.Data.CertInfo?.Email,
                result.Data.CertInfo?.Cert,
                result.Data.CertInfo?.DateIssue,
                result.Data.CertInfo?.DateExpire
            )
        );

        return new Response<CheckPersonalStatusResponse>(response);
    }
}
