using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.EidCAGtel.Interface;
using BuildingBlocks.Exceptions;
using FluentValidation;

namespace GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Queries;

public record GetExistCaQuery(string IdNumber) : IQuery<Response<GetExistCaResponse>>;

public record GetExistCaResponse(
    string? SerialNumber,
    string? IdNumber,
    string? FullName,
    string? Phone,
    string? Email,
    string? Cert,
    DateTime? DateIssue,
    DateTime? DateExpire
);

public class GetExistCaQueryValidator : AbstractValidator<GetExistCaQuery>
{
    public GetExistCaQueryValidator()
    {
        RuleFor(x => x.IdNumber)
            .NotEmpty()
            .WithMessage("ID number is required");
    }
}

public class GetExistCaQueryHandler(
    IEidcaService eidcaService
) : IQueryHandler<GetExistCaQuery, Response<GetExistCaResponse>>
{
    public async Task<Response<GetExistCaResponse>> Handle(GetExistCaQuery request, CancellationToken cancellationToken)
    {
        var result = await eidcaService.GetExistCaAsync(request.IdNumber, cancellationToken);

        if (!result.Success || result.Data == null)
        {
            throw new BadRequestException(result.Error?.Message ?? "Failed to check existing CA");
        }

        var response = new GetExistCaResponse(
            result.Data.SerialNumber,
            result.Data.IdNumber,
            result.Data.FullName,
            result.Data.Phone,
            result.Data.Email,
            result.Data.Cert,
            result.Data.DateIssue,
            result.Data.DateExpire);
            
        return new Response<GetExistCaResponse>(response);
    }
}
