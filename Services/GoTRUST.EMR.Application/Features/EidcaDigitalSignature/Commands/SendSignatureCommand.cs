using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.EidCAGtel.Interface;
using BuildingBlocks.Common.EidCAGtel.DTOs;
using BuildingBlocks.Exceptions;
using FluentValidation;

namespace GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Commands;

public class SendSignatureRequest : ICommand<Response<SendSignatureResponse>>
{
    public string Code { get; init; } = string.Empty;
    public string TransactionCode { get; init; } = string.Empty;
    public string TokenChallenge { get; init; } = string.Empty;
    public RawDataRequest RawData { get; init; } = new();
    public ContactInfoRequest Info { get; init; } = new();
    public string Signature { get; init; } = string.Empty;
}

public class RawDataRequest
{
    public string Sod { get; init; } = string.Empty;
    public string Dg1 { get; init; } = string.Empty;
    public string Dg2 { get; init; } = string.Empty;
    public string Dg13 { get; init; } = string.Empty;
    public string Dg15 { get; init; } = string.Empty;
}

public class ContactInfoRequest
{
    public string? Phone { get; init; }
    public string? Email { get; init; }
    public string Image { get; init; } = string.Empty;
}

public record SendSignatureResponse(
    string? TransactionCode,
    string? Status,
    string? Interval,
    string? ExpiredAt,
    string? TokenSignature
);

public class SendSignatureRequestValidator : AbstractValidator<SendSignatureRequest>
{
    public SendSignatureRequestValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty()
            .WithMessage("Code is required");
            
        RuleFor(x => x.TransactionCode)
            .NotEmpty()
            .WithMessage("Transaction code is required");
            
        RuleFor(x => x.TokenChallenge)
            .NotEmpty()
            .WithMessage("Token challenge is required");
            
        RuleFor(x => x.Signature)
            .NotEmpty()
            .WithMessage("Signature is required");
            
        RuleFor(x => x.RawData)
            .NotNull()
            .WithMessage("Raw data is required");
            
        RuleFor(x => x.Info)
            .NotNull()
            .WithMessage("Contact info is required");
            
        When(x => x.RawData != null, () => {
            RuleFor(x => x.RawData.Sod)
                .NotEmpty()
                .WithMessage("SOD is required");
                
            RuleFor(x => x.RawData.Dg1)
                .NotEmpty()
                .WithMessage("DG1 is required");
                
            RuleFor(x => x.RawData.Dg2)
                .NotEmpty()
                .WithMessage("DG2 is required");
                
            RuleFor(x => x.RawData.Dg13)
                .NotEmpty()
                .WithMessage("DG13 is required");
                
            RuleFor(x => x.RawData.Dg15)
                .NotEmpty()
                .WithMessage("DG15 is required");
        });
        
        When(x => x.Info != null, () => {
            RuleFor(x => x.Info.Image)
                .NotEmpty()
                .WithMessage("Image is required");
        });
    }
}

public class SendSignatureHandler(
    IEidcaService eidcaService
) : ICommandHandler<SendSignatureRequest, Response<SendSignatureResponse>>
{
    public async Task<Response<SendSignatureResponse>> Handle(SendSignatureRequest request, CancellationToken cancellationToken)
    {
        var eidcaRequest = new SignatureRequest
        {
            Code = request.Code,
            Transaction_Code = request.TransactionCode,
            Token_Challenge = request.TokenChallenge,
            Raw_Data = new RawData
            {
                Sod = request.RawData.Sod,
                Dg1 = request.RawData.Dg1,
                Dg2 = request.RawData.Dg2,
                Dg13 = request.RawData.Dg13,
                Dg15 = request.RawData.Dg15
            },
            Info = new ContactInfo
            {
                Phone = request.Info.Phone,
                Email = request.Info.Email,
                Image = request.Info.Image
            },
            Signature = request.Signature
        };

        var result = await eidcaService.SendSignatureAsync(eidcaRequest, cancellationToken);

        if (!result.Success || result.Data == null)
        {
            throw new BadRequestException(result.Error?.Message ?? "Failed to send signature");
        }

        var response = new SendSignatureResponse(
            result.Data.Transaction_code,
            result.Data.Status,
            result.Data.Interval,
            result.Data.Expired_at,
            result.Data.Token_signature
        );

        return new Response<SendSignatureResponse>(response);
    }
}


