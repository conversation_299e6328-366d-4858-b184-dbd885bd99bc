using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.EidCAGtel.Interface;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Commands;

public class GetChallengeRequest : ICommand<Response<GetChallengeResponse>>
{
    public string IdNumber { get; init; } = string.Empty;
}

public record GetChallengeResponse(
    string TransactionCode,
    string TokenChallenge
);

public class GetChallengeRequestValidator : AbstractValidator<GetChallengeRequest>
{
    public GetChallengeRequestValidator()
    {
        RuleFor(x => x.IdNumber)
            .NotEmpty()
            .WithMessage("ID number is required");
    }
}

public class GetChallengeHandler(
    IEidcaService eidcaService
) : ICommandHandler<GetChallengeRequest, Response<GetChallengeResponse>>
{
    public async Task<Response<GetChallengeResponse>> Handle(GetChallengeRequest request, CancellationToken cancellationToken)
    {
        var result = await eidcaService.GetChallengeAsync(request.IdNumber, cancellationToken);

        if (!result.Success || result.Data == null)
        {
            throw new BadRequestException(result.Error?.Message ?? "Failed to get challenge token");
        }

        var response = new GetChallengeResponse(
            result.Data.Transaction_Code,
            result.Data.Token_Challenge
        );

        return new Response<GetChallengeResponse>(response);
    }
}
