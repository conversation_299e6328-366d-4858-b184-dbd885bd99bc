using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.EidCAGtel.Interface;
using BuildingBlocks.Common.EidCAGtel.DTOs;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Commands;

public class ConfirmDocumentSignatureRequest : ICommand<Response<ConfirmDocumentSignatureResponse>>
{
    public string IdNumber { get; init; } = string.Empty;
    public string TransactionCode { get; init; } = string.Empty;
    public string TokenSign { get; init; } = string.Empty;
    public SignatureContactInfoRequest Info { get; init; } = new(string.Empty);
    public List<DocumentSignatureInfo> DocSigns { get; init; } = [];
}

public record SignatureContactInfoRequest(
    string Image
);

public record DocumentSignatureInfo(
    string DocId,
    string Signature
);

public record ConfirmDocumentSignatureResponse(
    string? TransactionCode,
    string? Status,
    string? Token,
    int? Interval,
    int? ExpiresAt,
    List<SignedDoc>? SignedDocs
);

public class SignedDoc
{
    public string? DocId { get; set; }
    public string? DocName { get; set; }
    public string? CaSignature { get; set; }
    public int? SignAt { get; set; }
    public int? ExpireAt { get; set; }
    public string? DocHash { get; set; }
}

public class ConfirmDocumentSignatureRequestValidator : AbstractValidator<ConfirmDocumentSignatureRequest>
{
    public ConfirmDocumentSignatureRequestValidator()
    {
        RuleFor(x => x.IdNumber)
            .NotEmpty()
            .WithMessage("ID number is required");
            
        RuleFor(x => x.TransactionCode)
            .NotEmpty()
            .WithMessage("Transaction code is required");
            
        RuleFor(x => x.TokenSign)
            .NotEmpty()
            .WithMessage("Token sign is required");
            
        RuleFor(x => x.Info)
            .NotNull()
            .WithMessage("Contact info is required");
            
        RuleFor(x => x.DocSigns)
            .NotNull()
            .NotEmpty()
            .WithMessage("Document signatures are required");
            
        When(x => x.Info != null, () => {
            RuleFor(x => x.Info.Image)
                .NotEmpty()
                .WithMessage("Image is required");
        });
        
        When(x => x.DocSigns != null, () => {
            RuleForEach(x => x.DocSigns).ChildRules(docSign => {
                docSign.RuleFor(x => x.DocId)
                    .NotEmpty()
                    .WithMessage("Document ID is required");
                    
                docSign.RuleFor(x => x.Signature)
                    .NotEmpty()
                    .WithMessage("Signature is required");
            });
        });
    }
}

public class ConfirmDocumentSignatureHandler(
    IEidcaService eidcaService
) : ICommandHandler<ConfirmDocumentSignatureRequest, Response<ConfirmDocumentSignatureResponse>>
{
    public async Task<Response<ConfirmDocumentSignatureResponse>> Handle(ConfirmDocumentSignatureRequest request, CancellationToken cancellationToken)
    {
        var eidcaRequest = new DocumentSignatureRequest
        {
            Id_Number = request.IdNumber,
            Transaction_Code = request.TransactionCode,
            Token_Sign = request.TokenSign,
            Info = new SignatureContactInfo
            {
                Image = request.Info.Image
            },
            Doc_Signs = [.. request.DocSigns.Select(ds => new DocumentSignature
            {
                Doc_Id = ds.DocId,
                Signature = ds.Signature
            })]
        };

        var result = await eidcaService.ConfirmDocumentSignatureAsync(eidcaRequest, cancellationToken);

        if (!result.Success || result.Data == null)
        {
            throw new BadRequestException(result.Error?.Message ?? "Failed to confirm document signature");
        }

        var response = new ConfirmDocumentSignatureResponse(
            result.Data.TransactionCode,
            result.Data.Status,
            result.Data.Token,
            result.Data.Interval,
            result.Data.ExpiresAt,
            result.Data.SignedDocs?.Select(sd => new SignedDoc {
                DocId = sd.DocId,
                DocName = sd.DocName,
                CaSignature = sd.CaSignature,
                SignAt = sd.SignAt,
                ExpireAt = sd.ExpireAt,
                DocHash = sd.DocHash
            }).ToList() ?? []
        );

        return new Response<ConfirmDocumentSignatureResponse>(response);
    }
}
