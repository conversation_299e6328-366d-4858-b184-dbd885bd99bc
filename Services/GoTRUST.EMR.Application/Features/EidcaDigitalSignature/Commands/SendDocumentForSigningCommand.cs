using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.EidCAGtel.Interface;
using BuildingBlocks.Exceptions;
using FluentValidation;
using Microsoft.AspNetCore.Http;

namespace GoTRUST.EMR.Application.Features.EidcaDigitalSignature.Commands;

public class SendDocumentForSigningRequest : ICommand<Response<SendDocumentForSigningResponse>>
{
    public string IdNumber { get; init; } = string.Empty;
    public IFormFile Document { get; init; } = null!;
    public string? SignProps { get; init; }
    public string? DocName { get; init; }
    public string SecurityLevel { get; init; } = "LEVEL_2";
}

public record SendDocumentForSigningResponse(
    string? TransactionCode,
    string? TokenSign,
    List<DocumentInfo>? Documents
);

public record DocumentInfo(
    string? DocId,
    string? DocName,
    string? DocType,
    string? DocChallenge
);

public class SendDocumentForSigningRequestValidator : AbstractValidator<SendDocumentForSigningRequest>
{
    public SendDocumentForSigningRequestValidator()
    {
        RuleFor(x => x.IdNumber)
            .NotEmpty()
            .WithMessage("ID number is required");
            
        RuleFor(x => x.Document)
            .NotNull()
            .WithMessage("Document is required");
            
        RuleFor(x => x.SignProps)
            .NotEmpty()
            .WithMessage("Sign properties are required");
            
        RuleFor(x => x.SecurityLevel)
            .NotEmpty()
            .WithMessage("Security level is required");
    }
}

public class SendDocumentForSigningHandler(
    IEidcaService eidcaService
) : ICommandHandler<SendDocumentForSigningRequest, Response<SendDocumentForSigningResponse>>
{
    public async Task<Response<SendDocumentForSigningResponse>> Handle(SendDocumentForSigningRequest request, CancellationToken cancellationToken)
    {
        var result = await eidcaService.SendDocumentForSigningAsync(
            request.IdNumber,
            request.Document,
            request.SignProps,
            request.DocName,
            request.SecurityLevel,
            cancellationToken);

        if (!result.Success || result.Data == null)
        {
            throw new BadRequestException(result.Error?.Message ?? "Failed to send document for signing");
        }

        var response = new SendDocumentForSigningResponse(
            result.Data.TransactionCode,
            result.Data.TokenSign,
            result.Data.Docs?.Select(d => new DocumentInfo(d.DocId, d.DocName, d.DocType, d.DocChallenge)).ToList() ?? []
        );

        return new Response<SendDocumentForSigningResponse>(response);
    }
}
