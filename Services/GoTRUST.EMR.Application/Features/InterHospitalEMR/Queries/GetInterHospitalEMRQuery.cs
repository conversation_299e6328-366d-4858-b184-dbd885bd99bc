using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.Interface;
using BuildingBlocks.Common.EMRApi.Models;
using GoTRUST.EMR.Application.Helpers;
using Mapster;

namespace GoTRUST.EMR.Application.Features.InterHospitalEMR.Queries;

public record GetInterHospitalEMRRequest(
    string CitizenId,
    string TxnId
) : IQuery<Response<GetInterHospitalEMRResponse>>;

public record GetInterHospitalEMRResponse(
    string CitizenId,
    string TxnId,
    string PatientCode,
    string FullName,
    string HospitalName,
    List<Domain.Models.HoSoBenhAn> EMRData
);

public class GetInterHospitalEMRHandler(
    IApplicationDbContext context,
    IInterHospitalEMRService emrService,
    ILogger<GetInterHospitalEMRHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : IQueryHandler<GetInterHospitalEMRRequest, Response<GetInterHospitalEMRResponse>>
{
    public async Task<Response<GetInterHospitalEMRResponse>> Handle(GetInterHospitalEMRRequest request, CancellationToken cancellationToken)
    {
        // 1. L<PERSON>y thông tin user hiện tại và HospitalId từ claims
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        var hospitalId = (httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal())
            ?? throw new UndefineHospitalInfoException();

        logger.LogInformation("Processing inter-hospital EMR request for CitizenId: {CitizenId}, TxnId: {TxnId} by user: {User} from hospital: {HospitalId}",
            request.CitizenId, request.TxnId, currentUser, hospitalId);

        // 2. Kiểm tra PatientShareInfo với TxnId có Result = "1" (đã đồng ý) không
        var patientShareInfo = await context.PatientShareInfos
            .FirstOrDefaultAsync(p => p.TxnId == request.TxnId && p.IdentityNo == request.CitizenId, cancellationToken);

        if (patientShareInfo == null)
        {
            logger.LogWarning("PatientShareInfo not found for TxnId: {TxnId}, CitizenId: {CitizenId}", request.TxnId, request.CitizenId);
            throw new PatientNotFoundException($"Không tìm thấy thông tin chia sẻ cho TxnId: {request.TxnId} và CCCD: {request.CitizenId}");
        }

        if (patientShareInfo.Result != "1")
        {
            var statusMessage = patientShareInfo.Result switch
            {
                "0" => "đang chờ đồng ý",
                "2" => "đã từ chối",
                "3" => "đã thu hồi",
                _ => "không xác định"
            };

            logger.LogWarning("Access denied for TxnId: {TxnId}, CitizenId: {CitizenId}. Status: {Status}",
                request.TxnId, request.CitizenId, statusMessage);

            throw new NoAccessHospitalException();
        }

        logger.LogInformation("Consent verified for TxnId: {TxnId}, CitizenId: {CitizenId}. Consent given at: {ConsentGivenAt}",
            request.TxnId, request.CitizenId, patientShareInfo.ConsentGivenAt);

        // 3. Truy vấn database để lấy HospitalId và PatientId từ CCCD
        var patient = await context.Patients
            .AsNoTracking()
            .Include(p => p.Hospital)
            .FirstOrDefaultAsync(p => p.CitizenId == request.CitizenId, cancellationToken);

        if (patient == null)
        {
            logger.LogWarning("Patient not found for CitizenId: {CitizenId}", request.CitizenId);
            throw new PatientNotFoundException($"Không tìm thấy thông tin bệnh nhân với CCCD: {request.CitizenId}");
        }

        logger.LogInformation("Found patient: {PatientCode} ({FullName}) from hospital: {HospitalName} for CitizenId: {CitizenId}",
            patient.PatientCode, patient.FullName, patient.Hospital.Name, request.CitizenId);

        // 4. Gọi EMR API external với HospitalId và PatientId
        var emrRequest = new GetEMRRequest
        {
            HospitalId = patient.HospitalId.ToString(),
            PatientId = patient.Id.ToString()
        };

        var emrResponse = await emrService.GetInterHospitalEMRAsync(emrRequest, cancellationToken);

        if (!emrResponse.IsSuccess)
        {
            logger.LogError("Failed to retrieve EMR data for patient: {PatientCode}, HospitalId: {HospitalId}. Error: {ErrorMessage}",
                patient.PatientCode, patient.HospitalId, emrResponse.Message);

            throw new PatientNotFoundException($"Không thể lấy thông tin bệnh án từ hệ thống EMR: {emrResponse.Message}");
        }

        logger.LogInformation("Successfully retrieved {EMRCount} EMR records for patient: {PatientCode}",
            emrResponse.Data?.Count ?? 0, patient.PatientCode);

        // 5. Trả về kết quả
        var response = new GetInterHospitalEMRResponse(
            CitizenId: request.CitizenId,
            TxnId: request.TxnId,
            PatientCode: patient.PatientCode,
            FullName: patient.FullName,
            HospitalName: patient.Hospital.Name,
            EMRData: emrResponse.Data?.Adapt<List<Domain.Models.HoSoBenhAn>>() ?? []
        );

        return new Response<GetInterHospitalEMRResponse>(response, "Tra cứu bệnh án liên viện thành công");
    }
}
