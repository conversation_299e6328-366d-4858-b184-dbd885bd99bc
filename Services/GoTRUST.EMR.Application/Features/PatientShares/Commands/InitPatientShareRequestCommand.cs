using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Application.Data;
using GoTRUST.EMR.Domain.Models;
using Microsoft.EntityFrameworkCore;
using FluentValidation;
using Mapster;

namespace GoTRUST.EMR.Application.Features.PatientShares.Commands;

public record InitPatientShareRequestRequest(
    string CitizenId
) : ICommand<Response<InitPatientShareRequestResponse>>;

public record InitPatientShareRequestResponse(
    Guid Id,
    string TxnId,
    string CitizenId,
    string Result,
    DateTime CreatedAt
);

public class InitPatientShareRequestValidator : AbstractValidator<InitPatientShareRequestRequest>
{
    public InitPatientShareRequestValidator()
    {
        RuleFor(x => x.CitizenId)
            .NotEmpty()
            .WithMessage("Số CCCD không được để trống")
            .Length(9, 12)
            .WithMessage("Số CCCD phải có độ dài từ 9 đến 12 ký tự")
            .Matches(@"^\d+$")
            .WithMessage("Số CCCD chỉ được chứa các chữ số");
    }
}

public class InitPatientShareRequestHandler(
    IApplicationDbContext context,
    ILogger<InitPatientShareRequestHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<InitPatientShareRequestRequest, Response<InitPatientShareRequestResponse>>
{
    public async Task<Response<InitPatientShareRequestResponse>> Handle(InitPatientShareRequestRequest request, CancellationToken cancellationToken)
    {
        // 1. Lấy thông tin user hiện tại và HospitalId từ claims
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";
        
        var hospitalId = (httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal())
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");

        // 2. Kiểm tra xem đã có yêu cầu chia sẻ nào đang pending cho CCCD này chưa
        var existingRequest = await context.PatientShareInfos
            .Where(p => p.IdentityNo == request.CitizenId && p.Result == "0")
            .FirstOrDefaultAsync(cancellationToken);

        if (existingRequest != null)
        {
            logger.LogWarning("Đã tồn tại yêu cầu chia sẻ đang chờ xử lý cho CCCD {CitizenId}", request.CitizenId);
            var existingResponse = existingRequest.Adapt<InitPatientShareRequestResponse>();
            return new Response<InitPatientShareRequestResponse>(existingResponse, "Đã tồn tại yêu cầu chia sẻ đang chờ xử lý");
        }

        // 3. Tạo TxnId unique
        var txnId = $"TXN_{DateTime.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid().ToString("N")[..8]}";

        // 4. Tạo PatientShareInfo mới
        var patientShareInfo = new PatientShareInfo
        {
            Id = Guid.NewGuid(),
            TxnId = txnId,
            IdentityNo = request.CitizenId,
            Result = "0", // 0 - đang đợi đồng ý truy cập
            CreatedAt = DateTime.UtcNow,
            CreatedBy = currentUser
        };

        // 5. Lưu vào database
        context.PatientShareInfos.Add(patientShareInfo);
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
        {
            logger.LogError("Không thể tạo yêu cầu chia sẻ cho CCCD {CitizenId}", request.CitizenId);
            throw new InvalidOperationException("Không thể tạo yêu cầu chia sẻ thông tin");
        }

        // 6. Log success
        logger.LogInformation("Đã tạo yêu cầu chia sẻ với TxnId {TxnId} cho CCCD {CitizenId} bởi {User}",
            txnId, request.CitizenId, currentUser);

        // 7. Return response
        var response = patientShareInfo.Adapt<InitPatientShareRequestResponse>();
        return new Response<InitPatientShareRequestResponse>(response, "Yêu cầu chia sẻ đã được tạo thành công");
    }
}
