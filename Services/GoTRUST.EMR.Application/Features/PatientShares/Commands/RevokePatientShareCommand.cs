using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Application.Data;
using GoTRUST.EMR.Domain.Models;
using Microsoft.EntityFrameworkCore;
using FluentValidation;
using Mapster;

namespace GoTRUST.EMR.Application.Features.PatientShares.Commands;

public record RevokePatientShareRequest(
    string TxnId,
    string? Reason
) : ICommand<Response<RevokePatientShareResponse>>;

public record RevokePatientShareResponse(
    Guid Id,
    string TxnId,
    string Result,
    DateTime? ConsentRevokedAt,
    string Message
);

public class RevokePatientShareValidator : AbstractValidator<RevokePatientShareRequest>
{
    public RevokePatientShareValidator()
    {
        RuleFor(x => x.TxnId)
            .NotEmpty()
            .WithMessage("TxnId không được để trống");

        RuleFor(x => x.Reason)
            .MaximumLength(500)
            .WithMessage("Lý do thu hồi không được vượt quá 500 ký tự");
    }
}

public class RevokePatientShareHandler(
    IApplicationDbContext context,
    ILogger<RevokePatientShareHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<RevokePatientShareRequest, Response<RevokePatientShareResponse>>
{
    public async Task<Response<RevokePatientShareResponse>> Handle(RevokePatientShareRequest request, CancellationToken cancellationToken)
    {
        // 1. Lấy thông tin user hiện tại
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        // 2. Tìm PatientShareInfo theo TxnId
        var patientShareInfo = await context.PatientShareInfos
            .FirstOrDefaultAsync(p => p.TxnId == request.TxnId, cancellationToken);

        if (patientShareInfo == null)
        {
            logger.LogWarning("Không tìm thấy yêu cầu chia sẻ với TxnId {TxnId}", request.TxnId);
            throw new InvalidOperationException($"Không tìm thấy yêu cầu chia sẻ với TxnId {request.TxnId}");
        }

        // 3. Kiểm tra trạng thái hiện tại - chỉ có thể thu hồi khi đã đồng ý (Result = "1")
        if (patientShareInfo.Result != "1")
        {
            var currentStatus = patientShareInfo.Result switch
            {
                "0" => "đang chờ xử lý",
                "2" => "đã từ chối",
                "3" => "đã thu hồi",
                _ => "không xác định"
            };

            logger.LogWarning("Không thể thu hồi yêu cầu chia sẻ với TxnId {TxnId}. Trạng thái hiện tại: {CurrentStatus}", 
                request.TxnId, currentStatus);
            
            throw new InvalidOperationException($"Không thể thu hồi yêu cầu chia sẻ. Trạng thái hiện tại: {currentStatus}");
        }

        // 4. Cập nhật trạng thái thu hồi
        patientShareInfo.Result = "3"; // 3 - thu hồi truy cập
        patientShareInfo.ConsentRevokedAt = DateTime.UtcNow;
        patientShareInfo.UpdatedAt = DateTime.UtcNow;
        patientShareInfo.UpdatedBy = currentUser;

        // 5. Lưu thay đổi
        context.PatientShareInfos.Update(patientShareInfo);
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
        {
            logger.LogError("Không thể thu hồi quyền chia sẻ cho TxnId {TxnId}", request.TxnId);
            throw new InvalidOperationException("Không thể thu hồi quyền chia sẻ thông tin");
        }

        // 6. Log success
        logger.LogInformation("Đã thu hồi quyền chia sẻ cho TxnId {TxnId} bởi {User}. Lý do: {Reason}",
            request.TxnId, currentUser, request.Reason ?? "Không có lý do");

        // 7. Return response
        var response = patientShareInfo.Adapt<RevokePatientShareResponse>();
        response = response with { Message = "Đã thu hồi quyền chia sẻ thông tin thành công" };
        return new Response<RevokePatientShareResponse>(response, "Đã thu hồi quyền chia sẻ thông tin thành công");
    }
}
