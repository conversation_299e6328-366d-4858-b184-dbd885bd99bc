using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Application.Data;
using GoTRUST.EMR.Domain.Models;
using Microsoft.EntityFrameworkCore;
using FluentValidation;
using Mapster;

namespace GoTRUST.EMR.Application.Features.PatientShares.Commands;

public record ProcessPatientShareConsentRequest(
    string TxnId,
    string ResultCode,
    string? ResultDesc
) : ICommand<Response<ProcessPatientShareConsentResponse>>;

public record ProcessPatientShareConsentResponse(
    Guid Id,
    string TxnId,
    string Result,
    DateTime? ConsentGivenAt,
    string Message
);

public class ProcessPatientShareConsentValidator : AbstractValidator<ProcessPatientShareConsentRequest>
{
    public ProcessPatientShareConsentValidator()
    {
        RuleFor(x => x.TxnId)
            .NotEmpty()
            .WithMessage("TxnId không được để trống");

        RuleFor(x => x.ResultCode)
            .NotEmpty()
            .WithMessage("Mã kết quả không được để trống")
            .Must(code => new[] { "1", "2" }.Contains(code))
            .WithMessage("Mã kết quả phải là '1' (đồng ý) hoặc '2' (từ chối)");
    }
}

public class ProcessPatientShareConsentHandler(
    IApplicationDbContext context,
    ILogger<ProcessPatientShareConsentHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<ProcessPatientShareConsentRequest, Response<ProcessPatientShareConsentResponse>>
{
    public async Task<Response<ProcessPatientShareConsentResponse>> Handle(ProcessPatientShareConsentRequest request, CancellationToken cancellationToken)
    {
        // 1. Lấy thông tin user hiện tại
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        // 2. Tìm PatientShareInfo theo TxnId
        var patientShareInfo = await context.PatientShareInfos
            .FirstOrDefaultAsync(p => p.TxnId == request.TxnId, cancellationToken);

        if (patientShareInfo == null)
        {
            logger.LogWarning("Không tìm thấy yêu cầu chia sẻ với TxnId {TxnId}", request.TxnId);
            throw new InvalidOperationException($"Không tìm thấy yêu cầu chia sẻ với TxnId {request.TxnId}");
        }

        // 3. Kiểm tra trạng thái hiện tại
        if (patientShareInfo.Result != "0")
        {
            logger.LogWarning("Yêu cầu chia sẻ với TxnId {TxnId} đã được xử lý trước đó. Trạng thái hiện tại: {CurrentResult}", 
                request.TxnId, patientShareInfo.Result);
            
            var existingResponse = patientShareInfo.Adapt<ProcessPatientShareConsentResponse>();
            existingResponse = existingResponse with { Message = "Yêu cầu đã được xử lý trước đó" };
            return new Response<ProcessPatientShareConsentResponse>(existingResponse, "Yêu cầu đã được xử lý trước đó");
        }

        // 4. Cập nhật trạng thái
        patientShareInfo.Result = request.ResultCode;
        patientShareInfo.UpdatedAt = DateTime.UtcNow;
        patientShareInfo.UpdatedBy = currentUser;

        if (request.ResultCode == "1") // Đồng ý
        {
            patientShareInfo.ConsentGivenAt = DateTime.UtcNow;
        }

        // 5. Lưu thay đổi
        context.PatientShareInfos.Update(patientShareInfo);
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
        {
            logger.LogError("Không thể cập nhật trạng thái cho TxnId {TxnId}", request.TxnId);
            throw new InvalidOperationException("Không thể cập nhật trạng thái yêu cầu chia sẻ");
        }

        // 6. Log success
        var resultMessage = request.ResultCode == "1" ? "đồng ý" : "từ chối";
        logger.LogInformation("Đã cập nhật trạng thái {ResultMessage} cho TxnId {TxnId} bởi {User}",
            resultMessage, request.TxnId, currentUser);

        // 7. Return response
        var response = patientShareInfo.Adapt<ProcessPatientShareConsentResponse>();
        var successMessage = request.ResultCode == "1" 
            ? "Đã ghi nhận đồng ý chia sẻ thông tin" 
            : "Đã ghi nhận từ chối chia sẻ thông tin";
        
        response = response with { Message = successMessage };
        return new Response<ProcessPatientShareConsentResponse>(response, successMessage);
    }
}
