using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.MedicalRecordTemplates.Queries;

/// <summary>
/// Query to get all medical record templates
/// </summary>
public record GetAllMedicalRecordTemplatesRequest(
    MedicalRecordTemplateType? TemplateType,
    AdmissionType? AdmissionType
) : IRequest<Response<List<MedicalRecordTemplateDto>>>;

public record MedicalRecordTemplateDto(
    Guid Id,
    Guid HospitalId,
    string HospitalName,
    string TemplateName,
    Guid DepartmentId,
    string DepartmentName,
    MedicalRecordTemplateType TemplateType,
    string TemplateContentJson,
    string TemplateContent,
    string Code,
    AdmissionType AdmissionType,
    DateTime? CreatedAt,
    string? CreatedBy
);

public class GetAllMedicalRecordTemplatesHandler(
    IApplicationDbContext context,
    ILogger<GetAllMedicalRecordTemplatesHandler> logger)
    : IRequestHandler<GetAllMedicalRecordTemplatesRequest, Response<List<MedicalRecordTemplateDto>>>
{
    public async Task<Response<List<MedicalRecordTemplateDto>>> Handle(GetAllMedicalRecordTemplatesRequest request, CancellationToken cancellationToken)
    {
        var query = context.MedicalRecordTemplates
            .AsNoTracking()
            .Include(t => t.Hospital)
            .Include(t => t.Department)
            .AsQueryable();

        if (request.TemplateType.HasValue)
        {
            query = query.Where(t => t.TemplateType == request.TemplateType.Value);
        }

        if (request.AdmissionType.HasValue)
        {
            query = query.Where(t => t.AdmissionType == request.AdmissionType.Value);
        }

        var templates = await query
            .Select(t => new MedicalRecordTemplateDto(
                t.Id,
                t.HospitalId,
                t.Hospital.Name,
                t.TemplateName,
                t.DepartmentId,
                t.Department.Name,
                t.TemplateType,
                t.TemplateContentJson,
                t.TemplateContent,
                t.Code,
                t.AdmissionType,
                t.CreatedAt,
                t.CreatedBy
            ))
            .ToListAsync(cancellationToken);

        logger.LogInformation("Retrieved medical record templates: {count}",
            templates.Count);

        return new Response<List<MedicalRecordTemplateDto>>(templates);
    }
}
