using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.MedicalRecordTemplates.Queries;

public record GetMedicalRecordTemplateByIdRequest(Guid Id) : IRequest<Response<GetMedicalRecordTemplateByIdResponse>>;

public record GetMedicalRecordTemplateByIdResponse(
    Guid Id,
    Guid HospitalId,
    string HospitalName,
    string TemplateName,
    Guid DepartmentId,
    string DepartmentName,
    MedicalRecordTemplateType TemplateType,
    string TemplateContentJson,
    string TemplateContent,
    string Code,
    AdmissionType AdmissionType,
    DateTime? CreatedAt,
    string? CreatedBy,
    DateTime? UpdatedAt,
    string? UpdatedBy
);

public class GetMedicalRecordTemplateByIdHandler(
    IApplicationDbContext context,
    ILogger<GetMedicalRecordTemplateByIdHandler> logger)
    : IRequestHandler<GetMedicalRecordTemplateByIdRequest, Response<GetMedicalRecordTemplateByIdResponse>>
{
    public async Task<Response<GetMedicalRecordTemplateByIdResponse>> Handle(GetMedicalRecordTemplateByIdRequest request, CancellationToken cancellationToken)
    {
        var template = await context.MedicalRecordTemplates.AsNoTracking()
            .Include(t => t.Hospital)
            .Include(t => t.Department)
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken)
            ?? throw new MedicalRecordTemplateNotFoundException(request.Id);

        logger.LogInformation("Retrieved MedicalRecordTemplate with Id {TemplateId}", template.Id);

        var response = new GetMedicalRecordTemplateByIdResponse(
            template.Id,
            template.HospitalId,
            template.Hospital.Name,
            template.TemplateName,
            template.DepartmentId,
            template.Department.Name,
            template.TemplateType,
            template.TemplateContentJson,
            template.TemplateContent,
            template.Code,
            template.AdmissionType,
            template.CreatedAt,
            template.CreatedBy,
            template.UpdatedAt,
            template.UpdatedBy
        );

        return new Response<GetMedicalRecordTemplateByIdResponse>(response);
    }
}
