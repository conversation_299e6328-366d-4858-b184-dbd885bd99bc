using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;
using Mapster;

namespace GoTRUST.EMR.Application.Features.MedicalRecordTemplates.Commands;

public record CreateMedicalRecordTemplateRequest(
    Guid HospitalId,
    string TemplateName,
    Guid DepartmentId,
    MedicalRecordTemplateType TemplateType,
    string TemplateContentJson,
    string TemplateContent,
    string Code,
    AdmissionType AdmissionType
) : ICommand<Response<CreateMedicalRecordTemplateResponse>>;

public record CreateMedicalRecordTemplateResponse(
    Guid Id,
    Guid HospitalId,
    string TemplateName,
    Guid DepartmentId,
    string TemplateType,
    string TemplateContentJson,
    string TemplateContent,
    string Code,
    string AdmissionType,
    DateTime? CreatedAt,
    string? CreatedBy
);

public class CreateMedicalRecordTemplateRequestValidator : AbstractValidator<CreateMedicalRecordTemplateRequest>
{
    public CreateMedicalRecordTemplateRequestValidator()
    {
        RuleFor(x => x.HospitalId)
            .NotEmpty()
            .WithMessage("Mã bệnh viện không được để trống");

        RuleFor(x => x.TemplateName)
            .NotEmpty()
            .WithMessage("Tên mẫu phiếu không được để trống")
            .MaximumLength(200)
            .WithMessage("Tên mẫu phiếu không được vượt quá 200 ký tự");

        RuleFor(x => x.DepartmentId)
            .NotEmpty()
            .WithMessage("Mã khoa không được để trống");

        RuleFor(x => x.TemplateType)
            .IsInEnum()
            .WithMessage("Loại mẫu phiếu không hợp lệ");

        RuleFor(x => x.TemplateContentJson)
            .NotEmpty()
            .WithMessage("Nội dung mẫu phiếu không được để trống");
    }
}

public class CreateMedicalRecordTemplateHandler(
    IApplicationDbContext context,
    ILogger<CreateMedicalRecordTemplateHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<CreateMedicalRecordTemplateRequest, Response<CreateMedicalRecordTemplateResponse>>
{
    public async Task<Response<CreateMedicalRecordTemplateResponse>> Handle(CreateMedicalRecordTemplateRequest request, CancellationToken cancellationToken)
    {
        // Kiểm tra Hospital có tồn tại không
        var hospital = await context.Hospitals.AsNoTracking()
            .FirstOrDefaultAsync(h => h.Id == request.HospitalId, cancellationToken)
            ?? throw new HospitalNotFoundException(request.HospitalId);

        // Kiểm tra Department có tồn tại không
        var department = await context.Departments.AsNoTracking()
            .FirstOrDefaultAsync(d => d.Id == request.DepartmentId, cancellationToken)
            ?? throw new DepartmentNotFoundException(request.DepartmentId);

        // Kiểm tra trùng tên mẫu phiếu trong cùng khoa
        var existingTemplate = await context.MedicalRecordTemplates
            .AsNoTracking()
            .FirstOrDefaultAsync(t => t.TemplateName == request.TemplateName
                          && t.DepartmentId == request.DepartmentId, cancellationToken);

        if (existingTemplate != null)
            throw new MedicalRecordTemplateNameAlreadyExistsException(request.TemplateName, request.DepartmentId);

        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        var template = request.Adapt<MedicalRecordTemplate>();
        template.Id = Guid.NewGuid();
        template.CreatedAt = DateTime.UtcNow;
        template.CreatedBy = currentUser;
        template.IsDeleted = false;

        context.MedicalRecordTemplates.Add(template);
        template.AddDomainEvent(new MedicalRecordTemplateCreateEvent(template));
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
            throw new MedicalRecordTemplateSaveFailedException("tạo");

        logger.LogInformation("Created MedicalRecordTemplate with Id {TemplateId} by {User}",
            template.Id, currentUser);

        var response = template.Adapt<CreateMedicalRecordTemplateResponse>();
        return new Response<CreateMedicalRecordTemplateResponse>(response);
    }
}
