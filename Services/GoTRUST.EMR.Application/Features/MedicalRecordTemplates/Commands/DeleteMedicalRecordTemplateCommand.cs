using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.MedicalRecordTemplates.Commands;

public record DeleteMedicalRecordTemplateRequest(Guid Id) : ICommand<Response<DeleteMedicalRecordTemplateResponse>>;

public record DeleteMedicalRecordTemplateResponse(bool Success, string Message);

public class DeleteMedicalRecordTemplateRequestValidator : AbstractValidator<DeleteMedicalRecordTemplateRequest>
{
    public DeleteMedicalRecordTemplateRequestValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Id không được để trống");
    }
}

public class DeleteMedicalRecordTemplateHandler(
    IApplicationDbContext context,
    ILogger<DeleteMedicalRecordTemplateHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<DeleteMedicalRecordTemplateRequest, Response<DeleteMedicalRecordTemplateResponse>>
{
    public async Task<Response<DeleteMedicalRecordTemplateResponse>> Handle(DeleteMedicalRecordTemplateRequest request, CancellationToken cancellationToken)
    {
        var template = await context.MedicalRecordTemplates
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken)
            ?? throw new MedicalRecordTemplateNotFoundException(request.Id);

        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        // Soft delete
        template.IsDeleted = true;
        template.UpdatedAt = DateTime.UtcNow;
        template.UpdatedBy = currentUser;

        context.MedicalRecordTemplates.Update(template);
        template.AddDomainEvent(new MedicalRecordTemplateDeleteEvent(template));

        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
            throw new MedicalRecordTemplateSaveFailedException("xóa");

        logger.LogInformation("Deleted MedicalRecordTemplate with Id {TemplateId} by {User}",
            template.Id, currentUser);

        var response = new DeleteMedicalRecordTemplateResponse(true, "Xóa mẫu phiếu thành công");
        return new Response<DeleteMedicalRecordTemplateResponse>(response);
    }
}
