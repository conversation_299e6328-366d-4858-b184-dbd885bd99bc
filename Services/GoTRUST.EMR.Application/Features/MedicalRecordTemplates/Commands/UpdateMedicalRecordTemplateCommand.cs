using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;
using Mapster;

namespace GoTRUST.EMR.Application.Features.MedicalRecordTemplates.Commands;

public record UpdateMedicalRecordTemplateRequest(
    Guid Id,
    Guid HospitalId,
    string TemplateName,
    Guid DepartmentId,
    MedicalRecordTemplateType TemplateType,
    string TemplateContentJson,
    string TemplateContent,
    string Code,
    AdmissionType AdmissionType
) : ICommand<Response<UpdateMedicalRecordTemplateResponse>>;

public record UpdateMedicalRecordTemplateResponse(
    Guid Id,
    Guid HospitalId,
    string TemplateName,
    Guid DepartmentId,
    MedicalRecordTemplateType TemplateType,
    string TemplateContentJson,
    string TemplateContent,
    string Code,
    AdmissionType AdmissionType,
    DateTime? UpdatedAt,
    string? UpdatedBy
);

public class UpdateMedicalRecordTemplateRequestValidator : AbstractValidator<UpdateMedicalRecordTemplateRequest>
{
    public UpdateMedicalRecordTemplateRequestValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Id không được để trống");

        RuleFor(x => x.HospitalId)
            .NotEmpty()
            .WithMessage("Mã bệnh viện không được để trống");

        RuleFor(x => x.TemplateName)
            .NotEmpty()
            .WithMessage("Tên mẫu phiếu không được để trống")
            .MaximumLength(200)
            .WithMessage("Tên mẫu phiếu không được vượt quá 200 ký tự");

        RuleFor(x => x.DepartmentId)
            .NotEmpty()
            .WithMessage("Mã khoa không được để trống");

        RuleFor(x => x.TemplateType)
            .IsInEnum()
            .WithMessage("Loại mẫu phiếu không hợp lệ");

        RuleFor(x => x.TemplateContentJson)
            .NotEmpty()
            .WithMessage("Nội dung mẫu phiếu không được để trống");
    }
}

public class UpdateMedicalRecordTemplateHandler(
    IApplicationDbContext context,
    ILogger<UpdateMedicalRecordTemplateHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<UpdateMedicalRecordTemplateRequest, Response<UpdateMedicalRecordTemplateResponse>>
{
    public async Task<Response<UpdateMedicalRecordTemplateResponse>> Handle(UpdateMedicalRecordTemplateRequest request, CancellationToken cancellationToken)
    {
        var template = await context.MedicalRecordTemplates
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken)
            ?? throw new MedicalRecordTemplateNotFoundException(request.Id);

        // Kiểm tra Hospital có tồn tại không
        var hospital = await context.Hospitals.AsNoTracking()
            .FirstOrDefaultAsync(h => h.Id == request.HospitalId, cancellationToken)
            ?? throw new HospitalNotFoundException(request.HospitalId);

        // Kiểm tra Department có tồn tại không
        var department = await context.Departments.AsNoTracking()
            .FirstOrDefaultAsync(d => d.Id == request.DepartmentId, cancellationToken)
            ?? throw new DepartmentNotFoundException(request.DepartmentId);

        // Kiểm tra trùng tên mẫu phiếu trong cùng khoa (trừ bản ghi hiện tại)
        var existingTemplate = await context.MedicalRecordTemplates
            .AsNoTracking()
            .FirstOrDefaultAsync(t => t.TemplateName == request.TemplateName
                          && t.DepartmentId == request.DepartmentId
                          && t.Id != request.Id, cancellationToken);

        if (existingTemplate != null)
            throw new MedicalRecordTemplateNameAlreadyExistsException(request.TemplateName, request.DepartmentId);

        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        // Cập nhật thông tin
        template = request.Adapt(template);
        template.UpdatedAt = DateTime.UtcNow;
        template.UpdatedBy = currentUser;

        context.MedicalRecordTemplates.Update(template);
        template.AddDomainEvent(new MedicalRecordTemplateUpdateEvent(template));
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
            throw new MedicalRecordTemplateSaveFailedException("cập nhật");

        logger.LogInformation("Updated MedicalRecordTemplate with Id {TemplateId} by {User}",
            template.Id, currentUser);

        var response = template.Adapt<UpdateMedicalRecordTemplateResponse>();

        return new Response<UpdateMedicalRecordTemplateResponse>(response);
    }
}
