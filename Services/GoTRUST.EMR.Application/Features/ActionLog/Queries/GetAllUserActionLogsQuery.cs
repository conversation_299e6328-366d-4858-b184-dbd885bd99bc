using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.Application.Features.ActionLog.Queries
{
    public record GetAllUserActionLogsRequest : PaginationRequest, IQuery<PaginationResponse<ActionLogResponse>>
    {
        public string? SearchTerm { get; set; }
        public DateOnly? FromDate { get; set; }
        public DateOnly? ToDate { get; set; }
        public string? ActionType { get; set; }
        public string? Result { get; set; }
    }
    public class ActionLogResponse
    {
        public Guid? UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string ActionName { get; set; } = string.Empty;
        public string ActionIcon { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime ActionDate { get; set; }
        public string MedicalRecordId { get; set; } = string.Empty;
        public string IPAddress { get; set; } = string.Empty;
        public string Result { get; set; } = string.Empty;
    }

    public class GetAllUserActionLogsHandler : IQueryHandler<GetAllUserActionLogsRequest, PaginationResponse<ActionLogResponse>>
    {
        private readonly IApplicationDbContext _dbContext;

        public GetAllUserActionLogsHandler(IApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<PaginationResponse<ActionLogResponse>> Handle(GetAllUserActionLogsRequest request, CancellationToken cancellationToken)
        {
            var query = _dbContext.UserActionLogs
                        .AsNoTracking()
                        .AsQueryable();
            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.ToLower() ?? string.Empty;
                query = query.Where(x => x.ActionName.ToLower().Contains(searchTerm) || x.ActionDetail.ToLower().Contains(searchTerm));
            }

            if (request.FromDate.HasValue)
            {
                query = query.Where(x => x.ActionTime >= request.FromDate.Value.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc));
            }

            if (request.ToDate.HasValue)
            {
                query = query.Where(x => x.ActionTime <= request.ToDate.Value.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc));
            }

            if (!string.IsNullOrEmpty(request.ActionType))
            {
                query = query.Where(x => x.ActionType.ToString() == request.ActionType);
            }

            if (!string.IsNullOrEmpty(request.Result))
            {
                query = query.Where(x => x.Result == request.Result);
            }

            var totalRecords = await query.CountAsync(cancellationToken);
            var logs = await query
                .Include(x => x.User)
                .OrderByDescending(x => x.ActionTime)
                .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
                .Take(request.PageSize!.Value)
                .Select(x => new ActionLogResponse
                {
                    UserId = x.UserId,
                    UserName = x.User == null ? string.Empty : x.User.FullName ?? string.Empty,
                    ActionName = x.ActionName,
                    Description = x.ActionDetail,
                    ActionDate = x.ActionTime,
                    ActionIcon = x.ActionIcon ?? string.Empty,
                    MedicalRecordId = x.MedicalRecordId.ToString() ?? string.Empty,
                    IPAddress = x.IpAddress,
                    Result = x.Result
                })
                .ToListAsync(cancellationToken);
            var logsResponse = new PaginationResponse<ActionLogResponse>(request.PageIndex, request.PageSize, totalRecords, logs);

            return logsResponse;
        }
    }
}