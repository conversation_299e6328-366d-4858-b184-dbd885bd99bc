﻿using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.Application.Features.ActionLog.Queries;

public record GetUserActionLogStatusRequest() : IQuery<Response<List<GetUserActionLogStatusResponse>>>;

public record GetUserActionLogStatusResponse(
    string Name
);

public class GetUserActionLogStatusHandler : IQueryHandler<GetUserActionLogStatusRequest, Response<List<GetUserActionLogStatusResponse>>>
{
    public Task<Response<List<GetUserActionLogStatusResponse>>> Handle(GetUserActionLogStatusRequest req, CancellationToken cancellationToken)
    {
        var statuses = typeof(UserActionLogStatusConstants)
            .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.FlattenHierarchy)
            .Where(fi => fi.IsLiteral && !fi.IsInitOnly && fi.FieldType == typeof(string))
            .Select(fi => new GetUserActionLogStatusResponse((string)fi.GetRawConstantValue()!))
            .ToList();
        return Task.FromResult(new Response<List<GetUserActionLogStatusResponse>>(statuses));
    }
}