using System;
using System.Collections.Generic;
using System.Composition;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.Helper;
using JasperFx.Core.Reflection;
using Microsoft.AspNetCore.Hosting;

namespace GoTRUST.EMR.Application.Features.ActionLog.Queries
{
    public class ExportCsvUserActionLogsRequest : IQuery<Response<ExportCsvUserActionLogsResponse>>
    {
        public string? SearchTerm { get; set; }
        public DateOnly? FromDate { get; set; }
        public DateOnly? ToDate { get; set; }
        public string? ActionType { get; set; }
        public string? Result { get; set; }
    }

    public class ExportCsvUserActionLogsResponse
    {
        public byte[]? FileContent { get; set; } // Nội dung file CSV
        public string FileName { get; set; } = string.Empty; // Tên file
    }

    public class ExportCsvUserActionLogsQueryValidator : AbstractValidator<ExportCsvUserActionLogsRequest>
    {
        public ExportCsvUserActionLogsQueryValidator()
        {
            RuleFor(x => x.SearchTerm)
                .MaximumLength(100).WithMessage("SearchTerm must not exceed 100 characters.");
        }
    }

    public class ExportCsvUserActionLogsHandler : IQueryHandler<ExportCsvUserActionLogsRequest, Response<ExportCsvUserActionLogsResponse>>
    {
        private readonly IApplicationDbContext _dbContext;
        private readonly IWebHostEnvironment _environment;

        public ExportCsvUserActionLogsHandler(IApplicationDbContext dbContext, IWebHostEnvironment environment)
        {
            _dbContext = dbContext;
            _environment = environment;
        }

        public async Task<Response<ExportCsvUserActionLogsResponse>> Handle(ExportCsvUserActionLogsRequest request, CancellationToken cancellationToken)
        {
            var query = _dbContext.UserActionLogs
                .Include(x => x.User)
                .AsNoTracking()
                .AsQueryable();
            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.ToLower() ?? string.Empty;
                query = query.Where(x => x.ActionName.ToLower().Contains(searchTerm) || x.ActionDetail.ToLower().Contains(searchTerm));
            }

            if (request.FromDate.HasValue)
            {
                query = query.Where(x => x.ActionTime >= request.FromDate.Value.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc));
            }

            if (request.ToDate.HasValue)
            {
                query = query.Where(x => x.ActionTime <= request.ToDate.Value.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc));
            }

            if (!string.IsNullOrEmpty(request.ActionType))
            {
                query = query.Where(x => x.ActionType.ToString() == request.ActionType);
            }

            if (!string.IsNullOrEmpty(request.Result))
            {
                query = query.Where(x => x.Result == request.Result);
            }

            var logs = await query.Select(x => new ActionLogResponse
            {
                UserId = x.UserId,
                UserName = x.User == null ? string.Empty : x.User.FullName ?? string.Empty,
                ActionName = x.ActionName,
                Description = x.ActionDetail,
                ActionIcon = x.ActionIcon ?? string.Empty,
                ActionDate = x.ActionTime,
                MedicalRecordId = x.MedicalRecordId.ToString() ?? string.Empty,
                IPAddress = x.IpAddress,
                Result = x.Result
            })
                .ToListAsync(cancellationToken);

            string csvString = ExportExcelHelper.ExportListToCsv(logs);
            if (string.IsNullOrEmpty(csvString))
            {
                return new Response<ExportCsvUserActionLogsResponse>(new ExportCsvUserActionLogsResponse
                {
                    FileContent = null,
                    FileName = string.Empty
                });
            }
            var fileName = $"UserActionLogs_{DateTime.UtcNow:yyyyMMddHHmmss}.csv";
            var fileContent = Encoding.UTF8.GetBytes(csvString);

            return new Response<ExportCsvUserActionLogsResponse>(new ExportCsvUserActionLogsResponse
            {
                FileContent = fileContent,
                FileName = fileName
            });
        }
    }
}