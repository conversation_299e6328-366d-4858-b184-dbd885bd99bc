using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using JasperFx.Core.Reflection;
using Marten;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.ActionLog.Queries
{
    public record GetUserActionLogsRequest : PaginationRequest, IQuery<PaginationResponse<UserActionLogDto>>
    {
        public Guid UserId { get; set; }
        public DateOnly? ActionDate { get; set; }
        public string? SearchTerm { get; set; }
    }

    public class UserActionLogDto
    {
        public string ActionName { get; set; } = string.Empty;
        public string ActionIcon { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime ActionDate { get; set; }
        public string MedicalRecordId { get; set; } = string.Empty;
        public string PatientName { get; set; } = string.Empty;
    }

    public class GetUserActionLogsQueryValidator : AbstractValidator<GetUserActionLogsRequest>
    {
        public GetUserActionLogsQueryValidator()
        {
            RuleFor(x => x.UserId)
                .NotEmpty().WithMessage("UserId is required.");
        }
    }

    public class GetUserActionLogsHandler(IApplicationDbContext dbContext, UserManager<User> userManager) : IQueryHandler<GetUserActionLogsRequest, PaginationResponse<UserActionLogDto>>
    {
        private readonly IApplicationDbContext _dbContext = dbContext;
        private readonly UserManager<User> _userManager = userManager;

        public async Task<PaginationResponse<UserActionLogDto>> Handle(GetUserActionLogsRequest request, CancellationToken cancellationToken)
        {
            var searchTerm = request.SearchTerm?.ToLower() ?? string.Empty;
            var query = _dbContext.UserActionLogs
                        .AsNoTracking()
                        .Include(log => log.MedicalRecord)
                        .ThenInclude(mr => mr!.Patient)
                        .Where(log => log.UserId == request.UserId && (string.IsNullOrEmpty(request.SearchTerm)
                        || log.ActionName.ToLower().Contains(searchTerm!)
                        || log.ActionDetail.ToLower().Contains(searchTerm!)))
                        .Where(log => !request.ActionDate.HasValue || log.ActionTime.Date == request.ActionDate.Value.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc))
                        .AsQueryable();

            var user = await _userManager.FindByIdAsync(request.UserId.ToString()) ?? throw new NotFoundException("User", request.UserId);

            var totalRecords = await query.CountAsync(cancellationToken: cancellationToken);

            var logs = await query
                .OrderByDescending(log => log.ActionTime)
                .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
                .Take(request.PageSize!.Value)
                .Select(log => new UserActionLogDto
                {
                    ActionName = log.ActionName,
                    ActionIcon = log.ActionIcon,
                    Description = log.ActionDetail,
                    ActionDate = log.ActionTime,
                    PatientName = log.MedicalRecord != null ? log.MedicalRecord.Patient.FullName : string.Empty,
                    MedicalRecordId = log.MedicalRecordId.ToString() ?? string.Empty
                })
                .ToListAsync(cancellationToken: cancellationToken);

            return new PaginationResponse<UserActionLogDto>(request.PageIndex, request.PageSize, totalRecords, logs);
        }
    }
}