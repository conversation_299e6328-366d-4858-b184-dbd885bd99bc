﻿using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.ActionLog.Queries;

public record GetUserActionLogTypesRequest() : IQuery<Response<List<GetUserActionLogTypeResponse>>>;

public record GetUserActionLogTypeResponse(
    int Id,
    string EnumName,
    string Name
);

public class GetUserActionLogTypesRequestValidator : AbstractValidator<GetUserActionLogTypesRequest>
{
    public GetUserActionLogTypesRequestValidator()
    {
    }
}

public class GetUserActionLogTypesHandler : IQueryHandler<GetUserActionLogTypesRequest, Response<List<GetUserActionLogTypeResponse>>>
{
    public Task<Response<List<GetUserActionLogTypeResponse>>> Handle(GetUserActionLogTypesRequest req, CancellationToken cancellationToken)
    {
        var types = Enum.GetValues(typeof(UserActionLogType))
            .Cast<UserActionLogType>()
            .Select(e => new GetUserActionLogTypeResponse(
                Id: (int)e,
                EnumName: e.ToString(),
                Name: UserActionLogTypeNames.VietnameseNames.GetValueOrDefault(e, e.ToString())
            ))
            .ToList();

        return Task.FromResult(new Response<List<GetUserActionLogTypeResponse>>(types));
    }

}