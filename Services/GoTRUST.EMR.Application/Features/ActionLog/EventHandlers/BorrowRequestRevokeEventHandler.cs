using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Application.Hubs;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;
using Microsoft.AspNetCore.SignalR;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers
{
    public class BorrowRequestRevokeEventHandler(
        IApplicationDbContext dbContext,
        ILogger<BorrowRequestRevokeEventHandler> logger,
        IHttpContextAccessor httpContextAccessor) : INotificationHandler<BorrowRequestRevokeEvent>
    {
        public async Task Handle(BorrowRequestRevokeEvent notification, CancellationToken cancellationToken)
        {
            logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);
            var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

            var userActionLog = new UserActionLog()
            {
                HospitalId = notification.BorrowRequest.HospitalId,
                ActionName = "Thu hồi yêu cầu mượn",
                ActionDetail = $"Thu hồi yêu cầu mượn {notification.BorrowRequest.Id}.",
                ActionIcon = UserActionLogConstants.RevokeIcon,
                ActionType = UserActionLogType.Revoke,
                UserId = userId,
                ActionTime = DateTime.UtcNow,
                IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
                Result = UserActionLogStatusConstants.SuccessResult
            };

            await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

            logger.LogInformation("UserActionLog created for BorrowRequest {BorrowRequestId} with status {Status}",
                notification.BorrowRequest.Id, "Revoked");
        }
    }
}