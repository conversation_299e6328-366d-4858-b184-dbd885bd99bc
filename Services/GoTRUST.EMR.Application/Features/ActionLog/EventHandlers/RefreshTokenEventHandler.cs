using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class RefreshTokenEventHandler(
    IApplicationDbContext dbContext,
    ILogger<RefreshTokenEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<RefreshTokenEvent>
{
    public async Task Handle(RefreshTokenEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = hospitalId,
            ActionName = "Lấy token mới",
            ActionDetail = $"Lấy token mới cho user {notification.User.UserName}.",
            ActionIcon = UserActionLogConstants.LoginIcon,
            ActionType = UserActionLogType.Authenticate,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for RefreshToken {UserId} with status {Status}",
            notification.User.Id, "Refreshed");
    }
}