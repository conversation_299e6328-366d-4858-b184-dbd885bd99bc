using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class MedicalRecordTemplateUpdateEventHandler(
    IApplicationDbContext dbContext,
    ILogger<MedicalRecordTemplateUpdateEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<MedicalRecordTemplateUpdateEvent>
{
    public async Task Handle(MedicalRecordTemplateUpdateEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.MedicalRecordTemplate.HospitalId,
            ActionName = "Cập nhật mẫu hồ sơ",
            ActionDetail = $"Cập nhật mẫu hồ sơ {notification.MedicalRecordTemplate.Code}.",
            ActionIcon = UserActionLogConstants.UpdateIcon,
            ActionType = UserActionLogType.Update,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog updated for MedicalRecordTemplate {MedicalRecordTemplateId}",
            notification.MedicalRecordTemplate.Id);
    }
}