using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class DepartmentDeleteEventHandler(
    IApplicationDbContext dbContext,
    ILogger<DepartmentDeleteEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<DepartmentDeleteEvent>
{
    public async Task Handle(DepartmentDeleteEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.Department.HospitalId,
            ActionName = "Xóa khoa",
            ActionDetail = $"Xóa khoa {notification.Department.Code}.",
            ActionIcon = UserActionLogConstants.DeleteIcon,
            ActionType = UserActionLogType.Delete,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for Department {DepartmentId} with status {Status}",
            notification.Department.Id, "Deleted");
    }
}
