using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class PatientDeleteEventHandler(
    IApplicationDbContext dbContext,
    ILogger<PatientDeleteEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<PatientDeleteEvent>
{
    public async Task Handle(PatientDeleteEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.Patient.HospitalId,
            ActionName = "Xóa bệnh nhân",
            ActionDetail = $"<PERSON><PERSON>a bệnh nhân {notification.Patient.PatientCode}.",
            ActionIcon = UserActionLogConstants.DeleteIcon,
            ActionType = UserActionLogType.Delete,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for Patient {PatientId} with status {Status}",
            notification.Patient.Id, "Deleted");
    }
}
