using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class ResetPasswordEventHandler(
    IApplicationDbContext dbContext,
    ILogger<ResetPasswordEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<ResetPasswordEvent>
{
    public async Task Handle(ResetPasswordEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = hospitalId,
            ActionName = "Đặt lại mật khẩu",
            ActionDetail = $"Đặt lại mật khẩu cho user {notification.User.UserName}.",
            ActionIcon = UserActionLogConstants.PasswordIcon,
            ActionType = UserActionLogType.Update,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for ResetPassword {UserId} with status {Status}",
            notification.User.Id, "Reset");
    }
}