using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class EmployeeUpdateEventHandler(
    IApplicationDbContext dbContext,
    ILogger<EmployeeUpdateEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<EmployeeUpdateEvent>
{
    public async Task Handle(EmployeeUpdateEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.Employee.HospitalId,
            ActionName = "Cập nhật nhân viên",
            ActionDetail = $"Cập nhật nhân viên {notification.Employee.EmployeeCode}.",
            ActionIcon = UserActionLogConstants.UpdateIcon,
            ActionType = UserActionLogType.Update,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for Employee {EmployeeId} with status {Status}",
            notification.Employee.Id, "Updated");
    }
}
