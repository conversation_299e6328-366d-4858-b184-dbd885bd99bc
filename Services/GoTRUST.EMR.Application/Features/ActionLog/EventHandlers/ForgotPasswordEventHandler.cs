using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class ForgotPasswordEventHandler(
    IApplicationDbContext dbContext,
    ILogger<ForgotPasswordEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<ForgotPasswordEvent>
{
    public async Task Handle(ForgotPasswordEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = hospitalId,
            ActionName = "Quên mật khẩu",
            ActionDetail = $"Yêu cầu quên mật khẩu cho user {notification.User.UserName}.",
            ActionIcon = UserActionLogConstants.PasswordIcon,
            ActionType = UserActionLogType.Authenticate,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for ForgotPassword {UserId} with status {Status}",
            notification.User.Id, "Requested");
    }
}