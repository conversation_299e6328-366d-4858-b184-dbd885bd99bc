﻿using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class MedicalRecordCreatedEventHandler(
    IApplicationDbContext dbContext,
    ILogger<MedicalRecordCreatedEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<MedicalRecordCreatedEvent>
{
    public async Task Handle(MedicalRecordCreatedEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.MedicalRecord.HospitalId,
            ActionName = "Tạo hồ sơ",
            ActionDetail = $"Tạo hồ sơ {notification.MedicalRecord.RecordCode}.",
            ActionIcon = UserActionLogConstants.CreateIcon,
            ActionType = UserActionLogType.Create,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for MedicalRecord {MedicalRecordId} with status {Status}",
            notification.MedicalRecord.Id, MedicalRecordHistoryStatus.Pending);
    }
}