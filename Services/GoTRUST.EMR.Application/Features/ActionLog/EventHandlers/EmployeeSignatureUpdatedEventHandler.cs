using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class EmployeeSignatureUpdatedEventHandler(
    IApplicationDbContext dbContext,
    ILogger<EmployeeSignatureUpdatedEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<EmployeeSignatureUpdatedEvent>
{
    public async Task Handle(EmployeeSignatureUpdatedEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal();

        var serialCount = notification.SignSerials?.Count ?? 0;
        var actionDetail = $"Cập nhật thông tin chữ ký số cho user {notification.User.UserName}. Số lượng serial: {serialCount}";

        var userActionLog = new UserActionLog()
        {
            HospitalId = hospitalId,
            ActionName = "Cập nhật chữ ký số",
            ActionDetail = actionDetail,
            ActionIcon = UserActionLogConstants.UpdateIcon,
            ActionType = UserActionLogType.Update,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for EmployeeSignatureUpdated {UserId} with status {Status}",
            notification.User.Id, "Updated");
    }
}
