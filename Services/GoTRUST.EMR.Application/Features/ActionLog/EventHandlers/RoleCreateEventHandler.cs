using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class RoleCreateEventHandler(
    IApplicationDbContext dbContext,
    ILogger<RoleCreateEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<RoleCreateEvent>
{
    public async Task Handle(RoleCreateEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = hospitalId,
            ActionName = "Tạo vai trò",
            ActionDetail = $"Tạo vai trò {notification.Role.Name}.",
            ActionIcon = UserActionLogConstants.CreateIcon,
            ActionType = UserActionLogType.Create,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for RoleCreate {RoleId} with status {Status}",
            notification.Role.Id, "Created");
    }
}
