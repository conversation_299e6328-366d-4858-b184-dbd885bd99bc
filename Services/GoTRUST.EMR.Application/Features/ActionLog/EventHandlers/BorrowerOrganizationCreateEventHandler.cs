using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class BorrowerOrganizationCreateEventHandler(
    IApplicationDbContext dbContext,
    ILogger<BorrowerOrganizationCreateEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<BorrowerOrganizationCreateEvent>
{
    public async Task Handle(BorrowerOrganizationCreateEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.BorrowerOrganization.HospitalId,
            ActionName = "Tạo tài khoản đối tác",
            ActionDetail = $"Tạo tài khoản đối tác {notification.BorrowerOrganization.Code}.",
            ActionIcon = UserActionLogConstants.CreateIcon,
            ActionType = UserActionLogType.Create,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for BorrowerOrganization {BorrowerOrganizationId} with status {Status}",
            notification.BorrowerOrganization.Id, "Created");
    }
}
