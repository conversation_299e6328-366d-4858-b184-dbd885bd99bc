using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class MedicalRecordRemovedEventHandler(
    IApplicationDbContext dbContext,
    ILogger<MedicalRecordRemovedEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<MedicalRecordRemovedEvent>
{
    public async Task Handle(MedicalRecordRemovedEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.MedicalRecord.HospitalId,
            ActionName = "<PERSON><PERSON><PERSON> hồ sơ",
            ActionDetail = $"<PERSON><PERSON><PERSON> hồ sơ {notification.MedicalRecord.RecordCode}.",
            ActionIcon = UserActionLogConstants.DeleteIcon,
            ActionType = UserActionLogType.Delete,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for MedicalRecord {MedicalRecordId} with status {Status}",
            notification.MedicalRecord.Id, "Removed");
    }
}