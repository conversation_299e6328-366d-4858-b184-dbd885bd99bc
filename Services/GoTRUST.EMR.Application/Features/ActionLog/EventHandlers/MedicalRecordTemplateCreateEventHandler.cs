using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class MedicalRecordTemplateCreateEventHandler(
    IApplicationDbContext dbContext,
    ILogger<MedicalRecordTemplateCreateEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<MedicalRecordTemplateCreateEvent>
{
    public async Task Handle(MedicalRecordTemplateCreateEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.MedicalRecordTemplate.HospitalId,
            ActionName = "Tạo mẫu hồ sơ",
            ActionDetail = $"Tạo mẫu hồ sơ {notification.MedicalRecordTemplate.Code}.",
            ActionIcon = UserActionLogConstants.CreateIcon,
            ActionType = UserActionLogType.Create,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for MedicalRecordTemplate {MedicalRecordTemplateId}",
            notification.MedicalRecordTemplate.Id);
    }
}