using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class SystemNotificationCreateEventHandler(
    IApplicationDbContext dbContext,
    ILogger<SystemNotificationCreateEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<SystemNotificationCreateEvent>
{
    public async Task Handle(SystemNotificationCreateEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            ActionName = "Tạo thông báo hệ thống",
            ActionDetail = $"Tạo thông báo hệ thống {notification.Notification.Title}.",
            ActionIcon = UserActionLogConstants.CreateIcon,
            ActionType = UserActionLogType.Create,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for Notification {NotificationId} with status {Status}",
            notification.Notification.Id, "Created");
    }
}