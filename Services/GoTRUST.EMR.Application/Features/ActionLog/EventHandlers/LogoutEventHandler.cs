using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class LogoutEventHandler(
    IApplicationDbContext dbContext,
    ILogger<LogoutEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<LogoutEvent>
{
    public async Task Handle(LogoutEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = hospitalId,
            ActionName = "Đăng xuất",
            ActionDetail = $"Đăng xuất user {notification.User.UserName}.",
            ActionIcon = UserActionLogConstants.LoginIcon,
            ActionType = UserActionLogType.Authenticate,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for Logout {UserId} with status {Status}",
            notification.User.Id, "LoggedOut");
    }
}