using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class DepartmentCreateEventHandler(
    IApplicationDbContext dbContext,
    ILogger<DepartmentCreateEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<DepartmentCreateEvent>
{
    public async Task Handle(DepartmentCreateEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.Department.HospitalId,
            ActionName = "Tạo khoa",
            ActionDetail = $"Tạo khoa {notification.Department.Code}.",
            ActionIcon = UserActionLogConstants.CreateIcon,
            ActionType = UserActionLogType.Create,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for Department {DepartmentId} with status {Status}",
            notification.Department.Id, "Created");
    }
}
