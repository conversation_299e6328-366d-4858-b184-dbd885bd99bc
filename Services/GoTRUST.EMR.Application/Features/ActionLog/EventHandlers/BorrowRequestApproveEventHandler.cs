using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class BorrowRequestApproveEventHandler(
    IApplicationDbContext dbContext,
    ILogger<BorrowRequestApproveEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<BorrowRequestApproveEvent>
{
    public async Task Handle(BorrowRequestApproveEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.BorrowRequest.HospitalId,
            ActionName = "Duyệt yêu cầu mượn",
            ActionDetail = $"Duyệt yêu cầu mượn {notification.BorrowRequest.Id}.",
            ActionIcon = UserActionLogConstants.ApproveIcon,
            ActionType = UserActionLogType.Approve,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for BorrowRequest {BorrowRequestId} with status {Status}",
            notification.BorrowRequest.Id, "Approved");
    }
}