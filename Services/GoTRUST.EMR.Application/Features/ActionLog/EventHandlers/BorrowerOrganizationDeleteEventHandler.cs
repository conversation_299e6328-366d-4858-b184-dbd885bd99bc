using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class BorrowerOrganizationDeleteEventHandler(
    IApplicationDbContext dbContext,
    ILogger<BorrowerOrganizationDeleteEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<BorrowerOrganizationDeleteEvent>
{
    public async Task Handle(BorrowerOrganizationDeleteEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.BorrowerOrganization.HospitalId,
            ActionName = "Xóa tài khoản đối tác",
            ActionDetail = $"Xóa tài khoản đối tác {notification.BorrowerOrganization.Code}.",
            ActionIcon = UserActionLogConstants.DeleteIcon,
            ActionType = UserActionLogType.Delete,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for BorrowerOrganization {BorrowerOrganizationId} with status {Status}",
            notification.BorrowerOrganization.Id, "Deleted");
    }
}
