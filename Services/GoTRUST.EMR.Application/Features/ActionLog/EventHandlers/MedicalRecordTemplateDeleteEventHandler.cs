using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class MedicalRecordTemplateDeleteEventHandler(
    IApplicationDbContext dbContext,
    ILogger<MedicalRecordTemplateDeleteEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<MedicalRecordTemplateDeleteEvent>
{
    public async Task Handle(MedicalRecordTemplateDeleteEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.MedicalRecordTemplate.HospitalId,
            ActionName = "Xóa mẫu hồ sơ",
            ActionDetail = $"Xóa mẫu hồ sơ {notification.MedicalRecordTemplate.Code}.",
            ActionIcon = UserActionLogConstants.DeleteIcon,
            ActionType = UserActionLogType.Delete,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog deleted for MedicalRecordTemplate {MedicalRecordTemplateId}",
            notification.MedicalRecordTemplate.Id);
    }
}