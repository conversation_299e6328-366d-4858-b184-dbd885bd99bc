using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class BorrowRequestCreateEventHandler(
    IApplicationDbContext dbContext,
    ILogger<BorrowRequestCreateEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<BorrowRequestCreateEvent>
{
    public async Task Handle(BorrowRequestCreateEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.BorrowRequest.HospitalId,
            ActionName = "Tạo yêu cầu mượn",
            ActionDetail = $"Tạo yêu cầu mượn {notification.BorrowRequest.Id}.",
            ActionIcon = UserActionLogConstants.CreateIcon,
            ActionType = UserActionLogType.Create,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for BorrowRequest {BorrowRequestId} with status {Status}",
            notification.BorrowRequest.Id, "Created");
    }
}