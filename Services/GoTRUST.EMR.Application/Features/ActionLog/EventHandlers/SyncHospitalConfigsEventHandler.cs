using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class SyncHospitalConfigsEventHandler(
    IApplicationDbContext dbContext,
    ILogger<SyncHospitalConfigsEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<SyncHospitalConfigsEvent>
{
    public async Task Handle(SyncHospitalConfigsEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.Hospital.Id,
            ActionName = "Đồng bộ cấu hình bệnh viện",
            ActionDetail = $"Đồng bộ cấu hình cho bệnh viện {notification.Hospital.Code}.",
            ActionIcon = UserActionLogConstants.UpdateIcon,
            ActionType = UserActionLogType.Update,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for SyncHospitalConfigsEvent for Hospital {HospitalId}",
            notification.Hospital.Id);
    }
}