using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class ApproveLoginEventHandler(
    IApplicationDbContext dbContext,
    ILogger<ApproveLoginEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<ApproveLoginEvent>
{
    public async Task Handle(ApproveLoginEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = hospitalId,
            ActionName = "Xác thực đăng nhập",
            ActionDetail = $"Xác thực đăng nhập cho user {notification.User.UserName}.",
            ActionIcon = UserActionLogConstants.ApproveIcon,
            ActionType = UserActionLogType.Approve,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for ApproveLogin {UserId} with status {Status}",
            notification.User.Id, "Approved");
    }
}