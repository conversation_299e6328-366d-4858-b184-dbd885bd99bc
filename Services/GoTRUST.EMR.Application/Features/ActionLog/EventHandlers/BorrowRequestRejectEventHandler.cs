using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class BorrowRequestRejectEventHandler(
    IApplicationDbContext dbContext,
    ILogger<BorrowRequestRejectEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<BorrowRequestRejectEvent>
{
    public async Task Handle(BorrowRequestRejectEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.BorrowRequest.HospitalId,
            ActionName = "Từ chối yêu cầu mượn",
            ActionDetail = $"Từ chối yêu cầu mượn {notification.BorrowRequest.Id}.",
            ActionIcon = UserActionLogConstants.RejectIcon,
            ActionType = UserActionLogType.Reject,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for BorrowRequest {BorrowRequestId} with status {Status}",
            notification.BorrowRequest.Id, "Rejected");
    }
}