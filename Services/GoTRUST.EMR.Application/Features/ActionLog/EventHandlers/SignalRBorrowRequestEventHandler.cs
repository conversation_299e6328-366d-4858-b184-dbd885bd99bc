using GoTRUST.EMR.Application.Hubs;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;
using Microsoft.AspNetCore.SignalR;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers
{
    public class SignalRBorrowRequestEventHandler(
        ILogger<SignalRBorrowRequestEventHandler> logger,
        IHubContext<NotificationHub> hubContext) : INotificationHandler<BorrowRequestRevokeEvent>
    {
        public async Task Handle(BorrowRequestRevokeEvent notification, CancellationToken cancellationToken)
        {
            logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);
            var medicalRecordNotification = new MedicalRecordNotification
            {
                Title = "Yêu cầu mượn đã bị hủy",
                Message = $"Yêu cầu mượn với ID {notification.BorrowRequest.Id} đã bị hủy.",
                ActionLogType = UserActionLogType.Revoke,
                MedicalRecordId = notification.BorrowRequest.MedicalRecordId
            };
            await hubContext.Clients.Group(RoleConstants.EMR)
                .SendAsync("MedicalRecord", medicalRecordNotification, cancellationToken: cancellationToken);
            logger.LogInformation("SignalR notification sent for BorrowRequest {BorrowRequestId} with status {Status}", notification.BorrowRequest.Id, "Revoked");
        }
    }

    public record MedicalRecordNotification
    {
        public string? Title { get; set; }
        public string? Message { get; set; }
        public UserActionLogType? ActionLogType { get; set; }
        public Guid? MedicalRecordId { get; set; }
    }
}