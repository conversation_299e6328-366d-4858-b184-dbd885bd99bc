using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class PatientUpdateEventHandler(
    IApplicationDbContext dbContext,
    ILogger<PatientUpdateEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<PatientUpdateEvent>
{
    public async Task Handle(PatientUpdateEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.Patient.HospitalId,
            ActionName = "Cập nhật bệnh nhân",
            ActionDetail = $"Cập nhật bệnh nhân {notification.Patient.PatientCode}.",
            ActionIcon = UserActionLogConstants.UpdateIcon,
            ActionType = UserActionLogType.Update,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for Patient {PatientId} with status {Status}",
            notification.Patient.Id, "Updated");
    }
}
