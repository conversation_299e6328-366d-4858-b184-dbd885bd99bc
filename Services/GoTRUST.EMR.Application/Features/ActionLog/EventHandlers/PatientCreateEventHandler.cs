using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.ActionLog.EventHandlers;

public class PatientCreateEventHandler(
    IApplicationDbContext dbContext,
    ILogger<PatientCreateEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<PatientCreateEvent>
{
    public async Task Handle(PatientCreateEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var userActionLog = new UserActionLog()
        {
            HospitalId = notification.Patient.HospitalId,
            ActionName = "Tạo bệnh nhân",
            ActionDetail = $"Tạo bệnh nhân {notification.Patient.PatientCode}.",
            ActionIcon = UserActionLogConstants.CreateIcon,
            ActionType = UserActionLogType.Create,
            UserId = userId,
            ActionTime = DateTime.UtcNow,
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? UserActionLogConstants.IpUnknown,
            Result = UserActionLogStatusConstants.SuccessResult
        };

        await dbContext.UserActionLogs.AddAsync(userActionLog, cancellationToken);

        logger.LogInformation("UserActionLog created for Patient {PatientId} with status {Status}",
            notification.Patient.Id, "Created");
    }
}
