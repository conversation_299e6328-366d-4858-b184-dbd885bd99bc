using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using System.ComponentModel.DataAnnotations;
using GoTRUST.EMR.Application.Features.Departments.Models;
using Mapster;

namespace GoTRUST.EMR.Application.Features.Departments.Commands;

public record SyncDepartmentsRequest(
    List<SyncDepartmentItemDto> Departments
) : ICommand<Response<SyncDepartmentsResponse>>;

public record SyncDepartmentItemDto(
    string STT,
    string TenKhoaTheoChuan,
    string MaKhoaTheoChuan,
    string TenKhoaTheoBenhVien,
    string MaKhoaTheoBenhVien,
    string MoTa
);

public record SyncDepartmentsResponse(
    int TotalProcessed,
    int Created,
    int Updated,
    List<SyncResultItemDto> Results
);

public record SyncResultItemDto(
    string STT,
    string MaKhoaTheoChuan,
    string TenKhoaTheoChuan,
    string Action, // "Created" | "Updated" | "Error"
    string Message,
    Guid? DepartmentId = null
);

public class SyncDepartmentsHandler(
    IApplicationDbContext context,
    ILogger<SyncDepartmentsHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<SyncDepartmentsRequest, Response<SyncDepartmentsResponse>>
{
    public async Task<Response<SyncDepartmentsResponse>> Handle(
        SyncDepartmentsRequest request, 
        CancellationToken cancellationToken)
    {
        // Kiểm tra Hospital có tồn tại không
        var hospitalId = (httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal())
            ?? throw new UnauthorizedAccessException();
        logger.LogInformation("Starting sync departments for hospital {HospitalId}. Total items: {Count}", 
            hospitalId, request.Departments.Count);
        
        var currentUser = httpContextAccessor.HttpContext?.User.Identity?.Name ?? "System";
        var results = new List<SyncResultItemDto>();
        var created = 0;
        var updated = 0;

        // Lấy tất cả departments hiện tại của hospital để check trùng lặp
        var existingDepartments = await context.Departments
            .Where(d => d.HospitalId == hospitalId)
            .ToListAsync(cancellationToken);

        foreach (var item in request.Departments)
        {
            try
            {
                var validationDto = item.Adapt<SyncDepartmentValidationDto>();
                var validationErrors = ValidateItem(validationDto);
                
                if (validationErrors.Count != 0)
                {
                    results.Add(new SyncResultItemDto(
                        item.STT,
                        item.MaKhoaTheoChuan,
                        item.TenKhoaTheoChuan,
                        "Error",
                        string.Join("; ", validationErrors)
                    ));
                    continue;
                }

                var result = ProcessDepartmentItem(item, hospitalId, 
                    existingDepartments, currentUser);
                
                switch (result.Action)
                {
                    case "Created":
                        created++;
                        break;
                    case "Updated":
                        updated++;
                        break;
                }
                
                results.Add(result);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing department item STT: {STT}, Code: {Code}", 
                    item.STT, item.MaKhoaTheoChuan);
                
                results.Add(new SyncResultItemDto(
                    item.STT,
                    item.MaKhoaTheoChuan,
                    item.TenKhoaTheoChuan,
                    "Error",
                    $"Lỗi xử lý: {ex.Message}"
                ));
            }
        }
        
        await context.SaveChangesAsync(cancellationToken);
        
        logger.LogInformation("Sync departments completed. Created: {Created}, Updated: {Updated}, Errors: {Errors}", 
            created, updated, results.Count(r => r.Action == "Error"));

        return new Response<SyncDepartmentsResponse>(
            new SyncDepartmentsResponse(
                request.Departments.Count,
                created,
                updated,
                results
            ));
    }

    private static List<string> ValidateItem(SyncDepartmentValidationDto dto)
    {
        var validationContext = new ValidationContext(dto);
        var validationResults = new List<ValidationResult>();
        
        if (!Validator.TryValidateObject(dto, validationContext, validationResults, true))
        {
            return validationResults.Select(vr => vr.ErrorMessage ?? "Lỗi validation").ToList();
        }
        
        return [];
    }

    private SyncResultItemDto ProcessDepartmentItem(
        SyncDepartmentItemDto item,
        Guid hospitalId,
        List<Department> existingDepartments,
        string currentUser)
    {
        // Tìm department hiện tại dựa trên Code
        var existingDepartment = existingDepartments
            .FirstOrDefault(d => d.Code == item.MaKhoaTheoChuan);

        if (existingDepartment != null)
        {
            // Cập nhật department hiện tại
            var nameByHospital = string.IsNullOrEmpty(item.TenKhoaTheoBenhVien) 
                ? item.TenKhoaTheoChuan 
                : item.TenKhoaTheoBenhVien;
                
            var codeByHospital = string.IsNullOrEmpty(item.MaKhoaTheoBenhVien) 
                ? item.MaKhoaTheoChuan 
                : item.MaKhoaTheoBenhVien;

            existingDepartment.Name = item.TenKhoaTheoChuan;
            existingDepartment.NameByHospital = nameByHospital;
            existingDepartment.CodeByHospital = codeByHospital;
            existingDepartment.Description = item.MoTa;
            existingDepartment.UpdatedAt = DateTime.UtcNow;
            existingDepartment.UpdatedBy = currentUser;

            context.Departments.Update(existingDepartment);

            return new SyncResultItemDto(
                item.STT,
                item.MaKhoaTheoChuan,
                item.TenKhoaTheoChuan,
                "Updated",
                "Cập nhật thành công",
                existingDepartment.Id
            );
        }
        else
        {
            // Tạo mới department
            var nameByHospital = string.IsNullOrWhiteSpace(item.TenKhoaTheoBenhVien) 
                ? item.TenKhoaTheoChuan 
                : item.TenKhoaTheoBenhVien;
                
            var codeByHospital = string.IsNullOrWhiteSpace(item.MaKhoaTheoBenhVien) 
                ? item.MaKhoaTheoChuan 
                : item.MaKhoaTheoBenhVien;

            var newDepartment = new Department
            {
                Id = Guid.NewGuid(),
                Code = item.MaKhoaTheoChuan,
                Name = item.TenKhoaTheoChuan,
                NameByHospital = nameByHospital,
                CodeByHospital = codeByHospital,
                Description = item.MoTa,
                HospitalId = hospitalId,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = currentUser
            };

            context.Departments.Add(newDepartment);
            
            existingDepartments.Add(newDepartment);

            return new SyncResultItemDto(
                item.STT,
                item.MaKhoaTheoChuan,
                item.TenKhoaTheoChuan,
                "Created",
                "Tạo mới thành công",
                newDepartment.Id
            );
        }
    }
}
