using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Events;
using Mapster;

namespace GoTRUST.EMR.Application.Features.Departments.Commands;

public record CreateDepartmentRequest(
    string Code,
    string Name,
    string NameByHospital,
    string CodeByHospital,
    string Description,
    Guid HospitalId
) : ICommand<Response<CreateDepartmentResponse>>;

public record CreateDepartmentResponse(Guid Id);

public class CreateDepartmentRequestValidator : AbstractValidator<CreateDepartmentRequest>
{
    public CreateDepartmentRequestValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty()
            .WithMessage("Mã khoa không được để trống")
            .MaximumLength(50)
            .WithMessage("Mã khoa không được vượt quá 50 ký tự");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Tên khoa không được để trống")
            .MaximumLength(255)
            .WithMessage("Tên khoa không được vượt quá 255 ký tự");

        RuleFor(x => x.NameByHospital)
            .NotEmpty()
            .WithMessage("Tên khoa theo bệnh viện không được để trống")
            .MaximumLength(255)
            .WithMessage("Tên khoa theo bệnh viện không được vượt quá 255 ký tự");

        RuleFor(x => x.CodeByHospital)
            .NotEmpty()
            .WithMessage("Mã khoa theo bệnh viện không được để trống")
            .MaximumLength(50)
            .WithMessage("Mã khoa theo bệnh viện không được vượt quá 50 ký tự");

        RuleFor(x => x.Description)
            .MaximumLength(500)
            .WithMessage("Mô tả không được vượt quá 500 ký tự");

        RuleFor(x => x.HospitalId)
            .NotEmpty()
            .WithMessage("Mã bệnh viện không được để trống");
    }
}

public class CreateDepartmentHandler(
    IApplicationDbContext context,
    ILogger<CreateDepartmentHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<CreateDepartmentRequest, Response<CreateDepartmentResponse>>
{
    public async Task<Response<CreateDepartmentResponse>> Handle(CreateDepartmentRequest request, CancellationToken cancellationToken)
    {
        // Kiểm tra Hospital có tồn tại không
        var hospital = await context.Hospitals.AsNoTracking()
            .FirstOrDefaultAsync(h => h.Id == request.HospitalId, cancellationToken)
            ?? throw new HospitalNotFoundException(request.HospitalId);

        // Kiểm tra trùng mã khoa trong cùng bệnh viện
        var existingDepartment = await context.Departments
            .AsNoTracking()
            .FirstOrDefaultAsync(d => d.Code == request.Code
                          && d.HospitalId == request.HospitalId, cancellationToken);

        if (existingDepartment != null)
            throw new DepartmentCodeAlreadyExistsException(request.Code, request.HospitalId);

        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        var department = new Department
        {
            Id = Guid.NewGuid(),
            Code = request.Code,
            Name = request.Name,
            NameByHospital = request.NameByHospital,
            CodeByHospital = request.CodeByHospital,
            Description = request.Description,
            HospitalId = request.HospitalId,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = currentUser,
            IsDeleted = false
        };

        context.Departments.Add(department);
        department.AddDomainEvent(new DepartmentCreateEvent(department));
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
            throw new DepartmentSaveFailedException("create");

        logger.LogInformation("Created Department with Id {DepartmentId} by {User}",
            department.Id, currentUser);

        return new Response<CreateDepartmentResponse>(new CreateDepartmentResponse(department.Id));
    }
}
