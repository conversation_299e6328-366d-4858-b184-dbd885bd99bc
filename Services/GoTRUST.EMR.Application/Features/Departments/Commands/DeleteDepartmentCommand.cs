using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Exceptions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Departments.Commands;

public record DeleteDepartmentRequest(Guid Id) : ICommand<Response<DeleteDepartmentResponse>>;

public record DeleteDepartmentResponse(bool Success, string Message);

public class DeleteDepartmentRequestValidator : AbstractValidator<DeleteDepartmentRequest>
{
    public DeleteDepartmentRequestValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Id không được để trống");
    }
}

public class DeleteDepartmentHandler(
    IApplicationDbContext context,
    ILogger<DeleteDepartmentHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<DeleteDepartmentRequest, Response<DeleteDepartmentResponse>>
{
    public async Task<Response<DeleteDepartmentResponse>> Handle(DeleteDepartmentRequest request, CancellationToken cancellationToken)
    {
        var department = await context.Departments
            .FirstOrDefaultAsync(d => d.Id == request.Id, cancellationToken)
            ?? throw new DepartmentNotFoundException(request.Id);

        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        // Soft delete
        department.IsDeleted = true;
        department.UpdatedAt = DateTime.UtcNow;
        department.UpdatedBy = currentUser;

        context.Departments.Update(department);
        department.AddDomainEvent(new DepartmentDeleteEvent(department));
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
            throw new DepartmentSaveFailedException("delete");

        logger.LogInformation("Deleted Department with Id {DepartmentId} by {User}",
            department.Id, currentUser);

        var response = new DeleteDepartmentResponse(true, "Xóa khoa thành công");
        return new Response<DeleteDepartmentResponse>(response);
    }
}
