using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Events;
using Mapster;

namespace GoTRUST.EMR.Application.Features.Departments.Commands;

public record UpdateDepartmentRequest(
    Guid Id,
    string Code,
    string Name,
    string NameByHospital,
    string CodeByHospital,
    string Description,
    Guid HospitalId
) : ICommand<Response<UpdateDepartmentResponse>>;

public record UpdateDepartmentResponse(Guid Id);

public class UpdateDepartmentRequestValidator : AbstractValidator<UpdateDepartmentRequest>
{
    public UpdateDepartmentRequestValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Id không được để trống");

        RuleFor(x => x.Code)
            .NotEmpty()
            .WithMessage("Mã khoa không được để trống")
            .MaximumLength(50)
            .WithMessage("Mã khoa không được vượt quá 50 ký tự");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Tên khoa không được để trống")
            .MaximumLength(255)
            .WithMessage("Tên khoa không được vượt quá 255 ký tự");

        RuleFor(x => x.NameByHospital)
            .NotEmpty()
            .WithMessage("Tên khoa theo bệnh viện không được để trống")
            .MaximumLength(255)
            .WithMessage("Tên khoa theo bệnh viện không được vượt quá 255 ký tự");

        RuleFor(x => x.CodeByHospital)
            .NotEmpty()
            .WithMessage("Mã khoa theo bệnh viện không được để trống")
            .MaximumLength(50)
            .WithMessage("Mã khoa theo bệnh viện không được vượt quá 50 ký tự");

        RuleFor(x => x.Description)
            .MaximumLength(500)
            .WithMessage("Mô tả không được vượt quá 500 ký tự");

        RuleFor(x => x.HospitalId)
            .NotEmpty()
            .WithMessage("Mã bệnh viện không được để trống");
    }
}

public class UpdateDepartmentHandler(
    IApplicationDbContext context,
    ILogger<UpdateDepartmentHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<UpdateDepartmentRequest, Response<UpdateDepartmentResponse>>
{
    public async Task<Response<UpdateDepartmentResponse>> Handle(UpdateDepartmentRequest request, CancellationToken cancellationToken)
    {
        var department = await context.Departments
            .FirstOrDefaultAsync(d => d.Id == request.Id, cancellationToken)
            ?? throw new DepartmentNotFoundException(request.Id);

        // Kiểm tra Hospital có tồn tại không
        var hospital = await context.Hospitals.AsNoTracking()
            .FirstOrDefaultAsync(h => h.Id == request.HospitalId, cancellationToken)
            ?? throw new HospitalNotFoundException(request.HospitalId);

        // Kiểm tra trùng mã khoa trong cùng bệnh viện (trừ chính nó)
        var existingDepartment = await context.Departments
            .AsNoTracking()
            .FirstOrDefaultAsync(d => d.Code == request.Code
                          && d.HospitalId == request.HospitalId
                          && d.Id != request.Id, cancellationToken);

        if (existingDepartment != null)
            throw new DepartmentCodeAlreadyExistsException(request.Code, request.HospitalId);

        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        // Update department
        department.Code = request.Code;
        department.Name = request.Name;
        department.NameByHospital = request.NameByHospital;
        department.CodeByHospital = request.CodeByHospital;
        department.Description = request.Description;
        department.HospitalId = request.HospitalId;
        department.UpdatedAt = DateTime.UtcNow;
        department.UpdatedBy = currentUser;

        // Save changes
        context.Departments.Update(department);
        department.AddDomainEvent(new DepartmentUpdateEvent(department));
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
            throw new DepartmentSaveFailedException("update");

        logger.LogInformation("Updated Department with Id {DepartmentId} by {User}",
            department.Id, currentUser);

        return new Response<UpdateDepartmentResponse>(new UpdateDepartmentResponse(department.Id));
    }
}
