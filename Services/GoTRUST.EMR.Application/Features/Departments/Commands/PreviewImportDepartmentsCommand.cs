using System.ComponentModel.DataAnnotations;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Features.Departments.Models;

namespace GoTRUST.EMR.Application.Features.Departments.Commands;

public record PreviewImportDepartmentsRequest(IFormFile File) : ICommand<Response<PreviewImportDepartmentsResponse>>;

public record PreviewImportDepartmentsResponse(List<DepartmentImportItemDto> Items);

public record DepartmentImportItemDto(
    string Stt,
    string TenKhoaTheoChuan,
    string MaKhoaTheoChuan,
    string TenKhoaTheoBenhVien,
    string MaKhoaTheoBenhVien,
    string MoTa,
    string HealthCheck
);

public class PreviewImportDepartmentsRequestValidator : AbstractValidator<PreviewImportDepartmentsRequest>
{
    public PreviewImportDepartmentsRequestValidator()
    {
        RuleFor(x => x.File)
            .NotNull()
            .WithMessage("File không được để trống");

        RuleFor(x => x.File.Length)
            .GreaterThan(0)
            .WithMessage("File không được rỗng");

        RuleFor(x => x.File.FileName)
            .Must(fileName =>
            {
                if (string.IsNullOrEmpty(fileName)) return false;
                var extension = Path.GetExtension(fileName).ToLower();
                return extension == ".xlsx" || extension == ".xls" || extension == ".csv";
            })
            .WithMessage("Chỉ hỗ trợ file .xlsx, .xls và .csv");
    }
}

public class PreviewImportDepartmentsHandler(
    ILogger<PreviewImportDepartmentsHandler> logger)
    : ICommandHandler<PreviewImportDepartmentsRequest, Response<PreviewImportDepartmentsResponse>>
{
    public async Task<Response<PreviewImportDepartmentsResponse>> Handle(
        PreviewImportDepartmentsRequest request,
        CancellationToken cancellationToken)
    {
            logger.LogInformation("Starting review import departments from file: {FileName}", request.File.FileName);

            var extension = Path.GetExtension(request.File.FileName).ToLower();

            List<Dictionary<string, object>> rawData;
            
            var headerKeywords = DepartmentImportRowDto.GetHeaderKeywords();

            if (extension is ".xlsx" or ".xls")
            {
                var result = await ImportExcelHelper.ReadExcelFileAsync(
                    request.File,
                    0,
                    headerKeywords);
                
                if (!result.IsSuccess)
                    throw new  NotFoundException(result.ErrorMessage ?? "Lỗi đọc file Excel");
                rawData = result.Data ?? [];
            }
            else if (extension is ".csv")
            {
                var result = await ImportExcelHelper.ReadCsvFileAsync(request.File, headerKeywords);
                if (!result.IsSuccess)
                    throw new NotFoundException(result.ErrorMessage ?? "Lỗi đọc file csv");
                rawData = result.Data ?? [];
            }
            else
            {
                throw new NotFoundException("Chỉ hỗ trợ file Excel (.xlsx, .xls) hoặc CSV (.csv)");
            }

            if (rawData.Count == 0)
                throw new NotFoundException("File không chứa dữ liệu hoặc định dạng không đúng");
            
            var allCodes = rawData
                .Select(row => DepartmentImportRowDto.CreateFromRow(row).MaKhoaTheoChuan)
                .Where(code => !string.IsNullOrWhiteSpace(code))
                .Select(code => code.Trim())
                .ToList();
            
            var importItems = rawData.Select((row, _) => ProcessRowData(row, allCodes)).ToList();

            logger.LogInformation("Completed review import departments. Processed {Count} items", importItems.Count);

            return new Response<PreviewImportDepartmentsResponse>(
                new PreviewImportDepartmentsResponse(importItems));
    }

    private static DepartmentImportItemDto ProcessRowData(Dictionary<string, object> row, List<string> allCodes)
    {
        var rowDto = DepartmentImportRowDto.CreateFromRow(row);
        
        var healthCheck = ValidateRowDto(rowDto, allCodes);

        return new DepartmentImportItemDto(
            rowDto.STT,
            rowDto.TenKhoaTheoChuan,
            rowDto.MaKhoaTheoChuan,
            rowDto.TenKhoaTheoBenhVien ?? string.Empty,
            rowDto.MaKhoaTheoBenhVien ?? string.Empty,
            rowDto.MoTa ?? string.Empty,
            healthCheck
        );
    }

    private static string ValidateRowDto(DepartmentImportRowDto rowDto, List<string> allCodes)
    {
        var errors = new List<string>();
        var validationContext = new ValidationContext(rowDto);
        var validationResults = new List<ValidationResult>();
        
        if (!Validator.TryValidateObject(rowDto, validationContext, validationResults, true))
        {
            errors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "Lỗi validation"));
        }

        if (string.IsNullOrWhiteSpace(rowDto.MaKhoaTheoChuan))
            return errors.Count != 0 ? string.Join("; ", errors) : "Không lỗi";
        var duplicateCount = allCodes.Count(code =>
            string.Equals(code, rowDto.MaKhoaTheoChuan.Trim(), StringComparison.OrdinalIgnoreCase));

        if (duplicateCount > 1)
        {
            errors.Add($"Mã khoa '{rowDto.MaKhoaTheoChuan}' bị trùng lặp trong file");
        }

        return errors.Count != 0 ? string.Join("; ", errors) : "Không lỗi";
    }
}
