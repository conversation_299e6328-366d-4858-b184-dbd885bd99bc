using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.Application.Features.Departments.Queries;

public record GetDepartmentByIdRequest(Guid Id) : IRequest<Response<GetDepartmentByIdResponse>>;

public record GetDepartmentByIdResponse(
    Guid Id,
    string Code,
    string Name,
    string NameByHospital,
    string CodeByHospital,
    string Description,
    Guid HospitalId,
    string HospitalName,
    DateTime? CreatedAt,
    string? CreatedBy,
    DateTime? UpdatedAt,
    string? UpdatedBy
);

public class GetDepartmentByIdHandler(
    IApplicationDbContext context,
    ILogger<GetDepartmentByIdHandler> logger)
    : IRequestHandler<GetDepartmentByIdRequest, Response<GetDepartmentByIdResponse>>
{
    public async Task<Response<GetDepartmentByIdResponse>> Handle(GetDepartmentByIdRequest request, CancellationToken cancellationToken)
    {
        var department = await context.Departments.AsNoTracking()
            .Include(d => d.Hospital)
            .FirstOrDefaultAsync(d => d.Id == request.Id, cancellationToken)
            ?? throw new DepartmentNotFoundException(request.Id);

        logger.LogInformation("Retrieved Department with Id {DepartmentId}", department.Id);

        var response = new GetDepartmentByIdResponse(
            department.Id,
            department.Code,
            department.Name,
            department.NameByHospital,
            department.CodeByHospital,
            department.Description,
            department.HospitalId,
            department.Hospital.Name,
            department.CreatedAt,
            department.CreatedBy,
            department.UpdatedAt,
            department.UpdatedBy
        );

        return new Response<GetDepartmentByIdResponse>(response);
    }
}
