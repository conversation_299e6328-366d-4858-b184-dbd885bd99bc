using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.Application.Features.Departments.Queries;

/// <summary>
/// Query to get paginated list of departments with optional filtering
/// </summary>
public record GetAllDepartmentsRequest : IQuery<Response<List<DepartmentDto>>>;

public record DepartmentDto(
    Guid Id,
    string Code,
    string Name,
    string NameByHospital,
    string CodeByHospital,
    string Description,
    Guid HospitalId,
    string HospitalName,
    DateTime? CreatedAt,
    string? CreatedBy
);

public class GetAllDepartmentsHandler(
    IApplicationDbContext context,
    ILogger<GetAllDepartmentsHandler> logger)
    : IQueryHandler<GetAllDepartmentsRequest, Response<List<DepartmentDto>>>
{
    public async Task<Response<List<DepartmentDto>>> Handle(GetAllDepartmentsRequest request, CancellationToken cancellationToken)
    {
        // Apply pagination and project to DTO
        var departments = await context.Departments
            .AsNoTracking()
            .Include(d => d.Hospital)
            .Select(d => new DepartmentDto(
                d.Id,
                d.<PERSON>,
                d.Name,
                d.<PERSON>y<PERSON>ospital,
                d.<PERSON>pital,
                d.Description,
                d.Hospital<PERSON>d,
                d.Hospital.Name,
                d.<PERSON>,
                d.CreatedBy
            ))
            .ToListAsync(cancellationToken);

        logger.LogInformation("Retrieved departments: {response} )", 
            departments);

        return new Response<List<DepartmentDto>>(departments);
    }
}
