using System.ComponentModel.DataAnnotations;
using GoTRUST.EMR.Application.Features.Departments.Commands;

namespace GoTRUST.EMR.Application.Features.Departments.Models
{
    /// <summary>
    /// DTO validation cho sync departments sử dụng same validation như import
    /// </summary>
    public record SyncDepartmentValidationDto
    {
        [Display(Name = "STT")]
        [Required(ErrorMessage = "STT không được để trống")]
        [Range(1, int.MaxValue, ErrorMessage = "STT phải là số nguyên dương")]
        public string? STT { get; init; } = string.Empty;

        [Display(Name = "Tên khoa theo chuẩn")]
        [Required(ErrorMessage = "Tên khoa theo chuẩn không được để trống")]
        [MaxLength(255, ErrorMessage = "Tên khoa theo chuẩn không được vượt quá 255 ký tự")]
        public string? TenKhoaTheoChuan { get; init; } = string.Empty;

        [Display(Name = "Mã khoa theo chuẩn")]
        [Required(ErrorMessage = "Mã khoa theo chuẩn không được để trống")]
        [MaxLength(50, ErrorMessage = "Mã khoa theo chuẩn không được vượt quá 50 ký tự")]
        public string? MaKhoaTheoChuan { get; init; } = string.Empty;

        [Display(Name = "Tên khoa theo bệnh viện")]
        [MaxLength(255, ErrorMessage = "Tên khoa theo bệnh viện không được vượt quá 255 ký tự")]
        public string? TenKhoaTheoBenhVien { get; init; }

        [Display(Name = "Mã khoa theo bệnh viện")]
        [MaxLength(50, ErrorMessage = "Mã khoa theo bệnh viện không được vượt quá 50 ký tự")]
        public string? MaKhoaTheoBenhVien { get; init; }

        [Display(Name = "Mô tả")]
        [MaxLength(500, ErrorMessage = "Mô tả không được vượt quá 500 ký tự")]
        public string? MoTa { get; init; }

        public static SyncDepartmentValidationDto FromSyncDto(SyncDepartmentItemDto dto)
        {
            return new SyncDepartmentValidationDto
            {
                STT = dto.STT,
                TenKhoaTheoChuan = dto.TenKhoaTheoChuan,
                MaKhoaTheoChuan = dto.MaKhoaTheoChuan,
                TenKhoaTheoBenhVien = dto.TenKhoaTheoBenhVien,
                MaKhoaTheoBenhVien = dto.MaKhoaTheoBenhVien,
                MoTa = dto.MoTa
            };
        }
    }
}