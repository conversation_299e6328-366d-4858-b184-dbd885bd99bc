using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace GoTRUST.EMR.Application.Features.Departments.Models;

/// <summary>
/// DTO cho validation row data import departments
/// </summary>
public record DepartmentImportRowDto
{
    [Display(Name = "STT")]
    [Required(ErrorMessage = "STT không được để trống")]
    [Range(1, int.MaxValue, ErrorMessage = "STT phải là số nguyên dương")]
    public string STT { get; init; } = string.Empty;

    [Display(Name = "Tên khoa theo chuẩn")]
    [Required(ErrorMessage = "Tên khoa theo chuẩn không được để trống")]
    [MaxLength(255, ErrorMessage = "Tên khoa theo chuẩn không được vượt quá 255 ký tự")]
    public string TenKhoaTheoChuan { get; init; } = string.Empty;

    [Display(Name = "Mã khoa theo chuẩn")]
    [Required(ErrorMessage = "Mã khoa theo chuẩn không được để trống")]
    [MaxLength(50, ErrorMessage = "Mã khoa theo chuẩn không được vượt quá 50 ký tự")]
    public string MaKhoaTheoChuan { get; init; } = string.Empty;

    [Display(Name = "Tên khoa theo bệnh viện")]
    [MaxLength(255, ErrorMessage = "Tên khoa theo bệnh viện không được vượt quá 255 ký tự")]
    public string? TenKhoaTheoBenhVien { get; init; }

    [Display(Name = "Mã khoa theo bệnh viện")]
    [MaxLength(50, ErrorMessage = "Mã khoa theo bệnh viện không được vượt quá 50 ký tự")]
    public string? MaKhoaTheoBenhVien { get; init; }

    [Display(Name = "Mô tả")]
    [MaxLength(500, ErrorMessage = "Mô tả không được vượt quá 500 ký tự")]
    public string? MoTa { get; init; }

    private static readonly Lazy<List<string>> _cachedHeaderKeywords = new(() =>
    {
        var properties = typeof(DepartmentImportRowDto).GetProperties();
        var keywords = new List<string>();

        foreach (var prop in properties)
        {
            var displayAttr = prop.GetCustomAttribute<DisplayAttribute>();
            if (displayAttr?.Name != null)
            {
                keywords.Add(displayAttr.Name);
                keywords.Add(prop.Name);
                keywords.Add(displayAttr.Name.Replace(" ", ""));
                keywords.Add(displayAttr.Name.Replace(" ", "").ToLower());
            }
        }
        return [.. keywords.Distinct()];
    });

    /// <summary>
    /// Lấy danh sách header keywords từ DisplayName attributes
    /// </summary>
    public static List<string> GetHeaderKeywords()
    {
        return _cachedHeaderKeywords.Value;
    }

    /// <summary>
    /// Lấy danh sách required columns từ Required attributes
    /// </summary>
    public static List<string> GetRequiredColumns()
    {
        var properties = typeof(DepartmentImportRowDto).GetProperties();
        var requiredColumns = new List<string>();

        foreach (var prop in properties)
        {
            var requiredAttr = prop.GetCustomAttribute<RequiredAttribute>();
            var displayAttr = prop.GetCustomAttribute<DisplayAttribute>();

            if (requiredAttr != null && displayAttr?.Name != null)
            {
                requiredColumns.Add(displayAttr.Name);
            }
        }

        return requiredColumns;
    }

    /// <summary>
    /// Tạo instance từ dictionary data với mapping động
    /// </summary>
    public static DepartmentImportRowDto CreateFromRow(Dictionary<string, object> row)
    {
        var properties = typeof(DepartmentImportRowDto).GetProperties();
        var values = new Dictionary<string, object>();

        foreach (var prop in properties)
        {
            var displayAttr = prop.GetCustomAttribute<DisplayAttribute>();
            if (displayAttr?.Name != null)
            {
                var value = GetValueFromRow(row, displayAttr.Name, prop.Name);
                values[prop.Name] = ConvertValue(value, prop.PropertyType);
            }
        }

        return new DepartmentImportRowDto
        {
            STT = (string)(values.GetValueOrDefault(nameof(STT)) ?? string.Empty),
            TenKhoaTheoChuan = (string)(values.GetValueOrDefault(nameof(TenKhoaTheoChuan)) ?? string.Empty),
            MaKhoaTheoChuan = (string)(values.GetValueOrDefault(nameof(MaKhoaTheoChuan)) ?? string.Empty),
            TenKhoaTheoBenhVien = (string?)values.GetValueOrDefault(nameof(TenKhoaTheoBenhVien)),
            MaKhoaTheoBenhVien = (string?)values.GetValueOrDefault(nameof(MaKhoaTheoBenhVien)),
            MoTa = (string?)values.GetValueOrDefault(nameof(MoTa))
        };
    }

    private static string GetValueFromRow(Dictionary<string, object> row, params string[] possibleKeys)
    {
        // Tạo danh sách tất cả các biến thể có thể của key
        var allKeys = new List<string>();
        foreach (var key in possibleKeys)
        {
            allKeys.Add(key);
            allKeys.Add(key.Replace(" ", ""));
            allKeys.Add(key.Replace(" ", "").ToLower());
            allKeys.Add(key.ToLower());
        }

        foreach (var key in allKeys)
        {
            if (row.TryGetValue(key, out var value))
            {
                return value.ToString()?.Trim() ?? string.Empty;
            }
        }
        return string.Empty;
    }

    private static object ConvertValue(string value, Type targetType)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            if (targetType == typeof(int))
                return 0;
            return targetType == typeof(string) ? string.Empty : null!;
        }

        if (targetType == typeof(int))
        {
            return int.TryParse(value, out var intValue) ? intValue : 0;
        }

        return value;
    }
}
