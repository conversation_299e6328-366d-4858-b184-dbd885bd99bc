using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Queries;

public record GetMedicalRecordIdByStorageNumberRequest(string Id) : IRequest<Response<GetMedicalRecordIdByStorageNumberResponse>>;

public record GetMedicalRecordIdByStorageNumberResponse(
    Guid Id
);


public class GetMedicalRecordIdByStorageNumberHandler(
    IApplicationDbContext context,
    ILogger<GetMedicalRecordIdByStorageNumberHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<GetMedicalRecordIdByStorageNumberRequest, Response<GetMedicalRecordIdByStorageNumberResponse>>
{
    public async Task<Response<GetMedicalRecordIdByStorageNumberResponse>> Handle(GetMedicalRecordIdByStorageNumberRequest request, CancellationToken cancellationToken)
    {
        // Get HospitalId using helper method
        var hospitalId = httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");
            
        // Get medical record with all related data
        var medicalRecord = await context.MedicalRecords.AsNoTracking()
            .FirstOrDefaultAsync(mr => mr.StorageNumber == request.Id && mr.HospitalId == hospitalId, cancellationToken)
            ?? throw new MedicalRecordNotFoundException();

        logger.LogInformation("Medical record found with ID: {Id}", medicalRecord.Id);
        

        return new Response<GetMedicalRecordIdByStorageNumberResponse>(new GetMedicalRecordIdByStorageNumberResponse(medicalRecord.Id));
    }
}