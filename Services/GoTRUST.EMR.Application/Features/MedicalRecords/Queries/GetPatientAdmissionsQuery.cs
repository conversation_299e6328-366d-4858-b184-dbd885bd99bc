using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Application.Services;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Queries;

/// <summary>
/// Request để lấy danh sách các lần nhập viện của bệnh nhân
/// </summary>
public record GetPatientAdmissionsRequest(Guid PatientId) : IRequest<Response<GetPatientAdmissionsResponse>>;

/// <summary>
/// Response chứa danh sách các lần nhập viện của bệnh nhân
/// </summary>
public record GetPatientAdmissionsResponse(
    List<PatientAdmissionDto> Admissions
);

/// <summary>
/// Handler xử lý request lấy danh sách các lần nhập viện của bệnh nhân
/// </summary>
public class GetPatientAdmissionsHandler(
    IHisService hisService,
    ILogger<GetPatientAdmissionsHandler> logger,
    IHttpContextAccessor httpContextAccessor,
    IApplicationDbContext context)
    : IRequestHandler<GetPatientAdmissionsRequest, Response<GetPatientAdmissionsResponse>>
{
    public async Task<Response<GetPatientAdmissionsResponse>> Handle(
        GetPatientAdmissionsRequest request,
        CancellationToken cancellationToken)
    {
        // 1. Lấy HospitalId từ claims của user hiện tại
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");

        logger.LogInformation("Getting admissions for patient {PatientId} in hospital {HospitalId}",
            request.PatientId, hospitalId);

        // 2. Kiểm tra patient có tồn tại và thuộc hospital hiện tại không
        var patient = await context.Patients
            .AsNoTracking()
            .FirstOrDefaultAsync(p => p.Id == request.PatientId && p.HospitalId == hospitalId, cancellationToken)
            ?? throw new PatientNotFoundException(request.PatientId);

        // 3. Lấy danh sách admissions từ HIS service
        var admissions = await hisService.GetPatientAdmissionsAsync(
            request.PatientId,
            hospitalId,
            cancellationToken);

        var admissionIds = admissions.Select(a => a.AdmissionId).ToList();

        // Check if admissions exist in db MedicalRecords together with HospitalId
        var existingAdmissions = await context.MedicalRecords
            .Where(mr => mr.HospitalId == hospitalId && admissionIds.Contains(mr.AdmissionCode))
            .Select(x => x.AdmissionCode)
            .ToListAsync(cancellationToken);

        // Mark existing admissions as disabled
        if (existingAdmissions.Count != 0)
        {
            foreach (var admission in admissions)
            {
                if (existingAdmissions.Contains(admission.AdmissionId))
                {
                    admission.isDisabled = true;
                }
            }
        }

        // 4. Tạo response
        var response = new GetPatientAdmissionsResponse(
            Admissions: admissions
        );

        logger.LogInformation("Successfully retrieved {Count} admissions for patient {PatientId}",
            admissions.Count, request.PatientId);

        return new Response<GetPatientAdmissionsResponse>(response);
    }
}
