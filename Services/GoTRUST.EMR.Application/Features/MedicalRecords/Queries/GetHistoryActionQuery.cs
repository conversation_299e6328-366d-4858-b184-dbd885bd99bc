using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Newtonsoft.Json;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Queries
{
    public class GetHistoryActionQuery : IQuery<Response<List<HistoryActionDto>>>
    {
        public Guid MedicalRecordId { get; set; }
    }

    public class HistoryActionDto
    {
        public Guid Id { get; set; }
        public string Text { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
    }

    public class GetHistoryActionHandler : IQueryHandler<GetHistoryActionQuery, Response<List<HistoryActionDto>>>
    {
        private readonly IApplicationDbContext _dbContext;

        public GetHistoryActionHandler(IApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<Response<List<HistoryActionDto>>> Handle(GetHistoryActionQuery request, CancellationToken cancellationToken)
        {
            var historyActions = await _dbContext.SystemConfigs
                .AsNoTracking()
                .Where(x => x.Key == "MedicalRecordHistoryActions")
                .FirstOrDefaultAsync(cancellationToken) ?? throw new NotFoundException("SystemConfig", "MedicalRecordHistoryActions");
            var actions = JsonConvert.DeserializeObject<List<HistoryActionDto>>(historyActions.Value)
                ?? throw new NotFoundException("Không tìm thấy lịch sử hành động");

            return new Response<List<HistoryActionDto>>(actions);
        }
    }
}