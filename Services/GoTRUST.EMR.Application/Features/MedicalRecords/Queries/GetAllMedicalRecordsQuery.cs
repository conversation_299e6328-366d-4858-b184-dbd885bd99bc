using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Helpers;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Queries;

public record GetAllMedicalRecordsRequest : PaginationWithSortRequest, IRequest<PaginationResponse<MedicalRecordsDto>>
{
    public string? Search { get; set; }
    public string[]? StatusList { get; set; }
}

public record MedicalRecordsDto(
    Guid Id,
    Guid PatientId,
    string PatientCode,
    Guid DepartmentId,
    string AdmissionCode,
    string RecordName,
    string RecordCode,
    string StorageNumber,
    DateTime AdmissionDate,
    DateTime? DischargeDate,
    int FilmCount,
    bool HasBHYT,
    string Note,
    int TotalFilesUploaded,
    string Status,
    int StorageYears,
    string EncounterType,
    Guid HospitalId,
    DateTime? CreatedAt,
    string? CreatedBy,
    DateTime? UpdatedAt,
    string? UpdatedBy,
    Patient Patient,
    Department Department
);

public class GetAllMedicalRecordsHandler(
    IApplicationDbContext context,
    ILogger<GetAllMedicalRecordsHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<GetAllMedicalRecordsRequest, PaginationResponse<MedicalRecordsDto>>
{
    public async Task<PaginationResponse<MedicalRecordsDto>> Handle(GetAllMedicalRecordsRequest request, CancellationToken cancellationToken)
    {
        // Get HospitalId using helper method
        var hospitalId = httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");
            
        IQueryable<MedicalRecord> query = context.MedicalRecords
            .AsNoTracking()
            .Where(mr => mr.HospitalId == hospitalId)
            .Include(mr => mr.Patient)
            .Include(mr => mr.Department);

        if (!string.IsNullOrEmpty(request.Search))
        {
            var searchLower = request.Search.ToLower();
            query = query.Where(mr =>
                mr.Patient.FullName.ToLower().Contains(searchLower) ||
                mr.Patient.PatientCode.ToLower().Contains(searchLower) ||
                mr.RecordCode.ToLower().Contains(searchLower) ||
                mr.AdmissionCode.ToLower().Contains(searchLower) ||
                mr.Patient.PhoneNumber.Contains(searchLower)
            );
        }

        // 3.1. Filter by status
        if (request.StatusList != null && request.StatusList.Length != 0)
        {
            var statusListLower = request.StatusList.Select(s => s.ToLower()).ToList();
            query = query.Where(mr => statusListLower.Contains(mr.Status.ToString().ToLower()));
        }

        // 4. Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // 5. Sorting
        query = request.SortBy?.ToLower() switch
        {
            "patientcode" => request.SortDescending == true
                ? query.OrderByDescending(mr => mr.Patient.PatientCode)
                : query.OrderBy(mr => mr.Patient.PatientCode),
            "patientname" => request.SortDescending == true
                ? query.OrderByDescending(mr => mr.Patient.FullName)
                : query.OrderBy(mr => mr.Patient.FullName),
            "admissioncode" => request.SortDescending == true
                ? query.OrderByDescending(mr => mr.AdmissionCode)
                : query.OrderBy(mr => mr.AdmissionCode),
            "recordcode" => request.SortDescending == true
                ? query.OrderByDescending(mr => mr.RecordCode)
                : query.OrderBy(mr => mr.RecordCode),
            "storagenumber" => request.SortDescending == true
                ? query.OrderByDescending(mr => mr.StorageNumber)
                : query.OrderBy(mr => mr.StorageNumber),
            "admissiondate" => request.SortDescending == true
                ? query.OrderByDescending(mr => mr.AdmissionDate)
                : query.OrderBy(mr => mr.AdmissionDate),
            "status" => request.SortDescending == true
                ? query.OrderByDescending(mr => mr.Status)
                : query.OrderBy(mr => mr.Status),
            "updatedat" => request.SortDescending == true
                ? query.OrderByDescending(mr => mr.UpdatedAt)
                : query.OrderBy(mr => mr.UpdatedAt),
            "createdat" => request.SortDescending == true
                ? query.OrderByDescending(mr => mr.CreatedAt)
                : query.OrderBy(mr => mr.CreatedAt),
            _ => query.OrderByDescending(mr => mr.UpdatedAt ?? mr.CreatedAt)
        };

        // 6. Apply pagination and load entities
        var medicalRecordDtos = await query
            .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
            .Take(request.PageSize!.Value)
            .ProjectToType<MedicalRecordsDto>()
            .ToListAsync(cancellationToken);

        var paginationResponse = new PaginationResponse<MedicalRecordsDto>(
            request.PageIndex,
            request.PageSize,
            totalCount,
            medicalRecordDtos
        );

        logger.LogInformation("Retrieved {Count} medical records (page {PageIndex}) for hospital {HospitalId}",
            totalCount,
            request.PageIndex,
            hospitalId);

        return paginationResponse;
    }
}