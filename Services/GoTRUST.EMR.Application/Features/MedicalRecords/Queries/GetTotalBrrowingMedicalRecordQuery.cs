using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Queries
{
    public record GetTotalBrrowingMedicalRecordRequest : IRequest<Response<GetTotalBrrowingMedicalRecordResponse>>;

    public record GetTotalBrrowingMedicalRecordResponse(int Total);

    public class GetTotalBrrowingMedicalRecordQuery(IApplicationDbContext dbContext, IHttpContextAccessor httpContextAccessor) : IRequestHandler<GetTotalBrrowingMedicalRecordRequest, Response<GetTotalBrrowingMedicalRecordResponse>>
    {
        public async Task<Response<GetTotalBrrowingMedicalRecordResponse>> Handle(GetTotalBrrowingMedicalRecordRequest request, CancellationToken cancellationToken)
        {
            // Get all hospital id from RetrieveHospitalIdFromPrincipal
            var hospitalId = httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
                ?? throw new UnauthorizedAccessException("Không xác định được thông tin bệnh viện.");

            // Check not approved in borrowing medical record in Borrowing Request table
            var notApprovedBorrowingMedicalRecord = await dbContext.BorrowRequests
                .Where(x => x.ApprovalStatus == BorrowRequestStatus.Pending
                && x.HospitalId == hospitalId)
                .CountAsync(cancellationToken);

            return new Response<GetTotalBrrowingMedicalRecordResponse>(new GetTotalBrrowingMedicalRecordResponse(notApprovedBorrowingMedicalRecord));
        }
    }
}