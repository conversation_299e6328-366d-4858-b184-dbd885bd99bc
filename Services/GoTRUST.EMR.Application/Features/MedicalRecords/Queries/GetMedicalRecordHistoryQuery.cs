using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Queries
{
    public class GetMedicalRecordHistoryQuery : IQuery<Response<List<MedicalRecordHistoryDto>>>
    {
        public Guid MedicalRecordId { get; set; }
    }

    public class MedicalRecordHistoryDto
    {
        public Guid Id { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public Guid? UserId { get; set; }
        public string UserName { get; set; } = string.Empty;

    }

    public class GetMedicalRecordHistoryHandler : IQueryHandler<GetMedicalRecordHistoryQuery, Response<List<MedicalRecordHistoryDto>>>
    {
        private readonly IApplicationDbContext _dbContext;

        public GetMedicalRecordHistoryHandler(IApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<Response<List<MedicalRecordHistoryDto>>> Handle(GetMedicalRecordHistoryQuery request, CancellationToken cancellationToken)
        {
            var history = await _dbContext.MedicalRecordStatusHistories
                .Include(h => h.ChangedByUser)
                .Where(h => h.MedicalRecordId == request.MedicalRecordId)
                .AsNoTracking()
                .OrderByDescending(h => h.ChangeDate)
                .Select(h => new MedicalRecordHistoryDto
                {
                    Id = h.Id,
                    Status = h.Status.ToString() ?? string.Empty,
                    Description = h.ChangeReason,
                    Timestamp = h.ChangeDate,
                    UserId = h.ChangedByUserId,
                    UserName = h.ChangedByUser.UserName ?? string.Empty
                }).ToListAsync(cancellationToken);

            return new Response<List<MedicalRecordHistoryDto>>(history);
        }
    }
}