using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Mapster;
using GoTRUST.EMR.Application.Helpers;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Queries;

public record GetMedicalRecordsByPatientsRequest : IRequest<Response<GetMedicalRecordsByPatientsResponse>>
{
    public string? Search { get; set; }
}

public record GetMedicalRecordsByPatientsResponse
{
    public Guid PatientId { get; set; }
    public string AvatarUrl { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string Sex { get; set; } = string.Empty;
    public int Age { get; set; }
    public DateTime DateOfBirth { get; set; }
    public string PatientCode { get; set; } = string.Empty;
    public string CitizenId { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public List<MedicalRecordsDto> MedicalRecords { get; set; } = new();
}

public class GetMedicalRecordsByPatientsHandler(
    IApplicationDbContext context,
    ILogger<GetMedicalRecordsByPatientsHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<GetMedicalRecordsByPatientsRequest, Response<GetMedicalRecordsByPatientsResponse>>
{
    public async Task<Response<GetMedicalRecordsByPatientsResponse>> Handle(GetMedicalRecordsByPatientsRequest request, CancellationToken cancellationToken)
    {
        var hospitalId = httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");

        // Tạo config chung để tránh circular reference
        var mappingConfig = new TypeAdapterConfig();
        mappingConfig.ForType<Patient, Patient>().Ignore(p => p.MedicalRecords);
        // Department không có circular reference với MedicalRecord

        GetMedicalRecordsByPatientsResponse? result = null;

        // Bước 1: Tìm kiếm Bệnh nhân trước
        if (!string.IsNullOrEmpty(request.Search))
        {
            var searchLower = request.Search.ToLower();
            
            // Tìm bệnh nhân theo tiêu chí tìm kiếm
            var patient = await context.Patients
                .AsNoTracking()
                .Where(p => p.HospitalId == hospitalId)
                .Where(p =>
                    p.FullName.ToLower().Contains(searchLower) ||
                    p.PatientCode.ToLower().Contains(searchLower) ||
                    p.CitizenId.ToLower().Contains(searchLower) ||
                    p.PhoneNumber.Contains(searchLower))
                .FirstOrDefaultAsync(cancellationToken);

            if (patient != null)
            {
                // Tìm thấy bệnh nhân - lấy tất cả hồ sơ bệnh án của bệnh nhân này
                var patientMedicalRecords = await context.MedicalRecords
                    .AsNoTracking()
                    .Where(mr => mr.HospitalId == hospitalId && mr.PatientId == patient.Id)
                    .Include(mr => mr.Patient)
                    .Include(mr => mr.Department)
                    .ToListAsync(cancellationToken);

                result = new GetMedicalRecordsByPatientsResponse
                {
                    PatientId = patient.Id,
                    AvatarUrl = patient.AvatarUrl ?? string.Empty,
                    FullName = patient.FullName,
                    Sex = patient.Sex,
                    Age = patient.DateOfBirth.CalculateAge(),
                    DateOfBirth = patient.DateOfBirth,
                    PatientCode = patient.PatientCode,
                    CitizenId = patient.CitizenId ?? string.Empty,
                    PhoneNumber = patient.PhoneNumber ?? string.Empty,
                    Address = patient.Address ?? string.Empty,
                    MedicalRecords = patientMedicalRecords.Adapt<List<MedicalRecordsDto>>(mappingConfig)
                };
            }
            else
            {
                // Không tìm thấy bệnh nhân - tìm kiếm hồ sơ bệnh án theo số lưu trữ/mã
                var foundMedicalRecord = await context.MedicalRecords
                    .AsNoTracking()
                    .Where(mr => mr.HospitalId == hospitalId)
                    .Where(mr => 
                        mr.StorageNumber.ToLower().Contains(searchLower) ||
                        mr.RecordCode.ToLower().Contains(searchLower) ||
                        mr.AdmissionCode.ToLower().Contains(searchLower))
                    .Include(mr => mr.Patient)
                    .Include(mr => mr.Department)
                    .FirstOrDefaultAsync(cancellationToken);

                // Nếu tìm thấy hồ sơ bệnh án, sử dụng thông tin bệnh nhân và lấy tất cả hồ sơ của họ
                if (foundMedicalRecord != null)
                {
                    var foundPatient = foundMedicalRecord.Patient;
                    
                    result = new GetMedicalRecordsByPatientsResponse
                    {
                        PatientId = foundPatient.Id,
                        AvatarUrl = foundPatient.AvatarUrl ?? string.Empty,
                        FullName = foundPatient.FullName,
                        Sex = foundPatient.Sex,
                        Age = foundPatient.DateOfBirth.CalculateAge(),
                        DateOfBirth = foundPatient.DateOfBirth,
                        PatientCode = foundPatient.PatientCode,
                        CitizenId = foundPatient.CitizenId ?? string.Empty,
                        PhoneNumber = foundPatient.PhoneNumber ?? string.Empty,
                        Address = foundPatient.Address ?? string.Empty,
                        MedicalRecords = [foundMedicalRecord.Adapt<MedicalRecordsDto>(mappingConfig)]
                    };
                }
            }
        }
        else
        {
            // Nếu không có tiêu chí tìm kiếm, trả về kết quả rỗng
            result = null;
        }

        if (result == null)
        {
            logger.LogWarning("No patient or medical records found for search term: {Search} in hospital {HospitalId}", 
                request.Search, hospitalId);
            throw new NotFoundException("Không tìm thấy hồ sơ bệnh án phù hợp.");
        }

        logger.LogInformation("Successfully retrieved {Count} medical records for patient {PatientId} ({PatientName}) in hospital {HospitalId}",
            result.MedicalRecords.Count,
            result.PatientId,
            result.FullName,
            hospitalId);

        return new Response<GetMedicalRecordsByPatientsResponse>(result);
    }
}