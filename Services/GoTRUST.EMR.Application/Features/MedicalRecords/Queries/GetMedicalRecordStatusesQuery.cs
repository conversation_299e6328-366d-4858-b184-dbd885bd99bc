using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Queries;

public record GetMedicalRecordStatusesRequest() : IRequest<Response<GetMedicalRecordStatusesResponse>>;

public record GetMedicalRecordStatusesResponse(
    List<MedicalRecordStatusDto> Statuses
);

public record MedicalRecordStatusDto(
    int Value,
    string Name,
    string DisplayName
);

public class GetMedicalRecordStatusesHandler(
    ILogger<GetMedicalRecordStatusesHandler> logger)
    : IRequestHandler<GetMedicalRecordStatusesRequest, Response<GetMedicalRecordStatusesResponse>>
{
    public async Task<Response<GetMedicalRecordStatusesResponse>> Handle(GetMedicalRecordStatusesRequest request, CancellationToken cancellationToken)
    {
        var statuses = new List<MedicalRecordStatusDto>
        {
            new((int)MedicalRecordStatus.Draft, nameof(MedicalRecordStatus.Draft), "<PERSON>ưu khoa"),
            new((int)MedicalRecordStatus.Pending, nameof(MedicalRecordStatus.Pending), "<PERSON><PERSON> tiếp nhận"),
            new((int)MedicalRecordStatus.Approved, nameof(MedicalRecordStatus.Approved), "Đã lưu kho"),
            new((int)MedicalRecordStatus.Rejected, nameof(MedicalRecordStatus.Rejected), "Đã trả khoa"),
            new((int)MedicalRecordStatus.Processing, nameof(MedicalRecordStatus.Processing), "Đang đồng bộ")
        };

        logger.LogInformation("Retrieved {Count} medical record statuses", statuses.Count);

        var response = new GetMedicalRecordStatusesResponse(statuses);
        return new Response<GetMedicalRecordStatusesResponse>(response);
    }
}
