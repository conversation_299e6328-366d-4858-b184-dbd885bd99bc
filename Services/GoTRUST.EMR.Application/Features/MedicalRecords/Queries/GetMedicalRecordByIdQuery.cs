using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using Mapster;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Queries;

public record GetMedicalRecordByIdRequest(Guid Id) : IRequest<Response<GetMedicalRecordByIdResponse>>;

public record GetMedicalRecordByIdResponse(
    Guid Id,
    string RecordCode,
    string AdmissionCode,
    string Status,
    DateTime AdmissionDate,
    DateTime? DischargeDate,
    string EncounterType,
    Guid PatientId,
    string PatientCode,
    string PatientFullName,
    string PatientSex,
    int PatientAge,
    string PatientAvatarUrl,
    string PatientStatus,
    int TotalDocumentCount,
    FilmDetailDto? FilmDetails,
    List<DocumentDepartmentGroupDto> DocumentDepartmentGroups
);

public record DocumentDepartmentGroupDto(
    Guid DepartmentId,
    string DepartmentName,
    List<DocumentTemplateGroupDto> Templates
);

public record DocumentTemplateGroupDto(
    Guid TemplateId,
    string TemplateName,
    List<DocumentInstanceDto> Documents
);

public record FilmDetailDto(
    int XRayCount,
    int RMICount,
    int CTCount,
    int ExternalFilmCount,
    int TotalCount
);

public record DocumentInstanceDto(
    Guid InstanceId,
    DateTime DateRecorded,
    string DataJson,
    Guid? FileId,
    string? FileName
);

public class GetMedicalRecordByIdHandler(
    IApplicationDbContext context,
    ILogger<GetMedicalRecordByIdHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<GetMedicalRecordByIdRequest, Response<GetMedicalRecordByIdResponse>>
{
    public async Task<Response<GetMedicalRecordByIdResponse>> Handle(GetMedicalRecordByIdRequest request, CancellationToken cancellationToken)
    {
        // Get HospitalId using helper method
        var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal()
                         ?? throw new UndefineHospitalInfoException();

        // Get medical record with all related data
        var medicalRecord = await context.MedicalRecords.AsNoTracking()
            .Include(mr => mr.Patient)
            .Include(mr => mr.InstanceData)
                .ThenInclude(id => id.MedicalRecordTemplate)
                .ThenInclude(id => id.Department)
            .Include(mr => mr.InstanceData)
                .ThenInclude(id => id.MedicalRecordFile)
            .FirstOrDefaultAsync(mr => mr.Id == request.Id && mr.HospitalId == hospitalId, cancellationToken)
            ?? throw new MedicalRecordNotFoundException(request.Id);

        // FilmDetailDto? filmDetails = null;
        var filmDetail = await context.FilmDetails.AsNoTracking().Where(f => f.MedicalRecordId == medicalRecord.Id).FirstOrDefaultAsync(cancellationToken);
        
        // Group theo Department -> Template -> InstanceData
        var documentDepartmentGroups = medicalRecord.InstanceData
            .GroupBy(x => new
            {
                DepartmentId = x.MedicalRecordTemplate.Department.Id,
                DepartmentName = x.MedicalRecordTemplate.Department.Name
            })
            .Select(deptGroup => new DocumentDepartmentGroupDto(
                deptGroup.Key.DepartmentId,
                deptGroup.Key.DepartmentName,
                [.. deptGroup
                    .GroupBy(x => new
                    {
                        TemplateId = x.MedicalRecordTemplate.Id,
                        TemplateName = x.MedicalRecordTemplate.TemplateName
                    })
                    .Select(templateGroup => new DocumentTemplateGroupDto(
                        templateGroup.Key.TemplateId,
                        templateGroup.Key.TemplateName,
                        [.. templateGroup
                            .Select(instance => new DocumentInstanceDto(
                                instance.Id,
                                instance.DateRecorded,
                                instance.DataJson,
                                instance.MedicalRecordFileId,
                                instance.MedicalRecordFile?.FileName
                            ))
                            .OrderByDescending(d => d.DateRecorded)]
                    ))
                    .OrderBy(t => t.TemplateName)]
            ))
            .OrderBy(d => d.DepartmentName)
            .ToList();

        var totalDocumentCount = medicalRecord.InstanceData.Count;

        // Chưa xác định được trạng thái bệnh nhân từ MedicalRecord, tạm thời sử dụng giá trị mặc định
        var patientStatus = "Đang điều trị";

        // Calculate patient age
        var patientAge = medicalRecord.Patient.DateOfBirth.CalculateAge();

        // Create film details DTO
        FilmDetailDto? filmResponse = null;
        if (filmDetail != null)
        {
            var totalFilmCount = filmDetail.XRayCount + 
                                filmDetail.RMICount + 
                                filmDetail.CTCount + 
                                filmDetail.ExternalFilmCount;
            
            filmResponse = new FilmDetailDto(
                filmDetail.XRayCount,
                filmDetail.RMICount,
                filmDetail.CTCount,
                filmDetail.ExternalFilmCount,
                totalFilmCount
            );
        }

        logger.LogInformation("Retrieved detailed MedicalRecord with Id {MedicalRecordId} for hospital {HospitalId}, found {DocumentCount} documents in {TemplateCount} template groups", 
            medicalRecord.Id, hospitalId, totalDocumentCount, documentDepartmentGroups.Count);

        var response = medicalRecord.Adapt<GetMedicalRecordByIdResponse>() with {
            PatientAge = patientAge,
            PatientStatus = patientStatus,
            TotalDocumentCount = totalDocumentCount,
            FilmDetails = filmResponse,
            DocumentDepartmentGroups = documentDepartmentGroups
        };

        return new Response<GetMedicalRecordByIdResponse>(response);
    }
}