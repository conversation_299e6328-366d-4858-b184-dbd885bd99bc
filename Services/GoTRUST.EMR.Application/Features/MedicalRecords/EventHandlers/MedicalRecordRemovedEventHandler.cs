using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.EventHandlers
{
    public class MedicalRecordRemovedEventHandler(IHttpContextAccessor httpContextAccessor, IApplicationDbContext dbContext, ILogger<MedicalRecordRemovedEventHandler> logger) : INotificationHandler<MedicalRecordRemovedEvent>
    {
        public async Task Handle(MedicalRecordRemovedEvent notification, CancellationToken cancellationToken)
        {
            // Log the event handling
            logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

            var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

            // Example of updating the status history
            var history = new MedicalRecordStatusHistory
            {
                Id = Guid.NewGuid(),
                MedicalRecordId = notification.MedicalRecord.Id,
                Status = MedicalRecordHistoryStatus.Removed,
                ChangedByUserId = userId,
                ChangeReason = "Medical record removed",
                ChangeDate = DateTime.UtcNow
            };

            // Assuming you have a method to save the history to the database
            dbContext.MedicalRecordStatusHistories.Add(history);
            // var result = await dbContext.SaveChangesAsync(cancellationToken);

            // if (result == 0)
            // {
            //     throw new DatabaseSaveChangesException();
            // }

            // Log the successful creation of the status history use logger
            logger.LogInformation("MedicalRecordStatusHistory created for MedicalRecord {MedicalRecordId} with status {Status}",
                notification.MedicalRecord.Id, MedicalRecordHistoryStatus.Removed);
        }
    }
}