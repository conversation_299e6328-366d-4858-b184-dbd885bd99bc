using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.EventHandlers;

public class MedicalRecordUpdatedEventHandler(IHttpContextAccessor httpContextAccessor, IApplicationDbContext dbContext, ILogger<MedicalRecordUpdatedEventHandler> logger) : INotificationHandler<MedicalRecordUpdatedEvent>
{
    public async Task Handle(MedicalRecordUpdatedEvent notification, CancellationToken cancellationToken)
    {
        // Log the event handling
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        // Example of updating the status history
        var history = new MedicalRecordStatusHistory
        {
            Id = Guid.NewGuid(),
            MedicalRecordId = notification.MedicalRecord.Id,
            Status = MedicalRecordHistoryStatus.Processing,
            ChangedByUserId = userId,
            ChangeReason = "Medical record updated",
            ChangeDate = DateTime.UtcNow
        };

        // Assuming you have a method to save the history to the database
        await dbContext.MedicalRecordStatusHistories.AddAsync(history, cancellationToken);
        // var result = await dbContext.SaveChangesAsync(cancellationToken);

        // if (result == 0)
        // {
        //     throw new DatabaseSaveChangesException();
        // }

        // Log the successful creation of the status history use logger
        logger.LogInformation("MedicalRecordStatusHistory created for MedicalRecord {MedicalRecordId} with status {Status}",
            notification.MedicalRecord.Id, MedicalRecordHistoryStatus.Processing);
    }
}