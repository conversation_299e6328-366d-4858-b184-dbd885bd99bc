﻿using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.EventHandlers;

public class MedicalRecordCreatedEventHandler(
    IApplicationDbContext dbContext,
    ILogger<MedicalRecordCreatedEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<MedicalRecordCreatedEvent>
{
    public async Task Handle(MedicalRecordCreatedEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

        var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var history = new MedicalRecordStatusHistory()
        {
            Id = Guid.NewGuid(),
            MedicalRecordId = notification.MedicalRecord.Id,
            Status = MedicalRecordHistoryStatus.Pending,
            ChangedByUserId = userId,
            ChangeReason = "Medical record created",
            ChangeDate = DateTime.UtcNow
        };

        await dbContext.MedicalRecordStatusHistories.AddAsync(history, cancellationToken);

        logger.LogInformation("MedicalRecordStatusHistory created for MedicalRecord {MedicalRecordId} with status {Status}",
            notification.MedicalRecord.Id, MedicalRecordHistoryStatus.Pending);
    }
}