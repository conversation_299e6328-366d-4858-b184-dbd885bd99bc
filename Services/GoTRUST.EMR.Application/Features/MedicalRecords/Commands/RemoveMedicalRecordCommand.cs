using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Commands
{
    public record RemoveMedicalRecordRequest(Guid Id) : ICommand<Response<object>>;

    public class RemoveMedicalRecordRequestValidator : AbstractValidator<RemoveMedicalRecordRequest>
    {
        public RemoveMedicalRecordRequestValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty()
                .WithMessage("ID hồ sơ bệnh án không được để trống");
        }
    }

    public class RemoveMedicalRecordHandler(IApplicationDbContext dbContext, IHttpContextAccessor httpContextAccessor) : ICommandHandler<RemoveMedicalRecordRequest, Response<object>>
    {
        public async Task<Response<object>> Handle(RemoveMedicalRecordRequest request, CancellationToken cancellationToken)
        {
            var hospitalId = httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
                ?? throw new UnauthorizedAccessException("Không xác định được thông tin bệnh viện.");

            // 2. Validate Hospital exists
            var hospital = await dbContext.Hospitals.AsNoTracking()
                    .FirstOrDefaultAsync(h => h.Id == hospitalId, cancellationToken)
                ?? throw new HospitalNotFoundException(hospitalId);

            var medicalRecord = await dbContext.MedicalRecords
                    .AsNoTracking()
                    .FirstOrDefaultAsync(mr => mr.Id == request.Id, cancellationToken)
                ?? throw new MedicalRecordNotFoundException(request.Id);

            if (medicalRecord.HospitalId != hospitalId)
                throw new NoAccessHospitalException();

            // Kiểm tra xem hồ sơ bệnh án có đang được mượn không
            var borrowRequest = await dbContext.BorrowRequests
                .AsNoTracking()
                .FirstOrDefaultAsync(br => br.MedicalRecordId == request.Id
                    && br.HospitalId == hospitalId
                    && br.ApprovalStatus == BorrowRequestStatus.Approved
                    && !br.ReturnedDate.HasValue,
                cancellationToken);

            if (borrowRequest != null)
            {
                throw new MedicalRecordBorrowingException("Không thể xóa hồ sơ bệnh án đang được mượn.");
            }

            dbContext.MedicalRecords.Remove(medicalRecord);
            medicalRecord.AddDomainEvent(new MedicalRecordRemovedEvent(medicalRecord));
            var result = await dbContext.SaveChangesAsync(cancellationToken);
            if (result == 0)
            {
                throw new DatabaseSaveChangesException();
            }

            return new Response<object>(new { status = true });
        }
    }
}