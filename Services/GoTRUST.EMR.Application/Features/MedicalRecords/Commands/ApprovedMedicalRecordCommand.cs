using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Commands
{
    public class ApprovedMedicalRecordRequest : ICommand<Response<ApprovedMedicalRecordResponse>>
    {
        public Guid Id { get; init; }
    }

    public class ApprovedMedicalRecordResponse
    {
        public bool IsApproved { get; set; }
    }

    public class ApprovedMedicalRecordValidator : AbstractValidator<ApprovedMedicalRecordRequest>
    {
        public ApprovedMedicalRecordValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty()
                .WithMessage("ID không được để trống");
        }
    }

    public class ApprovedMedicalRecordHandler(IApplicationDbContext _dbContext) : ICommandHandler<ApprovedMedicalRecordRequest, Response<ApprovedMedicalRecordResponse>>
    {
        public async Task<Response<ApprovedMedicalRecordResponse>> Handle(ApprovedMedicalRecordRequest request, CancellationToken cancellationToken)
        {
             var medicalRecord = await _dbContext.MedicalRecords.FindAsync([request.Id], cancellationToken: cancellationToken) ?? throw new NotFoundException($"Không tìm thấy hồ sơ bệnh án với ID {request.Id}");
            if (medicalRecord.Status != MedicalRecordStatus.Pending)
            {
                throw new MedicalRecordApproveException();
            }

            medicalRecord.Status = MedicalRecordStatus.Approved; // Assuming there is a Status property to update
            _dbContext.MedicalRecords.Update(medicalRecord);
            int result = await _dbContext.SaveChangesAsync(cancellationToken);

            if (result <= 0)
            {
                throw new DatabaseSaveChangesException();
            }
            // Handle the command
            return new Response<ApprovedMedicalRecordResponse>
            (
                new ApprovedMedicalRecordResponse
                {
                    IsApproved = true
                },
                "Hồ sơ bệnh án đã được chấp nhận thành công."
            );
        }
    }
}