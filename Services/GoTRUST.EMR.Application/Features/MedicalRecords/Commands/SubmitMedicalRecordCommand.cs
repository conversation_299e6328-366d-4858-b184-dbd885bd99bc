using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Commands
{
    public class SubmitMedicalRecordRequest : ICommand<Response<SubmitMedicalRecordResponse>>
    {
        public Guid Id { get; init; }
    }

    public class SubmitMedicalRecordResponse
    {
        public bool IsSubmitted { get; set; }
    }

    public class SubmitMedicalRecordValidator : AbstractValidator<SubmitMedicalRecordRequest>
    {
        public SubmitMedicalRecordValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty()
                .WithMessage("ID không được để trống");
        }
    }

    public class SubmitMedicalRecordHandler(IApplicationDbContext _dbContext) : ICommandHandler<SubmitMedicalRecordRequest, Response<SubmitMedicalRecordResponse>>
    {
        public async Task<Response<SubmitMedicalRecordResponse>> Handle(SubmitMedicalRecordRequest request, CancellationToken cancellationToken)
        {
            var medicalRecord = await _dbContext.MedicalRecords.FindAsync([request.Id], cancellationToken: cancellationToken) ?? throw new NotFoundException($"Không tìm thấy hồ sơ bệnh án với ID {request.Id}");
            if (medicalRecord.Status != MedicalRecordStatus.Draft)
            {
                throw new MedicalRecordSubmitException();
            }

            medicalRecord.Status = MedicalRecordStatus.Pending;
            _dbContext.MedicalRecords.Update(medicalRecord);
            int result = await _dbContext.SaveChangesAsync(cancellationToken);

            if (result <= 0)
            {
                throw new DatabaseSaveChangesException();
            }

            return new Response<SubmitMedicalRecordResponse>
            (
                new SubmitMedicalRecordResponse
                {
                    IsSubmitted = true
                },
                "Hồ sơ bệnh án đã được gửi duyệt thành công."
            );
        }
    }
}
