using System.Text.Json;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;
using Mapster;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Commands;

public record UpdateMedicalRecordRequest(
    Guid Id,
    UpdateMedicalRecordDto UpdateMedicalRecordDto
) : ICommand<Response<UpdateMedicalRecordResponse>>;

public record UpdateMedicalRecordDto(
    string AdmissionCode,
    string RecordCode,
    string StorageNumber,
    DateTime AdmissionDate,
    DateTime? DischargeDate,
    int FilmCount,
    bool HasBHYT,
    Guid DepartmentId,
    string Note,
    string Status,
    int StorageYears,
    Guid MedicalRecordInstanceDataId,
    string DataJson
);

public record UpdateMedicalRecordResponse(Guid Id);

public class UpdateMedicalRecordRequestValidator : AbstractValidator<UpdateMedicalRecordRequest>
{
    public UpdateMedicalRecordRequestValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID bệnh án không được để trống");

        RuleFor(x => x.UpdateMedicalRecordDto.AdmissionCode)
            .NotEmpty()
            .WithMessage("Mã tiếp nhận không được để trống")
            .MaximumLength(50)
            .WithMessage("Mã tiếp nhận không được vượt quá 50 ký tự");

        RuleFor(x => x.UpdateMedicalRecordDto.RecordCode)
            .NotEmpty()
            .WithMessage("Mã hồ sơ bệnh án không được để trống")
            .MaximumLength(50)
            .WithMessage("Mã hồ sơ bệnh án không được vượt quá 50 ký tự");

        RuleFor(x => x.UpdateMedicalRecordDto.StorageNumber)
            .NotEmpty()
            .WithMessage("Số lưu trữ không được để trống")
            .MaximumLength(50)
            .WithMessage("Số lưu trữ không được vượt quá 50 ký tự");

        RuleFor(x => x.UpdateMedicalRecordDto.AdmissionDate)
            .NotEmpty()
            .WithMessage("Ngày nhập viện không được để trống")
            .LessThanOrEqualTo(DateTime.Now)
            .WithMessage("Ngày nhập viện không được lớn hơn ngày hiện tại");

        RuleFor(x => x.UpdateMedicalRecordDto.DepartmentId)
            .NotEmpty()
            .WithMessage("ID khoa không được để trống");

        RuleFor(x => x.UpdateMedicalRecordDto.FilmCount)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Số lượng phim phải lớn hơn hoặc bằng 0");

        RuleFor(x => x.UpdateMedicalRecordDto.StorageYears)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Số năm lưu trữ phải lớn hơn hoặc bằng 0");

        RuleFor(x => x.UpdateMedicalRecordDto.Status)
            .NotEmpty()
            .WithMessage("Trạng thái không được để trống")
            .MaximumLength(100)
            .WithMessage("Trạng thái không được vượt quá 100 ký tự");

        RuleFor(x => x.UpdateMedicalRecordDto.Note)
            .MaximumLength(1000)
            .WithMessage("Ghi chú không được vượt quá 1000 ký tự");

        RuleFor(x => x.UpdateMedicalRecordDto.MedicalRecordInstanceDataId)
            .NotEmpty()
            .WithMessage("ID template bệnh án không được để trống");
    }
}

public class UpdateMedicalRecordHandler(
    IApplicationDbContext context,
    ILogger<UpdateMedicalRecordHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<UpdateMedicalRecordRequest, Response<UpdateMedicalRecordResponse>>
{
    public async Task<Response<UpdateMedicalRecordResponse>> Handle(UpdateMedicalRecordRequest request, CancellationToken cancellationToken)
    {
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        var hospitalId = httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");

        var medicalRecord = await context.MedicalRecords
                .FirstOrDefaultAsync(mr => mr.Id == request.Id
                    && mr.HospitalId == hospitalId
                    && mr.Status != MedicalRecordStatus.Approved
                    && mr.Status != MedicalRecordStatus.Processing
                    , cancellationToken)
                ?? throw new MedicalRecordNotFoundException(request.Id);

        var department = await context.Departments.AsNoTracking()
                .FirstOrDefaultAsync(d => d.Id == request.UpdateMedicalRecordDto.DepartmentId && d.HospitalId == hospitalId, cancellationToken)
            ?? throw new DepartmentNotFoundException(request.UpdateMedicalRecordDto.DepartmentId);

        var existingRecordCode = await context.MedicalRecords.AsNoTracking()
            .FirstOrDefaultAsync(mr => mr.RecordCode == request.UpdateMedicalRecordDto.RecordCode
                && mr.HospitalId == hospitalId
                && mr.Id != request.Id,
            cancellationToken);

        if (existingRecordCode != null)
            throw new MedicalRecordCodeAlreadyExistsException(request.UpdateMedicalRecordDto.RecordCode);

        var existingAdmissionCode = await context.MedicalRecords.AsNoTracking()
            .FirstOrDefaultAsync(mr => mr.AdmissionCode == request.UpdateMedicalRecordDto.AdmissionCode
                && mr.HospitalId == hospitalId
                && mr.Id != request.Id,
            cancellationToken);

        if (existingAdmissionCode != null)
            throw new MedicalRecordAdmissionCodeAlreadyExistsException(request.UpdateMedicalRecordDto.AdmissionCode);

        // Find and update existing instance data
        var existingInstanceData = await context.MedicalRecordInstanceDatas
            .FirstOrDefaultAsync(x => x.Id == request.UpdateMedicalRecordDto.MedicalRecordInstanceDataId, cancellationToken)
            ?? throw new InvalidOperationException($"Không tìm thấy instance data với ID: {request.UpdateMedicalRecordDto.MedicalRecordInstanceDataId}");

        var medicalRecordTemplate = await context.MedicalRecordTemplates
            .FirstOrDefaultAsync(x => x.Id == existingInstanceData.MedicalRecordTemplateId, cancellationToken)
            ?? throw new InvalidOperationException($"Không tìm thấy template bệnh án với ID: {existingInstanceData.MedicalRecordTemplateId}");

        _ = MappingMedicalRecordHelper.DeserializeToMedicalRecord(request.UpdateMedicalRecordDto.DataJson, medicalRecordTemplate.Code) ??
            throw new InvalidDataException("Dữ liệu JSON không hợp lệ hoặc không chứa thông tin bệnh án.");

        medicalRecord = request.UpdateMedicalRecordDto.Adapt(medicalRecord);
        medicalRecord.UpdatedAt = DateTime.UtcNow;
        medicalRecord.UpdatedBy = currentUser;

        existingInstanceData.DataJson = request.UpdateMedicalRecordDto.DataJson;
        existingInstanceData.DateRecorded = DateTime.UtcNow;
        existingInstanceData.UpdatedAt = DateTime.UtcNow;
        existingInstanceData.UpdatedBy = currentUser;

        // Update medical record
        context.MedicalRecords.Update(medicalRecord);
        context.MedicalRecordInstanceDatas.Update(existingInstanceData);
        medicalRecord.AddDomainEvent(new MedicalRecordUpdatedEvent(medicalRecord));
        var result = await context.SaveChangesAsync(cancellationToken);

        if (result == 0)
        {
            throw new DatabaseSaveChangesException();
        }

        var response = new UpdateMedicalRecordResponse(medicalRecord.Id);
        return new Response<UpdateMedicalRecordResponse>(response);
    }
}
