using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Commands
{
    public class RejectedMedicalRecordRequest : ICommand<Response<RejectedMedicalRecordResponse>>
    {
        public Guid Id { get; init; }
        public string? Reason { get; init; }
    }

    public class RejectedMedicalRecordResponse
    {
        public bool IsRejected { get; set; }
    }

    public class RejectedMedicalRecordValidator : AbstractValidator<RejectedMedicalRecordRequest>
    {
        public RejectedMedicalRecordValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty()
                .WithMessage("ID không được để trống");
        }
    }

    public class RejectedMedicalRecordHandler(IApplicationDbContext _dbContext) : ICommandHandler<RejectedMedicalRecordRequest, Response<RejectedMedicalRecordResponse>>
    {
        public async Task<Response<RejectedMedicalRecordResponse>> Handle(RejectedMedicalRecordRequest request, CancellationToken cancellationToken)
        {
            var medicalRecord = await _dbContext.MedicalRecords.FindAsync([request.Id], cancellationToken: cancellationToken) ?? throw new NotFoundException($"Không tìm thấy hồ sơ bệnh án với ID {request.Id}");
            if (medicalRecord.Status != MedicalRecordStatus.Pending)
            {
                throw new MedicalRecordRejectException();
            }

            medicalRecord.Status = MedicalRecordStatus.Rejected;

            _dbContext.MedicalRecords.Update(medicalRecord);
            int result = await _dbContext.SaveChangesAsync(cancellationToken);

            if (result <= 0)
            {
                throw new DatabaseSaveChangesException();
            }
            // Handle the command
            return new Response<RejectedMedicalRecordResponse>
            (
                new RejectedMedicalRecordResponse
                {
                    IsRejected = true
                },
                "Hồ sơ bệnh án đã bị từ chối thành công."
);
        }
    }
}