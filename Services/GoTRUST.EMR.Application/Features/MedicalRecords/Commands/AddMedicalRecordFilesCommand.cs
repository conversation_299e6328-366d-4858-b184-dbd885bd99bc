using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.Interface;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Features.Upload.Commands;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Application.MedicalRecordTemplates;
using Mapster;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Commands;

public record AddMedicalRecordFilesRequest(
    Guid MedicalRecordId,
    List<AddMedicalRecordFileItem> Items
) : ICommand<Response<AddMedicalRecordFilesResponse>>;

public record AddMedicalRecordFileItem(
    Guid MedicalRecordTemplateId,
    string DataJson
);

public record AddMedicalRecordFilesResponse(
    Guid MedicalRecordId,
    List<MedicalRecordInstanceDataResponse> MedicalRecordInstanceDatas
);

public class AddMedicalRecordFilesRequestValidator : AbstractValidator<AddMedicalRecordFilesRequest>
{
    public AddMedicalRecordFilesRequestValidator()
    {
        RuleFor(x => x.MedicalRecordId)
            .NotEmpty()
            .WithMessage("Medical Record ID không được để trống");

        RuleFor(x => x.Items)
            .NotEmpty()
            .WithMessage("Danh sách items không được để trống")
            .Must(items => items.Count <= 10)
            .WithMessage("Không thể thêm quá 10 files trong một lần");

        RuleForEach(x => x.Items).ChildRules(item =>
        {
            item.RuleFor(x => x.MedicalRecordTemplateId)
                .NotEmpty()
                .WithMessage("Medical Record Template ID không được để trống");

            item.RuleFor(x => x.DataJson)
                .NotEmpty()
                .WithMessage("Data JSON không được để trống")
                .Must(BeValidJson)
                .WithMessage("Data JSON không hợp lệ");
        });
    }

    private static bool BeValidJson(string json)
    {
        try
        {
            System.Text.Json.JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }
}

public class AddMedicalRecordFilesHandler(
    IApplicationDbContext context,
    ILogger<AddMedicalRecordFilesHandler> logger,
    IFileConverterService fileConverterService,
    IHttpContextAccessor httpContextAccessor,
    IMediator mediator)
    : ICommandHandler<AddMedicalRecordFilesRequest, Response<AddMedicalRecordFilesResponse>>
{
    public async Task<Response<AddMedicalRecordFilesResponse>> Handle(AddMedicalRecordFilesRequest request,
        CancellationToken cancellationToken)
    {
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        // Get HospitalId using helper method
        var hospitalId = httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
                         ?? throw new UndefineHospitalInfoException();

        // Validate medical record exists and belongs to hospital
        var medicalRecord = await context.MedicalRecords.AsNoTracking()
                                .FirstOrDefaultAsync(mr => mr.Id == request.MedicalRecordId
                                                          && mr.HospitalId == hospitalId,
                                    cancellationToken)
                            ?? throw new MedicalRecordNotFoundException(request.MedicalRecordId);

        var filesToAdd = new List<MedicalRecordFile>();
        var instanceDataToAdd = new List<MedicalRecordInstanceData>();

        // Process each item
        foreach (var item in request.Items)
        {
            // Validate template exists and belongs to hospital
            var medicalRecordTemplate = await context.MedicalRecordTemplates.AsNoTracking()
                                            .FirstOrDefaultAsync(t => t.Id == item.MedicalRecordTemplateId
                                                                      && t.HospitalId == hospitalId,
                                                cancellationToken)
                                        ?? throw new MedicalRecordTemplateNotFoundException(item.MedicalRecordTemplateId);

            // Validate and process data JSON
            object medicalRecordData =
                MappingMedicalRecordHelper.DeserializeToMedicalRecord(item.DataJson, medicalRecordTemplate.Code) ??
                throw new InvalidDataException($"Dữ liệu JSON không hợp lệ cho template {medicalRecordTemplate.TemplateName}.");

            // Get HTML content from template
            string htmlContent = medicalRecordTemplate.TemplateContent
                                 ?? throw new MedicalRecordTemplateNotFoundException(hospitalId);

            var replacements = BuildContentMedicalRecord.BuildReplacementDictionary(medicalRecordData);
            var processedHtmlContent = BuildContentMedicalRecord.ReplacePlaceholders(htmlContent, replacements);

            // Generate unique filename
            var fileName = $"{medicalRecord.Id}_{medicalRecordTemplate.TemplateName.RemoveVietnameseCharacter()}_{Guid.NewGuid()}.pdf";

            // Convert HTML to PDF bytes
            var (success, pdfBytes, errorMessage) = await fileConverterService.ConvertHtmlToPdfBytesAsync(
                processedHtmlContent, fileName, cancellationToken: cancellationToken);

            if (!success || pdfBytes == null)
            {
                logger.LogError("Failed to convert HTML to PDF for template {TemplateId}: {ErrorMessage}",
                    item.MedicalRecordTemplateId, errorMessage);
                throw new InternalServerException($"Không thể chuyển đổi HTML sang PDF cho template {medicalRecordTemplate.TemplateName}: {errorMessage}");
            }

            // Encrypt file
            var encryptFileCommand = new EncryptFileBytesCommand(pdfBytes, fileName);
            var encryptFileResponse = await mediator.Send(encryptFileCommand, cancellationToken);

            if (encryptFileResponse.Data == null)
                throw new BadRequestException($"Không thể mã hóa file cho template {medicalRecordTemplate.TemplateName}: {encryptFileResponse.Message}");

            // Create entities
            var medicalRecordFile = new MedicalRecordFile()
            {
                Id = Guid.NewGuid(),
                FileName = fileName,
                FilePath = encryptFileResponse.Data.FileUrl,
                FileType = "pdf",
                FileSize = pdfBytes.Length,
                IsEncrypted = true,
                InitialVector = encryptFileResponse.Data.IV,
                WatermarkEnabled = false,
                UploadedAt = DateTime.UtcNow,
                MedicalRecordId = medicalRecord.Id,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = currentUser
            };

            var medicalRecordInstanceData = new MedicalRecordInstanceData
            {
                Id = Guid.NewGuid(),
                MedicalRecordId = medicalRecord.Id,
                MedicalRecordTemplateId = item.MedicalRecordTemplateId,
                MedicalRecordFileId = medicalRecordFile.Id,
                DataJson = item.DataJson,
                DateRecorded = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = currentUser
            };

            filesToAdd.Add(medicalRecordFile);
            instanceDataToAdd.Add(medicalRecordInstanceData);
        }

        // Add all entities to context
        context.MedicalRecordFiles.AddRange(filesToAdd);
        context.MedicalRecordInstanceDatas.AddRange(instanceDataToAdd);

        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
            throw new DatabaseSaveChangesException();

        var instanceDataResponses = instanceDataToAdd.Adapt<List<MedicalRecordInstanceDataResponse>>();

        logger.LogInformation(
            "Added {FileCount} files to MedicalRecord {MedicalRecordId} by {User}",
            instanceDataResponses.Count,
            medicalRecord.Id,
            currentUser);

        return new Response<AddMedicalRecordFilesResponse>(new AddMedicalRecordFilesResponse(medicalRecord.Id, instanceDataResponses));
    }
}
