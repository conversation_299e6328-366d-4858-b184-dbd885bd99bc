using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Events;
using GoTRUST.EMR.Application.MedicalRecordTemplates;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Features.Upload.Commands;
using BuildingBlocks.Common.Interface;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Commands;

public record MedicalRecordTemplateData(
    Guid MedicalRecordTemplateId,
    string DataJson
);

public record CreateMedicalRecordRequest(
    Guid PatientId,
    string AdmissionCode,
    string RecordCode,
    string StorageNumber,
    DateTime AdmissionDate,
    DateTime? DischargeDate,
    int FilmCount,
    bool HasBHYT,
    Guid DepartmentId,
    string Note,
    string Status,
    int StorageYears,
    List<MedicalRecordTemplateData> MedicalRecordTemplates
) : ICommand<Response<CreateMedicalRecordResponse>>;

public record CreateMedicalRecordResponse(
    Guid Id,
    List<MedicalRecordInstanceDataResponse> MedicalRecordInstanceDatas
);

public record MedicalRecordInstanceDataResponse(
    Guid Id,
    Guid? MedicalRecordTemplateId,
    Guid? MedicalRecordFileId
);

public class CreateMedicalRecordRequestValidator : AbstractValidator<CreateMedicalRecordRequest>
{
    public CreateMedicalRecordRequestValidator()
    {
        RuleFor(x => x.PatientId)
            .NotEmpty();

        RuleFor(x => x.AdmissionCode)
            .NotEmpty()
            .MaximumLength(50);

        RuleFor(x => x.RecordCode)
            .NotEmpty()
            .MaximumLength(50);

        RuleFor(x => x.StorageNumber)
            .NotEmpty()
            .MaximumLength(50);

        RuleFor(x => x.AdmissionDate)
            .NotEmpty()
            .LessThanOrEqualTo(DateTime.Now);

        RuleFor(x => x.DepartmentId)
            .NotEmpty();

        RuleFor(x => x.FilmCount)
            .GreaterThanOrEqualTo(0);

        RuleFor(x => x.StorageYears)
            .GreaterThanOrEqualTo(0);

        RuleFor(x => x.Status)
            .NotEmpty()
            .MaximumLength(100);

        RuleFor(x => x.Note)
            .MaximumLength(1000);

        RuleFor(x => x.MedicalRecordTemplates)
            .NotEmpty()
            .WithMessage("Cần ít nhất một mẫu phiếu bệnh án");

        RuleForEach(x => x.MedicalRecordTemplates)
            .ChildRules(template =>
            {
                template.RuleFor(t => t.MedicalRecordTemplateId)
                    .NotEmpty();

                template.RuleFor(t => t.DataJson)
                    .NotEmpty();
            });
    }
}

public class CreateMedicalRecordHandler(
    IApplicationDbContext context,
    ILogger<CreateMedicalRecordHandler> logger,
    IFileConverterService fileConverterService,
    IHttpContextAccessor httpContextAccessor,
    IMediator mediator)
    : ICommandHandler<CreateMedicalRecordRequest, Response<CreateMedicalRecordResponse>>
{
    public async Task<Response<CreateMedicalRecordResponse>> Handle(CreateMedicalRecordRequest request,
        CancellationToken cancellationToken)
    {
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        // Get HospitalId using helper method
        var hospitalId = httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
                         ?? throw new UndefineHospitalInfoException();

        var patient = await context.Patients.AsNoTracking()
                          .FirstOrDefaultAsync(p => p.Id == request.PatientId
                                                    && p.HospitalId == hospitalId,
                              cancellationToken)
                      ?? throw new PatientNotFoundException(request.PatientId);

        var department = await context.Departments.AsNoTracking()
                             .FirstOrDefaultAsync(d => d.Id == request.DepartmentId
                                                       && d.HospitalId == hospitalId,
                                 cancellationToken)
                         ?? throw new DepartmentNotFoundException(request.DepartmentId);

        var existingRecordCode = await context.MedicalRecords.AsNoTracking()
            .FirstOrDefaultAsync(mr => mr.RecordCode == request.RecordCode
                                       && mr.HospitalId == hospitalId,
                cancellationToken);

        if (existingRecordCode != null)
            throw new MedicalRecordCodeAlreadyExistsException(request.RecordCode);

        var existing = await context.MedicalRecords.AsNoTracking()
            .FirstOrDefaultAsync(mr => mr.StorageNumber == request.StorageNumber
                                       && mr.HospitalId == hospitalId,
                cancellationToken);

        if (existing != null)
            throw new MedicalRecordStorageNumberAlreadyExistsException(request.StorageNumber);

        var existingAdmissionCode = await context.MedicalRecords.AsNoTracking()
            .FirstOrDefaultAsync(mr => mr.AdmissionCode == request.AdmissionCode
                                       && mr.HospitalId == hospitalId,
                cancellationToken);

        if (existingAdmissionCode != null)
            throw new MedicalRecordAdmissionCodeAlreadyExistsException(request.AdmissionCode);

        // Validate all templates exist and belong to hospital
        var templateIds = request.MedicalRecordTemplates.Select(t => t.MedicalRecordTemplateId).ToList();
        var medicalRecordTemplates = await context.MedicalRecordTemplates.AsNoTracking()
            .Where(t => templateIds.Contains(t.Id) && t.HospitalId == hospitalId)
            .ToListAsync(cancellationToken);

        // Create MedicalRecord entity
        var medicalRecord = request.Adapt<MedicalRecord>();
        medicalRecord.Id = Guid.NewGuid();
        medicalRecord.HospitalId = hospitalId;
        medicalRecord.CreatedAt = DateTime.UtcNow;
        medicalRecord.CreatedBy = currentUser;

        // Combine template names for RecordName
        var templateNames = medicalRecordTemplates.Select(t => t.TemplateName).ToList();
        medicalRecord.RecordName = string.Join(", ", templateNames);

        var medicalRecordFiles = new List<MedicalRecordFile>();
        var medicalRecordInstanceDatas = new List<MedicalRecordInstanceData>();

        // Process each template
        foreach (var templateData in request.MedicalRecordTemplates)
        {
            var template = medicalRecordTemplates.First(t => t.Id == templateData.MedicalRecordTemplateId);

            object medicalRecordData =
                MappingMedicalRecordHelper.DeserializeToMedicalRecord(templateData.DataJson, template.Code) ??
                throw new InvalidDataException($"Dữ liệu JSON không hợp lệ cho template {template.TemplateName}.");

            // Get html content from template
            string htmlContent = template.TemplateContent
                                 ?? throw new MedicalRecordTemplateNotFoundException(templateData.MedicalRecordTemplateId);

            var replacements = BuildContentMedicalRecord.BuildReplacementDictionary(medicalRecordData);
            var processedHtmlContent = BuildContentMedicalRecord.ReplacePlaceholders(htmlContent, replacements);

            var fileName = $"{medicalRecord.Id}_{template.TemplateName.RemoveVietnameseCharacter()}_{Guid.NewGuid()}.pdf";

            // Convert HTML to PDF bytes using the converter service
            var (success, pdfBytes, errorMessage) = await fileConverterService.ConvertHtmlToPdfBytesAsync(processedHtmlContent, fileName, cancellationToken: cancellationToken);

            if (!success || pdfBytes == null)
            {
                logger.LogError("Failed to convert HTML to PDF for template {TemplateName}: {ErrorMessage}", template.TemplateName, errorMessage);
                throw new InternalServerException($"Không thể chuyển đổi HTML sang PDF cho template {template.TemplateName}: {errorMessage}");
            }

            // Call through to EncryptFileBytesCommand
            var encryptFileCommand = new EncryptFileBytesCommand(pdfBytes, fileName);
            var encryptFileResponse = await mediator.Send(encryptFileCommand, cancellationToken);

            if (encryptFileResponse.Data == null)
                throw new BadRequestException($"Không thể mã hóa file cho template {template.TemplateName}: {encryptFileResponse.Message}");

            var medicalRecordFile = new MedicalRecordFile()
            {
                Id = Guid.NewGuid(),
                FileName = fileName,
                FilePath = encryptFileResponse.Data.FileUrl,
                FileType = "pdf",
                Type = template.TemplateType,
                FileSize = pdfBytes.Length,
                IsEncrypted = true,
                InitialVector = encryptFileResponse.Data.IV,
                WatermarkEnabled = false,
                UploadedAt = DateTime.UtcNow,
                MedicalRecordId = medicalRecord.Id
            };

            var medicalRecordInstanceData = new MedicalRecordInstanceData
            {
                Id = Guid.NewGuid(),
                MedicalRecordId = medicalRecord.Id,
                MedicalRecordTemplateId = templateData.MedicalRecordTemplateId,
                DataJson = templateData.DataJson,
                DateRecorded = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = currentUser,
                MedicalRecordFileId = medicalRecordFile.Id
            };

            medicalRecordFiles.Add(medicalRecordFile);
            medicalRecordInstanceDatas.Add(medicalRecordInstanceData);
        }

        // Add all entities to context
        context.MedicalRecords.Add(medicalRecord);
        context.MedicalRecordFiles.AddRange(medicalRecordFiles);
        context.MedicalRecordInstanceDatas.AddRange(medicalRecordInstanceDatas);

        medicalRecord.AddDomainEvent(new MedicalRecordCreatedEvent(medicalRecord));
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
            throw new DatabaseSaveChangesException();

        logger.LogInformation(
                "Created MedicalRecord with Id {MedicalRecordId}, RecordCode {RecordCode} for Patient {PatientId} with {TemplateCount} templates processed by {User}",
                medicalRecord.Id,
                medicalRecord.RecordCode,
                medicalRecord.PatientId,
                request.MedicalRecordTemplates.Count,
                currentUser);

        var MedicalRecordInstanceDataResponse = medicalRecordInstanceDatas.Adapt<List<MedicalRecordInstanceDataResponse>>();

        return new Response<CreateMedicalRecordResponse>(
            new CreateMedicalRecordResponse(medicalRecord.Id, MedicalRecordInstanceDataResponse));
    }
}