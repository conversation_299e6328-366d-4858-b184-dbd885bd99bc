using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Marten;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Commands
{
    public class UpdateFilmRequest : ICommand<Response<string>>
    {
        public Guid Id { get; init; }
        public int XRayCount { get; init; }
        public int RMICount { get; init; }
        public int CTCount { get; init; }
        public int ExternalFilmCount { get; init; }
        public int Quantity => XRayCount + RMICount + CTCount + ExternalFilmCount;
    }
    public class UpdateFilmRequestValidator : AbstractValidator<UpdateFilmRequest>
    {
        public UpdateFilmRequestValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty()
                .WithMessage("ID không được để trống");

            RuleFor(x => x.Quantity)
                .GreaterThan(0)
                .WithMessage("Số lượng phải lớn hơn 0");
        }
    }

    public class UpdateFilmHandler : ICommandHandler<UpdateFilmRequest, Response<string>>
    {
        private readonly IApplicationDbContext _dbContext;

        public UpdateFilmHandler(IApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<Response<string>> Handle(UpdateFilmRequest request, CancellationToken cancellationToken)
        {
            var medicalRecord = await _dbContext.MedicalRecords
                    .Include(x => x.FilmDetails)
                    .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken: cancellationToken) ?? throw new NotFoundException($"Không tìm thấy hồ sơ bệnh án với ID {request.Id}");
            medicalRecord.FilmCount = request.Quantity;
            if (medicalRecord.FilmDetails == null)
            {
                medicalRecord.FilmDetails = new FilmDetail
                {
                    MedicalRecordId = medicalRecord.Id,
                    XRayCount = 0,
                    RMICount = 0,
                    CTCount = 0,
                    ExternalFilmCount = 0
                };
                _dbContext.FilmDetails.Add(medicalRecord.FilmDetails);
            }
            else
            {
                medicalRecord.FilmDetails!.XRayCount = request.XRayCount;
                medicalRecord.FilmDetails.RMICount = request.RMICount;
                medicalRecord.FilmDetails.CTCount = request.CTCount;
                medicalRecord.FilmDetails.ExternalFilmCount = request.ExternalFilmCount;
                _dbContext.FilmDetails.Update(medicalRecord.FilmDetails);
            }           

            _dbContext.MedicalRecords.Update(medicalRecord);
            int result = await _dbContext.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                throw new DatabaseSaveChangesException();
            }
            return new Response<string>("Medical record updated successfully.");
        }
    }
}