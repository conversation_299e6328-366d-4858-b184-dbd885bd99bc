using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.Helper;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System.Net.Mime;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Commands;

public record DownloadMedicalRecordFileRequest(
    Guid FileId
) : ICommand<Response<FileStreamResult>>;

public class DownloadMedicalRecordFileRequestValidator : AbstractValidator<DownloadMedicalRecordFileRequest>
{
    public DownloadMedicalRecordFileRequestValidator()
    {
        RuleFor(x => x.FileId)
            .NotEmpty()
            .WithMessage("File ID không được để trống");
    }
}

public class DownloadMedicalRecordFileHandler(
    IApplicationDbContext context,
    ILogger<DownloadMedicalRecordFileHandler> logger,
    IHttpContextAccessor httpContextAccessor,
    IConfiguration configuration,
    IHttpClientFactory httpClientFactory)
    : ICommandHandler<DownloadMedicalRecordFileRequest, Response<FileStreamResult>>
{
    public async Task<Response<FileStreamResult>> Handle(DownloadMedicalRecordFileRequest request,
        CancellationToken cancellationToken)
    {
        // Get HospitalId using helper method
        var hospitalId = httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal()
                         ?? throw new UndefineHospitalInfoException();

        // Get current user
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

        // Validate medical record file exists and user has access
        var medicalRecordFile = await context.MedicalRecordFiles.AsNoTracking()
                                    .Include(f => f.MedicalRecord)
                                    .FirstOrDefaultAsync(f => f.Id == request.FileId,
                                        cancellationToken)
                                ?? throw new NotFoundException($"Không tìm thấy file với ID: {request.FileId}");



        try
        {
            // Download file from the stored path
            byte[] fileContent;
            using var httpClient = httpClientFactory.CreateClient();
            var response = await httpClient.GetAsync(medicalRecordFile.FilePath, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Failed to download file from path: {FilePath}. Status: {StatusCode}", 
                    medicalRecordFile.FilePath, response.StatusCode);
                throw new NotFoundException($"Không thể tải file từ đường dẫn: {response.ReasonPhrase}");
            }

            fileContent = await response.Content.ReadAsByteArrayAsync(cancellationToken);

            // If file is encrypted, decrypt it
            if (medicalRecordFile.IsEncrypted)
            {
                if (string.IsNullOrEmpty(medicalRecordFile.InitialVector))
                {
                    throw new BadRequestException("File đã mã hóa nhưng không có thông tin IV để giải mã.");
                }

                // Decrypt using PdfEncryptionHelper with stream
                using var encryptedStream = new MemoryStream(fileContent);
                using var decryptedStream = await PdfEncryptionHelper.DecryptStreamAsync(
                    encryptedStream,
                    configuration.GetValue<string>("File_SercretKey") ?? string.Empty,
                    medicalRecordFile.InitialVector);

                if (decryptedStream == null)
                {
                    logger.LogError("Failed to decrypt file content for FileId: {FileId}", request.FileId);
                    throw new BadRequestException("Không thể giải mã file.");
                }

                fileContent = decryptedStream.ToArray();
            }

            // Create memory stream from file content
            var memoryStream = new MemoryStream(fileContent)
            {
                Position = 0
            };

            // Create FileStreamResult
            var fileStreamResult = new FileStreamResult(memoryStream, MediaTypeNames.Application.Octet)
            {
                FileDownloadName = medicalRecordFile.FileName
            };

            logger.LogInformation("Successfully downloaded medical record file: {FileName} for user: {User}", 
                medicalRecordFile.FileName, currentUser);

            return new Response<FileStreamResult>(fileStreamResult);
        }
        catch (HttpRequestException ex)
        {
            logger.LogError(ex, "HTTP error occurred while downloading file: {FileId}", request.FileId);
            throw new BadRequestException("Có lỗi xảy ra khi tải file từ hệ thống lưu trữ.", ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while downloading medical record file: {FileId}", request.FileId);
            throw new BadRequestException("Có lỗi xảy ra trong quá trình tải file.", ex.Message);
        }
    }
}
