﻿using BuildingBlocks.Abstractions;
using Mapster;

namespace GoTRUST.EMR.Application.Features.TonGiaos.Queries;

public record GetTonGiaosRequest() : IQuery<Response<List<GetTonGiaosResponse>>>;

public record GetTonGiaosResponse(
    string Id,
    string? Name
);

public class GetTonGiaosRequestValidator : AbstractValidator<GetTonGiaosRequest>
{
    public GetTonGiaosRequestValidator()
    {
    }
}

public class GetTonGiaosHandler(
    IApplicationDbContext _dbContext
) : IQueryHandler<GetTonGiaosRequest, Response<List<GetTonGiaosResponse>>>
{
    public async Task<Response<List<GetTonGiaosResponse>>> Handle(GetTonGiaosRequest req, CancellationToken cancellationToken)
    {
        var query = _dbContext.TonGiaos.AsQueryable();

        var TonGiaos = await query
            .ProjectToType<GetTonGiaosResponse>()
            .ToListAsync(cancellationToken);

        return new Response<List<GetTonGiaosResponse>>(TonGiaos);
    }
}