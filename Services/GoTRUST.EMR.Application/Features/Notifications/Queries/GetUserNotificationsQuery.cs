﻿using GoTRUST.EMR.Application.Helpers;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.Notifications.Queries;

public record GetUserNotificationsQuery : PaginationRequest, IQuery<PaginationResponse<GetUserNotificationsResponse>>
{
    public bool? IsSortByStatus { get; set; }
    public NotificationType? FilterType { get; set; }
}

public record GetUserNotificationsResponse(
    Guid NotificationId,
    string Title,
    string Message,
    NotificationType Type,
    bool IsRead,
    DateTime? ReadAt,
    DateTime? CreatedAt
);

public class GetUserNotificationsQueryHandler(
    IApplicationDbContext dbContext,
    IHttpContextAccessor httpContextAccessor
) : IQueryHandler<GetUserNotificationsQuery, PaginationResponse<GetUserNotificationsResponse>>
{
    public async Task<PaginationResponse<GetUserNotificationsResponse>> Handle(GetUserNotificationsQuery request, CancellationToken cancellationToken)
    {
        var httpContext = httpContextAccessor.HttpContext;
        if (httpContext?.User == null)
            throw new UserNotFoundException();

        var userId = httpContext.User.RetrieveUserIdFromPrincipal();
        var query = dbContext.NotificationRecipients
            .Where(nr => nr.UserId == userId)
            .Select(nr => new
            {
                nr.NotificationId,
                nr.Notification.Title,
                nr.Notification.Message,
                nr.Notification.Type,
                nr.IsRead,
                nr.ReadAt,
                nr.Notification.CreatedAt
            });
        if (request.FilterType.HasValue)
            query = query.Where(x => x.Type == request.FilterType.Value);
        if (request.IsSortByStatus.HasValue && request.IsSortByStatus.Value)
            query = query.OrderBy(x => x.IsRead).ThenByDescending(x => x.CreatedAt);
        else
            query = query.OrderByDescending(x => x.CreatedAt);
        var totalCount = await query.CountAsync(cancellationToken);
        var result = await query
            .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
            .Take(request.PageSize!.Value)
            .ToListAsync(cancellationToken);
        var mapped = result.Select(x => new GetUserNotificationsResponse(
            x.NotificationId,
            x.Title,
            x.Message,
            x.Type,
            x.IsRead,
            x.ReadAt,
            x.CreatedAt
        )).ToList();
        return new PaginationResponse<GetUserNotificationsResponse>(
            request.PageIndex!.Value,
            request.PageSize!.Value,
            totalCount,
            mapped
        );
    }
}