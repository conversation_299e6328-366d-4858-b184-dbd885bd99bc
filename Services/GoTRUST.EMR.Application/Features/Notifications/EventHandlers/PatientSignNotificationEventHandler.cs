using BuildingBlocks.Common.PushNotification.Services;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Notifications.EventHandlers;

public class PatientSignNotificationEventHandler(
    PushNotificationService pushNotificationService,
    ILogger<PatientSignNotificationEventHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : INotificationHandler<PatientSignNotificationEvent>
{
    public async Task Handle(PatientSignNotificationEvent notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Domain Event handled: {DomainEvent} for Patient {PatientCode}", 
            notification.GetType().Name, notification.Patient.PatientCode);

        var patient = notification.Patient;

        if (string.IsNullOrEmpty(patient.CitizenId))
        {
            logger.LogWarning("Patient {PatientCode} does not have CitizenId, cannot send notification", 
                patient.PatientCode);
            return;
        }

        try
        {
            logger.LogInformation("Sending sign notification to patient {PatientCode} with CitizenId: {CitizenId}", 
                patient.PatientCode, patient.CitizenId);

            // Gửi notification đến mobile app
            var notificationSent = await pushNotificationService.SendNotificationAsync(patient.CitizenId);

            if (notificationSent)
            {
                logger.LogInformation("Successfully sent sign notification to patient {PatientCode} with CitizenId: {CitizenId}", 
                    patient.PatientCode, patient.CitizenId);
            }
            else
            {
                logger.LogWarning("Failed to send sign notification to patient {PatientCode} with CitizenId: {CitizenId}", 
                    patient.PatientCode, patient.CitizenId);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending sign notification to patient {PatientCode} with CitizenId: {CitizenId}", 
                patient.PatientCode, patient.CitizenId);
        }
    }
}
