using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.Notifications.Commands;

public record CreateSystemNotificationRequest(
    string Title,
    string Message,
    List<string> RoleFunctionIds
) : ICommand<Response<CreateSystemNotificationResponse>>;

public record CreateSystemNotificationResponse(
    Guid NotificationId,
    int RecipientCount
);

public class CreateSystemNotificationRequestValidator : AbstractValidator<CreateSystemNotificationRequest>
{
    public CreateSystemNotificationRequestValidator()
    {
        RuleFor(x => x.Title)
            .NotEmpty()
            .WithMessage("Tiêu đề không được để trống")
            .MaximumLength(255)
            .WithMessage("Tiêu đề không được vượt quá 255 ký tự");

        RuleFor(x => x.Message)
            .NotEmpty()
            .WithMessage("Nội dung không được để trống");
    }
}

public class CreateSystemNotificationHandler(
    IApplicationDbContext context,
    ILogger<CreateSystemNotificationHandler> logger,
    IHttpContextAccessor httpContextAccessor,
    UserManager<User> userManager,
    RoleManager<Role> roleManager)
    : ICommandHandler<CreateSystemNotificationRequest, Response<CreateSystemNotificationResponse>>
{
    public async Task<Response<CreateSystemNotificationResponse>> Handle(CreateSystemNotificationRequest request, CancellationToken cancellationToken)
    {
        var httpContext = httpContextAccessor.HttpContext;
        if (httpContext?.User == null)
            throw new UserNotFoundException();

        var currentUser = httpContext.User?.Identity?.Name ?? "System";
        var hospitalId = httpContext.User?.RetrieveHospitalIdFromPrincipal()
            ?? throw new UnauthorizedAccessException("Không xác định được thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");

        List<RoleFunction> roleFunctions;
        if (request.RoleFunctionIds == null || request.RoleFunctionIds.Count == 0)
        {
            roleFunctions = await context.RoleFunctions
                .AsNoTracking()
                .Include(x => x.Function)
                .Include(x => x.Role)
                .Where(x => x.Function != null && x.PermissionType != null)
                .ToListAsync(cancellationToken);
        }
        else
        {
            var roleFunctionCodes = request.RoleFunctionIds;
            roleFunctions = await context.RoleFunctions
                .AsNoTracking()
                .Include(x => x.Function)
                .Include(x => x.Role)
                .Where(x => x.Function != null && x.PermissionType != null && roleFunctionCodes.Contains(x.Function.Code + x.PermissionType))
                .ToListAsync(cancellationToken);
        }

        if (roleFunctions.Count == 0)
            throw new BadRequestException("Không tìm thấy RoleFunction nào phù hợp");

        var roleIds = roleFunctions.Select(rf => rf.RoleId.ToString()).Distinct().ToList();
        var processedRoleFunctionCodes = roleFunctions.Select(rf => rf.Function.Code + rf.PermissionType).Distinct().ToList();

        var roles = await roleManager.Roles.Where(r => roleIds.Contains(r.Id.ToString())).ToListAsync(cancellationToken);
        var roleNames = roles.Select(r => r.Name).ToList();

        var allUsers = await userManager.Users.Where(u => u.HospitalId == hospitalId).ToListAsync(cancellationToken);
        var users = new List<User>();
        foreach (var user in allUsers)
        {
            var userRoles = await userManager.GetRolesAsync(user);
            if (userRoles.Any(roleNames.Contains))
            {
                users.Add(user);
            }
        }

        if (users.Count == 0)
            throw new BadRequestException("Không tìm thấy người dùng nào cho các RoleFunction này trong bệnh viện này");

        var notificationId = Guid.NewGuid();
        var notification = new Notification
        {
            Id = notificationId,
            Title = request.Title,
            Message = request.Message,
            Type = NotificationType.System,
            RoleFunctionIds = processedRoleFunctionCodes,
            NotificationRecipients = [.. users.Select(u => new NotificationRecipient
            {
                NotificationId = notificationId,
                UserId = u.Id,
                IsRead = false
            })]
        };

        context.Notifications.Add(notification);
        notification.AddDomainEvent(new SystemNotificationCreateEvent(notification));

        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
            throw new DatabaseSaveChangesException();

        logger.LogInformation("Đã tạo thông báo hệ thống {NotificationId} cho {RecipientCount} người dùng bởi {User}",
            notification.Id, users.Count, currentUser);

        return new Response<CreateSystemNotificationResponse>(new CreateSystemNotificationResponse(notification.Id, users.Count));
    }
}
