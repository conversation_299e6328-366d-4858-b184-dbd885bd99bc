﻿using BuildingBlocks.Abstractions;
using Mapster;

namespace GoTRUST.EMR.Application.Features.DanTocs.Queries;

public record GetDanTocsRequest() : IQuery<Response<List<GetDanTocsResponse>>>;

public record GetDanTocsResponse(
    string Id,
    string? Name
);

public class GetDanTocsRequestValidator : AbstractValidator<GetDanTocsRequest>
{
    public GetDanTocsRequestValidator()
    {
    }
}

public class GetDanTocsHandler(
    IApplicationDbContext _dbContext
) : IQueryHandler<GetDanTocsRequest, Response<List<GetDanTocsResponse>>>
{
    public async Task<Response<List<GetDanTocsResponse>>> Handle(GetDanTocsRequest req, CancellationToken cancellationToken)
    {
        var query = _dbContext.DanTocs.AsQueryable();

        var danTocs = await query
            .ProjectToType<GetDanTocsResponse>()
            .ToListAsync(cancellationToken);

        return new Response<List<GetDanTocsResponse>>(danTocs);
    }
}