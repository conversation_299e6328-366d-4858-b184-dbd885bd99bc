using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.Interface;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;

namespace GoTRUST.EMR.Application.Features.HospitalConfigs.Commands;

/// <summary>
/// Request để test kết nối SoftDream
/// </summary>
public record TestSoftDreamConnectionRequest(
    string? Username = null,
    string? Password = null
) : ICommand<Response<TestSoftDreamConnectionResponse>>;

/// <summary>
/// Response cho test kết nối SoftDream
/// </summary>
public record TestSoftDreamConnectionResponse(
    bool IsSuccess,
    string Message
);

/// <summary>
/// Validator cho TestSoftDreamConnectionRequest
/// </summary>
public class TestSoftDreamConnectionRequestValidator : AbstractValidator<TestSoftDreamConnectionRequest>
{
    public TestSoftDreamConnectionRequestValidator()
    {
        // Username và Password có thể null (sẽ lấy từ HospitalConfigs)
        RuleFor(x => x.Username)
            .MaximumLength(255)
            .WithMessage("Username không được vượt quá 255 ký tự")
            .When(x => !string.IsNullOrEmpty(x.Username));

        RuleFor(x => x.Password)
            .MaximumLength(255)
            .WithMessage("Password không được vượt quá 255 ký tự")
            .When(x => !string.IsNullOrEmpty(x.Password));
    }
}

/// <summary>
/// Handler để test kết nối SoftDream
/// </summary>
public class TestSoftDreamConnectionHandler(
    IApplicationDbContext context,
    ISignService signService,
    IHttpContextAccessor httpContextAccessor,
    ILogger<TestSoftDreamConnectionHandler> logger)
    : ICommandHandler<TestSoftDreamConnectionRequest, Response<TestSoftDreamConnectionResponse>>
{
    public async Task<Response<TestSoftDreamConnectionResponse>> Handle(TestSoftDreamConnectionRequest request, CancellationToken cancellationToken)
    {
        var hospitalId = (httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal())
            ?? throw new UndefineHospitalInfoException();

        // Validate Hospital exists
        var hospital = await context.Hospitals.AsNoTracking()
            .FirstOrDefaultAsync(h => h.Id == hospitalId, cancellationToken)
            ?? throw new HospitalNotFoundException(hospitalId);

        logger.LogInformation("Testing SoftDream connection for Hospital {HospitalId}", hospitalId);

        string username;
        string password;

        // Nếu request có username/password thì dùng, không thì lấy từ HospitalConfigs
        if (!string.IsNullOrEmpty(request.Username) && !string.IsNullOrEmpty(request.Password))
        {
            username = request.Username;
            password = request.Password;
            logger.LogInformation("Using credentials from request for SoftDream test");
        }
        else
        {
            // Lấy credentials từ HospitalConfigs
            var configKeys = new[] 
            { 
                HospitalConfigConstants.SoftDreamUsername, 
                HospitalConfigConstants.SoftDreamPassword 
            };

            var configs = await context.HospitalConfigs
                .Where(x => x.HospitalId == hospitalId && configKeys.Contains(x.Key))
                .ToListAsync(cancellationToken);

            var configDict = configs.ToDictionary(c => c.Key, c => c.Value, StringComparer.OrdinalIgnoreCase);

            username = configDict.GetValueOrDefault(HospitalConfigConstants.SoftDreamUsername, "");
            password = configDict.GetValueOrDefault(HospitalConfigConstants.SoftDreamPassword, "");

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                logger.LogWarning("SoftDream credentials not configured for Hospital {HospitalId}", hospitalId);
                return new Response<TestSoftDreamConnectionResponse>(
                    new TestSoftDreamConnectionResponse(
                        false,
                        "Chưa cấu hình thông tin đăng nhập SoftDream. Vui lòng cập nhật SoftDreamUsername và SoftDreamPassword trong cấu hình bệnh viện."
                    )
                );
            }

            logger.LogInformation("Using credentials from HospitalConfigs for SoftDream test");
        }

        try
        {
            // Test authentication với SoftDream
            var token = await signService.AuthenticateAsync(username, password, false);

            if (!string.IsNullOrEmpty(token))
            {
                logger.LogInformation("SoftDream authentication successful for Hospital {HospitalId}", hospitalId);
                
                return new Response<TestSoftDreamConnectionResponse>(
                    new TestSoftDreamConnectionResponse(
                        true,
                        "Kết nối SoftDream thành công"
                    )
                );
            }
            else
            {
                logger.LogWarning("SoftDream authentication returned empty token for Hospital {HospitalId}", hospitalId);
                
                return new Response<TestSoftDreamConnectionResponse>(
                    new TestSoftDreamConnectionResponse(
                        false,
                        "Kết nối SoftDream thất bại: Không nhận được token xác thực"
                    )
                );
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "SoftDream authentication failed for Hospital {HospitalId}: {ErrorMessage}", hospitalId, ex.Message);
            
            return new Response<TestSoftDreamConnectionResponse>(
                new TestSoftDreamConnectionResponse(
                    false,
                    $"Kết nối SoftDream thất bại: {ex.Message}"
                )
            );
        }
    }
}
