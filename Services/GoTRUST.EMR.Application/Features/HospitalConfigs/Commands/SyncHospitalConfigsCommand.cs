using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Events;
using GoTRUST.EMR.Application.Helpers;

namespace GoTRUST.EMR.Application.Features.HospitalConfigs.Commands;

public record SyncHospitalConfigsRequest(
    List<HospitalConfigItem> Configs
) : ICommand<Response<SyncHospitalConfigsResponse>>;

public record HospitalConfigItem(
    string Key,
    string Value,
    string? Description
);

public record SyncHospitalConfigsResponse(
    int TotalProcessed,
    int Created,
    int Updated,
    string Message
);

public class SyncHospitalConfigsRequestValidator : AbstractValidator<SyncHospitalConfigsRequest>
{
    public SyncHospitalConfigsRequestValidator()
    {
        RuleFor(x => x.Configs)
            .NotNull()
            .WithMessage("Danh sách configs không được null")
            .Must(configs => configs.Count > 0)
            .WithMessage("Danh sách configs không được rỗng");

        RuleForEach(x => x.Configs).ChildRules(config =>
        {
            config.RuleFor(c => c.Key)
                .NotEmpty()
                .WithMessage("Key không được để trống")
                .MaximumLength(255)
                .WithMessage("Key không được vượt quá 255 ký tự");

            config.RuleFor(c => c.Value)
                .NotEmpty()
                .WithMessage("Value không được để trống");

            config.RuleFor(c => c.Description)
                .MaximumLength(500)
                .WithMessage("Description không được vượt quá 500 ký tự");
        });

        // Check for duplicate keys in request
        RuleFor(x => x.Configs)
            .Must(configs => configs.Select(c => c.Key).Distinct().Count() == configs.Count)
            .WithMessage("Danh sách configs chứa key trùng lặp");
    }
}
public class SyncHospitalConfigsHandler(
    IApplicationDbContext context,
    ILogger<SyncHospitalConfigsHandler> logger,
    IHttpContextAccessor httpContextAccessor)
    : ICommandHandler<SyncHospitalConfigsRequest, Response<SyncHospitalConfigsResponse>>
{
    public async Task<Response<SyncHospitalConfigsResponse>> Handle(SyncHospitalConfigsRequest request, CancellationToken cancellationToken)
    {

        var hospitalId = (httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal())
            ?? throw new UndefineHospitalInfoException();

        // Validate Hospital exists
        var hospital = await context.Hospitals.AsNoTracking()
            .FirstOrDefaultAsync(h => h.Id == hospitalId, cancellationToken)
            ?? throw new HospitalNotFoundException(hospitalId);

        // 2. Get current user
        var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";
        var currentTime = DateTime.UtcNow;

        // 3. Get existing configs for this hospital
        var existingConfigs = await context.HospitalConfigs
            .Where(hc => hc.HospitalId == hospitalId)
            .ToListAsync(cancellationToken);

        var existingConfigsDict = existingConfigs.ToDictionary(hc => hc.Key, hc => hc);

        int created = 0;
        int updated = 0;

        // 4. Process each config item
        foreach (var configItem in request.Configs)
        {
            if (existingConfigsDict.TryGetValue(configItem.Key, out var existingConfig))
            {
                // Update existing config
                existingConfig.Value = configItem.Value;
                existingConfig.Description = configItem.Description;
                existingConfig.UpdatedAt = currentTime;
                existingConfig.UpdatedBy = currentUser;

                context.HospitalConfigs.Update(existingConfig);
                updated++;

                logger.LogInformation("Updated HospitalConfig with Key '{Key}' for Hospital {HospitalId}",
                    configItem.Key, hospitalId);
            }
            else
            {
                // Create new config
                var newConfig = new HospitalConfig
                {
                    Id = Guid.NewGuid(),
                    Key = configItem.Key,
                    Value = configItem.Value,
                    Description = configItem.Description,
                    HospitalId = hospitalId,
                    CreatedAt = currentTime,
                    CreatedBy = currentUser,
                    IsDeleted = false
                };

                context.HospitalConfigs.Add(newConfig);
                created++;

                logger.LogInformation("Created new HospitalConfig with Key '{Key}' for Hospital {HospitalId}",
                    configItem.Key, hospitalId);
            }
        }

        hospital.AddDomainEvent(new SyncHospitalConfigsEvent(hospital));
        // 5. Save changes
        var affectedRows = await context.SaveChangesAsync(cancellationToken);

        if (affectedRows == 0)
            throw new HospitalConfigSaveFailedException("đồng bộ");

        // 6. Return response
        var response = new SyncHospitalConfigsResponse(
            TotalProcessed: request.Configs.Count,
            Created: created,
            Updated: updated,
            Message: $"Đồng bộ thành công {request.Configs.Count} configs: {created} tạo mới, {updated} cập nhật"
        );

        logger.LogInformation("Synced HospitalConfigs for Hospital {HospitalId}: {Created} created, {Updated} updated",
            hospitalId, created, updated);

        return new Response<SyncHospitalConfigsResponse>(response);
    }
}
