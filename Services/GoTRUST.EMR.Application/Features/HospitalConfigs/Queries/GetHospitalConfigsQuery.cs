using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Constants;
using Mapster;

namespace GoTRUST.EMR.Application.Features.HospitalConfigs.Queries;

public record GetHospitalConfigsRequest : IQuery<Response<List<HospitalConfigDto>>>;

public record HospitalConfigDto(
    Guid Id,
    string Key,
    string Value,
    string? Description,
    Guid HospitalId,
    string HospitalName,
    DateTime? CreatedAt,
    string? CreatedBy,
    DateTime? UpdatedAt,
    string? UpdatedBy
);

public class GetHospitalConfigsHandler(
    IApplicationDbContext context,
    IHttpContextAccessor httpContextAccessor,
    ILogger<GetHospitalConfigsHandler> logger)
    : IQueryHandler<GetHospitalConfigsRequest, Response<List<HospitalConfigDto>>>
{
    public async Task<Response<List<HospitalConfigDto>>> Handle(GetHospitalConfigsRequest request, CancellationToken cancellationToken)
    {
        var hospitalId = (httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal())
            ?? throw new UndefineHospitalInfoException();

        // Validate Hospital exists
        var hospital = await context.Hospitals.AsNoTracking()
            .FirstOrDefaultAsync(h => h.Id == hospitalId, cancellationToken)
            ?? throw new HospitalNotFoundException(hospitalId);

        // Get all configs for the hospital
        var configs = await context.HospitalConfigs
            .AsNoTracking()
            .Include(hc => hc.Hospital)
            .Where(hc => hc.HospitalId == hospitalId)
            .ToListAsync(cancellationToken);

        // Build a dictionary for quick lookup
        var configDict = configs.ToDictionary(c => c.Key, StringComparer.OrdinalIgnoreCase);

        // List of all possible config keys and their default values
        var allConfigKeys = new (string Key, object DefaultValue)[]
        {
            (HospitalConfigConstants.LoginFailureLockThreshold, HospitalConfigConstants.LoginFailureLockThresholdDefaultValue),
            (HospitalConfigConstants.RequireTwoFactorAuth, HospitalConfigConstants.RequireTwoFactorAuthDefaultValue),
            (HospitalConfigConstants.NotifyUserUnusualActivity, HospitalConfigConstants.NotifyUserUnusualActivityDefaultValue),
            (HospitalConfigConstants.PasswordRequireLowercase, HospitalConfigConstants.PasswordRequireLowercaseDefaultValue),
            (HospitalConfigConstants.AdminNotificationEmail, HospitalConfigConstants.AdminNotificationEmailDefaultValue),
            (HospitalConfigConstants.ForcePasswordChangeDays, HospitalConfigConstants.ForcePasswordChangeDaysDefaultValue),
            (HospitalConfigConstants.PasswordRequireUppercase, HospitalConfigConstants.PasswordRequireUppercaseDefaultValue),
            (HospitalConfigConstants.SystemLanguage, HospitalConfigConstants.SystemLanguageDefaultValue),
            (HospitalConfigConstants.NotifySystemUnusualActivity, HospitalConfigConstants.NotifySystemUnusualActivityDefaultValue),
            (HospitalConfigConstants.PasswordRequireSpecialChar, HospitalConfigConstants.PasswordRequireSpecialCharDefaultValue),
            (HospitalConfigConstants.SessionTimeoutMinutes, HospitalConfigConstants.SessionTimeoutMinutesDefaultValue),
            (HospitalConfigConstants.HospitalAddress, HospitalConfigConstants.HospitalAddressDefaultValue),
            (HospitalConfigConstants.HospitalName, HospitalConfigConstants.HospitalNameDefaultValue),
            (HospitalConfigConstants.PasswordMinLength, HospitalConfigConstants.PasswordMinLengthDefaultValue),
            (HospitalConfigConstants.PasswordRequireDigit, HospitalConfigConstants.PasswordRequireDigitDefaultValue),
            (HospitalConfigConstants.BorrowRequestExpireTimeHours, HospitalConfigConstants.BorrowRequestExpireTimeHoursDefaultValue),
            (HospitalConfigConstants.LogoUrl, HospitalConfigConstants.LogoUrlDefaultValue),
            (HospitalConfigConstants.AdminEmail, HospitalConfigConstants.AdminEmailDefaultValue),
            (HospitalConfigConstants.NotificationTypes, string.Join(",", HospitalConfigConstants.NotificationTypesDefaultValue)),
            (HospitalConfigConstants.SoftDreamUsername, HospitalConfigConstants.SoftDreamUsernameDefaultValue),
            (HospitalConfigConstants.SoftDreamPassword, HospitalConfigConstants.SoftDreamPasswordDefaultValue),
            (HospitalConfigConstants.AutoRevokeValue, HospitalConfigConstants.AutoRevokeValueDefaultValue),
            (HospitalConfigConstants.AutoRevokeUnit, HospitalConfigConstants.AutoRevokeUnitDefaultValue)
        };

        var result = new List<HospitalConfigDto>();

        foreach (var (key, defaultValue) in allConfigKeys)
        {
            if (configDict.TryGetValue(key, out var config))
            {
                var dto = config.Adapt<HospitalConfigDto>();
                result.Add(dto);
            }
            else
            {
                result.Add(new HospitalConfigDto(
                    Guid.Empty,
                    key,
                    defaultValue?.ToString() ?? "",
                    null,
                    hospitalId,
                    hospital.Name,
                    null,
                    null,
                    null,
                    null
                ));
            }
        }

        logger.LogInformation("Retrieved {Count} hospital configs for Hospital {HospitalId}",
            result.Count, hospitalId);

        return new Response<List<HospitalConfigDto>>(result);
    }
}
