﻿using Mapster;
using Microsoft.AspNetCore.Identity;
using GoTRUST.EMR.Application.Helpers;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.BorrowerOrganizations.Queries;

public record GetBorrowerOrganizationDetailQuery(Guid BorrowerOrganizationId) : IQuery<Response<GetBorrowerOrganizationDetailResponse>>;

public record GetBorrowerOrganizationDetailResponse(
    Guid Id,
    Guid HospitalId,
    Guid? UserId,
    string FullName,
    string Code,
    string? AvatarUrl,
    string? Age,
    DateTime DateOfBirth,
    string? Gender,
    string? CitizenId,
    string? Email,
    bool IsActive,
    DateTime? CitizenIdIssueDate,
    string? MaritalStatusId,
    string? MaritalStatusName,
    string? PhoneNumber,
    string? ReligionId,
    string? ReligionName,
    string? EthnicityId,
    string? EthnicityName,
    string? Address,
    string? TemporaryAddress,
    string? OrganizationName,
    string? OfficeUnit,
    string? Position,
    string? Note,
    Guid RoleId,
    string RoleName,
    string UserType
);

public class GetBorrowerOrganizationDetailQueryHandler(
    IApplicationDbContext dbContext,
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager
) : IQueryHandler<GetBorrowerOrganizationDetailQuery, Response<GetBorrowerOrganizationDetailResponse>>
{
    public async Task<Response<GetBorrowerOrganizationDetailResponse>> Handle(GetBorrowerOrganizationDetailQuery request, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

        var user = await _userManager.FindByIdAsync(userId.ToString()) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var organization = await dbContext.BorrowerOrganizations
            .AsNoTracking()
            .Include(e => e.User)
            .Where(e => e.Id == request.BorrowerOrganizationId && e.HospitalId == hospitalId)
            .FirstOrDefaultAsync(cancellationToken) ?? throw new NotFoundException("Không tìm thấy người dùng bên ngoài.");
        Guid roleId = Guid.Empty;
        string roleName = "";

        if (organization.User != null)
        {
            var roles = await _userManager.GetRolesAsync(organization.User);
            if (roles.Count > 0)
            {
                roleName = roles[0];
                var role = await _roleManager.FindByNameAsync(roleName);
                if (role != null)
                    roleId = role.Id;
            }
        }

        string? maritalName = null;
        if (!string.IsNullOrEmpty(organization.ReligionId))
        {
            maritalName = await dbContext.TinhTrangHonNhans
                .Where(r => r.Id == organization.MaritalStatusId)
                .Select(r => r.Name)
                .FirstOrDefaultAsync(cancellationToken);
        }
        string? religionName = null;
        if (!string.IsNullOrEmpty(organization.ReligionId))
        {
            religionName = await dbContext.TonGiaos
                .Where(r => r.Id == organization.ReligionId)
                .Select(r => r.Name)
                .FirstOrDefaultAsync(cancellationToken);
        }
        string? ethnicityName = null;
        if (!string.IsNullOrEmpty(organization.EthnicityId))
        {
            ethnicityName = await dbContext.DanTocs
                .Where(e => e.Id == organization.EthnicityId)
                .Select(e => e.Name)
                .FirstOrDefaultAsync(cancellationToken);
        }

        var response = organization.Adapt<GetBorrowerOrganizationDetailResponse>() with
        {
            RoleId = roleId,
            RoleName = roleName,
            Email = organization.User?.Email ?? string.Empty,
            IsActive = organization.User?.IsActive ?? false,
            UserType = organization.User?.UserType! ?? string.Empty,
            AvatarUrl = organization.User?.AvatarUrl!,
            Age = organization.DateOfBirth?.CalculateAge().ToString() ?? null,
            ReligionName = religionName,
            EthnicityName = ethnicityName,
            MaritalStatusName = maritalName,
            FullName = organization.ContactPerson,
            OrganizationName = organization.Name
        };

        return new Response<GetBorrowerOrganizationDetailResponse>(response);
    }
}