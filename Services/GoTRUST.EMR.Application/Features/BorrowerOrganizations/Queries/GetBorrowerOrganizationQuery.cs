﻿using GoTRUST.EMR.Application.Helpers;
using Mapster;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.Application.Features.BorrowerOrganizations.Queries;

public record GetBorrowerOrganizationQuery : PaginationRequest, IQuery<PaginationResponse<GetBorrowerOrganizationResponse>>
{
    public string? SearchTerm { get; set; }
}

public record GetBorrowerOrganizationResponse(
    Guid Id,
    string EmployeeCode,
    string FullName,
    string AvatarUrl,
    string Email,
    string ReligionId,
    string EthnicityId,
    Guid RoleId,
    string RoleName,
    string UserType,
    bool IsActive
);

public class GetBorrowerOrganizationQueryHandler(
    IApplicationDbContext dbContext,
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager
) : IQueryHandler<GetBorrowerOrganizationQuery, PaginationResponse<GetBorrowerOrganizationResponse>>
{
    public async Task<PaginationResponse<GetBorrowerOrganizationResponse>> Handle(GetBorrowerOrganizationQuery request, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await _userManager.FindByIdAsync(userId.ToString()) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        // Query BorrowerOrganizations instead of Employees
        var query = dbContext.BorrowerOrganizations
            .Include(b => b.User)
            .Where(b => b.HospitalId == hospitalId);

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var term = request.SearchTerm.Trim();
            query = query.Where(b =>
                (!string.IsNullOrEmpty(b.Code) && b.Code.ToLower().Contains(term.ToLower())) ||
                (!string.IsNullOrEmpty(b.Name) && b.Name.ToLower().Contains(term.ToLower())) ||
                (!string.IsNullOrEmpty(b.Email) && b.Email.ToLower().Contains(term.ToLower()))
            );
        }

        var borrowerOrgs = await query
            .AsNoTracking()
            .Include(b => b.User)
            .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
            .Take(request.PageSize!.Value)
            .ToListAsync(cancellationToken);
        var orgCount = await query.CountAsync(cancellationToken);

        var enriched = new List<object>();

        foreach (var org in borrowerOrgs)
        {
            Guid roleId = Guid.Empty;
            string roleName = "";
            string avatarUrl = "";
            string email = org.Email;
            string userType = "";

            if (org.User != null)
            {
                avatarUrl = org.User.AvatarUrl!;
                email = org.User.Email!;
                userType = org.User.UserType ?? string.Empty;

                var roles = await _userManager.GetRolesAsync(org.User);
                if (roles.Count > 0)
                {
                    roleName = roles[0];
                    var role = await _roleManager.FindByNameAsync(roleName);
                    if (role != null)
                        roleId = role.Id;
                }
            }

            enriched.Add(new
            {
                org.Id,
                EmployeeCode = org.Code,
                FullName = org.ContactPerson,
                org.ReligionId,
                org.EthnicityId,
                AvatarUrl = avatarUrl,
                Email = email,
                RoleId = roleId,
                RoleName = roleName,
                UserType = userType,
                IsActive = org.User?.IsActive ?? false
            });
        }

        var orgList = enriched.Adapt<List<GetBorrowerOrganizationResponse>>();

        return new PaginationResponse<GetBorrowerOrganizationResponse>(request.PageIndex, request.PageSize, orgCount, orgList);
    }
}