﻿using Mapster;
using Microsoft.AspNetCore.Identity;
using GoTRUST.EMR.Application.Helpers;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.Application.Features.BorrowerOrganizations.Queries;

public record GetBorrowerOrganizationActionLogQuery : PaginationRequest, IQuery<PaginationResponse<GetBorrowerOrganizationActionLogResponse>>
{
    public Guid BorrowerOrganizationId { get; set; }
    public string? SearchTerm { get; set; }
    public DateTime? SearchDate { get; set; }
}

public record GetBorrowerOrganizationActionLogResponse(
    Guid Id,
    string ActionName,
    string ActionDetail,
    string ActionIcon,
    DateTime ActionTime,
    string Result
);

public class GetBorrowerOrganizationActionLogQueryHandler(
    IApplicationDbContext dbContext,
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager
) : IQueryHandler<GetBorrowerOrganizationActionLogQuery, PaginationResponse<GetBorrowerOrganizationActionLogResponse>>
{
    public async Task<PaginationResponse<GetBorrowerOrganizationActionLogResponse>> Handle(GetBorrowerOrganizationActionLogQuery request, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await _userManager.FindByIdAsync(userId.ToString()) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var query = dbContext.BorrowerOrganizations
            .Where(e => e.Id == request.BorrowerOrganizationId && e.HospitalId == hospitalId)
            .SelectMany(e => e.User!.UserActionLogs);

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var term = request.SearchTerm.Trim().ToLower();
            query = query.Where(log => log.ActionName.ToLower().Contains(term));
        }

        if (request.SearchDate.HasValue)
        {
            var day = request.SearchDate.Value.Date;
            query = query.Where(log => log.ActionTime.Date == day);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var result = await query
            .AsNoTracking()
            .OrderByDescending(log => log.ActionTime)
            .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
            .Take(request.PageSize!.Value)
            .ProjectToType<GetBorrowerOrganizationActionLogResponse>()
            .ToListAsync(cancellationToken);

        return new PaginationResponse<GetBorrowerOrganizationActionLogResponse>(
            request.PageIndex,
            request.PageSize,
            totalCount,
            result
        );
    }
}