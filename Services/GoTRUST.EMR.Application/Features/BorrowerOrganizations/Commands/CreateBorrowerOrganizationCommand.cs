﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Microsoft.AspNetCore.Identity;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;
using GoTRUST.EMR.Domain.Constants;
using BuildingBlocks.Common.Interface;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.BorrowerOrganizations.Commands;

public class CreateBorrowerOrganizationRequest : ICommand<Response<CreateBorrowerOrganizationResponse>>
{
    public string Name { get; init; } = string.Empty;
    public string Code { get; init; } = string.Empty;
    public string Email { get; init; } = string.Empty;
    public Guid RoleId { get; init; }
    public IFormFile? Avatar { get; init; }
}

public record CreateBorrowerOrganizationResponse(
    bool IsSuccess,
    Guid BorrowerOrganizationId,
    Guid UserId
);

public class CreateBorrowerOrganizationRequestValidator : AbstractValidator<CreateBorrowerOrganizationRequest>
{
    public CreateBorrowerOrganizationRequestValidator()
    {
        RuleFor(x => x.Name).NotEmpty().MaximumLength(255);
        RuleFor(x => x.Code).NotEmpty().MaximumLength(100);
        RuleFor(x => x.Email).NotEmpty().MaximumLength(255).EmailAddress();
        RuleFor(x => x.RoleId).NotEmpty();
    }
}

public class CreateBorrowerOrganizationHandler(
    IApplicationDbContext _dbContext,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager,
    IConfiguration _config,
    IWebHostEnvironment _env,
    IHttpContextAccessor _httpContextAccessor,
    IFileStorageService _fileStorageService
) : ICommandHandler<CreateBorrowerOrganizationRequest, Response<CreateBorrowerOrganizationResponse>>
{
    public async Task<Response<CreateBorrowerOrganizationResponse>> Handle(CreateBorrowerOrganizationRequest req, CancellationToken cancellationToken)
    {
        var currentUserId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var currentUser = await _userManager.FindByIdAsync(currentUserId.ToString()) ?? throw new UserNotFoundException();
        var hospitalId = currentUser.HospitalId;

        if (await _dbContext.BorrowerOrganizations.AnyAsync(e => e.Code == req.Code && e.HospitalId == hospitalId, cancellationToken))
            throw new BadRequestException($"Mã tổ chức đã tồn tại: {req.Code}");

        var existingUser = await _userManager.FindByEmailAsync(req.Email);
        if (existingUser != null)
            throw new BadRequestException("Email đã tồn tại");

        var user = new User
        {
            UserName = req.Email,
            Email = req.Email,
            FullName = req.Name,
            HospitalId = hospitalId,
            UserType = UserTypeConstants.External
        };

        var tempPassword = StringExtensions.GenerateRandomPassword();
        var createUserResult = await _userManager.CreateAsync(user, tempPassword);

        if (!createUserResult.Succeeded)
            throw new BadRequestException("Tạo tài khoản thất bại: " + createUserResult.Errors.FirstOrDefault()!.Description);

        if (req.Avatar != null && req.Avatar.Length > 0)
        {
            using var ms = new MemoryStream();
            await req.Avatar.CopyToAsync(ms, cancellationToken);
            ms.Position = 0;
            var fileKey = $"avatars/{user.Id}{Path.GetExtension(req.Avatar.FileName)}";
            var (success, message, fileUrl) = await _fileStorageService.UploadFileAsync(ms, fileKey);
            if (!success)
                throw new BadRequestException($"Không thể upload ảnh đại diện: {message}");
            user.AvatarUrl = fileUrl;
        }
        var role = await _roleManager.FindByIdAsync(req.RoleId.ToString()) ?? throw new NotFoundException($"Không tìm thấy quyền với Id: {req.RoleId}");

        var addRoleResult = await _userManager.AddToRoleAsync(user, role.Name!);
        if (!addRoleResult.Succeeded)
            throw new BadRequestException("Không thể thêm quyền cho tài khoản");

        var organization = new BorrowerOrganization
        {
            Name = req.Name,
            Code = req.Code,
            ContactPerson = req.Name,
            Email = req.Email,
            UserId = user.Id,
            HospitalId = (Guid)hospitalId!
        };
        _dbContext.BorrowerOrganizations.Add(organization);

        var expiresAt = DateTime.UtcNow.AddMinutes(int.TryParse(_config["UpdatePasswordExpireInMinutes"], out var minutes) ? minutes : 10080);

        var session = new ForgotPasswordSession
        {
            UserId = user.Id,
            ExpiresAt = expiresAt
        };

        _dbContext.ForgotPasswordSessions.Add(session);

        var resetLink = $"{_config["ClientUrl"]}/tao-mat-khau?session={session.Id}&user={user.UserName}";

        var templatePath = Path.Combine(_env.WebRootPath, "Templates", "Mails", "EMRForgotPassword.html");
        var template = await File.ReadAllTextAsync(templatePath, cancellationToken);
        template = template.Replace("{{AccountName}}", user.FullName ?? user.UserName);
        template = template.Replace("{{CreatePassword}}", resetLink);

        _ = MailHelper.SendAsync(
            int.TryParse(_config["EmailPort"], out int port) ? port : 587,
            _config["EmailHost"]!,
            _config["EmailPassword"]!,
            _config["EmailFromMail"]!,
            user.Email,
            _config["EmailDisplayName"]!,
            "Tài khoản tổ chức bên ngoài mới",
            template
        );

        organization.AddDomainEvent(new BorrowerOrganizationCreateEvent(organization));
        if (await _dbContext.SaveChangesAsync(cancellationToken) <= 0)
            throw new BadRequestException("Lưu dữ liệu thất bại");

        return new Response<CreateBorrowerOrganizationResponse>(
            new CreateBorrowerOrganizationResponse(
                true,
                organization.Id,
                user.Id
            )
        );
    }
}