﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Common.Interface;
using Mapster;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.BorrowerOrganizations.Commands;

public class UpdateBorrowerOrganizationRequest : ICommand<Response<UpdateBorrowerOrganizationResponse>>
{
    public Guid Id { get; init; }
    public string ContactPerson { get; init; } = string.Empty;
    public Guid? RoleId { get; init; }
    public IFormFile? Avatar { get; init; }
    public DateTime? DateOfBirth { get; init; }
    public string? Gender { get; init; }
    public string? CitizenId { get; init; }
    public DateTime? CitizenIdIssueDate { get; init; }
    public string? MaritalStatusId { get; init; }
    public string? PhoneNumber { get; init; }
    public string? ReligionId { get; init; }
    public string? EthnicityId { get; init; }
    public string? Address { get; init; }
    public string? TemporaryAddress { get; init; }
    public string? Name { get; init; }
    public string? OfficeUnit { get; init; }
    public string? Position { get; init; }
    public string? Note { get; init; }
}

public record UpdateBorrowerOrganizationResponse(
    bool IsSuccess,
    Guid OrganizationId,
    Guid UserId
);

public class UpdateBorrowerOrganizationRequestValidator : AbstractValidator<UpdateBorrowerOrganizationRequest>
{
    public UpdateBorrowerOrganizationRequestValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.ContactPerson).NotEmpty().MaximumLength(255);
    }
}
public class UpdateBorrowerOrganizationHandler(
    IApplicationDbContext _dbContext,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager,
    IFileStorageService _fileStorageService
) : ICommandHandler<UpdateBorrowerOrganizationRequest, Response<UpdateBorrowerOrganizationResponse>>
{
    public async Task<Response<UpdateBorrowerOrganizationResponse>> Handle(UpdateBorrowerOrganizationRequest req, CancellationToken cancellationToken)
    {
        var organization = await _dbContext.BorrowerOrganizations
            .FirstOrDefaultAsync(e => e.Id == req.Id, cancellationToken)
            ?? throw new NotFoundException($"Không tìm thấy BorrowerOrganization với Id: {req.Id}");

        var user = await _userManager.FindByIdAsync(organization.UserId.ToString()!)
            ?? throw new NotFoundException($"Không tìm thấy User với Id: {organization.UserId}");

        user.FullName = req.ContactPerson;
        user.UserName = user.Email;

        if (req.Avatar != null && req.Avatar.Length > 0)
        {
            using var ms = new MemoryStream();
            await req.Avatar.CopyToAsync(ms, cancellationToken);
            ms.Position = 0;
            var fileKey = $"avatars/{user.Id}{Path.GetExtension(req.Avatar.FileName)}";
            var (success, message, fileUrl) = await _fileStorageService.UploadFileAsync(ms, fileKey);
            if (!success)
                throw new BadRequestException($"Không thể upload ảnh đại diện: {message}");
            user.AvatarUrl = fileUrl;
        }

        var updateUserResult = await _userManager.UpdateAsync(user);
        if (!updateUserResult.Succeeded)
            throw new BadRequestException("Cập nhật tài khoản thất bại: " + updateUserResult.Errors.FirstOrDefault()!.Description);

        var userRoles = await _userManager.GetRolesAsync(user);
        if (req.RoleId != null)
        {
            var newRole = await _roleManager.FindByIdAsync(req.RoleId.ToString()!) ?? throw new NotFoundException($"Không tìm thấy quyền với Id: {req.RoleId}");

            if (!userRoles.Contains(newRole.Name!))
            {
                if (userRoles.Count > 0)
                {
                    var removeResult = await _userManager.RemoveFromRolesAsync(user, userRoles);
                    if (!removeResult.Succeeded)
                        throw new BadRequestException("Không thể xóa quyền cũ của tài khoản");
                }
                var addRoleResult = await _userManager.AddToRoleAsync(user, newRole.Name!);
                if (!addRoleResult.Succeeded)
                    throw new BadRequestException("Không thể thêm quyền mới cho tài khoản");
            }
        }

        DateTime? dateOfBirthUtc = req.DateOfBirth.HasValue
            ? DateTime.SpecifyKind(req.DateOfBirth.Value, DateTimeKind.Utc)
            : null;
        DateTime? citizenIdIssueDateUtc = req.CitizenIdIssueDate.HasValue
            ? DateTime.SpecifyKind(req.CitizenIdIssueDate.Value, DateTimeKind.Utc)
            : null;
        req.Adapt(organization);
        organization.DateOfBirth = dateOfBirthUtc;
        organization.CitizenIdIssueDate = citizenIdIssueDateUtc;
        _dbContext.BorrowerOrganizations.Update(organization);

        organization.AddDomainEvent(new BorrowerOrganizationUpdateEvent(organization));
        if (await _dbContext.SaveChangesAsync(cancellationToken) <= 0)
            throw new BadRequestException("Lưu dữ liệu thất bại");

        return new Response<UpdateBorrowerOrganizationResponse>(
            new UpdateBorrowerOrganizationResponse(
                true,
                organization.Id,
                user.Id
            )
        );
    }
}