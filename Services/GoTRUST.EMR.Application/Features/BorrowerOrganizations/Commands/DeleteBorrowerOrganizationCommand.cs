﻿using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Events;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.BorrowerOrganizations.Commands;

public record DeleteBorrowerOrganizationRequest(
    Guid Id
) : ICommand<Response<DeleteBorrowerOrganizationResponse>>;

public record DeleteBorrowerOrganizationResponse(
    Guid Id,
    bool IsSuccess
);

public class DeleteBorrowerOrganizationRequestValidator : AbstractValidator<DeleteBorrowerOrganizationRequest>
{
    public DeleteBorrowerOrganizationRequestValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteBorrowerOrganizationHandler(
    IApplicationDbContext _dbContext,
    UserManager<User> _userManager
) : ICommandHandler<DeleteBorrowerOrganizationRequest, Response<DeleteBorrowerOrganizationResponse>>
{
    public async Task<Response<DeleteBorrowerOrganizationResponse>> Handle(DeleteBorrowerOrganizationRequest req, CancellationToken cancellationToken)
    {
        var organization = await _dbContext.BorrowerOrganizations.FindAsync([req.Id], cancellationToken)
            ?? throw new BorrowerOrganizationNotFoundException(req.Id);
        User? user = null;
        if (organization.UserId.HasValue)
        {
            user = await _userManager.FindByIdAsync(organization.UserId.ToString()!);
        }

        _dbContext.BorrowerOrganizations.Remove(organization);

        organization.AddDomainEvent(new BorrowerOrganizationDeleteEvent(organization));
        await _dbContext.SaveChangesAsync(cancellationToken);

        if (user != null)
        {
            var result = await _userManager.DeleteAsync(user);
            if (!result.Succeeded)
                throw new Exception("Xóa User thất bại");
        }

        var response = new DeleteBorrowerOrganizationResponse(organization.Id, true);

        return new Response<DeleteBorrowerOrganizationResponse>(response);
    }
}