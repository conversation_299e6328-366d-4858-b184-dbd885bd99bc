using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.Upload.Commands
{
    public record DecryptFileCommand : ICommand<Response<string>>
    {
        public required IFormFile File { get; set; }
        public required string IV { get; set; }
    }
    public class DecryptPdfValidator : AbstractValidator<DecryptFileCommand>
    {
        public DecryptPdfValidator()
        {
            RuleFor(x => x.IV).NotEmpty().WithMessage("IV is required");
            RuleFor(x => x.File).NotNull().WithMessage("File is required");
        }
    }
    public class DecryptPdfHandler(
        ILogger<DecryptPdfHandler> logger,
        IConfiguration configuration,
        IWebHostEnvironment webHostEnvironment
    ) : ICommandHandler<DecryptFileCommand, Response<string>>
    {
        public async Task<Response<string>> Handle(DecryptFileCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Decrypt PDF file
                using var stream = await PdfEncryptionHelper.DecryptPdfIFormFileAsync(
                    request.File,
                    configuration.GetValue<string>("File_SercretKey") ?? string.Empty,
                    request.IV,
                    configuration.GetValue<string>("File_Watermark") ?? string.Empty);

                if (stream == null)
                {
                    logger.LogError("Failed to decrypt file.");
                    throw new DecryptFileErrorException();
                }
                // Use original filename
                var key = request.File.FileName;
                var pdfDirectory = Path.Combine(webHostEnvironment.WebRootPath, "files");
                var filePath = Path.Combine(pdfDirectory, key);

                // Ensure files directory exists
                Directory.CreateDirectory(pdfDirectory);

                // Check if file exists and delete it
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                // Save decrypted PDF to file
                using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.ReadWrite))
                {
                    stream.Position = 0;
                    await stream.CopyToAsync(fileStream, cancellationToken);
                }

                logger.LogInformation("Successfully decrypted and saved PDF file to: {filePath}", filePath);

                return new Response<string>(filePath);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while decrypting PDF file");
                throw new BadRequestException("Có lỗi xảy ra trong quá trình giải mã tệp tin.", ex.Message);
            }
        }
    }
}