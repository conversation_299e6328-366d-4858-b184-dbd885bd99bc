using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.Helper;
using BuildingBlocks.Common.Interface;
using BuildingBlocks.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;

namespace GoTRUST.EMR.Application.Features.Upload.Commands
{
    public record EncryptedFileResult(string FileUrl, string IV);

    public record EncryptFileCommand(IFormFile File) : ICommand<Response<EncryptedFileResult>>;

    public record EncryptFileBytesCommand(byte[] FileBytes, string FileName) : ICommand<Response<EncryptedFileResult>>;

    public class EncryptFileCommandValidator : AbstractValidator<EncryptFileCommand>
    {
        public EncryptFileCommandValidator()
        {
            RuleFor(x => x.File)
                .NotNull()
                .WithMessage("File is required.")
                .Must(file => file.Length > 0)
                .WithMessage("File cannot be empty.");
        }
    }

    public class EncryptFileBytesCommandValidator : AbstractValidator<EncryptFileBytesCommand>
    {
        public EncryptFileBytesCommandValidator()
        {
            RuleFor(x => x.FileBytes)
                .NotNull()
                .WithMessage("File bytes are required.")
                .Must(bytes => bytes.Length > 0)
                .WithMessage("File bytes cannot be empty.");

            RuleFor(x => x.FileName)
                .NotEmpty()
                .WithMessage("File name is required.");
        }
    }

    public class UploadEncryptPdfCommandHandler(
        ILogger<UploadEncryptPdfCommandHandler> logger,
        IFileStorageService fileStorageService,
        IConfiguration configuration
    ) : ICommandHandler<EncryptFileCommand, Response<EncryptedFileResult>>
    {
        public async Task<Response<EncryptedFileResult>> Handle(EncryptFileCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var encryptionResult = await PdfEncryptionHelper.EncryptPdfIFormFileAsync(request.File,
                                configuration.GetValue<string>("File_SercretKey") ?? string.Empty);

                if (encryptionResult?.EncryptedData == null)
                {
                    logger.LogError("Failed to encrypt file.");
                    throw new EncryptFileErrorException();
                }

                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                var fileNameWithoutExt = Path.GetFileNameWithoutExtension(request.File.FileName);
                var extension = Path.GetExtension(request.File.FileName);
                var key = $"{fileNameWithoutExt}_{timestamp}{extension}";

                var (success, message, fileUrl) = await fileStorageService.UploadFileAsync(encryptionResult.EncryptedData, key);

                if (!success)
                {
                    throw new UploadS3FailedException(message);
                }

                var result = new EncryptedFileResult(fileUrl, encryptionResult.IV);

                logger.LogInformation("File uploaded successfully: {FileUrl}", fileUrl);
                return new Response<EncryptedFileResult>(result, "File encrypted and uploaded successfully", "000");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing file upload.");
                throw new BadRequestException("Có lỗi xảy ra trong quá trình mã hoá tệp tin.", ex.Message);
            }

        }
    }

    public class UploadEncryptPdfBytesCommandHandler(
        ILogger<UploadEncryptPdfBytesCommandHandler> logger,
        IFileStorageService fileStorageService,
        IConfiguration configuration
    ) : ICommandHandler<EncryptFileBytesCommand, Response<EncryptedFileResult>>
    {
        public async Task<Response<EncryptedFileResult>> Handle(EncryptFileBytesCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var encryptionResult = await PdfEncryptionHelper.EncryptPdfBytesAsync(request.FileBytes,
                                configuration.GetValue<string>("File_SercretKey") ?? string.Empty);

                if (encryptionResult?.EncryptedData == null)
                {
                    logger.LogError("Failed to encrypt file.");
                    throw new EncryptFileErrorException();
                }

                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                var fileNameWithoutExt = Path.GetFileNameWithoutExtension(request.FileName);
                var extension = Path.GetExtension(request.FileName);
                var key = $"{fileNameWithoutExt}_{timestamp}{extension}";

                var (success, message, fileUrl) = await fileStorageService.UploadFileAsync(encryptionResult.EncryptedData, key);

                if (!success)
                {
                    throw new UploadS3FailedException(message);
                }

                var result = new EncryptedFileResult(fileUrl, encryptionResult.IV);

                logger.LogInformation("File uploaded successfully: {FileUrl}", fileUrl);
                return new Response<EncryptedFileResult>(result, "File encrypted and uploaded successfully", "000");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing file upload.");
                throw new BadRequestException("Có lỗi xảy ra trong quá trình mã hoá tệp tin.", ex.Message);
            }
        }
    }
}