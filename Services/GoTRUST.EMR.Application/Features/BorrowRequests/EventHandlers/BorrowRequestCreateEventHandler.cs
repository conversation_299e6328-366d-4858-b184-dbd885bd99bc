using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.BorrowRequests.EventHandlers
{
    public class BorrowRequestCreateEventHandler(IApplicationDbContext dbContext, ILogger<BorrowRequestCreateEventHandler> logger, IHttpContextAccessor httpContextAccessor) : INotificationHandler<BorrowRequestCreateEvent>
    {
        public async Task Handle(BorrowRequestCreateEvent notification, CancellationToken cancellationToken)
        {
            // Log the event handling
            logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

            var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

            var history = new MedicalRecordStatusHistory
            {
                Id = Guid.NewGuid(),
                MedicalRecordId = notification.BorrowRequest.MedicalRecordId,
                Status = MedicalRecordHistoryStatus.Pending,
                ChangedByUserId = userId,
                ChangeReason = "Medical record created",
                ChangeDate = DateTime.UtcNow
            };

            dbContext.MedicalRecordStatusHistories.Add(history);
            // var result = await dbContext.SaveChangesAsync(cancellationToken);

            // if (result == 0)
            // {
            //     throw new DatabaseSaveChangesException();
            // }

            // Log the successful creation of the status history
            logger.LogInformation("MedicalRecordStatusHistory created for MedicalRecord {MedicalRecordId} with status {Status}",
                notification.BorrowRequest.MedicalRecordId, MedicalRecordHistoryStatus.Pending);
        }
    }
}