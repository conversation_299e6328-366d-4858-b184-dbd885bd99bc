using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.BorrowRequests.EventHandlers
{
    public class BorrowRequestApproveEventHandler(IApplicationDbContext dbContext, ILogger<BorrowRequestApproveEventHandler> logger, IHttpContextAccessor httpContextAccessor) : INotificationHandler<BorrowRequestApproveEvent>
    {
        public async Task Handle(BorrowRequestApproveEvent notification, CancellationToken cancellationToken)
        {
            // Log the event handling
            logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

            var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

            var history = new MedicalRecordStatusHistory
            {
                Id = Guid.NewGuid(),
                MedicalRecordId = notification.BorrowRequest.MedicalRecordId,
                Status = notification.BorrowRequest.Purpose == "1"
                    ? MedicalRecordHistoryStatus.ReadBorrowed
                    : MedicalRecordHistoryStatus.UpdateBorrowed,
                ChangedByUserId = userId,
                ChangeReason = "Medical record approved",
                ChangeDate = DateTime.UtcNow
            };

            dbContext.MedicalRecordStatusHistories.Add(history);
            // var result = await dbContext.SaveChangesAsync(cancellationToken);

            // if (result == 0)
            // {
            //     throw new DatabaseSaveChangesException();
            // }

            // Log the successful creation of the status history
            logger.LogInformation("MedicalRecordStatusHistory created for MedicalRecord {MedicalRecordId} with status {Status}",
                notification.BorrowRequest.MedicalRecordId, notification.BorrowRequest.Purpose == "1"
                    ? MedicalRecordHistoryStatus.ReadBorrowed
                    : MedicalRecordHistoryStatus.UpdateBorrowed);
        }
    }
}