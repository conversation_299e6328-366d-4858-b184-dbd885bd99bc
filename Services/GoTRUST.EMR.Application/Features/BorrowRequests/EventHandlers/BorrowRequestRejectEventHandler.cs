using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.BorrowRequests.EventHandlers
{
    public class BorrowRequestRejectEventHandler(IApplicationDbContext dbContext, ILogger<BorrowRequestRejectEventHandler> logger, IHttpContextAccessor httpContextAccessor) : INotificationHandler<BorrowRequestRejectEvent>
    {
        public async Task Handle(BorrowRequestRejectEvent notification, CancellationToken cancellationToken)
        {
            // Log the event handling
            logger.LogInformation("Domain Event handled: {DomainEvent}", notification.GetType().Name);

            var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();

            var history = new MedicalRecordStatusHistory
            {
                Id = Guid.NewGuid(),
                MedicalRecordId = notification.BorrowRequest.MedicalRecordId,
                Status = MedicalRecordHistoryStatus.RejectBorrowed,
                ChangedByUserId = userId,
                ChangeReason = "Medical record rejected",
                ChangeDate = DateTime.UtcNow
            };

            dbContext.MedicalRecordStatusHistories.Add(history);
            // var result = await dbContext.SaveChangesAsync(cancellationToken);

            // if (result == 0)
            // {
            //     throw new DatabaseSaveChangesException();
            // }

            // Log the successful creation of the status history
            logger.LogInformation("MedicalRecordStatusHistory created for MedicalRecord {MedicalRecordId} with status {Status}",
                notification.BorrowRequest.MedicalRecordId, MedicalRecordHistoryStatus.RejectBorrowed);
        }
    }
}