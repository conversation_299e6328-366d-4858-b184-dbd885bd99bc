using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using JasperFx.Core.Reflection;

namespace GoTRUST.EMR.Application.Features.BorrowRequests.Queries
{
    public record GetBorrowRequestsRequest : PaginationWithSortRequest, IQuery<PaginationResponse<BorrowRequestResponse>>
    {
        public string? Search { get; set; }
        public string? Status { get; set; }
    };

    public class BorrowRequestResponse
    {
        public Guid Id { get; set; }
        public string PurposeId { get; set; } = string.Empty;
        public Guid MedicalRecordId { get; set; }
        public string MedicalRecordCode { get; set; } = string.Empty;
        public Guid HospitalId { get; set; }
        public string? BorrowerUserCode { get; set; }
        public string? ApprovedByUserCode { get; set; }
        public DateTime RequestedDate { get; set; }
        public DateTime? BorrowedDate { get; set; }
        public DateTime? ReturnedDate { get; set; }
        public BorrowRequestStatus ApprovalStatus { get; set; } = BorrowRequestStatus.Pending;
        public string AdmissionCode { get; set; } = string.Empty;
        public string StorageNumber { get; set; } = string.Empty;
        public string BorrowerUser { get; set; } = string.Empty;
    }

    public class GetBorrowRequestsHandler(IApplicationDbContext dbContext, IHttpContextAccessor httpContextAccessor) : IQueryHandler<GetBorrowRequestsRequest, PaginationResponse<BorrowRequestResponse>>
    {
        private readonly IApplicationDbContext _dbContext = dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

        public async Task<PaginationResponse<BorrowRequestResponse>> Handle(GetBorrowRequestsRequest request, CancellationToken cancellationToken)
        {
            var hospital_id = _httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal();
            var query = _dbContext.BorrowRequests
                        .AsNoTracking()
                        .Include(br => br.BorrowerUser)
                        .Include(br => br.MedicalRecord)
                        .Where(br => br.HospitalId == hospital_id)
                        .AsQueryable();

            // Apply search filter if provided
            if (!string.IsNullOrEmpty(request.Search))
            {
                var searchLower = request.Search.ToLower();
                query = query.Where(br =>
                    br.MedicalRecordCode.ToLower().Contains(searchLower) ||
                    br.MedicalRecord.AdmissionCode.ToLower().Contains(searchLower) ||
                    br.MedicalRecord.StorageNumber.ToLower().Contains(searchLower) ||
                    (br.BorrowerUser != null && br.BorrowerUser.FullName != null && br.BorrowerUser.FullName.ToLower().Contains(searchLower)) ||
                    (br.BorrowerUserCode != null && br.BorrowerUserCode.ToLower().Contains(searchLower)));
            }

            // Apply status filter if provided
            if (!string.IsNullOrEmpty(request.Status))
            {
                query = query.Where(br => br.ApprovalStatus.ToString() == request.Status);
            }

            // Apply sorting based on parameters
            query = request.SortBy?.ToLower() switch
            {
                "borrowstatus" => request.SortDescending == true
                    ? query.OrderByDescending(br => (int)br.ApprovalStatus)
                    : query.OrderBy(br => (int)br.ApprovalStatus),
                "purpose" => request.SortDescending == true
                    ? query.OrderByDescending(br => br.Purpose)
                    : query.OrderBy(br => br.Purpose),
                "borroweddate" => request.SortDescending == true
                    ? query.OrderByDescending(br => br.BorrowedDate)
                    : query.OrderBy(br => br.BorrowedDate),
                "returneddate" => request.SortDescending == true
                    ? query.OrderByDescending(br => br.ReturnedDate)
                    : query.OrderBy(br => br.ReturnedDate),
                "storagenumber" => request.SortDescending == true
                    ? query.OrderByDescending(br => br.MedicalRecord.StorageNumber)
                    : query.OrderBy(br => br.MedicalRecord.StorageNumber),
                _ => query.OrderByDescending(br => br.RequestedDate) // Default sort
            };
            if (request.SortBy == null)
            {
                query = query.OrderByDescending(br => br.RequestedDate); // Default sort if no SortBy provided
            }
            var totalCount = await query.CountAsync(cancellationToken);
            var borrowRequests = await query
                .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
                .Take(request.PageSize!.Value)
                .ToListAsync(cancellationToken);
            var borrowRequestDtos = borrowRequests.Select(br => new BorrowRequestResponse
                {
                    Id = br.Id,
                    PurposeId = br.Purpose.ToString(),
                    MedicalRecordId = br.MedicalRecordId,
                    MedicalRecordCode = br.MedicalRecordCode,
                    HospitalId = br.HospitalId,
                    BorrowerUserCode = br.BorrowerUserCode,
                    ApprovedByUserCode = br.ApprovedByUserCode,
                    ApprovalStatus = br.ApprovalStatus,
                    RequestedDate = br.RequestedDate,
                    BorrowedDate = br.BorrowedDate,
                    ReturnedDate = br.ReturnedDate,
                    AdmissionCode = br.MedicalRecord.AdmissionCode,
                    StorageNumber = br.MedicalRecord.StorageNumber,
                    BorrowerUser = br.BorrowerUser.FullName ?? string.Empty
                }).ToList();

            return new PaginationResponse<BorrowRequestResponse>(request.PageIndex, request.PageSize, totalCount, borrowRequestDtos);
        }
    }
}