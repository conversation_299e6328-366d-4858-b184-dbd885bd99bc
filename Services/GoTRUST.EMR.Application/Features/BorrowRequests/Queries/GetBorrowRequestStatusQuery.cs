using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Domain.Enums;
using Newtonsoft.Json;

namespace GoTRUST.EMR.Application.Features.BorrowRequests.Queries
{
    public class GetBorrowRequestStatusQuery : ICommand<Response<List<BorrowRequestStatusDto>>>
    {
    }

    public class GetBorrowRequestStatusResponse
    {
        public List<List<BorrowRequestStatusDto>> BorrowTypes { get; set; } = [];
    }
    public class GetBorrowRequestStatusHandler(ILogger<GetBorrowRequestStatusHandler> logger) : ICommandHandler<GetBorrowRequestStatusQuery, Response<List<BorrowRequestStatusDto>>>
    {
        public async Task<Response<List<BorrowRequestStatusDto>>> Handle(GetBorrowRequestStatusQuery request, CancellationToken cancellationToken)
        {
            var statuses = new List<BorrowRequestStatusDto>
            {
                new() { Id = BorrowRequestStatus.Pending.ToString(), Name = "Chờ duyệt" },
                new() { Id = BorrowRequestStatus.Approved.ToString(), Name = "Đã duyệt" },
                new() { Id = BorrowRequestStatus.Rejected.ToString(), Name = "Đã từ chối" },
                new() { Id = BorrowRequestStatus.Returned.ToString(), Name = "Đã trả" },
            };

            logger.LogInformation("Retrieved {Count} borrow request statuses", statuses.Count);
            return new Response<List<BorrowRequestStatusDto>>(statuses);
        }
    }

    public class BorrowRequestStatusDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

}