using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Newtonsoft.Json;

namespace GoTRUST.EMR.Application.Features.BorrowRequests.Queries
{
    public class GetBorrowRequestTypeQuery : ICommand<Response<List<BorrowRequestReasonDto>>>
    {
    }

    public class GetBorrowrequestTypeResponse
    {
        public List<List<BorrowRequestReasonDto>> BorrowTypes { get; set; } = [];
    }
    public class GetBorrowTypeHandler(IApplicationDbContext dbContext) : ICommandHandler<GetBorrowRequestTypeQuery, Response<List<BorrowRequestReasonDto>>>
    {
        public async Task<Response<List<BorrowRequestReasonDto>>> Handle(GetBorrowRequestTypeQuery request, CancellationToken cancellationToken)
        {
            // Simulate fetching borrow types from a data source
            var metadata = await dbContext.SystemConfigs
                .Where(x => x.Key == "BorrowRequestReasons")
                .FirstOrDefaultAsync(cancellationToken) ?? throw new NotFoundException("SystemConfig", "BorrowRequestReasons");

            var borrowTypes = JsonConvert.DeserializeObject<List<BorrowRequestReasonDto>>(metadata.Value)
                ?? throw new NotFoundException("Không tìm thấy lý do mượn");

            return new Response<List<BorrowRequestReasonDto>>(borrowTypes);
        }
    }

    public class BorrowRequestReasonDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

}