using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Application.Repositories;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.BorrowRequests.Commands
{
    public class RejectBorrowRequestCommand : ICommand<Response<RejectBorrowRequestResponse>>
    {
        public Guid BorrowRequestId { get; set; }
        public string Reason { get; set; } = string.Empty;
    }

    public class RejectBorrowRequestResponse
    {
        public Guid Id { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime RejectdAt { get; set; }
        public string RejectdBy { get; set; } = string.Empty;
    }

    public class RejectBorrowRequestCommandHandler : ICommandHandler<RejectBorrowRequestCommand, Response<RejectBorrowRequestResponse>>
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IApplicationDbContext _dbContext;
        private readonly IUserRepository _userCodeRepository;

        public RejectBorrowRequestCommandHandler(IHttpContextAccessor httpContextAccessor, IApplicationDbContext dbContext,
            IUserRepository userCodeRepository)
        {
            _httpContextAccessor = httpContextAccessor;
            _dbContext = dbContext;
            _userCodeRepository = userCodeRepository;
        }

        public async Task<Response<RejectBorrowRequestResponse>> Handle(RejectBorrowRequestCommand request, CancellationToken cancellationToken)
        {
            var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
            if (userId == null)
            {
                throw new UnauthorizedAccessException("User is not authenticated.");
            }

            var code = await _userCodeRepository.GetUserCodeEmployeeAsync(userId.Value, cancellationToken);

            var borrowRequest = await _dbContext.BorrowRequests
                .FindAsync([request.BorrowRequestId], cancellationToken) ?? throw new NotFoundException($"Không tìm thấy yêu cầu mượn với ID {request.BorrowRequestId}");
            var medicalRecord = await _dbContext.MedicalRecords
                .FindAsync([borrowRequest.MedicalRecordId], cancellationToken) ?? throw new NotFoundException($"Không tìm thấy hồ sơ bệnh án với ID {borrowRequest.MedicalRecordId}");
            if (borrowRequest.ApprovalStatus != BorrowRequestStatus.Pending)
            {
                throw new BorrowRequestRejectException();
            }
            if (medicalRecord.Status != MedicalRecordStatus.Approved)
            {
                throw new MedicalRecordBorrowException();
            }
            borrowRequest.ApprovalStatus = BorrowRequestStatus.Rejected;
            borrowRequest.ApprovedByUserId = userId;
            borrowRequest.ApprovedByUserCode = code ?? string.Empty;
            borrowRequest.ApprovalReason = request.Reason;


            _dbContext.BorrowRequests.Update(borrowRequest);
            borrowRequest.AddDomainEvent(new BorrowRequestRejectEvent(borrowRequest));
            int result = await _dbContext.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                throw new DatabaseSaveChangesException();
            }

            var response = new RejectBorrowRequestResponse
            {
                Id = borrowRequest.Id,
                Status = borrowRequest.ApprovalStatus.ToString(),
                RejectdAt = DateTime.UtcNow,
                RejectdBy = borrowRequest.ApprovedByUserId.ToString() ?? string.Empty
            };

            return new Response<RejectBorrowRequestResponse>(response);
        }
    }
}