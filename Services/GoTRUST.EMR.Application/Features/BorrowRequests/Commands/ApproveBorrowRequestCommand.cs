using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Application.Jobs;
using GoTRUST.EMR.Application.Repositories;
using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;
using Hangfire;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.BorrowRequests.Commands
{
    public class ApproveBorrowRequestCommand : ICommand<Response<ApproveBorrowRequestResponse>>
    {
        public Guid BorrowRequestId { get; set; }
    }

    public class ApproveBorrowRequestResponse
    {
        public Guid Id { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime ApprovedAt { get; set; }
        public string ApprovedBy { get; set; } = string.Empty;
    }

    public class ApproveBorrowRequestCommandHandler(IHttpContextAccessor httpContextAccessor,
            IApplicationDbContext dbContext,
            IUserRepository userCodeRepository) : ICommandHandler<ApproveBorrowRequestCommand, Response<ApproveBorrowRequestResponse>>
    {
        public async Task<Response<ApproveBorrowRequestResponse>> Handle(ApproveBorrowRequestCommand request, CancellationToken cancellationToken)
        {
            var userId = (httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal()) ?? throw new UnauthorizedAccessException("User is not authenticated.");
            var code = await userCodeRepository.GetUserCodeEmployeeAsync(userId, cancellationToken);

            var borrowRequest = await dbContext.BorrowRequests
                .FindAsync([request.BorrowRequestId], cancellationToken) ?? throw new NotFoundException($"Không tìm thấy yêu cầu mượn với ID {request.BorrowRequestId}");
            var medicalRecord = await dbContext.MedicalRecords
                .FindAsync([borrowRequest.MedicalRecordId], cancellationToken) ?? throw new NotFoundException($"Không tìm thấy hồ sơ bệnh án với ID {borrowRequest.MedicalRecordId}");
            if (borrowRequest.ApprovalStatus != BorrowRequestStatus.Pending)
            {
                throw new BorrowRequestApproveException();
            }
            if (medicalRecord.Status != MedicalRecordStatus.Approved)
            {
                throw new MedicalRecordBorrowException();
            }

            var hospitalId = (httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal())
                ?? throw new UndefineHospitalInfoException();
            
            var hospitalConfig = await dbContext.HospitalConfigs
            .AsNoTracking()
            .FirstOrDefaultAsync(
                hc => hc.HospitalId == hospitalId && hc.Key == HospitalConfigConstants.BorrowRequestExpireTimeHours,
                cancellationToken);
            var expireTime = hospitalConfig?.Value != null
                ? TimeSpan.FromHours(int.Parse(hospitalConfig.Value))
                : TimeSpan.FromHours(HospitalConfigConstants.BorrowRequestExpireTimeHoursDefaultValue);

            borrowRequest.ApprovalStatus = BorrowRequestStatus.Approved;
            borrowRequest.BorrowedDate = DateTime.UtcNow;
            borrowRequest.ReturnedDate = DateTime.UtcNow.Add(expireTime);
            borrowRequest.ApprovedByUserId = userId;
            borrowRequest.ApprovedByUserCode = code ?? string.Empty;
            if (borrowRequest.Purpose == "2")
            {
                medicalRecord.Status = MedicalRecordStatus.Processing;
                dbContext.MedicalRecords.Update(medicalRecord);
            }
            dbContext.BorrowRequests.Update(borrowRequest);
            borrowRequest.AddDomainEvent(new BorrowRequestApproveEvent(borrowRequest));
            int result = await dbContext.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                throw new DatabaseSaveChangesException();
            }

            var response = new ApproveBorrowRequestResponse
            {
                Id = borrowRequest.Id,
                Status = borrowRequest.ApprovalStatus.ToString(),
                ApprovedAt = borrowRequest.BorrowedDate.Value,
                ApprovedBy = borrowRequest.ApprovedByUserId.ToString() ?? string.Empty
            };



            BackgroundJob.Schedule<AutoExpireBorrowRequestJob>(
                job => job.ExecuteAsync(borrowRequest.Id, cancellationToken),
                expireTime
            );

            return new Response<ApproveBorrowRequestResponse>(response);
        }
    }
}