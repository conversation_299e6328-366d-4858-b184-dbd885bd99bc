using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Features.BorrowRequests.Commands
{
    public class AsyncBorrowRequestRequest : ICommand<Response<AsyncBorrowRequestResponse>>
    {
        public Guid MedicalRecordId { get; set; }
        public Guid BorrowRequestId { get; set; }
    }

    public class AsyncBorrowRequestResponse
    {
        public Guid MedicalRecordId { get; set; }
        public Guid BorrowRequestId { get; set; }
    }

    public class AsyncBorrowRequestCommandHandler : ICommandHandler<AsyncBorrowRequestRequest, Response<AsyncBorrowRequestResponse>>
    {
        private readonly IApplicationDbContext _dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AsyncBorrowRequestCommandHandler(IApplicationDbContext dbContext, IHttpContextAccessor httpContextAccessor)
        {
            _dbContext = dbContext;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<Response<AsyncBorrowRequestResponse>> Handle(AsyncBorrowRequestRequest request, CancellationToken cancellationToken)
        {
            var borrowRequest = await _dbContext.BorrowRequests
                .FindAsync([request.BorrowRequestId], cancellationToken) ?? throw new NotFoundException($"Không tìm thấy yêu cầu mượn với ID {request.BorrowRequestId}");
            var medicalRecord = await _dbContext.MedicalRecords
                .FindAsync([request.MedicalRecordId], cancellationToken) ?? throw new NotFoundException($"Không tìm thấy hồ sơ bệnh án với ID {request.MedicalRecordId}");
            var hospital_id = _httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal();
            if (borrowRequest.HospitalId != hospital_id)
            {
                throw new NoAccessHospitalException();
            }

            if (borrowRequest.ApprovalStatus != BorrowRequestStatus.Approved)
            {
                throw new BorrowRequestAsyncException();
            }

            if (medicalRecord.Status != MedicalRecordStatus.Processing)
            {
                throw new MedicalRecordProcessingException();
            }

            borrowRequest.ApprovalStatus = BorrowRequestStatus.Returned;
            borrowRequest.ReturnedDate = DateTime.UtcNow;

            medicalRecord.Status = MedicalRecordStatus.Pending;

            _dbContext.BorrowRequests.Update(borrowRequest);
            _dbContext.MedicalRecords.Update(medicalRecord);
            await _dbContext.SaveChangesAsync(cancellationToken);

            return new Response<AsyncBorrowRequestResponse>(new AsyncBorrowRequestResponse
            {
                MedicalRecordId = request.MedicalRecordId,
                BorrowRequestId = request.BorrowRequestId
            });
        }
    }
}