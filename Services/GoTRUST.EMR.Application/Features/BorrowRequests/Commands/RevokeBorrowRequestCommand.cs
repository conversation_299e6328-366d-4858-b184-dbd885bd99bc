using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.BorrowRequests.Commands
{
    public class RevokeBorrowRequestCommand : ICommand<RevokeBorrowRequestResponse>
    {
        public Guid BorrowRequestId { get; set; }
    }

    public class RevokeBorrowRequestResponse
    {
        public Guid BorrowRequestId { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime? RevokedDate { get; set; }
    }

    public class RevokeBorrowRequestValidator : AbstractValidator<RevokeBorrowRequestCommand>
    {
        public RevokeBorrowRequestValidator()
        {
            RuleFor(x => x.BorrowRequestId).NotEmpty();
        }
    }
    public class RevokeBorrowRequestCommandHandler(IApplicationDbContext dbContext) : ICommandHandler<RevokeBorrowRequestCommand, RevokeBorrowRequestResponse>
    {
        private readonly IApplicationDbContext _dbContext = dbContext;

        public async Task<RevokeBorrowRequestResponse> Handle(RevokeBorrowRequestCommand request, CancellationToken cancellationToken)
        {
            var borrowRequest = await _dbContext.BorrowRequests
                .FindAsync([request.BorrowRequestId], cancellationToken)
                ?? throw new NotFoundException($"Không tìm thấy yêu cầu mượn với ID {request.BorrowRequestId}");

            if (borrowRequest.ApprovalStatus != BorrowRequestStatus.Approved)
            {
                throw new BorrowRequestRevokeException();
            }

            borrowRequest.ApprovalStatus = BorrowRequestStatus.Returned;
            borrowRequest.ReturnedDate = DateTime.UtcNow;

            _dbContext.BorrowRequests.Update(borrowRequest);
            // Gửi thông báo đến SignalR + Ghi userlog
            borrowRequest.AddDomainEvent(new BorrowRequestRevokeEvent(borrowRequest));
            var result = await _dbContext.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                throw new DatabaseSaveChangesException();
            }

            return new RevokeBorrowRequestResponse
            {
                BorrowRequestId = borrowRequest.Id,
                Status = borrowRequest.ApprovalStatus.ToString(),
                RevokedDate = borrowRequest.ReturnedDate
            };
        }
    }
}