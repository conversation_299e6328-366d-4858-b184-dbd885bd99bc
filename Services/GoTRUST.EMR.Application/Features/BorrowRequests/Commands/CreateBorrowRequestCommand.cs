using System.Globalization;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Application.Repositories;
using GoTRUST.EMR.Domain.Enums;
using GoTRUST.EMR.Domain.Events;
using Mapster;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.BorrowRequests.Commands
{
    public record CreateBorrowRequestRequest(
        string PurposeId,
        List<Guid> MedicalRecordIds,
        string BorrowedDate, // yyyy-MM-dd HH:mm format
        string Note) : ICommand<Response<List<CreateBorrowRequestResponse>>>;

    public record CreateBorrowRequestResponse(
        Guid Id,
        string PurposeId,
        Guid MedicalRecordId,
        DateTime BorrowedDate,
        DateTime ReturnedDate,
        string Note,
        string BorrowerUserId,
        DateTime RequestedDate,
        DateTime CreatedAt,
        string CreatedBy);

    public class CreateBorrowRequestValidator : AbstractValidator<CreateBorrowRequestRequest>
    {
        public CreateBorrowRequestValidator()
        {
            RuleFor(x => x.PurposeId)
                .NotEmpty()
                .WithMessage("<PERSON>ụ<PERSON> đích mượn không được để trống");

            RuleFor(x => x.MedicalRecordIds)
                .NotNull()
                .WithMessage("Danh sách hồ sơ bệnh án không được null")
                .NotEmpty()
                .WithMessage("Phải chọn ít nhất một hồ sơ bệnh án")
                .Must(ids => ids.All(id => id != Guid.Empty))
                .WithMessage("ID hồ sơ bệnh án không hợp lệ");

            RuleFor(x => x.BorrowedDate)
                .NotEmpty()
                .WithMessage("Ngày mượn không được để trống")
                .Must(BeValidDateTime)
                .WithMessage("Ngày mượn phải có định dạng yyyy-MM-dd HH:mm")
                .Must(BeNotInPast)
                .WithMessage("Ngày mượn không được là thời gian trong quá khứ");

            RuleFor(x => x.Note)
                .MaximumLength(500)
                .WithMessage("Ghi chú không được vượt quá 500 ký tự");
        }

        private bool BeValidDateTime(string dateString)
        {
            return DateTime.TryParseExact(dateString, "yyyy-MM-dd HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None, out _);
        }

        private bool BeNotInPast(string dateString)
        {
            if (DateTime.TryParseExact(dateString, "yyyy-MM-dd HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
            {
                // parsedDate is already UTC time, so compare directly
                return parsedDate >= DateTime.UtcNow;
            }
            return true; // If parsing fails, let the format validation handle it
        }
    }

    public class CreateBorrowRequestCommandHandler(IHttpContextAccessor httpContextAccessor,
        UserManager<User> userManager,
        IApplicationDbContext dbContext,
        IUserRepository userCodeRepository) : ICommandHandler<CreateBorrowRequestRequest, Response<List<CreateBorrowRequestResponse>>>
    {
        public async Task<Response<List<CreateBorrowRequestResponse>>> Handle(CreateBorrowRequestRequest request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal(); // This should be replaced with actual user context
            var hospitalId = httpContextAccessor.HttpContext?.User.RetrieveHospitalIdFromPrincipal();
            if (userId == null || hospitalId == null)
            {
                throw new UnauthorizedAccessException("User is not authenticated or hospital ID is missing.");
            }

            var code = await userCodeRepository.GetUserCodeAsync(userId.Value, cancellationToken);

            var user = await userManager.FindByIdAsync(userId.Value.ToString())
                ?? throw new NotFoundException($"Thông tin người dùng không tồn tại với ID {userId}");

            // Tải tất cả medical records trong một truy vấn để tránh lỗi concurrency với DbContext
            var medicalRecords = await dbContext.MedicalRecords
                .AsNoTracking()
                .Where(mr => request.MedicalRecordIds.Contains(mr.Id))
                .ToListAsync(cancellationToken);

            if (medicalRecords.Count != request.MedicalRecordIds.Count)
            {
                throw new NotFoundException($"Không tìm thấy hồ sơ bệnh án với ID: {string.Join(", ", request.MedicalRecordIds.Except(medicalRecords.Select(mr => mr.Id)))}");
            }

            // Tạo borrow requests đồng thời bằng Task.WhenAll (bao gồm validation)
            var borrowRequestTasks = medicalRecords.Select(medicalRecord => Task.Run(() =>
            {
                // Kiểm tra trạng thái medical record
                if (medicalRecord.Status != MedicalRecordStatus.Approved)
                {
                    throw new MedicalRecordBorrowException();
                }

                var borrowRequest = new BorrowRequest
                {
                    Purpose = request.PurposeId,
                    MedicalRecordId = medicalRecord.Id,
                    MedicalRecordCode = medicalRecord.RecordCode ?? string.Empty,
                    Note = request.Note,
                    HospitalId = hospitalId.Value,
                    BorrowerUserId = userId,
                    BorrowerUserCode = code ?? string.Empty,
                    UserType = user.UserType ?? string.Empty,
                    RequestedDate = DateTime.UtcNow,
                    BorrowedDate = DateTime.ParseExact(request.BorrowedDate, "yyyy-MM-dd HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None),
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = userId.ToString()
                };

                borrowRequest.AddDomainEvent(new BorrowRequestCreateEvent(borrowRequest));
                return borrowRequest;
            }));

            var borrowRequests = await Task.WhenAll(borrowRequestTasks);
            
            dbContext.BorrowRequests.AddRange(borrowRequests);

            await dbContext.SaveChangesAsync(cancellationToken);

            // Tạo responses
            var responses = borrowRequests.Select(br => br.Adapt<CreateBorrowRequestResponse>()).ToList();
            return new Response<List<CreateBorrowRequestResponse>>(responses);
        }
    }
}