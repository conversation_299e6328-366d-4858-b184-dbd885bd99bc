using GoTRUST.EMR.Application.Features.EMRs.Models;
using Mapster;
using Marten;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRGiayNghiHuongBHXHCommand(CreateEMRGiayNghiHuongBHXHRequestModel GIAY_NGHI_HUONG_BHXH, string HospitalCode) : IRequest<Response<CreateEMRGiayNghiHuongBHXHResponse>>;

    public record CreateEMRGiayNghiHuongBHXHResponse(Guid Id, string MA_LK);

    public class CreateEMRGiayNghiHuongBHXHCommandValidator : AbstractValidator<CreateEMRGiayNghiHuongBHXHCommand>
    {
        public CreateEMRGiayNghiHuongBHXHCommandValidator()
        {
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.SO_CT).NotEmpty().MaximumLength(200);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.SO_SERI).NotEmpty().MaximumLength(200);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.SO_KCB).NotEmpty().MaximumLength(200);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.DON_VI).NotEmpty().MaximumLength(1024);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.MA_BHXH).NotEmpty().MaximumLength(10);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.CHAN_DOAN_RV).NotEmpty();
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.PP_DIEUTRI).NotEmpty();
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.MA_DINH_CHI_THAI).InclusiveBetween(0, 1);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.SO_NGAY_NGHI).GreaterThanOrEqualTo(0);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.TU_NGAY).NotEmpty().MaximumLength(8);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.DEN_NGAY).NotEmpty().MaximumLength(8);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.MA_TTDV).NotEmpty().MaximumLength(255);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.MA_BS).NotEmpty().MaximumLength(255);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.NGAY_CT).NotEmpty().MaximumLength(8);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.MA_THE_TAM).MaximumLength(15);
            RuleFor(x => x.GIAY_NGHI_HUONG_BHXH.MAU_SO).MaximumLength(5);
        }
    }

    public class CreateEMRGiayNghiHuongBHXHCommandHandler(
        ILogger<CreateEMRGiayNghiHuongBHXHCommandHandler> logger,
        IDocumentStore documentStore
    ) : IRequestHandler<CreateEMRGiayNghiHuongBHXHCommand, Response<CreateEMRGiayNghiHuongBHXHResponse>>
    {
        public async Task<Response<CreateEMRGiayNghiHuongBHXHResponse>> Handle(CreateEMRGiayNghiHuongBHXHCommand request, CancellationToken cancellationToken)
        {
            var giayNghiHuongBHXH = request.GIAY_NGHI_HUONG_BHXH.Adapt<EMRGiayNghiHuongBHXH>();

            var wrapper = new EMRGiayNghiHuongBHXHWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIEU_GIAYNGHIHUONGBHXH = giayNghiHuongBHXH
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRGiayNghiHuongBHXH with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRGiayNghiHuongBHXHResponse>(
                new CreateEMRGiayNghiHuongBHXHResponse(wrapper.Id, giayNghiHuongBHXH.MA_LK)
            );
        }
    }
}