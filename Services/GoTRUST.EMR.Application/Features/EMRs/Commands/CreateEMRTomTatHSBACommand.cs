using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRTomTatHSBACommand(CreateEMRTomTatHSBARequestModel TOM_TAT_HSBA, string HospitalCode) : ICommand<Response<CreateEMRTomTatHSBAResponse>>;
    public record CreateEMRTomTatHSBAResponse(Guid Id, string MA_LK);

    public class CreateEMRTomTatHSBACommandValidator : AbstractValidator<CreateEMRTomTatHSBACommand>
    {
        public CreateEMRTomTatHSBACommandValidator()
        {
            RuleFor(x => x.TOM_TAT_HSBA.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.TOM_TAT_HSBA.MA_LOAI_KCB).MaximumLength(2);
            RuleFor(x => x.TOM_TAT_HSBA.HO_TEN_CHA).MaximumLength(255);
            RuleFor(x => x.TOM_TAT_HSBA.HO_TEN_ME).MaximumLength(255);
            RuleFor(x => x.TOM_TAT_HSBA.NGUOI_GIAM_HO).MaximumLength(255);
            RuleFor(x => x.TOM_TAT_HSBA.DON_VI).MaximumLength(1024);
            RuleFor(x => x.TOM_TAT_HSBA.NGAY_VAO).NotEmpty().MaximumLength(12);
            RuleFor(x => x.TOM_TAT_HSBA.NGAY_RA).NotEmpty().MaximumLength(12);
            RuleFor(x => x.TOM_TAT_HSBA.MA_TTDV).NotEmpty().MaximumLength(255);
            RuleFor(x => x.TOM_TAT_HSBA.NGAY_CT).NotEmpty().MaximumLength(8);
            RuleFor(x => x.TOM_TAT_HSBA.MA_THE_TAM).MaximumLength(15);
            RuleFor(x => x.TOM_TAT_HSBA.NGAY_SINHCON).MaximumLength(8);
            RuleFor(x => x.TOM_TAT_HSBA.NGAY_CONCHET).MaximumLength(8);
        }
    }

    public class CreateEMRTomTatHSBACommandHandler(
        ILogger<CreateEMRTomTatHSBACommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRTomTatHSBACommand, Response<CreateEMRTomTatHSBAResponse>>
    {
        public async Task<Response<CreateEMRTomTatHSBAResponse>> Handle(CreateEMRTomTatHSBACommand request, CancellationToken cancellationToken)
        {
            var emrTomTatHSBA = request.TOM_TAT_HSBA.Adapt<EMRTomTatHSBA>();

            var wrapper = new EMRTomTatHSBAWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIEU_TOMTATHSBA = emrTomTatHSBA
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRTomTatHSBA with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRTomTatHSBAResponse>(new CreateEMRTomTatHSBAResponse(wrapper.Id, emrTomTatHSBA.MA_LK));
        }
    }
}