using GoTRUST.EMR.Application.Features.EMRs.Models;
using Mapster;
using Marten;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRGiayHenKhamLaiCommand(CreateEMRGiayHenKhamLaiRequestModel GIAY_HEN_KHAM_LAI, string HospitalCode) : IRequest<Response<CreateEMRGiayHenKhamLaiResponse>>;

    public record CreateEMRGiayHenKhamLaiResponse(Guid Id, string MA_LK);

    public class CreateEMRGiayHenKhamLaiCommandValidator : AbstractValidator<CreateEMRGiayHenKhamLaiCommand>
    {
        public CreateEMRGiayHenKhamLaiCommandValidator()
        {
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.SO_GIAYHEN_KL).MaximumLength(50);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.MA_CSKCB).MaximumLength(5);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.HO_TEN).MaximumLength(255);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.NGAY_SINH).MaximumLength(12);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.DIA_CHI).MaximumLength(1024);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.NGAY_VAO).MaximumLength(12);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.NGAY_VAO_NOI_TRU).MaximumLength(12);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.NGAY_RA).MaximumLength(12);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.NGAY_HEN_KL).MaximumLength(8);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.MA_BENH_CHINH).MaximumLength(7);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.MA_BENH_KT).MaximumLength(100);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.MA_BENH_YHCT).MaximumLength(255);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.MA_DOITUONG_KCB).MaximumLength(4);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.MA_BAC_SI).MaximumLength(255);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.MA_TTDV).MaximumLength(255);
            RuleFor(x => x.GIAY_HEN_KHAM_LAI.NGAY_CT).MaximumLength(8);
        }
    }

    public class CreateEMRGiayHenKhamLaiCommandHandler(
        ILogger<CreateEMRGiayHenKhamLaiCommandHandler> logger,
        IDocumentStore documentStore
    ) : IRequestHandler<CreateEMRGiayHenKhamLaiCommand, Response<CreateEMRGiayHenKhamLaiResponse>>
    {
        public async Task<Response<CreateEMRGiayHenKhamLaiResponse>> Handle(CreateEMRGiayHenKhamLaiCommand request, CancellationToken cancellationToken)
        {
            var giayHenKhamLai = request.GIAY_HEN_KHAM_LAI.Adapt<EMRGiayHenKhamLai>();

            var wrapper = new EMRGiayHenKhamLaiWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIEU_GIAYHENKHAMLAI = giayHenKhamLai
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRGiayHenKhamLai with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRGiayHenKhamLaiResponse>(
                new CreateEMRGiayHenKhamLaiResponse(wrapper.Id, giayHenKhamLai.MA_LK)
            );
        }
    }
}