using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
namespace GoTRUST.EMR.Application.Features.EMRs.Commands;

public record CreateEMRCommand(HoSoBenhAn HoSoBenhAn, string? HospitalId) : ICommand<Response<CreateEMRResult>>;

public record CreateEMRResult(HoSoBenhAnWrapper HoSoBenhAn);
public class CreateEMRCommandValidator : AbstractValidator<CreateEMRCommand>
{
    public CreateEMRCommandValidator()
    {
    }
}

public class CreateEMRCommandHandler(
    ILogger<CreateEMRCommandHandler> logger,
    IDocumentStore documentStore
) : ICommandHandler<CreateEMRCommand, Response<CreateEMRResult>>
{
    public async Task<Response<CreateEMRResult>> Handle(CreateEMRCommand request, CancellationToken cancellationToken)
    {
        var emrModel = request.HoSoBenhAn.Adapt<HoSoBenhAn>();
        var wrapper = new HoSoBenhAnWrapper
        {
            HospitalCode = request.HospitalId ?? string.Empty,
            HoSoBenhAn = emrModel
        };
        using var session = documentStore.LightweightSession();
        session.Store(wrapper);
        await session.SaveChangesAsync();
        logger.LogInformation("Creating EMR with HospitalId {HospitalId}: Id {Id}", request.HospitalId, wrapper.Id);

        return new Response<CreateEMRResult>(new CreateEMRResult(wrapper));
    }
}