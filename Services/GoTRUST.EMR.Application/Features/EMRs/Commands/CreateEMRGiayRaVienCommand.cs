using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRGiayRaVienCommand(CreateEMRGiayRaVienRequestModel GIAI_RAVIEN, string HospitalCode) : ICommand<Response<CreateEMRGiayRaVienResponse>>;
    public record CreateEMRGiayRaVienResponse(Guid Id, string MA_LK);

    public class CreateEMRGiayRaVienCommandValidator : AbstractValidator<CreateEMRGiayRaVienCommand>
    {
        public CreateEMRGiayRaVienCommandValidator()
        {
            RuleFor(x => x.GIAI_RAVIEN.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.GIAI_RAVIEN.SO_LUU_TRU).MaximumLength(200);
            RuleFor(x => x.GIAI_RAVIEN.MA_YTE).MaximumLength(200);
            RuleFor(x => x.GIAI_RAVIEN.MA_KHOA_RV).MaximumLength(200);
            RuleFor(x => x.GIAI_RAVIEN.NGAY_VAO).NotEmpty().MaximumLength(12);
            RuleFor(x => x.GIAI_RAVIEN.NGAY_RA).NotEmpty().MaximumLength(12);
            RuleFor(x => x.GIAI_RAVIEN.CHAN_DOAN_RV).NotEmpty().MaximumLength(1500);
            RuleFor(x => x.GIAI_RAVIEN.PP_DIEUTRI).NotEmpty();
            RuleFor(x => x.GIAI_RAVIEN.GHI_CHU).MaximumLength(1500);
            RuleFor(x => x.GIAI_RAVIEN.MA_TTDV).MaximumLength(255);
            RuleFor(x => x.GIAI_RAVIEN.MA_BS).MaximumLength(255);
            RuleFor(x => x.GIAI_RAVIEN.TEN_BS).MaximumLength(255);
            RuleFor(x => x.GIAI_RAVIEN.NGAY_CT).NotEmpty().MaximumLength(8);
            RuleFor(x => x.GIAI_RAVIEN.MA_CHA).MaximumLength(10);
            RuleFor(x => x.GIAI_RAVIEN.MA_ME).MaximumLength(10);
            RuleFor(x => x.GIAI_RAVIEN.MA_THE_TAM).MaximumLength(15);
            RuleFor(x => x.GIAI_RAVIEN.HO_TEN_CHA).MaximumLength(255);
            RuleFor(x => x.GIAI_RAVIEN.HO_TEN_ME).MaximumLength(255);
            RuleFor(x => x.GIAI_RAVIEN.NGOAITRU_TUNGAY).MaximumLength(8);
            RuleFor(x => x.GIAI_RAVIEN.NGOAITRU_DENNGAY).MaximumLength(8);
            RuleFor(x => x.GIAI_RAVIEN.THOIGIAN_DINHCHI).MaximumLength(12);
        }
    }

    public class CreateEMRGiayRaVienCommandHandler(
        ILogger<CreateEMRGiayRaVienCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRGiayRaVienCommand, Response<CreateEMRGiayRaVienResponse>>
    {
        public async Task<Response<CreateEMRGiayRaVienResponse>> Handle(CreateEMRGiayRaVienCommand request, CancellationToken cancellationToken)
        {
            var emrGiayRaVien = request.GIAI_RAVIEN.Adapt<EMRGiayRaVien>();

            var wrapper = new EMRGiayRaVienWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIEU_GIAYNGHIHUONGBHXH = emrGiayRaVien
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRGiayRaVien with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRGiayRaVienResponse>(new CreateEMRGiayRaVienResponse(wrapper.Id, emrGiayRaVien.MA_LK));
        }
    }
}