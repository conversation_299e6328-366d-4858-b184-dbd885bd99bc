using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRDichVuCLSCommand(CreateEMRDichVuCLSRequestModel DICH_VU_CLS, string HospitalCode) : ICommand<Response<CreateEMRDichVuCLSResponse>>;
    public record CreateEMRDichVuCLSResponse(Guid Id, string MA_LK);

    public class CreateEMRDichVuCLSCommandValidator : AbstractValidator<CreateEMRDichVuCLSCommand>
    {
        public CreateEMRDichVuCLSCommandValidator()
        {
            RuleFor(x => x.DICH_VU_CLS.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.DICH_VU_CLS.STT).GreaterThanOrEqualTo(1);
            RuleFor(x => x.DICH_VU_CLS.MA_DICH_VU).NotEmpty().MaximumLength(50);
            RuleFor(x => x.DICH_VU_CLS.MA_CHI_SO).NotEmpty().MaximumLength(255);
            RuleFor(x => x.DICH_VU_CLS.TEN_CHI_SO).NotEmpty().MaximumLength(255);
            RuleFor(x => x.DICH_VU_CLS.GIA_TRI).MaximumLength(255);
            RuleFor(x => x.DICH_VU_CLS.DON_VI_DO).MaximumLength(50);
            RuleFor(x => x.DICH_VU_CLS.NGAY_KQ).MaximumLength(12);
            RuleFor(x => x.DICH_VU_CLS.MA_BS_DOC_KQ).NotEmpty().MaximumLength(255);
        }
    }

    public class CreateEMRDichVuCLSCommandHandler(
        ILogger<CreateEMRDichVuCLSCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRDichVuCLSCommand, Response<CreateEMRDichVuCLSResponse>>
    {
        public async Task<Response<CreateEMRDichVuCLSResponse>> Handle(CreateEMRDichVuCLSCommand request, CancellationToken cancellationToken)
        {
            var emrDichVuCLS = request.DICH_VU_CLS.Adapt<EMRDichVuCLS>();

            var wrapper = new EMRDichVuCLSWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIET_CLS = emrDichVuCLS
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRDichVuCLS with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRDichVuCLSResponse>(
                new CreateEMRDichVuCLSResponse(wrapper.Id, emrDichVuCLS.MA_LK)
            );
        }
    }
}