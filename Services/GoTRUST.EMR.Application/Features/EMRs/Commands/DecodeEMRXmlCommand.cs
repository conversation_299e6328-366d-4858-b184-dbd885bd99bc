using System.ComponentModel.DataAnnotations;
using System.Xml.Serialization;
using Microsoft.AspNetCore.Http;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using GoTRUST.EMR.Application.Features.EMRs.XmlHelpers;
using Marten;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record DecodeEMRXmlCommand(IFormFile XmlFile) : ICommand<Response<List<DecodeEMRXMLResponse>>>;

    public record DecodeEMRXMLResponse(string LoaiHoSo, object? DecodedContent);

    public class DecodeEMRXmlCommandHandler(
        ILogger<DecodeEMRXmlCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<DecodeEMRXmlCommand, Response<List<DecodeEMRXMLResponse>>>
    {
        private readonly ILogger<DecodeEMRXmlCommandHandler> _logger = logger;
        private readonly IDocumentStore _documentStore = documentStore;

        public async Task<Response<List<DecodeEMRXMLResponse>>> Handle(DecodeEMRXmlCommand request, CancellationToken cancellationToken)
        {
            try
            {
                ArgumentNullException.ThrowIfNull(request);
                ArgumentNullException.ThrowIfNull(request.XmlFile);

                var serializer = new XmlSerializer(typeof(GiamDinhHS));
                GiamDinhHS giamDinhHS;
                using (var stream = request.XmlFile.OpenReadStream())
                {
                    giamDinhHS = (GiamDinhHS)serializer.Deserialize(stream)!;
                }

                var response = new List<DecodeEMRXMLResponse>();
                using var session = _documentStore.LightweightSession();

                foreach (var hoso in giamDinhHS.ThongTinHoSo.DanhSachHoSo)
                {
                    foreach (var file in hoso.FileHoSo!)
                    {
                        if (string.IsNullOrEmpty(file.NoiDungFile))
                            continue;

                        try
                        {
                            var bytes = Convert.FromBase64String(file.NoiDungFile.Trim());
                            using var ms = new MemoryStream(bytes);

                            object? decoded = file.LoaiHoSo switch
                            {
                                "XML1" => DeepXmlSerializer.Deserialize<EMRTongHop>(ms)!,
                                "XML2" => DeepXmlSerializer.Deserialize<EMRThuoc>(ms)!,
                                "XML3" => DeepXmlSerializer.Deserialize<EMRDVKTvaVTYT>(ms)!,
                                "XML4" => DeepXmlSerializer.Deserialize<EMRDichVuCLS>(ms)!,
                                "XML5" => DeepXmlSerializer.Deserialize<EMRDienBienLamSang>(ms)!,
                                "XML6" => DeepXmlSerializer.Deserialize<EMRHIVvaAIDS>(ms)!,
                                "XML7" => DeepXmlSerializer.Deserialize<EMRGiayRaVien>(ms)!,
                                "XML8" => DeepXmlSerializer.Deserialize<EMRTomTatHSBA>(ms)!,
                                "XML9" => DeepXmlSerializer.Deserialize<EMRGiayChungSinh>(ms)!,
                                "XML10" => DeepXmlSerializer.Deserialize<EMRGiayNghiDuongThai>(ms)!,
                                "XML11" => DeepXmlSerializer.Deserialize<EMRGiayNghiHuongBHXH>(ms)!,
                                "XML12" => DeepXmlSerializer.Deserialize<EMRGDYK>(ms)!,
                                "XML13" => DeepXmlSerializer.Deserialize<EMRGiayChuyenTuyenBHYT>(ms)!,
                                "XML14" => DeepXmlSerializer.Deserialize<EMRGiayHenKhamLai>(ms)!,
                                "XML15" => DeepXmlSerializer.Deserialize<EMRLao>(ms)!,
                                _ => null!,
                            };

                            file.DecodedContent = decoded;

                            // Save to Marten with individual wrappers
                            switch (file.LoaiHoSo)
                            {
                                case "XML1":
                                    if (decoded is EMRTongHop emrTongHop)
                                        session.Store(new EMRTongHopWrapper { TONG_HOP = emrTongHop });
                                    break;
                                case "XML2":
                                    if (decoded is List<EMRThuoc> thuocList)
                                        foreach (var item in thuocList)
                                            session.Store(new EMRThuocWrapper { CHITIET_THUOC = item });
                                    else if (decoded is EMRThuoc thuoc)
                                        session.Store(new EMRThuocWrapper { CHITIET_THUOC = thuoc });
                                    break;
                                case "XML3":
                                    if (decoded is List<EMRDVKTvaVTYT> dvktList)
                                        foreach (var item in dvktList)
                                            session.Store(new EMRDVKTvaVTYTWrapper { CHI_TIET_DVKT = item });
                                    else if (decoded is EMRDVKTvaVTYT dvkt)
                                        session.Store(new EMRDVKTvaVTYTWrapper { CHI_TIET_DVKT = dvkt });
                                    break;
                                case "XML4":
                                    if (decoded is List<EMRDichVuCLS> clsList)
                                        foreach (var item in clsList)
                                            session.Store(new EMRDichVuCLSWrapper { CHI_TIET_CLS = item });
                                    else if (decoded is EMRDichVuCLS cls)
                                        session.Store(new EMRDichVuCLSWrapper { CHI_TIET_CLS = cls });
                                    break;
                                case "XML5":
                                    if (decoded is List<EMRDienBienLamSang> dienBienList)
                                        foreach (var item in dienBienList)
                                            session.Store(new EMRDienBienLamSangWrapper { CHI_TIET_DIEN_BIEN_BENH = item });
                                    else if (decoded is EMRDienBienLamSang dienBien)
                                        session.Store(new EMRDienBienLamSangWrapper { CHI_TIET_DIEN_BIEN_BENH = dienBien });
                                    break;
                                case "XML6":
                                    if (decoded is EMRHIVvaAIDS hiv)
                                        session.Store(new EMRHIVvaAIDSWrapper { CHI_TIEU_CHAMSOCVADIEUTRIHIVVAAIDS = hiv });
                                    break;
                                case "XML7":
                                    if (decoded is EMRGiayRaVien raVien)
                                        session.Store(new EMRGiayRaVienWrapper { CHI_TIEU_GIAYNGHIHUONGBHXH = raVien });
                                    break;
                                case "XML8":
                                    if (decoded is EMRTomTatHSBA tomTat)
                                        session.Store(new EMRTomTatHSBAWrapper { CHI_TIEU_TOMTATHSBA = tomTat });
                                    break;
                                case "XML9":
                                    if (decoded is EMRGiayChungSinh chungSinh)
                                        session.Store(new EMRGiayChungSinhWrapper { CHI_TIEU_GIAYCHUNGSINH = chungSinh });
                                    break;
                                case "XML10":
                                    if (decoded is EMRGiayNghiDuongThai nghiDuongThai)
                                        session.Store(new EMRGiayNghiDuongThaiWrapper { CHI_TIEU_GIAYNGHIDUONGTHAI = nghiDuongThai });
                                    break;
                                case "XML11":
                                    if (decoded is EMRGiayNghiHuongBHXH nghiHuong)
                                        session.Store(new EMRGiayNghiHuongBHXHWrapper { CHI_TIEU_GIAYNGHIHUONGBHXH = nghiHuong });
                                    break;
                                case "XML12":
                                    if (decoded is EMRGDYK gdyk)
                                        session.Store(new EMRGDYKWrapper { CHI_TIEU_GIAM_DINH_Y_KHOA = gdyk });
                                    break;
                                case "XML13":
                                    if (decoded is EMRGiayChuyenTuyenBHYT chuyenTuyen)
                                        session.Store(new EMRGiayChuyenTuyenBHYTWrapper { CHI_TIEU_GIAYCHUYENTUYEN = chuyenTuyen });
                                    break;
                                case "XML14":
                                    if (decoded is EMRGiayHenKhamLai henKhamLai)
                                        session.Store(new EMRGiayHenKhamLaiWrapper { CHI_TIEU_GIAYHENKHAMLAI = henKhamLai });
                                    break;
                                case "XML15":
                                    if (decoded is EMRLao lao)
                                        session.Store(new EMRLaoWrapper { CHI_TIEU_LAO = lao });
                                    break;
                            }

                            if (decoded != null)
                            {
                                response.Add(new DecodeEMRXMLResponse(file.LoaiHoSo, decoded));
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error decoding file type {FileType} in EMR XML", file.LoaiHoSo);
                            file.DecodedContent = null!;
                        }
                    }
                }

                await session.SaveChangesAsync(cancellationToken);

                return new Response<List<DecodeEMRXMLResponse>>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {CommandName}", nameof(DecodeEMRXmlCommand));
                throw new InternalServerException("Failed to decode EMR XML file.");
            }
        }
    }
}