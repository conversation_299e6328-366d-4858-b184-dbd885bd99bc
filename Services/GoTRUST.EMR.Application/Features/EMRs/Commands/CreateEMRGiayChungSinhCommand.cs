using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRGiayChungSinhCommand(CreateEMRGiayChungSinhRequestModel GIAY_CHUNG_SINH, string HospitalCode) : ICommand<Response<CreateEMRGiayChungSinhResponse>>;

    public record CreateEMRGiayChungSinhResponse(Guid Id, string MA_LK);

    public class CreateEMRGiayChungSinhCommandValidator : AbstractValidator<CreateEMRGiayChungSinhCommand>
    {
        public CreateEMRGiayChungSinhCommandValidator()
        {
            RuleFor(x => x.GIAY_CHUNG_SINH.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.GIAY_CHUNG_SINH.HO_TEN_NND).NotEmpty().MaximumLength(255);
            RuleFor(x => x.GIAY_CHUNG_SINH.NGAYSINH_NND).NotEmpty().MaximumLength(8);
            RuleFor(x => x.GIAY_CHUNG_SINH.MA_DANTOC_NND).NotEmpty().MaximumLength(2);
            RuleFor(x => x.GIAY_CHUNG_SINH.SO_CCCD_NND).NotEmpty();
            RuleFor(x => x.GIAY_CHUNG_SINH.NGAYCAP_CCCD_NND).NotEmpty().MaximumLength(8);
            RuleFor(x => x.GIAY_CHUNG_SINH.NOICAP_CCCD_NND).NotEmpty().MaximumLength(1024);
            RuleFor(x => x.GIAY_CHUNG_SINH.NOI_CU_TRU_NND).NotEmpty().MaximumLength(1024);
            RuleFor(x => x.GIAY_CHUNG_SINH.MA_QUOCTICH).NotEmpty().MaximumLength(3);
            RuleFor(x => x.GIAY_CHUNG_SINH.MATINH_CU_TRU).NotEmpty().MaximumLength(3);
            RuleFor(x => x.GIAY_CHUNG_SINH.MAHUYEN_CU_TRU).NotEmpty().MaximumLength(3);
            RuleFor(x => x.GIAY_CHUNG_SINH.MAXA_CU_TRU).NotEmpty().MaximumLength(5);
            RuleFor(x => x.GIAY_CHUNG_SINH.HO_TEN_CHA).NotEmpty().MaximumLength(255);
            RuleFor(x => x.GIAY_CHUNG_SINH.MA_THE_TAM).MaximumLength(15);
            RuleFor(x => x.GIAY_CHUNG_SINH.GIOI_TINH_CON).InclusiveBetween(1, 3);
            RuleFor(x => x.GIAY_CHUNG_SINH.SO_CON).GreaterThanOrEqualTo(1);
            RuleFor(x => x.GIAY_CHUNG_SINH.LAN_SINH).GreaterThanOrEqualTo(1);
            RuleFor(x => x.GIAY_CHUNG_SINH.SO_CON_SONG).GreaterThanOrEqualTo(1);
            RuleFor(x => x.GIAY_CHUNG_SINH.CAN_NANG_CON).GreaterThan(0);
            RuleFor(x => x.GIAY_CHUNG_SINH.NGAY_SINH_CON).NotEmpty().MaximumLength(12);
            RuleFor(x => x.GIAY_CHUNG_SINH.NOI_SINH_CON).NotEmpty().MaximumLength(1024);
            RuleFor(x => x.GIAY_CHUNG_SINH.TINH_TRANG_CON).NotEmpty();
            RuleFor(x => x.GIAY_CHUNG_SINH.SINHCON_PHAUTHUAT).InclusiveBetween(0, 1);
            RuleFor(x => x.GIAY_CHUNG_SINH.SINHCON_DUOI32TUAN).InclusiveBetween(0, 1);
            RuleFor(x => x.GIAY_CHUNG_SINH.NGUOI_DO_DE).NotEmpty().MaximumLength(255);
            RuleFor(x => x.GIAY_CHUNG_SINH.NGUOI_GHI_PHIEU).NotEmpty().MaximumLength(255);
            RuleFor(x => x.GIAY_CHUNG_SINH.NGAY_CT).NotEmpty().MaximumLength(8);
            RuleFor(x => x.GIAY_CHUNG_SINH.SO).NotEmpty().MaximumLength(200);
            RuleFor(x => x.GIAY_CHUNG_SINH.QUYEN_SO).NotEmpty().MaximumLength(200);
            RuleFor(x => x.GIAY_CHUNG_SINH.MA_TTDV).NotEmpty().MaximumLength(255);
        }
    }

    public class CreateEMRGiayChungSinhCommandHandler(
        ILogger<CreateEMRGiayChungSinhCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRGiayChungSinhCommand, Response<CreateEMRGiayChungSinhResponse>>
    {
        public async Task<Response<CreateEMRGiayChungSinhResponse>> Handle(CreateEMRGiayChungSinhCommand request, CancellationToken cancellationToken)
        {
            var entity = request.GIAY_CHUNG_SINH.Adapt<EMRGiayChungSinh>();

            var wrapper = new EMRGiayChungSinhWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIEU_GIAYCHUNGSINH = entity
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRGiayChungSinh with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRGiayChungSinhResponse>(
                new CreateEMRGiayChungSinhResponse(wrapper.Id, entity.MA_LK)
            );
        }
    }
}