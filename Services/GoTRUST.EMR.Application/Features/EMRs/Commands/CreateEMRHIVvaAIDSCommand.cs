using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRHIVvaAIDSCommand(CreateEMRHIVvaAIDSRequestModel HIV_VA_AIDS, string HospitalCode) : ICommand<Response<CreateEMRHIVvaAIDSResponse>>;
    public record CreateEMRHIVvaAIDSResponse(Guid Id, string MA_LK);

    public class CreateEMRHIVvaAIDSCommandValidator : AbstractValidator<CreateEMRHIVvaAIDSCommand>
    {
        public CreateEMRHIVvaAIDSCommandValidator()
        {
            RuleFor(x => x.HIV_VA_AIDS.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.HIV_VA_AIDS.SO_CCCD).NotEmpty();
            RuleFor(x => x.HIV_VA_AIDS.NGAY_SINH).NotEmpty().MaximumLength(12);
            RuleFor(x => x.HIV_VA_AIDS.DIA_CHI).MaximumLength(1024);
            RuleFor(x => x.HIV_VA_AIDS.MATINH_CU_TRU).MaximumLength(3);
            RuleFor(x => x.HIV_VA_AIDS.MAHUYEN_CU_TRU).MaximumLength(3);
            RuleFor(x => x.HIV_VA_AIDS.MAXA_CU_TRU).MaximumLength(5);
            RuleFor(x => x.HIV_VA_AIDS.NGAYKD_HIV).MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.NOI_LAY_MAU_XN).MaximumLength(5);
            RuleFor(x => x.HIV_VA_AIDS.NOI_XN_KD).MaximumLength(5);
            RuleFor(x => x.HIV_VA_AIDS.NOI_BDDT_ARV).MaximumLength(5);
            RuleFor(x => x.HIV_VA_AIDS.BDDT_ARV).NotEmpty().MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.MA_PHAC_DO_DIEU_TRI_BD).MaximumLength(200);
            RuleFor(x => x.HIV_VA_AIDS.NGAYBD_DTRI_LAO).MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.NGAYKT_DTRI_LAO).MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.NGAY_XN_TLVR).MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.NGAY_KQ_XN_TLVR).MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.MA_TINH_TRANG_DK).MaximumLength(18);
            RuleFor(x => x.HIV_VA_AIDS.NGAY_XN_PCR).MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.NGAY_KQ_XN_PCR).MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.NGAY_NHAN_TT_MANG_THAI).MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.NGAY_BAT_DAU_DT_CTX).MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.MA_XU_TRI).NotEmpty();
            RuleFor(x => x.HIV_VA_AIDS.NGAY_BAT_DAU_XU_TRI).MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.NGAY_KET_THUC_XU_TRI).MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.MA_PHAC_DO_DIEU_TRI).MaximumLength(200);
            RuleFor(x => x.HIV_VA_AIDS.NGAY_CHUYEN_PHAC_DO).MaximumLength(8);
            RuleFor(x => x.HIV_VA_AIDS.MA_CSKCB).MaximumLength(5);
        }
    }

    public class CreateEMRHIVvaAIDSCommandHandler(
        ILogger<CreateEMRHIVvaAIDSCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRHIVvaAIDSCommand, Response<CreateEMRHIVvaAIDSResponse>>
    {
        public async Task<Response<CreateEMRHIVvaAIDSResponse>> Handle(CreateEMRHIVvaAIDSCommand request, CancellationToken cancellationToken)
        {
            var hivVaAids = request.HIV_VA_AIDS.Adapt<EMRHIVvaAIDS>();

            var wrapper = new EMRHIVvaAIDSWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIEU_CHAMSOCVADIEUTRIHIVVAAIDS = hivVaAids
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRHIVvaAIDS with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRHIVvaAIDSResponse>(new CreateEMRHIVvaAIDSResponse(wrapper.Id, hivVaAids.MA_LK));
        }
    }
}