using GoTRUST.EMR.Application.Features.EMRs.Models;
using Mapster;
using Marten;
using BuildingBlocks.Abstractions;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRGiayNghiDuongThaiCommand(CreateEMRGiayNghiDuongThaiRequestModel GIAY_NGHI_DUONG_THAI, string HospitalCode) : IRequest<Response<CreateEMRGiayNghiDuongThaiResponse>>;

    public record CreateEMRGiayNghiDuongThaiResponse(Guid Id, string MA_LK);

    public class CreateEMRGiayNghiDuongThaiCommandValidator : AbstractValidator<CreateEMRGiayNghiDuongThaiCommand>
    {
        public CreateEMRGiayNghiDuongThaiCommandValidator()
        {
            RuleFor(x => x.GIAY_NGHI_DUONG_THAI.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.GIAY_NGHI_DUONG_THAI.SO_SERI).MaximumLength(200);
            RuleFor(x => x.GIAY_NGHI_DUONG_THAI.SO_CT).MaximumLength(200);
            RuleFor(x => x.GIAY_NGHI_DUONG_THAI.SO_NGAY).GreaterThanOrEqualTo(0);
            RuleFor(x => x.GIAY_NGHI_DUONG_THAI.DON_VI).MaximumLength(1024);
            RuleFor(x => x.GIAY_NGHI_DUONG_THAI.CHAN_DOAN_RV).NotEmpty();
            RuleFor(x => x.GIAY_NGHI_DUONG_THAI.TU_NGAY).NotEmpty().Length(8);
            RuleFor(x => x.GIAY_NGHI_DUONG_THAI.DEN_NGAY).NotEmpty().Length(8);
            RuleFor(x => x.GIAY_NGHI_DUONG_THAI.MA_TTDV).NotEmpty().MaximumLength(255);
            RuleFor(x => x.GIAY_NGHI_DUONG_THAI.TEN_BS).MaximumLength(255);
            RuleFor(x => x.GIAY_NGHI_DUONG_THAI.MA_BS).MaximumLength(255);
            RuleFor(x => x.GIAY_NGHI_DUONG_THAI.NGAY_CT).NotEmpty().Length(8);
        }
    }

    public class CreateEMRGiayNghiDuongThaiCommandHandler(
        ILogger<CreateEMRGiayNghiDuongThaiCommandHandler> logger,
        IDocumentStore documentStore
    ) : IRequestHandler<CreateEMRGiayNghiDuongThaiCommand, Response<CreateEMRGiayNghiDuongThaiResponse>>
    {
        public async Task<Response<CreateEMRGiayNghiDuongThaiResponse>> Handle(CreateEMRGiayNghiDuongThaiCommand request, CancellationToken cancellationToken)
        {
            var giayNghiDuongThai = request.GIAY_NGHI_DUONG_THAI.Adapt<EMRGiayNghiDuongThai>();

            var wrapper = new EMRGiayNghiDuongThaiWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIEU_GIAYNGHIDUONGTHAI = giayNghiDuongThai
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRGiayNghiDuongThai with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRGiayNghiDuongThaiResponse>(
                new CreateEMRGiayNghiDuongThaiResponse(wrapper.Id, giayNghiDuongThai.MA_LK)
            );
        }
    }
}