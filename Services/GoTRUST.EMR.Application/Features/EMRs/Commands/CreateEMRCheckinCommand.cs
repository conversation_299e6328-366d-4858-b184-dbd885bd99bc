using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRCheckinCommand(CreateEMRCheckinRequestModel CHECK_IN, string HospitalCode) : ICommand<Response<CreateEMRCheckinResponse>>;
    public record CreateEMRCheckinResponse(Guid Id, string MA_LK);

    public class CreateEMRCheckinCommandValidator : AbstractValidator<CreateEMRCheckinCommand>
    {
        public CreateEMRCheckinCommandValidator()
        {
            RuleFor(x => x.CHECK_IN.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.CHECK_IN.STT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.CHECK_IN.MA_BN).NotEmpty().MaximumLength(100);
            RuleFor(x => x.CHECK_IN.HO_TEN).NotEmpty().MaximumLength(255);
            RuleFor(x => x.CHECK_IN.SO_CCCD).MaximumLength(50);
            RuleFor(x => x.CHECK_IN.NGAY_SINH).NotEmpty().MaximumLength(12);
            RuleFor(x => x.CHECK_IN.GIOI_TINH).InclusiveBetween(1, 3);
            RuleFor(x => x.CHECK_IN.MA_THE_BHYT).MaximumLength(15);
            RuleFor(x => x.CHECK_IN.MA_DKBD).MaximumLength(5);
            RuleFor(x => x.CHECK_IN.GT_THE_TU).MaximumLength(8);
            RuleFor(x => x.CHECK_IN.GT_THE_DEN).MaximumLength(8);
            RuleFor(x => x.CHECK_IN.MA_DOITUONG_KCB).NotEmpty().MaximumLength(4);
            RuleFor(x => x.CHECK_IN.NGAY_VAO).NotEmpty().MaximumLength(12);
            RuleFor(x => x.CHECK_IN.NGAY_VAO_NOI_TRU).MaximumLength(12);
            RuleFor(x => x.CHECK_IN.LY_DO_VNT).MaximumLength(255);
            RuleFor(x => x.CHECK_IN.MA_LY_DO_VNT).MaximumLength(5);
            RuleFor(x => x.CHECK_IN.MA_LOAI_KCB).NotEmpty().MaximumLength(2);
            RuleFor(x => x.CHECK_IN.MA_CSKCB).NotEmpty().MaximumLength(5);
            RuleFor(x => x.CHECK_IN.MA_DICH_VU).MaximumLength(50);
            RuleFor(x => x.CHECK_IN.TEN_DICH_VU).MaximumLength(1024);
            RuleFor(x => x.CHECK_IN.MA_THUOC).MaximumLength(255);
            RuleFor(x => x.CHECK_IN.TEN_THUOC).MaximumLength(1024);
            RuleFor(x => x.CHECK_IN.MA_VAT_TU).MaximumLength(255);
            RuleFor(x => x.CHECK_IN.TEN_VAT_TU).MaximumLength(1024);
            RuleFor(x => x.CHECK_IN.NGAY_YL).MaximumLength(12);
            RuleFor(x => x.CHECK_IN.DU_PHONG).MaximumLength(255);
        }
    }

    public class CreateEMRCheckinCommandHandler(
        ILogger<CreateEMRCheckinCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRCheckinCommand, Response<CreateEMRCheckinResponse>>
    {
        public async Task<Response<CreateEMRCheckinResponse>> Handle(CreateEMRCheckinCommand request, CancellationToken cancellationToken)
        {
            var emrCheckin = request.CHECK_IN.Adapt<EMRCheckin>();

            var wrapper = new EMRCheckinWrapper
            {
                HospitalCode = request.HospitalCode,
                CHECK_IN = emrCheckin
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRCheckin with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRCheckinResponse>(new CreateEMRCheckinResponse(wrapper.Id, emrCheckin.MA_LK));
        }
    }
}