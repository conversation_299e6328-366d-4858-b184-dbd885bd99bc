using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRGiayChuyenTuyenBHYTCommand(CreateEMRGiayChuyenTuyenBHYTRequestModel GIAY_CHUYEN_TUYEN, string HospitalCode) : ICommand<Response<CreateEMRGiayChuyenTuyenBHYTResponse>>;

    public record CreateEMRGiayChuyenTuyenBHYTResponse(Guid Id, string MA_LK);

    public class CreateEMRGiayChuyenTuyenBHYTCommandValidator : AbstractValidator<CreateEMRGiayChuyenTuyenBHYTCommand>
    {
        public CreateEMRGiayChuyenTuyenBHYTCommandValidator()
        {
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.SO_HOSO).MaximumLength(50);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.SO_CHUYENTUYEN).MaximumLength(50);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.GIAY_CHUYEN_TUYEN).MaximumLength(50);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.MA_CSKCB).MaximumLength(5);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.MA_NOI_DI).MaximumLength(100);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.MA_NOI_DEN).MaximumLength(5);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.HO_TEN).MaximumLength(255);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.NGAY_SINH).MaximumLength(12);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.MA_QUOCTICH).MaximumLength(3);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.MA_DANTOC).MaximumLength(2);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.MA_NGHE_NGHIEP).MaximumLength(5);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.DIA_CHI).MaximumLength(1024);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.NGAY_VAO).MaximumLength(100);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.NGAY_VAO_NOI_TRU).MaximumLength(12);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.NGAY_RA).MaximumLength(100);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.MA_BENH_CHINH).MaximumLength(7);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.MA_BENH_KT).MaximumLength(100);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.MA_BENH_YHCT).MaximumLength(255);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.TEN_DICH_VU).MaximumLength(1024);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.TEN_THUOC).MaximumLength(1024);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.PHUONGTIEN_VC).MaximumLength(255);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.HOTEN_NGUOI_HT).MaximumLength(255);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.CHUCDANH_NGUOI_HT).MaximumLength(255);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.MA_BAC_SI).MaximumLength(255);
            RuleFor(x => x.GIAY_CHUYEN_TUYEN.MA_TTDV).MaximumLength(255);
        }
    }

    public class CreateEMRGiayChuyenTuyenBHYTCommandHandler(
        ILogger<CreateEMRGiayChuyenTuyenBHYTCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRGiayChuyenTuyenBHYTCommand, Response<CreateEMRGiayChuyenTuyenBHYTResponse>>
    {
        public async Task<Response<CreateEMRGiayChuyenTuyenBHYTResponse>> Handle(CreateEMRGiayChuyenTuyenBHYTCommand request, CancellationToken cancellationToken)
        {
            var entity = request.GIAY_CHUYEN_TUYEN.Adapt<EMRGiayChuyenTuyenBHYT>();

            var wrapper = new EMRGiayChuyenTuyenBHYTWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIEU_GIAYCHUYENTUYEN = entity
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRGiayChuyenTuyenBHYT with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRGiayChuyenTuyenBHYTResponse>(
                new CreateEMRGiayChuyenTuyenBHYTResponse(wrapper.Id, entity.MA_LK)
            );
        }
    }
}