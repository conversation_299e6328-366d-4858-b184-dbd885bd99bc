using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRDVKTvaVTYTCommand(CreateEMRDVKTvaVTYTRequestModel DVKT_VTYT, string HospitalCode) : ICommand<Response<CreateEMRDVKTvaVTYTResponse>>;
    public record CreateEMRDVKTvaVTYTResponse(Guid Id, string MA_LK);

    public class CreateEMRDVKTvaVTYTCommandValidator : AbstractValidator<CreateEMRDVKTvaVTYTCommand>
    {
        public CreateEMRDVKTvaVTYTCommandValidator()
        {
            RuleFor(x => x.DVKT_VTYT.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.DVKT_VTYT.STT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.MA_DICH_VU).NotEmpty().MaximumLength(50);
            RuleFor(x => x.DVKT_VTYT.MA_PTTT_QT).MaximumLength(255);
            RuleFor(x => x.DVKT_VTYT.MA_VAT_TU).MaximumLength(255);
            RuleFor(x => x.DVKT_VTYT.MA_NHOM).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.GOI_VTYT).NotEmpty().MaximumLength(3);
            RuleFor(x => x.DVKT_VTYT.TEN_VAT_TU).MaximumLength(1024);
            RuleFor(x => x.DVKT_VTYT.TEN_DICH_VU).NotEmpty().MaximumLength(1024);
            RuleFor(x => x.DVKT_VTYT.MA_XANG_DAU).MaximumLength(20);
            RuleFor(x => x.DVKT_VTYT.DON_VI_TINH).NotEmpty().MaximumLength(50);
            RuleFor(x => x.DVKT_VTYT.PHAM_VI).InclusiveBetween(1, 3);
            RuleFor(x => x.DVKT_VTYT.SO_LUONG).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.DON_GIA_BV).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.DON_GIA_BH).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.TT_THAU).MaximumLength(25);
            RuleFor(x => x.DVKT_VTYT.TYLE_TT_DV).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.TYLE_TT_BH).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.THANH_TIEN_BV).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.THANH_TIEN_BH).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.MUC_HUONG).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.T_NGUONKHAC_NSNN).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.T_NGUONKHAC_VTNN).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.T_NGUONKHAC_VTTN).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.T_NGUONKHAC_CL).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.T_NGUONKHAC).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.T_BNTT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.T_BNCCT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.T_BHTT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.MA_KHOA).NotEmpty().MaximumLength(50);
            RuleFor(x => x.DVKT_VTYT.MA_GIUONG).MaximumLength(50);
            RuleFor(x => x.DVKT_VTYT.MA_BAC_SI).NotEmpty().MaximumLength(255);
            RuleFor(x => x.DVKT_VTYT.NGUOI_THUC_HIEN).NotEmpty().MaximumLength(255);
            RuleFor(x => x.DVKT_VTYT.MA_BENH).MaximumLength(100);
            RuleFor(x => x.DVKT_VTYT.MA_BENH_YHCT).MaximumLength(150);
            RuleFor(x => x.DVKT_VTYT.NGAY_YL).NotEmpty().MaximumLength(12);
            RuleFor(x => x.DVKT_VTYT.NGAY_TH_YL).NotEmpty().MaximumLength(12);
            RuleFor(x => x.DVKT_VTYT.NGAY_KQ).MaximumLength(12);
            RuleFor(x => x.DVKT_VTYT.MA_PTTT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DVKT_VTYT.MA_MAY).MaximumLength(1024);
            RuleFor(x => x.DVKT_VTYT.MA_HIEU_SP).MaximumLength(255);
        }
    }

    public class CreateEMRDVKTvaVTYTCommandHandler(
        ILogger<CreateEMRDVKTvaVTYTCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRDVKTvaVTYTCommand, Response<CreateEMRDVKTvaVTYTResponse>>
    {
        public async Task<Response<CreateEMRDVKTvaVTYTResponse>> Handle(CreateEMRDVKTvaVTYTCommand request, CancellationToken cancellationToken)
        {
            var emrDvktVaVtyt = request.DVKT_VTYT.Adapt<EMRDVKTvaVTYT>();

            var wrapper = new EMRDVKTvaVTYTWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIET_DVKT = emrDvktVaVtyt
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRDVKTvaVTYT with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRDVKTvaVTYTResponse>(
                new CreateEMRDVKTvaVTYTResponse(wrapper.Id, emrDvktVaVtyt.MA_LK)
            );
        }
    }
}
