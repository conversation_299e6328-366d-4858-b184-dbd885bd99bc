using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRGDYKCommand(CreateEMRGDYKRequestModel GDYK, string HospitalCode) : ICommand<Response<CreateEMRGDYKResponse>>;
    public record CreateEMRGDYKResponse(Guid Id, string SO_BIEN_BAN);

    public class CreateEMRGDYKCommandValidator : AbstractValidator<CreateEMRGDYKCommand>
    {
        public CreateEMRGDYKCommandValidator()
        {
            RuleFor(x => x.GDYK.SO_BIEN_BAN).MaximumLength(200);
            RuleFor(x => x.GDYK.NGUOI_CHU_TRI).MaximumLength(255);
            RuleFor(x => x.GDYK.NGAY_HOP).Length(8);
            RuleFor(x => x.GDYK.HO_TEN).MaximumLength(255);
            RuleFor(x => x.GDYK.NGAY_SINH).Length(8);
            RuleFor(x => x.GDYK.NGAY_CAP_CCCD).Length(8);
            RuleFor(x => x.GDYK.NOI_CAP_CCCD).MaximumLength(1024);
            RuleFor(x => x.GDYK.DIA_CHI).MaximumLength(1024);
            RuleFor(x => x.GDYK.MATINH_CU_TRU).MaximumLength(3);
            RuleFor(x => x.GDYK.MAHUYEN_CU_TRU).MaximumLength(3);
            RuleFor(x => x.GDYK.MAXA_CU_TRU).MaximumLength(5);
            RuleFor(x => x.GDYK.MA_BHXH).MaximumLength(10);
            RuleFor(x => x.GDYK.MA_THE_BHYT).MaximumLength(15);
            RuleFor(x => x.GDYK.NGHE_NGHIEP).MaximumLength(100);
            RuleFor(x => x.GDYK.DIEN_THOAI).MaximumLength(15);
            RuleFor(x => x.GDYK.MA_DOI_TUONG).MaximumLength(20);
            RuleFor(x => x.GDYK.NGAY_CHUNG_TU).Length(8);
            RuleFor(x => x.GDYK.SO_GIAY_GIOI_THIEU).MaximumLength(200);
            RuleFor(x => x.GDYK.NGAY_DE_NGHI).Length(8);
            RuleFor(x => x.GDYK.MA_DONVI).MaximumLength(200);
            RuleFor(x => x.GDYK.GIOI_THIEU_CUA).MaximumLength(1024);
            RuleFor(x => x.GDYK.SO_VAN_BAN_CAN_CU).MaximumLength(200);
            RuleFor(x => x.GDYK.DANG_HUONG_CHE_DO).MaximumLength(10);
        }
    }

    public class CreateEMRGDYKCommandHandler(
        ILogger<CreateEMRGDYKCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRGDYKCommand, Response<CreateEMRGDYKResponse>>
    {
        public async Task<Response<CreateEMRGDYKResponse>> Handle(CreateEMRGDYKCommand request, CancellationToken cancellationToken)
        {
            var emrgdyk = request.GDYK.Adapt<EMRGDYK>();

            var wrapper = new EMRGDYKWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIEU_GIAM_DINH_Y_KHOA = emrgdyk
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRGDYK with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRGDYKResponse>(new CreateEMRGDYKResponse(wrapper.Id, emrgdyk.SO_BIEN_BAN));
        }
    }
}