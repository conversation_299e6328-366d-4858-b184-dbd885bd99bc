using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRLaoCommand(CreateEMRLaoRequestModel LAO, string HospitalCode) : ICommand<Response<CreateEMRLaoResponse>>;
    public record CreateEMRLaoResponse(Guid Id, string MA_LK);

    public class CreateEMRLaoCommandValidator : AbstractValidator<CreateEMRLaoCommand>
    {
        public CreateEMRLaoCommandValidator()
        {
            RuleFor(x => x.LAO.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.LAO.MA_BN).MaximumLength(100);
            RuleFor(x => x.LAO.HO_TEN).MaximumLength(255);
            RuleFor(x => x.LAO.NGAYBD_DTRI_LAO).MaximumLength(8);
            RuleFor(x => x.LAO.MA_CSKCB).MaximumLength(5);
            RuleFor(x => x.LAO.NGAYKT_DTRI_LAO).MaximumLength(8);
            RuleFor(x => x.LAO.NGAYKD_HIV).MaximumLength(8);
            RuleFor(x => x.LAO.BDDT_ARV).MaximumLength(8);
            RuleFor(x => x.LAO.NGAY_BAT_DAU_DT_CTX).MaximumLength(8);
        }
    }

    public class CreateEMRLaoCommandHandler(
        ILogger<CreateEMRLaoCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRLaoCommand, Response<CreateEMRLaoResponse>>
    {
        public async Task<Response<CreateEMRLaoResponse>> Handle(CreateEMRLaoCommand request, CancellationToken cancellationToken)
        {
            var lao = request.LAO.Adapt<EMRLao>();

            var wrapper = new EMRLaoWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIEU_LAO = lao
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRLao with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRLaoResponse>(new CreateEMRLaoResponse(wrapper.Id, lao.MA_LK));
        }
    }
}