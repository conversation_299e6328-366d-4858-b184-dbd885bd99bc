using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRThuocCommand(CreateEMRThuocRequestModel THUOC, string HospitalCode) : ICommand<Response<CreateEMRThuocResponse>>;
    public record CreateEMRThuocResponse(Guid Id, string MA_LK);

    public class CreateEMRThuocCommandValidator : AbstractValidator<CreateEMRThuocCommand>
    {
        public CreateEMRThuocCommandValidator()
        {
            RuleFor(x => x.THUOC.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.THUOC.STT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.MA_THUOC).NotEmpty().MaximumLength(255);
            RuleFor(x => x.THUOC.MA_PP_CHEBIEN).MaximumLength(255);
            RuleFor(x => x.THUOC.MA_CSKCB_THUOC).MaximumLength(10);
            RuleFor(x => x.THUOC.MA_NHOM).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.TEN_THUOC).NotEmpty().MaximumLength(1024);
            RuleFor(x => x.THUOC.DON_VI_TINH).NotEmpty().MaximumLength(50);
            RuleFor(x => x.THUOC.HAM_LUONG).NotEmpty().MaximumLength(1024);
            RuleFor(x => x.THUOC.DUONG_DUNG).NotEmpty().MaximumLength(4);
            RuleFor(x => x.THUOC.DANG_BAO_CHE).NotEmpty().MaximumLength(1024);
            RuleFor(x => x.THUOC.LIEU_DUNG).NotEmpty().MaximumLength(1024);
            RuleFor(x => x.THUOC.CACH_DUNG).MaximumLength(1024);
            RuleFor(x => x.THUOC.SO_DANG_KY).MaximumLength(255);
            RuleFor(x => x.THUOC.TT_THAU).NotEmpty().MaximumLength(50);
            RuleFor(x => x.THUOC.PHAM_VI).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.TYLE_TT_BH).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.SO_LUONG).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.DON_GIA).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.THANH_TIEN_BV).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.THANH_TIEN_BH).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.T_NGUONKHAC_NSNN).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.T_NGUONKHAC_VTNN).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.T_NGUONKHAC_VTTN).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.T_NGUONKHAC_CL).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.T_NGUONKHAC).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.MUC_HUONG).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.T_BNTT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.T_BNCCT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.T_BHTT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.MA_KHOA).NotEmpty().MaximumLength(50);
            RuleFor(x => x.THUOC.MA_BAC_SI).NotEmpty().MaximumLength(255);
            RuleFor(x => x.THUOC.MA_DICH_VU).MaximumLength(255);
            RuleFor(x => x.THUOC.NGAY_YL).NotEmpty().MaximumLength(12);
            RuleFor(x => x.THUOC.NGAY_TH_YL).NotEmpty().MaximumLength(12);
            RuleFor(x => x.THUOC.MA_PTTT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.THUOC.NGUON_CTRA).GreaterThanOrEqualTo(0);
        }
    }

    public class CreateEMRThuocCommandHandler(
        ILogger<CreateEMRThuocCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRThuocCommand, Response<CreateEMRThuocResponse>>
    {
        public async Task<Response<CreateEMRThuocResponse>> Handle(CreateEMRThuocCommand request, CancellationToken cancellationToken)
        {
            var emrThuoc = request.THUOC.Adapt<EMRThuoc>();

            var wrapper = new EMRThuocWrapper
            {
                HospitalCode = request.HospitalCode,
                CHITIET_THUOC = emrThuoc
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRThuoc with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRThuocResponse>(new CreateEMRThuocResponse(wrapper.Id, emrThuoc.MA_LK));
        }
    }
}