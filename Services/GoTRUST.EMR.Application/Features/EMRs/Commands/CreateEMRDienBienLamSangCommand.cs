using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRDienBienLamSangCommand(CreateEMRDienBienLamSangRequestModel DIEN_BIEN_LAM_SANG, string HospitalCode) : ICommand<Response<CreateEMRDienBienLamSangResponse>>;
    public record CreateEMRDienBienLamSangResponse(Guid Id, string MA_LK);

    public class CreateEMRDienBienLamSangCommandValidator : AbstractValidator<CreateEMRDienBienLamSangCommand>
    {
        public CreateEMRDienBienLamSangCommandValidator()
        {
            RuleFor(x => x.DIEN_BIEN_LAM_SANG.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.DIEN_BIEN_LAM_SANG.STT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.DIEN_BIEN_LAM_SANG.DIEN_BIEN_LS).NotEmpty();
            RuleFor(x => x.DIEN_BIEN_LAM_SANG.THOI_DIEM_DBLS).NotEmpty().MaximumLength(12);
            RuleFor(x => x.DIEN_BIEN_LAM_SANG.NGUOI_THUC_HIEN).NotEmpty().MaximumLength(255);
        }
    }

    public class CreateEMRDienBienLamSangCommandHandler(
        ILogger<CreateEMRDienBienLamSangCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRDienBienLamSangCommand, Response<CreateEMRDienBienLamSangResponse>>
    {
        public async Task<Response<CreateEMRDienBienLamSangResponse>> Handle(CreateEMRDienBienLamSangCommand request, CancellationToken cancellationToken)
        {
            var emrDienBienLamSang = request.DIEN_BIEN_LAM_SANG.Adapt<EMRDienBienLamSang>();

            var wrapper = new EMRDienBienLamSangWrapper
            {
                HospitalCode = request.HospitalCode,
                CHI_TIET_DIEN_BIEN_BENH = emrDienBienLamSang
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRDienBienLamSang with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRDienBienLamSangResponse>(
                new CreateEMRDienBienLamSangResponse(wrapper.Id, emrDienBienLamSang.MA_LK)
            );
        }
    }
}