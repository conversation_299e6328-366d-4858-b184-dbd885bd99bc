using Marten;
using BuildingBlocks.Abstractions;
using Mapster;
using GoTRUST.EMR.Application.Features.EMRs.Models;

namespace GoTRUST.EMR.Application.Features.EMRs.Commands
{
    public record CreateEMRTongHopCommand(CreateEMRTongHopRequestModel TONG_HOP, string HospitalCode) : ICommand<Response<CreateEMRTongHopResponse>>;
    public record CreateEMRTongHopResponse(Guid Id, string MA_LK);
    public class CreateEMRTongHopCommandValidator : AbstractValidator<CreateEMRTongHopCommand>
    {
        public CreateEMRTongHopCommandValidator()
        {
            RuleFor(x => x.TONG_HOP.MA_LK).NotEmpty().MaximumLength(100);
            RuleFor(x => x.TONG_HOP.STT).GreaterThanOrEqualTo(0);
            RuleFor(x => x.TONG_HOP.MA_BN).NotEmpty().MaximumLength(100);
            RuleFor(x => x.TONG_HOP.HO_TEN).NotEmpty().MaximumLength(255);
            RuleFor(x => x.TONG_HOP.NGAY_SINH).NotEmpty().MaximumLength(12);
            RuleFor(x => x.TONG_HOP.NHOM_MAU).MaximumLength(5);
            RuleFor(x => x.TONG_HOP.MA_QUOCTICH).MaximumLength(3);
            RuleFor(x => x.TONG_HOP.MA_DANTOC).MaximumLength(2);
            RuleFor(x => x.TONG_HOP.MA_NGHE_NGHIEP).MaximumLength(2);
            RuleFor(x => x.TONG_HOP.GIOI_TINH).InclusiveBetween(1, 3);
            RuleFor(x => x.TONG_HOP.DIA_CHI).MaximumLength(1024);
            RuleFor(x => x.TONG_HOP.MATINH_CU_TRU).MaximumLength(3);
            RuleFor(x => x.TONG_HOP.MAHUYEN_CU_TRU).MaximumLength(3);
            RuleFor(x => x.TONG_HOP.MAXA_CU_TRU).MaximumLength(5);
            RuleFor(x => x.TONG_HOP.DIEN_THOAI).MaximumLength(15);
            RuleFor(x => x.TONG_HOP.NGAY_MIEN_CCT).MaximumLength(12);
            RuleFor(x => x.TONG_HOP.MA_LY_DO_VNT).MaximumLength(5);
            RuleFor(x => x.TONG_HOP.MA_BENH_CHINH).NotEmpty().MaximumLength(7);
            RuleFor(x => x.TONG_HOP.MA_BENH_KT).MaximumLength(100);
            RuleFor(x => x.TONG_HOP.MA_BENH_YHCT).MaximumLength(150);
            RuleFor(x => x.TONG_HOP.MA_PTTT_QT).MaximumLength(125);
            RuleFor(x => x.TONG_HOP.MA_DOITUONG_KCB).NotEmpty().MaximumLength(4);
            RuleFor(x => x.TONG_HOP.MA_NOI_DI).MaximumLength(5);
            RuleFor(x => x.TONG_HOP.MA_NOI_DEN).MaximumLength(5);
            RuleFor(x => x.TONG_HOP.NGAY_VAO).NotEmpty().MaximumLength(12);
            RuleFor(x => x.TONG_HOP.NGAY_VAO_NOI_TRU).MaximumLength(12);
            RuleFor(x => x.TONG_HOP.NGAY_RA).NotEmpty().MaximumLength(12);
            RuleFor(x => x.TONG_HOP.GIAY_CHUYEN_TUYEN).MaximumLength(50);
            RuleFor(x => x.TONG_HOP.NGAY_TTOAN).MaximumLength(12);
            RuleFor(x => x.TONG_HOP.MA_LOAI_KCB).MaximumLength(2);
            RuleFor(x => x.TONG_HOP.MA_KHOA).MaximumLength(50);
            RuleFor(x => x.TONG_HOP.MA_CSKCB).MaximumLength(5);
            RuleFor(x => x.TONG_HOP.MA_KHUVUC).MaximumLength(2);
            RuleFor(x => x.TONG_HOP.CAN_NANG).MaximumLength(6);
            RuleFor(x => x.TONG_HOP.CAN_NANG_CON).MaximumLength(100);
            RuleFor(x => x.TONG_HOP.NAM_NAM_LIEN_TUC).MaximumLength(8);
            RuleFor(x => x.TONG_HOP.NGAY_TAI_KHAM).MaximumLength(50);
            RuleFor(x => x.TONG_HOP.MA_HSBA).MaximumLength(100);
            RuleFor(x => x.TONG_HOP.MA_TTDV).MaximumLength(255);
        }
    }

    public class CreateEMRTongHopCommandHandler(
        ILogger<CreateEMRTongHopCommandHandler> logger,
        IDocumentStore documentStore
    ) : ICommandHandler<CreateEMRTongHopCommand, Response<CreateEMRTongHopResponse>>
    {
        public async Task<Response<CreateEMRTongHopResponse>> Handle(CreateEMRTongHopCommand request, CancellationToken cancellationToken)
        {
            var emrTongHop = request.TONG_HOP.Adapt<EMRTongHop>();

            var wrapper = new EMRTongHopWrapper
            {
                HospitalCode = request.HospitalCode,
                TONG_HOP = emrTongHop
            };

            using var session = documentStore.LightweightSession();
            session.Store(wrapper);
            await session.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Created EMRTongHop with HospitalCode {HospitalCode}: Id {Id}", request.HospitalCode, wrapper.Id);

            return new Response<CreateEMRTongHopResponse>(new CreateEMRTongHopResponse(wrapper.Id, emrTongHop.MA_LK));
        }
    }
}