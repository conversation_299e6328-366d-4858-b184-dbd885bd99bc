using System.Text;
using System.Xml.Serialization;
using System.Collections;
using System.Xml;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using Marten;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.EMRs.Queries
{
    public record GetEMRByCCCDAsXML(string MA_BN) : IRequest<Response<byte[]>>;

    public class GetEMRByCCCDAsXMLHandler(IDocumentStore documentStore) : IRequestHandler<GetEMRByCCCDAsXML, Response<byte[]>>
    {
        private readonly IDocumentStore _documentStore = documentStore;

        public async Task<Response<byte[]>> Handle(GetEMRByCCCDAsXML request, CancellationToken cancellationToken)
        {
            using var session = _documentStore.QuerySession();

            var tongHopWrapper = await session.Query<EMRTongHopWrapper>()
                .FirstOrDefaultAsync(x => x.TONG_HOP!.SO_CCCD == request.MA_BN, token: cancellationToken);

            if (tongHopWrapper?.TONG_HOP == null)
                throw new NotFoundException("Not found");

            var maLk = tongHopWrapper.TONG_HOP.MA_LK;
            var fileIndex = 1;
            var hosoList = new List<string>();

            AddHosoIfNotNull(hosoList, tongHopWrapper.TONG_HOP, fileIndex++);
            AddHosoIfNotNull(hosoList, session.Query<EMRThuocWrapper>().Where(x => x.CHITIET_THUOC != null && x.CHITIET_THUOC.MA_LK == maLk).Select(x => x.CHITIET_THUOC!).ToList(), fileIndex++);
            AddHosoIfNotNull(hosoList, session.Query<EMRDVKTvaVTYTWrapper>().Where(x => x.CHI_TIET_DVKT != null && x.CHI_TIET_DVKT.MA_LK == maLk).Select(x => x.CHI_TIET_DVKT!).ToList(), fileIndex++);
            AddHosoIfNotNull(hosoList, session.Query<EMRDichVuCLSWrapper>().Where(x => x.CHI_TIET_CLS != null && x.CHI_TIET_CLS.MA_LK == maLk).Select(x => x.CHI_TIET_CLS!).ToList(), fileIndex++);
            AddHosoIfNotNull(hosoList, session.Query<EMRDienBienLamSangWrapper>().Where(x => x.CHI_TIET_DIEN_BIEN_BENH != null && x.CHI_TIET_DIEN_BIEN_BENH.MA_LK == maLk).Select(x => x.CHI_TIET_DIEN_BIEN_BENH!).ToList(), fileIndex++);
            AddHosoIfNotNull(hosoList, session.Query<EMRHIVvaAIDSWrapper>().Where(x => x.CHI_TIEU_CHAMSOCVADIEUTRIHIVVAAIDS != null && x.CHI_TIEU_CHAMSOCVADIEUTRIHIVVAAIDS.MA_LK == maLk).Select(x => x.CHI_TIEU_CHAMSOCVADIEUTRIHIVVAAIDS!).FirstOrDefault(), fileIndex++);
            AddHosoIfNotNull(hosoList, session.Query<EMRGiayRaVienWrapper>().Where(x => x.CHI_TIEU_GIAYNGHIHUONGBHXH != null && x.CHI_TIEU_GIAYNGHIHUONGBHXH.MA_LK == maLk).Select(x => x.CHI_TIEU_GIAYNGHIHUONGBHXH!).FirstOrDefault(), fileIndex++);
            AddHosoIfNotNull(hosoList, session.Query<EMRTomTatHSBAWrapper>().Where(x => x.CHI_TIEU_TOMTATHSBA != null && x.CHI_TIEU_TOMTATHSBA.MA_LK == maLk).Select(x => x.CHI_TIEU_TOMTATHSBA!).FirstOrDefault(), fileIndex++);
            AddHosoIfNotNull(hosoList, session.Query<EMRGiayChungSinhWrapper>().Where(x => x.CHI_TIEU_GIAYCHUNGSINH != null && x.CHI_TIEU_GIAYCHUNGSINH.MA_LK == maLk).Select(x => x.CHI_TIEU_GIAYCHUNGSINH!).FirstOrDefault(), fileIndex++);
            AddHosoIfNotNull(hosoList, session.Query<EMRGiayNghiDuongThaiWrapper>().Where(x => x.CHI_TIEU_GIAYNGHIDUONGTHAI != null && x.CHI_TIEU_GIAYNGHIDUONGTHAI.MA_LK == maLk).Select(x => x.CHI_TIEU_GIAYNGHIDUONGTHAI!).FirstOrDefault(), fileIndex++);
            AddHosoIfNotNull(hosoList, session.Query<EMRGiayNghiHuongBHXHWrapper>().Where(x => x.CHI_TIEU_GIAYNGHIHUONGBHXH != null && x.CHI_TIEU_GIAYNGHIHUONGBHXH.MA_LK == maLk).Select(x => x.CHI_TIEU_GIAYNGHIHUONGBHXH!).FirstOrDefault(), fileIndex++);
            fileIndex++;
            AddHosoIfNotNull(hosoList, session.Query<EMRGiayChuyenTuyenBHYTWrapper>().Where(x => x.CHI_TIEU_GIAYCHUYENTUYEN != null && x.CHI_TIEU_GIAYCHUYENTUYEN.MA_LK == maLk).Select(x => x.CHI_TIEU_GIAYCHUYENTUYEN!).FirstOrDefault(), fileIndex++);
            AddHosoIfNotNull(hosoList, session.Query<EMRGiayHenKhamLaiWrapper>().Where(x => x.CHI_TIEU_GIAYHENKHAMLAI != null && x.CHI_TIEU_GIAYHENKHAMLAI.MA_LK == maLk).Select(x => x.CHI_TIEU_GIAYHENKHAMLAI!).FirstOrDefault(), fileIndex++);
            AddHosoIfNotNull(hosoList, session.Query<EMRLaoWrapper>().Where(x => x.CHI_TIEU_LAO != null && x.CHI_TIEU_LAO.MA_LK == maLk).Select(x => x.CHI_TIEU_LAO!).FirstOrDefault(), fileIndex++);

            var macskcb = tongHopWrapper.TONG_HOP.MA_CSKCB;
            var ngayLap = DateTime.Now.ToString("yyyyMMdd");

            var xmlResult = $@"<GIAMDINHHS>
                <THONGTINDONVI>
                <MACSKCB>{macskcb}</MACSKCB>
                </THONGTINDONVI>
                <THONGTINHOSO>
                <NGAYLAP>{ngayLap}</NGAYLAP>
                <SOLUONGHOSO>1</SOLUONGHOSO>
                <DANHSACHHOSO>
                {string.Join("\n", hosoList)}
                </DANHSACHHOSO>
                </THONGTINHOSO>
                </GIAMDINHHS>";

            var xmlBytes = Encoding.UTF8.GetBytes(xmlResult);
            return new Response<byte[]>(xmlBytes);
        }
        private static void AddHosoIfNotNull(List<string> list, object? data, int index)
        {
            if (index == 12 || data == null) return;
            if (data is IList ilist && ilist.Count == 0) return;

            if (data is IList ilistData)
            {
                for (int i = 0; i < ilistData.Count; i++)
                {
                    var item = ilistData[i];
                    var sttProp = item?.GetType().GetProperty("STT");
                    if (sttProp != null && sttProp.CanWrite)
                    {
                        sttProp.SetValue(item, i + 1);
                    }
                }
            }
            else
            {
                var sttProp = data.GetType().GetProperty("STT");
                if (sttProp != null && sttProp.CanWrite)
                {
                    sttProp.SetValue(data, 1);
                }
            }

            var xml = SerializeToXml(data);
            var base64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(xml));
            list.Add($@"
                <HOSO>
                <FILEHOSO>
                <LOAIHOSO>XML{index}</LOAIHOSO>
                <NOIDUNGFILE>{base64}</NOIDUNGFILE>
                </FILEHOSO>
                </HOSO>");
        }
        private static string SerializeToXml(object data)
        {
            var settings = new XmlWriterSettings
            {
                OmitXmlDeclaration = true,
                Encoding = new UTF8Encoding(false),
                Indent = false
            };

            if (data is IList list && list.Count > 0)
            {
                var typeName = list[0]!.GetType().Name;
                return typeName switch
                {
                    "EMRThuoc" => WrapList(list, "CHITIEU_CHITIET_THUOC", "DSACH_CHI_TIET_THUOC", "CHI_TIET_THUOC", settings),
                    "EMRDVKTvaVTYT" => WrapList(list, "CHITIEU_CHITIET_DVKT_VTYT", "DSACH_CHI_TIET_DVKT", "CHI_TIET_DVKT", settings),
                    "EMRDichVuCLS" => WrapList(list, "CHITIEU_CHITIET_DICHVUCANLAMSANG", "DSACH_CHI_TIET_CLS", "CHI_TIET_CLS", settings),
                    "EMRDienBienLamSang" => WrapList(list, "CHITIEU_CHITIET_DIENBIENLAMSANG", "DSACH_CHI_TIET_DIEN_BIEN_BENH", "TIET_DIEN_BIEN_BENH", settings),
                    _ => throw new InvalidOperationException($"Unhandled list type: {typeName}")
                };
            }
            else
            {
                var ns = new XmlSerializerNamespaces();
                ns.Add("", "");

                var type = data.GetType();
                var rawTypeName = type.Name;
                var strippedName = Regex.Replace(rawTypeName, "^EMR", "", RegexOptions.IgnoreCase);
                strippedName = Regex.Replace(strippedName, @"[^A-Za-z0-9_]", "");
                var wrapperTag = rawTypeName == "EMRTongHop" ? "TONG_HOP" : $"CHI_TIET_{strippedName.ToUpper()}";
                var serializer = new XmlSerializer(type);

                using var sw = new StringWriter();
                using var xmlWriter = XmlWriter.Create(sw, settings);
                serializer.Serialize(xmlWriter, data, ns);

                var xml = XElement.Parse(sw.ToString());

                var newRoot = new XElement(wrapperTag, xml.Elements());
                return newRoot.ToString(SaveOptions.DisableFormatting);
            }
        }

        private static string WrapList(IList list, string outerTag, string middleTag, string itemTag, XmlWriterSettings settings)
        {
            var sb = new StringBuilder();
            sb.Append($"<{outerTag}><{middleTag}>");

            foreach (var item in list)
            {
                var serializer = new XmlSerializer(item.GetType());

                var ns = new XmlSerializerNamespaces();
                ns.Add("", "");
                ns.Add("xsi", "http://www.w3.org/2001/XMLSchema-instance");

                using var sw = new StringWriter();
                using var xmlWriter = XmlWriter.Create(sw, settings);
                serializer.Serialize(xmlWriter, item, ns);

                var xml = XElement.Parse(sw.ToString());

                foreach (var elem in xml.DescendantsAndSelf())
                {
                    var nilAttr = elem.Attribute(XName.Get("nil", "http://www.w3.org/2001/XMLSchema-instance"));
                    nilAttr?.Remove();
                }

                sb.Append($"<{itemTag}>{string.Concat(xml.Elements())}</{itemTag}>");
            }

            sb.Append($"</{middleTag}></{outerTag}>");
            return sb.ToString();
        }

    }
}