using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BuildingBlocks.Abstractions;
using Marten;

namespace GoTRUST.EMR.Application.Features.EMRs.Queries
{
    public record GetEMRQuery : IQuery<Response<List<HoSoBenhAn>>>
    {
        public string? HospitalId { get; set; }
        public string? PatientId { get; set; }
        public class GetEMRQueryHandler(
            IDocumentStore store
        ) : IQueryHandler<GetEMRQuery, Response<List<HoSoBenhAn>>>
        {
            public async Task<Response<List<HoSoBenhAn>>> Handle(GetEMRQuery request, CancellationToken cancellationToken)
            {
                using var session = store.QuerySession();

                // <PERSON>en không hỗ trợ null, nên phải kiểm tra null trước
                string hospitalcode = request.HospitalId ?? string.Empty;
                string patientId = request.PatientId ?? string.Empty;
                var emrs = await session.Query<HoSoBenhAnWrapper>()
                        .Where(p => p.HospitalCode.Search(hospitalcode) &&
                              (p.HoSoBenhAn == null ||
                               p.HoSoBenhAn.ThongTinBenhNhan == null ||
                               p.HoSoBenhAn.ThongTinBenhNhan.mabenhnhan.Search(patientId)))
                        .Select(p => p.HoSoBenhAn!)
                        .Where(p => p != null)
                        .ToListAsync(token: cancellationToken);

                var response = new Response<List<HoSoBenhAn>>(emrs?.ToList() ?? []);

                return response;
            }
        }
    }
}