using Marten;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using EF = Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions;

namespace GoTRUST.EMR.Application.Features.EMRs.Queries
{
    public record GetPatientsBHXHInfoQuery(
        string? SearchTerm = null,
        string? MaKhoa = null,
        DateTime? SyncedAt = null
    ) : PaginationRequest, IRequest<PaginationResponse<GetPatientsBHXHInfoQueryResponse>>;

    public record GetPatientsBHXHInfoQueryResponse
    {
        public string SyncCode { get; set; } = string.Empty;
        public DateTime? SyncedAt { get; set; }
        public string NGAY_SINH { get; set; } = string.Empty;
        public string MA_THE_BHYT { get; set; } = string.Empty;
        public string DIA_CHI { get; set; } = string.Empty;
        public string MA_DANTOC { get; set; } = string.Empty;
        public string TEN_DANTOC { get; set; } = string.Empty;
        public string MA_LK { get; set; } = string.Empty;
        public string MA_BN { get; set; } = string.Empty;
        public string MA_LOAI_KCB { get; set; } = string.Empty;
        public string TEN_LOAI_KCB { get; set; } = string.Empty;
        public string MA_NGHE_NGHIEP { get; set; } = string.Empty;
        public string TEN_NGHE_NGHIEP { get; set; } = string.Empty;
        public string MA_KHOA { get; set; } = string.Empty;
        public string TEN_KHOA { get; set; } = string.Empty;
        public string HO_TEN { get; set; } = string.Empty;
        public string SO_CCCD { get; set; } = string.Empty;
        public string DIEN_THOAI { get; set; } = string.Empty;
        public string EMAIL { get; set; } = string.Empty;
        public string TT_HON_NHAN { get; set; } = string.Empty;
        public string NOI_LAM_VIEC { get; set; } = string.Empty;
        public string AvatarUrl { get; set; } = string.Empty;
    }

    public class GetPatientsBHXHInfoQueryHandler(
        IDocumentStore documentStore,
        IApplicationDbContext dbContext,
        IHttpContextAccessor httpContextAccessor
    ) : IRequestHandler<GetPatientsBHXHInfoQuery, PaginationResponse<GetPatientsBHXHInfoQueryResponse>>
    {
        private readonly IDocumentStore _documentStore = documentStore;

        public async Task<PaginationResponse<GetPatientsBHXHInfoQueryResponse>> Handle(GetPatientsBHXHInfoQuery request, CancellationToken cancellationToken)
        {
            using var session = _documentStore.QuerySession();
            var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

            var hospitalId = (httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal())
                ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");

            var hospital = await EF.FirstOrDefaultAsync(
                dbContext.Hospitals.AsNoTracking(),
                h => h.Id == hospitalId,
                cancellationToken
            ) ?? throw new HospitalNotFoundException(hospitalId);

            var query = session.Query<EMRTongHopWrapper>()
                .Where(x => x.HospitalCode == hospital.Code);

            if (!string.IsNullOrWhiteSpace(request.MaKhoa))
            {
                query = query.Where(x => x.TONG_HOP != null && x.TONG_HOP.MA_KHOA == request.MaKhoa);
            }

            if (request.SyncedAt.HasValue)
            {
                var date = request.SyncedAt.Value.Date;
                var nextDate = date.AddDays(1);

                query = query.Where(x =>
                    x.SyncedAt.HasValue &&
                    x.SyncedAt.Value >= date &&
                    x.SyncedAt.Value < nextDate
                );
            }

            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                var term = request.SearchTerm.Trim().ToLower();
                query = query.Where(x =>
                    x.TONG_HOP != null && (
                        (x.TONG_HOP.HO_TEN != null && x.TONG_HOP.HO_TEN.ToLower().Contains(term)) ||
                        (x.TONG_HOP.MA_BN != null && x.TONG_HOP.MA_BN.ToLower().Contains(term)) ||
                        (x.TONG_HOP.SO_CCCD != null && x.TONG_HOP.SO_CCCD.ToLower().Contains(term)) ||
                        (x.TONG_HOP.DIEN_THOAI != null && x.TONG_HOP.DIEN_THOAI.ToLower().Contains(term))
                    )
                );
            }

            var totalCount = query.Count();

            var list = query
                .Skip((request.PageIndex!.Value - 1) * request.PageSize!.Value)
                .Take(request.PageSize!.Value)
                .ToList();

            var maBns = list
                .Where(x => x.TONG_HOP != null && !string.IsNullOrEmpty(x.TONG_HOP.MA_BN))
                .Select(x => x.TONG_HOP!.MA_BN)
                .Distinct()
                .ToList();

            var patients = await EF.ToListAsync(
                dbContext.Patients.AsNoTracking()
                    .Where(p => maBns.Contains(p.PatientCode))
                    .Select(p => new { p.PatientCode, p.AvatarUrl }),
                cancellationToken
            );

            var patientAvatarDict = patients.ToDictionary(p => p.PatientCode, p => p.AvatarUrl);

            var ngheNghieps = await EF.ToListAsync(
                dbContext.NgheNghieps.AsNoTracking()
                    .Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            var danTocs = await EF.ToListAsync(
                dbContext.DanTocs.AsNoTracking()
                    .Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            var loaiKCBs = await EF.ToListAsync(
                dbContext.LoaiKCBs.AsNoTracking()
                    .Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            var khoas = await EF.ToListAsync(
                dbContext.Khoas.AsNoTracking()
                    .Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            var responses = list.Select(x => new GetPatientsBHXHInfoQueryResponse
            {
                SyncCode = x.SyncCode,
                SyncedAt = x.SyncedAt,
                NGAY_SINH = x.TONG_HOP?.NGAY_SINH ?? string.Empty,
                MA_THE_BHYT = x.TONG_HOP?.MA_THE_BHYT ?? string.Empty,
                DIA_CHI = x.TONG_HOP?.DIA_CHI ?? string.Empty,
                DIEN_THOAI = x.TONG_HOP?.DIEN_THOAI ?? string.Empty,
                MA_DANTOC = x.TONG_HOP?.MA_DANTOC ?? string.Empty,
                MA_LK = x.TONG_HOP?.MA_LK ?? string.Empty,
                MA_BN = x.TONG_HOP?.MA_BN ?? string.Empty,
                MA_LOAI_KCB = x.TONG_HOP?.MA_LOAI_KCB ?? string.Empty,
                MA_KHOA = x.TONG_HOP?.MA_KHOA ?? string.Empty,
                HO_TEN = x.TONG_HOP?.HO_TEN ?? string.Empty,
                SO_CCCD = x.TONG_HOP?.SO_CCCD ?? string.Empty,
                TEN_DANTOC = danTocs.Find(d => d.Id == x.TONG_HOP?.MA_DANTOC)?.Name ?? string.Empty,
                TEN_LOAI_KCB = loaiKCBs.Find(l => l.Id == x.TONG_HOP?.MA_LOAI_KCB)?.Name ?? string.Empty,
                MA_NGHE_NGHIEP = x.TONG_HOP?.MA_NGHE_NGHIEP ?? string.Empty,
                TEN_NGHE_NGHIEP = ngheNghieps.Find(n => n.Id == x.TONG_HOP?.MA_NGHE_NGHIEP)?.Name ?? string.Empty,
                TEN_KHOA = khoas.Find(k => k.Id == x.TONG_HOP?.MA_KHOA)?.Name ?? string.Empty,
                AvatarUrl = (x.TONG_HOP != null && !string.IsNullOrEmpty(x.TONG_HOP.MA_BN) && patientAvatarDict.TryGetValue(x.TONG_HOP.MA_BN, out var avatar)) ? avatar : string.Empty
            }).ToList();

            return new PaginationResponse<GetPatientsBHXHInfoQueryResponse>(
                request.PageIndex,
                request.PageSize,
                totalCount,
                responses
            );
        }
    }
}