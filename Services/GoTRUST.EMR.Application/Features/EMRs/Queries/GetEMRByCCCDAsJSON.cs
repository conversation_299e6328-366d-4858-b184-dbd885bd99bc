using Mapster;
using Marten;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Features.EMRs.Models;
using GoTRUST.EMR.Application.Helpers;
using BuildingBlocks.Exceptions;
using EF = Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions;

namespace GoTRUST.EMR.Application.Features.EMRs.Queries
{
    public record GetEMRByCCCDAsJSON(
        string MA_LK
    ) : IRequest<Response<GetEMRByCCCDAsJSONResponse>>;

    public record GetEMRByCCCDAsJSONResponse
    {
        public CreateEMRCheckinJSONResponse? Checkin { get; set; }
        public CreateEMRTongHopJSONResponse? TongHop { get; set; }
        public List<CreateEMRThuocJSONResponse> Thuocs { get; set; } = [];
        public List<CreateEMRDVKTvaVTYTJSONResponse> DVKTvaVTYTs { get; set; } = [];
        public List<CreateEMRDichVuCLSJSONResponse> DichVuCLSes { get; set; } = [];
        public List<CreateEMRDienBienLamSangJSONResponse> DienBienLamSangs { get; set; } = [];
        public CreateEMRHIVvaAIDSJSONResponse? HIVvaAIDS { get; set; }
        public CreateEMRGiayRaVienJSONResponse? GiayRaVien { get; set; }
        public CreateEMRTomTatHSBAJSONResponse? TomTatHSBA { get; set; }
        public CreateEMRGiayChungSinhJSONResponse? GiayChungSinh { get; set; }
        public CreateEMRGiayNghiDuongThaiJSONResponse? GiayNghiDuongThai { get; set; }
        public CreateEMRGiayNghiHuongBHXHJSONResponse? GiayNghiHuongBHXH { get; set; }
        public CreateEMRGiayChuyenTuyenBHYTJSONResponse? GiayChuyenTuyenBHYT { get; set; }
        public CreateEMRGiayHenKhamLaiJSONResponse? GiayHenKhamLai { get; set; }
        public CreateEMRLaoJSONResponse? Lao { get; set; }
        public List<CreateEMRGDYKJSONResponse>? GDYK { get; set; }
    }

    public class GetEMRByCCCDAsJSONHandler(IDocumentStore documentStore, IApplicationDbContext dbContext,
    IHttpContextAccessor httpContextAccessor) : IRequestHandler<GetEMRByCCCDAsJSON, Response<GetEMRByCCCDAsJSONResponse>>
    {
        private readonly IDocumentStore _documentStore = documentStore;

        public async Task<Response<GetEMRByCCCDAsJSONResponse>> Handle(GetEMRByCCCDAsJSON request, CancellationToken cancellationToken)
        {
            using var session = _documentStore.QuerySession();
            var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

            var hospitalId = (httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal())
                ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");

            var hospital = await EF.FirstOrDefaultAsync(
                dbContext.Hospitals.AsNoTracking(),
                h => h.Id == hospitalId,
                cancellationToken
            ) ?? throw new HospitalNotFoundException(hospitalId);

            // Query EMR by MA_LK
            var tongHopWrapper = session.Query<EMRTongHopWrapper>()
                .FirstOrDefault(x => x.TONG_HOP != null && x.TONG_HOP.MA_LK == request.MA_LK && x.HospitalCode == hospital.Code);

            if (tongHopWrapper == null)
                throw new NotFoundException("Không tìm thấy hồ sơ bệnh án với MA_LK đã cung cấp.");

            var maLk = tongHopWrapper.TONG_HOP!.MA_LK;

            var tongHop = tongHopWrapper.TONG_HOP.Adapt<CreateEMRTongHopJSONResponse>();
            var thuocs = session.Query<EMRThuocWrapper>()
                .Where(x => x.CHITIET_THUOC != null && x.CHITIET_THUOC.MA_LK == maLk)
                .ToList()
                .Select(x => x.CHITIET_THUOC!.Adapt<CreateEMRThuocJSONResponse>())
                .ToList();
            var dvktvaVTYTs = session.Query<EMRDVKTvaVTYTWrapper>()
                .Where(x => x.CHI_TIET_DVKT != null && x.CHI_TIET_DVKT.MA_LK == maLk)
                .ToList()
                .Select(x => x.CHI_TIET_DVKT!.Adapt<CreateEMRDVKTvaVTYTJSONResponse>())
                .ToList();
            var dichVuCLSes = session.Query<EMRDichVuCLSWrapper>()
                .Where(x => x.CHI_TIET_CLS != null && x.CHI_TIET_CLS.MA_LK == maLk)
                .ToList()
                .Select(x => x.CHI_TIET_CLS!.Adapt<CreateEMRDichVuCLSJSONResponse>())
                .ToList();
            var dienBienLamSangs = session.Query<EMRDienBienLamSangWrapper>()
                .Where(x => x.CHI_TIET_DIEN_BIEN_BENH != null && x.CHI_TIET_DIEN_BIEN_BENH.MA_LK == maLk)
                .ToList()
                .Select(x => x.CHI_TIET_DIEN_BIEN_BENH!.Adapt<CreateEMRDienBienLamSangJSONResponse>())
                .ToList();
            var gdyks = session.Query<EMRGDYKWrapper>()
                .Where(x => x.CHI_TIEU_GIAM_DINH_Y_KHOA != null && x.CHI_TIEU_GIAM_DINH_Y_KHOA.SO_CCCD == tongHop.SO_CCCD)
                .ToList()
                .Select(x => x.CHI_TIEU_GIAM_DINH_Y_KHOA!.Adapt<CreateEMRGDYKJSONResponse>())
                .ToList();

            var checkin = session.Query<EMRCheckinWrapper>()
                .FirstOrDefault(x => x.CHECK_IN != null && x.CHECK_IN.MA_LK == maLk)?
                .CHECK_IN?.Adapt<CreateEMRCheckinJSONResponse>();

            var hivvaAIDS = session.Query<EMRHIVvaAIDSWrapper>()
                .FirstOrDefault(x => x.CHI_TIEU_CHAMSOCVADIEUTRIHIVVAAIDS != null && x.CHI_TIEU_CHAMSOCVADIEUTRIHIVVAAIDS.MA_LK == maLk)?
                .CHI_TIEU_CHAMSOCVADIEUTRIHIVVAAIDS?.Adapt<CreateEMRHIVvaAIDSJSONResponse>();

            var giayRaVien = session.Query<EMRGiayRaVienWrapper>()
                .FirstOrDefault(x => x.CHI_TIEU_GIAYNGHIHUONGBHXH != null && x.CHI_TIEU_GIAYNGHIHUONGBHXH.MA_LK == maLk)?
                .CHI_TIEU_GIAYNGHIHUONGBHXH?.Adapt<CreateEMRGiayRaVienJSONResponse>();

            var tomTatHSBA = session.Query<EMRTomTatHSBAWrapper>()
                .FirstOrDefault(x => x.CHI_TIEU_TOMTATHSBA != null && x.CHI_TIEU_TOMTATHSBA.MA_LK == maLk)?
                .CHI_TIEU_TOMTATHSBA?.Adapt<CreateEMRTomTatHSBAJSONResponse>();

            var giayChungSinh = session.Query<EMRGiayChungSinhWrapper>()
                .FirstOrDefault(x => x.CHI_TIEU_GIAYCHUNGSINH != null && x.CHI_TIEU_GIAYCHUNGSINH.MA_LK == maLk)?
                .CHI_TIEU_GIAYCHUNGSINH?.Adapt<CreateEMRGiayChungSinhJSONResponse>();

            var giayNghiDuongThai = session.Query<EMRGiayNghiDuongThaiWrapper>()
                .FirstOrDefault(x => x.CHI_TIEU_GIAYNGHIDUONGTHAI != null && x.CHI_TIEU_GIAYNGHIDUONGTHAI.MA_LK == maLk)?
                .CHI_TIEU_GIAYNGHIDUONGTHAI?.Adapt<CreateEMRGiayNghiDuongThaiJSONResponse>();

            var giayNghiHuongBHXH = session.Query<EMRGiayNghiHuongBHXHWrapper>()
                .FirstOrDefault(x => x.CHI_TIEU_GIAYNGHIHUONGBHXH != null && x.CHI_TIEU_GIAYNGHIHUONGBHXH.MA_LK == maLk)?
                .CHI_TIEU_GIAYNGHIHUONGBHXH?.Adapt<CreateEMRGiayNghiHuongBHXHJSONResponse>();

            var giayChuyenTuyenBHYT = session.Query<EMRGiayChuyenTuyenBHYTWrapper>()
                .FirstOrDefault(x => x.CHI_TIEU_GIAYCHUYENTUYEN != null && x.CHI_TIEU_GIAYCHUYENTUYEN.MA_LK == maLk)?
                .CHI_TIEU_GIAYCHUYENTUYEN?.Adapt<CreateEMRGiayChuyenTuyenBHYTJSONResponse>();

            var giayHenKhamLai = session.Query<EMRGiayHenKhamLaiWrapper>()
                .FirstOrDefault(x => x.CHI_TIEU_GIAYHENKHAMLAI != null && x.CHI_TIEU_GIAYHENKHAMLAI.MA_LK == maLk)?
                .CHI_TIEU_GIAYHENKHAMLAI?.Adapt<CreateEMRGiayHenKhamLaiJSONResponse>();

            var lao = session.Query<EMRLaoWrapper>()
                .FirstOrDefault(x => x.CHI_TIEU_LAO != null && x.CHI_TIEU_LAO.MA_LK == maLk)?
                .CHI_TIEU_LAO?.Adapt<CreateEMRLaoJSONResponse>();

            // Map fields names
            var ngheNghiepIds = new List<string>
            {
                tongHop.MA_NGHE_NGHIEP
            };
            if (giayChuyenTuyenBHYT != null)
                ngheNghiepIds.Add(giayChuyenTuyenBHYT.MA_NGHE_NGHIEP);

            var ngheNghieps = await EF.ToListAsync(
                dbContext.NgheNghieps.AsNoTracking()
                    .Where(x => ngheNghiepIds.Contains(x.Id))
                    .Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            tongHop.TEN_NGHE_NGHIEP = ngheNghieps.FirstOrDefault(x => x.Id == tongHop.MA_NGHE_NGHIEP)?.Name;
            if (giayChuyenTuyenBHYT != null)
                giayChuyenTuyenBHYT.TEN_NGHE_NGHIEP = ngheNghieps.FirstOrDefault(x => x.Id == giayChuyenTuyenBHYT.MA_NGHE_NGHIEP)?.Name;

            var tinhCuTruIds = new List<string>
            {
                tongHop.MATINH_CU_TRU
            };
            if (giayChungSinh != null)
                tinhCuTruIds.Add(giayChungSinh.MATINH_CU_TRU);
            if (hivvaAIDS != null)
                tinhCuTruIds.Add(hivvaAIDS.MATINH_CU_TRU);

            var tinhCuTrus = await EF.ToListAsync(
                dbContext.Tinhs.AsNoTracking()
                    .Where(x => tinhCuTruIds.Contains(x.Id))
                    .Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            tongHop.TENTINH_CU_TRU = tinhCuTrus.FirstOrDefault(x => x.Id == tongHop.MATINH_CU_TRU)?.Name;
            if (giayChungSinh != null)
                giayChungSinh.TENTINH_CU_TRU = tinhCuTrus.FirstOrDefault(x => x.Id == giayChungSinh.MATINH_CU_TRU)?.Name;
            if (hivvaAIDS != null)
                hivvaAIDS.TENTINH_CU_TRU = tinhCuTrus.FirstOrDefault(x => x.Id == hivvaAIDS.MATINH_CU_TRU)?.Name;

            var huyenCuTruIds = new List<string>
            {
                tongHop.MAHUYEN_CU_TRU
            };
            if (giayChungSinh != null)
                huyenCuTruIds.Add(giayChungSinh.MAHUYEN_CU_TRU);
            if (hivvaAIDS != null)
                huyenCuTruIds.Add(hivvaAIDS.MAHUYEN_CU_TRU);

            var huyenCuTrus = await EF.ToListAsync(
                dbContext.Huyens.AsNoTracking()
                    .Where(x => huyenCuTruIds.Contains(x.Id))
                    .Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            tongHop.TENHUYEN_CU_TRU = huyenCuTrus.FirstOrDefault(x => x.Id == tongHop.MAHUYEN_CU_TRU)?.Name;
            if (giayChungSinh != null)
                giayChungSinh.TENHUYEN_CU_TRU = huyenCuTrus.FirstOrDefault(x => x.Id == giayChungSinh.MAHUYEN_CU_TRU)?.Name;
            if (hivvaAIDS != null)
                hivvaAIDS.TENHUYEN_CU_TRU = huyenCuTrus.FirstOrDefault(x => x.Id == hivvaAIDS.MAHUYEN_CU_TRU)?.Name;

            var xaCuTruIds = new List<string>
            {
                tongHop.MAXA_CU_TRU
            };
            if (giayChungSinh != null)
                xaCuTruIds.Add(giayChungSinh.MAXA_CU_TRU);
            if (hivvaAIDS != null)
                xaCuTruIds.Add(hivvaAIDS.MAXA_CU_TRU);

            var xaCuTrus = await EF.ToListAsync(
                dbContext.Xas.AsNoTracking()
                    .Where(x => xaCuTruIds.Contains(x.Id))
                    .Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            tongHop.TENXA_CU_TRU = xaCuTrus.FirstOrDefault(x => x.Id == tongHop.MAXA_CU_TRU)?.Name;
            if (giayChungSinh != null)
                giayChungSinh.TENXA_CU_TRU = xaCuTrus.FirstOrDefault(x => x.Id == giayChungSinh.MAXA_CU_TRU)?.Name;
            if (hivvaAIDS != null)
                hivvaAIDS.TENXA_CU_TRU = xaCuTrus.FirstOrDefault(x => x.Id == hivvaAIDS.MAXA_CU_TRU)?.Name;

            var danTocIds = new List<string>
            {
                tongHop.MA_DANTOC
            };
            if (giayChungSinh != null)
                danTocIds.Add(giayChungSinh.MA_DANTOC_NND);
            if (giayChuyenTuyenBHYT != null)
            {
                danTocIds.Add(giayChuyenTuyenBHYT.MA_DANTOC);
            }

            var danTocs = await EF.ToListAsync(
                dbContext.DanTocs.AsNoTracking()
                    .Where(x => danTocIds.Contains(x.Id))
                    .Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            tongHop.TEN_DANTOC = danTocs.FirstOrDefault(x => x.Id == tongHop.MA_DANTOC)?.Name;
            if (giayChungSinh != null)
                giayChungSinh.TEN_DANTOC_NND = danTocs.FirstOrDefault(x => x.Id == giayChungSinh.MA_DANTOC_NND)?.Name;
            if (giayChuyenTuyenBHYT != null)
                giayChuyenTuyenBHYT.TEN_DANTOC = danTocs.FirstOrDefault(x => x.Id == giayChuyenTuyenBHYT.MA_DANTOC)?.Name;

            var quocTichIds = new List<string>
            {
                tongHop.MA_QUOCTICH
            };
            if (giayChungSinh != null)
                quocTichIds.Add(giayChungSinh.MA_QUOCTICH);
            if (giayChuyenTuyenBHYT != null)
                quocTichIds.Add(giayChuyenTuyenBHYT.MA_QUOCTICH);

            var quoctichs = await EF.ToListAsync(
                dbContext.QuocTiches.AsNoTracking()
                    .Where(x => quocTichIds.Contains(x.Id))
                    .Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            tongHop.TEN_QUOCTICH = quoctichs.FirstOrDefault(x => x.Id == tongHop.MA_QUOCTICH)?.Name;
            if (giayChungSinh != null)
                giayChungSinh.TEN_QUOCTICH = quoctichs.FirstOrDefault(x => x.Id == giayChungSinh.MA_QUOCTICH)?.Name;
            if (giayChuyenTuyenBHYT != null)
                giayChuyenTuyenBHYT.TEN_QUOCTICH = quoctichs.FirstOrDefault(x => x.Id == giayChuyenTuyenBHYT.MA_QUOCTICH)?.Name;

            var response = new GetEMRByCCCDAsJSONResponse
            {
                Checkin = checkin,
                TongHop = tongHop,
                Thuocs = thuocs,
                DVKTvaVTYTs = dvktvaVTYTs,
                DichVuCLSes = dichVuCLSes,
                DienBienLamSangs = dienBienLamSangs,
                HIVvaAIDS = hivvaAIDS,
                GiayRaVien = giayRaVien,
                TomTatHSBA = tomTatHSBA,
                GiayChungSinh = giayChungSinh,
                GiayNghiDuongThai = giayNghiDuongThai,
                GiayNghiHuongBHXH = giayNghiHuongBHXH,
                GiayChuyenTuyenBHYT = giayChuyenTuyenBHYT,
                GiayHenKhamLai = giayHenKhamLai,
                GDYK = gdyks,
                Lao = lao
            };

            return new Response<GetEMRByCCCDAsJSONResponse>(response);
        }
    }
}