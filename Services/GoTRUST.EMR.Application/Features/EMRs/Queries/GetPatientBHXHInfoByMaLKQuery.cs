using Marten;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using EF = Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions;

namespace GoTRUST.EMR.Application.Features.EMRs.Queries
{
    public record GetPatientBHXHInfoByMaLKQuery(
        string MaLK
    ) : IRequest<Response<GetPatientBHXHInfoByMaLKQueryResponse>>;

    public record GetPatientBHXHInfoByMaLKQueryResponse
    {
        public string SyncCode { get; set; } = string.Empty;
        public DateTime? SyncedAt { get; set; }
        public string NGAY_SINH { get; set; } = string.Empty;
        public string MA_THE_BHYT { get; set; } = string.Empty;
        public string DIA_CHI { get; set; } = string.Empty;
        public string MA_DANTOC { get; set; } = string.Empty;
        public string TEN_DANTOC { get; set; } = string.Empty;
        public string MA_LK { get; set; } = string.Empty;
        public string MA_BN { get; set; } = string.Empty;
        public string MA_LOAI_KCB { get; set; } = string.Empty;
        public string TEN_LOAI_KCB { get; set; } = string.Empty;
        public string MA_NGHE_NGHIEP { get; set; } = string.Empty;
        public string TEN_NGHE_NGHIEP { get; set; } = string.Empty;
        public string MA_KHOA { get; set; } = string.Empty;
        public string TEN_KHOA { get; set; } = string.Empty;
        public string HO_TEN { get; set; } = string.Empty;
        public string SO_CCCD { get; set; } = string.Empty;
        public string DIEN_THOAI { get; set; } = string.Empty;
        public string EMAIL { get; set; } = string.Empty;
        public string TT_HON_NHAN { get; set; } = string.Empty;
        public string NOI_LAM_VIEC { get; set; } = string.Empty;
        public string GIOI_TINH { get; set; } = string.Empty;
        public string TUOI { get; set; } = string.Empty;
        public string AvatarUrl { get; set; } = string.Empty;
    }
    public class GetPatientBHXHInfoByMaLKQueryHandler(
        IDocumentStore documentStore,
        IApplicationDbContext dbContext,
        IHttpContextAccessor httpContextAccessor
    ) : IRequestHandler<GetPatientBHXHInfoByMaLKQuery, Response<GetPatientBHXHInfoByMaLKQueryResponse>>
    {
        private readonly IDocumentStore _documentStore = documentStore;

        public async Task<Response<GetPatientBHXHInfoByMaLKQueryResponse>> Handle(GetPatientBHXHInfoByMaLKQuery request, CancellationToken cancellationToken)
        {
            using var session = _documentStore.QuerySession();
            var currentUser = httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "System";

            var hospitalId = (httpContextAccessor.HttpContext?.User?.RetrieveHospitalIdFromPrincipal())
                ?? throw new UnauthorizedAccessException("Không thể xác định thông tin bệnh viện của người dùng. Vui lòng đăng nhập lại.");

            var hospital = await EF.FirstOrDefaultAsync(
                dbContext.Hospitals.AsNoTracking(),
                h => h.Id == hospitalId,
                cancellationToken
            ) ?? throw new HospitalNotFoundException(hospitalId);

            var emr = session.Query<EMRTongHopWrapper>()
                .Where(x => x.HospitalCode == hospital.Code && x.TONG_HOP != null && x.TONG_HOP.MA_LK == request.MaLK)
                .FirstOrDefault();

            if (emr == null)
                throw new Exception($"Không tìm thấy bệnh nhân với MA_LK: {request.MaLK}");

            var maBn = emr.TONG_HOP?.MA_BN;
            string avatarUrl = string.Empty;
            if (!string.IsNullOrEmpty(maBn))
            {
                var patient = await EF.FirstOrDefaultAsync(
                    dbContext.Patients.AsNoTracking().Where(p => p.PatientCode == maBn),
                    cancellationToken
                );
                avatarUrl = patient?.AvatarUrl ?? string.Empty;
            }

            var ngheNghieps = await EF.ToListAsync(
                dbContext.NgheNghieps.AsNoTracking().Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            var danTocs = await EF.ToListAsync(
                dbContext.DanTocs.AsNoTracking().Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            var loaiKCBs = await EF.ToListAsync(
                dbContext.LoaiKCBs.AsNoTracking().Select(x => new { x.Id, x.Name }),
                cancellationToken
            );
            var khoas = await EF.ToListAsync(
                dbContext.Khoas.AsNoTracking().Select(x => new { x.Id, x.Name }),
                cancellationToken
            );

            var response = new GetPatientBHXHInfoByMaLKQueryResponse
            {
                SyncCode = emr.SyncCode,
                SyncedAt = emr.SyncedAt,
                NGAY_SINH = emr.TONG_HOP?.NGAY_SINH ?? string.Empty,
                MA_THE_BHYT = emr.TONG_HOP?.MA_THE_BHYT ?? string.Empty,
                DIA_CHI = emr.TONG_HOP?.DIA_CHI ?? string.Empty,
                DIEN_THOAI = emr.TONG_HOP?.DIEN_THOAI ?? string.Empty,
                MA_DANTOC = emr.TONG_HOP?.MA_DANTOC ?? string.Empty,
                MA_LK = emr.TONG_HOP?.MA_LK ?? string.Empty,
                MA_BN = emr.TONG_HOP?.MA_BN ?? string.Empty,
                MA_LOAI_KCB = emr.TONG_HOP?.MA_LOAI_KCB ?? string.Empty,
                MA_KHOA = emr.TONG_HOP?.MA_KHOA ?? string.Empty,
                HO_TEN = emr.TONG_HOP?.HO_TEN ?? string.Empty,
                SO_CCCD = emr.TONG_HOP?.SO_CCCD ?? string.Empty,
                TEN_DANTOC = danTocs.Find(d => d.Id == emr.TONG_HOP?.MA_DANTOC)?.Name ?? string.Empty,
                TEN_LOAI_KCB = loaiKCBs.Find(l => l.Id == emr.TONG_HOP?.MA_LOAI_KCB)?.Name ?? string.Empty,
                MA_NGHE_NGHIEP = emr.TONG_HOP?.MA_NGHE_NGHIEP ?? string.Empty,
                TEN_NGHE_NGHIEP = ngheNghieps.Find(n => n.Id == emr.TONG_HOP?.MA_NGHE_NGHIEP)?.Name ?? string.Empty,
                TEN_KHOA = khoas.Find(k => k.Id == emr.TONG_HOP?.MA_KHOA)?.Name ?? string.Empty,
                GIOI_TINH = emr.TONG_HOP?.GIOI_TINH switch
                {
                    1 => "Nam",
                    2 => "Nữ",
                    3 => "Không xác định",
                    _ => string.Empty
                },
                TUOI = DateTime.TryParseExact(emr.TONG_HOP?.NGAY_SINH, "yyyyMMddHHmm", null, System.Globalization.DateTimeStyles.None, out var dob)
                    ? ((int)((DateTime.Now - dob).TotalDays / 365.25)).ToString()
                    : string.Empty,
                AvatarUrl = avatarUrl
            };

            return new Response<GetPatientBHXHInfoByMaLKQueryResponse>(response);
        }
    }
}
