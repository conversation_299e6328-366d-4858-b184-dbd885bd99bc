using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml;
using System.Xml.Linq;
using Microsoft.Extensions.Logging;

namespace GoTRUST.EMR.Application.Features.EMRs.XmlHelpers
{
    public class DeepXmlSerializer
    {
        private static readonly ILogger _logger = LoggerFactory.Create(builder => builder.AddConsole())
                                                             .CreateLogger(typeof(DeepXmlSerializer));

        public static object? Deserialize<T>(Stream stream) where T : class
        {
            using var reader = XmlReader.Create(stream);
            var doc = XDocument.Load(reader);

            var targetType = typeof(T);
            var propertyNames = targetType.GetProperties().Select(p => p.Name).ToHashSet();

            // Find all elements that have our properties
            var candidateElements = doc.Descendants()
                .Where(e => e.Elements().Any(child =>
                    propertyNames.Contains(child.Name.LocalName) &&
                    !string.IsNullOrWhiteSpace(child.Value)))
                .ToList();

            if (!candidateElements.Any())
                return null;

            // Group by structure and find the best match
            var bestElements = candidateElements
                .GroupBy(e => string.Join(",", e.Elements().Select(x => x.Name.LocalName).OrderBy(x => x)))
                .OrderByDescending(g => g.First().Elements().Count(e => propertyNames.Contains(e.Name.LocalName)))
                .ThenByDescending(g => g.Count())
                .FirstOrDefault()
                ?.ToList() ?? [];

            // If we found multiple matching elements, return as list
            if (bestElements.Count > 1)
            {
                var listType = typeof(List<>).MakeGenericType(targetType);
                var list = Activator.CreateInstance(listType);
                var addMethod = listType.GetMethod("Add");

                foreach (var element in bestElements)
                {
                    var item = DeserializeElement(element, targetType);
                    if (HasAnyValue(item, targetType))
                    {
                        addMethod!.Invoke(list, [item]);
                    }
                }
                return list;
            }

            // Otherwise return single object
            var bestElement = bestElements.FirstOrDefault();
            return bestElement != null ? DeserializeElement(bestElement, targetType) : null;
        }

        private static object? ConvertValue(string value, Type targetType)
        {
            if (string.IsNullOrEmpty(value))
                return null;

            value = value.Trim();

            // Handle nullable types
            var underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;

            try
            {
                return underlyingType switch
                {
                    Type t when t == typeof(int) => int.Parse(value),
                    Type t when t == typeof(decimal) => decimal.Parse(value),
                    Type t when t == typeof(DateTime) => DateTime.Parse(value),
                    Type t when t == typeof(bool) => bool.Parse(value),
                    Type t when t == typeof(double) => double.Parse(value),
                    Type t when t == typeof(float) => float.Parse(value),
                    Type t when t == typeof(long) => long.Parse(value),
                    Type t when t.IsEnum => Enum.Parse(t, value),
                    _ => value
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to convert value '{Value}' to type {Type}", value, targetType.Name);
                return null;
            }
        }

        private static object DeserializeElement(XElement element, Type type)
        {
            var result = Activator.CreateInstance(type);
            var properties = type.GetProperties()
                .ToDictionary(p => p.Name, p => p);

            foreach (var childElement in element.Elements())
            {
                if (properties.TryGetValue(childElement.Name.LocalName, out var prop))
                {
                    try
                    {
                        var value = ConvertValue(childElement.Value, prop.PropertyType);
                        if (value != null)
                        {
                            prop.SetValue(result, value);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to set value for property {Property}", prop.Name);
                    }
                }
            }

            return result!;
        }

        private static object DeserializeLeafElements(List<XElement> elements, Type type)
        {
            var result = Activator.CreateInstance(type);
            var properties = type.GetProperties()
                .ToDictionary(p => p.Name, p => p);

            foreach (var element in elements)
            {
                if (properties.TryGetValue(element.Name.LocalName, out var prop))
                {
                    try
                    {
                        var value = ConvertValue(element.Value, prop.PropertyType);
                        if (value != null)
                        {
                            prop.SetValue(result, value);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to set value for property {Property}", prop.Name);
                    }
                }
            }

            return result!;
        }

        private static bool HasAnyValue(object obj, Type type)
        {
            return type.GetProperties().Any(p =>
                p.GetValue(obj) != null &&
                !string.IsNullOrWhiteSpace(p.GetValue(obj)?.ToString()));
        }
    }
}
