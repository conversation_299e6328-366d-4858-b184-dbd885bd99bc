namespace GoTRUST.EMR.Application.Features.EMRs.Models
{
    public class CreateEMRGiayNghiHuongBHXHRequestModel
    {
        public string MA_LK { get; set; } = string.Empty;
        public string SO_CT { get; set; } = string.Empty;
        public string SO_SERI { get; set; } = string.Empty;
        public string SO_KCB { get; set; } = string.Empty;
        public string DON_VI { get; set; } = string.Empty;
        public string MA_BHXH { get; set; } = string.Empty;
        public string? MA_THE_BHYT { get; set; }
        public string CHAN_DOAN_RV { get; set; } = string.Empty;
        public string PP_DIEUTRI { get; set; } = string.Empty;
        public int MA_DINH_CHI_THAI { get; set; }
        public string? NGUYENNHAN_DINHCHI { get; set; }
        public int? TUOI_THAI { get; set; }
        public int SO_NGAY_NGHI { get; set; }
        public string TU_NGAY { get; set; } = string.Empty;
        public string DEN_NGAY { get; set; } = string.Empty;
        public string? HO_TEN_CHA { get; set; }
        public string? HO_TEN_ME { get; set; }
        public string MA_TTDV { get; set; } = string.Empty;
        public string MA_BS { get; set; } = string.Empty;
        public string NGAY_CT { get; set; } = string.Empty;
        public string MA_THE_TAM { get; set; } = string.Empty;
        public string MAU_SO { get; set; } = "CT07";
        public string? DU_PHONG { get; set; }
    }
}
