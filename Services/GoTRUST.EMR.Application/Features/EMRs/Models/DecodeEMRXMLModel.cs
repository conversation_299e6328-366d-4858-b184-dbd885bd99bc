using System.Collections.Generic;
using System.Xml.Serialization;

namespace GoTRUST.EMR.Application.Features.EMRs.Models
{
    [XmlRoot("GIAMDINHHS")]
    public record GiamDinhHS
    {
        [XmlElement("THONGTINDONVI")]
        public ThongTinDonVi ThongTinDonVi { get; set; } = new();
        [XmlElement("THONGTINHOSO")]
        public ThongTinHoSo ThongTinHoSo { get; set; } = new();
    }

    public record ThongTinDonVi
    {
        [XmlElement("MACSKCB")]
        public string MaCSKCB { get; set; } = string.Empty;
    }

    public record ThongTinHoSo
    {
        [XmlElement("NGAYLAP")]
        public string NgayLap { get; set; } = string.Empty;
        [XmlElement("SOLUONGHOSO")]
        public int SoLuongHoSo { get; set; }
        [XmlArray("DANHSACHHOSO")]
        [XmlArrayItem("HOSO")]
        public List<HoSo> DanhSachHoSo { get; set; } = new();
    }

    public record HoSo
    {
        [XmlElement("FILEHOSO")]
        public List<FileHoSo>? FileHoSo { get; set; }
    }

    public record FileHoSo
    {
        [XmlElement("LOAIHOSO")]
        public string LoaiHoSo { get; set; } = string.Empty;
        [XmlElement("NOIDUNGFILE")]
        public string NoiDungFile { get; set; } = string.Empty;
        public object? DecodedContent { get; set; }
    }
}
