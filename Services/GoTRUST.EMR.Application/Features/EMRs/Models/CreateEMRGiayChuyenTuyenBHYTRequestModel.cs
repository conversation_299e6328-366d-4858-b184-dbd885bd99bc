namespace GoTRUST.EMR.Application.Features.EMRs.Models
{
    public class CreateEMRGiayChuyenTuyenBHYTRequestModel
    {
        public string MA_LK { get; set; } = string.Empty;
        public string SO_HOSO { get; set; } = string.Empty;
        public string SO_CHUYEN<PERSON>YEN { get; set; } = string.Empty;
        public string GIAY_CHUYEN_TUYEN { get; set; } = string.Empty;
        public string MA_CSKCB { get; set; } = string.Empty;
        public string MA_NOI_DI { get; set; } = string.Empty;
        public string MA_NOI_DEN { get; set; } = string.Empty;
        public string HO_TEN { get; set; } = string.Empty;
        public string NGAY_SINH { get; set; } = string.Empty;
        public int GIOI_TINH { get; set; }
        public string MA_QUOCTICH { get; set; } = string.Empty;
        public string MA_DANTOC { get; set; } = string.Empty;
        public string MA_NGHE_NGHIEP { get; set; } = string.Empty;
        public string DIA_CHI { get; set; } = string.Empty;
        public string MA_THE_BHYT { get; set; } = string.Empty;
        public string GT_THE_DEN { get; set; } = string.Empty;
        public string NGAY_VAO { get; set; } = string.Empty;
        public string NGAY_VAO_NOI_TRU { get; set; } = string.Empty;
        public string NGAY_RA { get; set; } = string.Empty;
        public string DAU_HIEU_LS { get; set; } = string.Empty;
        public string CHAN_DOAN_RV { get; set; } = string.Empty;
        public string QT_BENHLY { get; set; } = string.Empty;
        public string TOMTAT_KQ { get; set; } = string.Empty;
        public string PP_DIEUTRI { get; set; } = string.Empty;
        public string MA_BENH_CHINH { get; set; } = string.Empty;
        public string MA_BENH_KT { get; set; } = string.Empty;
        public string MA_BENH_YHCT { get; set; } = string.Empty;
        public string TEN_DICH_VU { get; set; } = string.Empty;
        public string TEN_THUOC { get; set; } = string.Empty;
        public string PP_DIEU_TRI { get; set; } = string.Empty;
        public int MA_LOAI_RV { get; set; }
        public int MA_LYDO_CT { get; set; }
        public string HUONG_DIEU_TRI { get; set; } = string.Empty;
        public string PHUONGTIEN_VC { get; set; } = string.Empty;
        public string HOTEN_NGUOI_HT { get; set; } = string.Empty;
        public string CHUCDANH_NGUOI_HT { get; set; } = string.Empty;
        public string MA_BAC_SI { get; set; } = string.Empty;
        public string MA_TTDV { get; set; } = string.Empty;
        public string? DU_PHONG { get; set; }
    }
}
