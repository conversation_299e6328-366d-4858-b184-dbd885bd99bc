using GoTRUST.EMR.Application.Features.EMRs.Commands;

namespace GoTRUST.EMR.Application.Features.EMRs.Models
{
    public class CreateEMRCheckinJSONResponse : CreateEMRCheckinRequestModel
    {
    }
    public class CreateEMRTongHopJSONResponse : CreateEMRTongHopRequestModel
    {
        public string? TEN_NGHE_NGHIEP { get; set; }
        public string? TENTINH_CU_TRU { get; set; }
        public string? TENHUYEN_CU_TRU { get; set; }
        public string? TENXA_CU_TRU { get; set; }
        public string? TEN_DANTOC { get; set; }
        public string? TEN_QUOCTICH { get; set; }
    }
    public class CreateEMRThuocJSONResponse : CreateEMRThuocRequestModel { }
    public class CreateEMRDVKTvaVTYTJSONResponse : CreateEMRDVKTvaVTYTRequestModel { }
    public class CreateEMRDichVuCLSJSONResponse : CreateEMRDichVuCLSRequestModel { }
    public class CreateEMRDienBienLamSangJSONResponse : CreateEMRDienBienLamSangRequestModel { }
    public class CreateEMRHIVvaAIDSJSONResponse : CreateEMRHIVvaAIDSRequestModel
    {
        public string? TENTINH_CU_TRU { get; set; }
        public string? TENHUYEN_CU_TRU { get; set; }
        public string? TENXA_CU_TRU { get; set; }
    }
    public class CreateEMRGiayRaVienJSONResponse : CreateEMRGiayRaVienRequestModel { }
    public class CreateEMRTomTatHSBAJSONResponse : CreateEMRTomTatHSBARequestModel { }
    public class CreateEMRGiayChungSinhJSONResponse : CreateEMRGiayChungSinhRequestModel
    {
        public string? TENTINH_CU_TRU { get; set; }
        public string? TENHUYEN_CU_TRU { get; set; }
        public string? TENXA_CU_TRU { get; set; }
        public string? TEN_DANTOC_NND { get; set; }
        public string? TEN_QUOCTICH { get; set; }
    }
    public class CreateEMRGiayNghiDuongThaiJSONResponse : CreateEMRGiayNghiDuongThaiRequestModel { }
    public class CreateEMRGiayNghiHuongBHXHJSONResponse : CreateEMRGiayNghiHuongBHXHRequestModel
    {
    }
    public class CreateEMRGiayChuyenTuyenBHYTJSONResponse : CreateEMRGiayChuyenTuyenBHYTRequestModel
    {
        public string? TEN_NGHE_NGHIEP { get; set; }
        public string? TEN_DANTOC { get; set; }
        public string? TEN_QUOCTICH { get; set; }
    }
    public class CreateEMRGiayHenKhamLaiJSONResponse : CreateEMRGiayHenKhamLaiRequestModel { }
    public class CreateEMRLaoJSONResponse : CreateEMRLaoRequestModel { }
    public class CreateEMRGDYKJSONResponse : CreateEMRGDYKRequestModel { }
}
