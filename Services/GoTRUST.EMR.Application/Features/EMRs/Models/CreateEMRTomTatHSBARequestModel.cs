namespace GoTRUST.EMR.Application.Features.EMRs.Models
{
    public class CreateEMRTomTatHSBARequestModel
    {
        public string MA_LK { get; set; } = string.Empty;
        public string MA_LOAI_KCB { get; set; } = string.Empty;
        public string? HO_TEN_CHA { get; set; }
        public string? HO_TEN_ME { get; set; }
        public string? NGUOI_GIAM_HO { get; set; }
        public string? DON_VI { get; set; }
        public string NGAY_VAO { get; set; } = string.Empty;
        public string NGAY_RA { get; set; } = string.Empty;
        public string CHAN_DOAN_VAO { get; set; } = string.Empty;
        public string CHAN_DOAN_RV { get; set; } = string.Empty;
        public string QT_BENHLY { get; set; } = string.Empty;
        public string TOMTAT_KQ { get; set; } = string.Empty;
        public string PP_DIEUTRI { get; set; } = string.Empty;
        public string? NGAY_SINHCON { get; set; }
        public string? NGAY_CONCHET { get; set; }
        public int? SO_CONCHET { get; set; }
        public int KET_QUA_DTRI { get; set; }
        public string? GHI_CHU { get; set; }
        public string MA_TTDV { get; set; } = string.Empty;
        public string NGAY_CT { get; set; } = string.Empty;
        public string MA_THE_TAM { get; set; } = string.Empty;
        public string? DU_PHONG { get; set; }
    }
}
