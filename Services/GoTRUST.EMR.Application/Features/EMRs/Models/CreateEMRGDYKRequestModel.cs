namespace GoTRUST.EMR.Application.Features.EMRs.Models
{
    public class CreateEMRGDYKRequestModel
    {
        public string SO_BIEN_BAN { get; set; } = string.Empty;
        public string NGUOI_CHU_TRI { get; set; } = string.Empty;
        public int CHUC_VU { get; set; }
        public string NGAY_HOP { get; set; } = string.Empty;
        public string HO_TEN { get; set; } = string.Empty;
        public string NGAY_SINH { get; set; } = string.Empty;
        public string SO_CCCD { get; set; } = string.Empty;
        public string NGAY_CAP_CCCD { get; set; } = string.Empty;
        public string NOI_CAP_CCCD { get; set; } = string.Empty;
        public string DIA_CHI { get; set; } = string.Empty;
        public string MATINH_CU_TRU { get; set; } = string.Empty;
        public string MAHUYEN_CU_TRU { get; set; } = string.Empty;
        public string MAXA_CU_TRU { get; set; } = string.Empty;
        public string MA_BHXH { get; set; } = string.Empty;
        public string? MA_THE_BHYT { get; set; }
        public string? NGHE_NGHIEP { get; set; }
        public string DIEN_THOAI { get; set; } = string.Empty;
        public string MA_DOI_TUONG { get; set; } = string.Empty;
        public int KHAM_GIAM_DINH { get; set; }
        public string NGAY_CHUNG_TU { get; set; } = string.Empty;
        public string SO_GIAY_GIOI_THIEU { get; set; } = string.Empty;
        public string NGAY_DE_NGHI { get; set; } = string.Empty;
        public string MA_DONVI { get; set; } = string.Empty;
        public string GIOI_THIEU_CUA { get; set; } = string.Empty;
        public string KET_QUA_KHAM { get; set; } = string.Empty;
        public string SO_VAN_BAN_CAN_CU { get; set; } = string.Empty;
        public int TYLE_TTCT_MOI { get; set; }
        public int? TYLE_TTCT_CU { get; set; }
        public string? DANG_HUONG_CHE_DO { get; set; }
        public int? TONG_TYLE_TTCT { get; set; }
        public int? DANG_KHUYETTAT { get; set; }
        public int? MUC_DO_KHUYETTAT { get; set; }
        public string DE_NGHI { get; set; } = string.Empty;
        public string? DUOC_XACDINH { get; set; }
        public string? DU_PHONG { get; set; }
    }
}
