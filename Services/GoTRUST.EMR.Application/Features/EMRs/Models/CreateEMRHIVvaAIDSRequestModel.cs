namespace GoTRUST.EMR.Application.Features.EMRs.Models
{
    public class CreateEMRHIVvaAIDSRequestModel
    {
        public string MA_LK { get; set; } = string.Empty;
        public string? MA_THE_BHYT { get; set; }
        public string SO_CCCD { get; set; } = string.Empty;
        public string NGAY_SINH { get; set; } = string.Empty;
        public int GIOI_TINH { get; set; }
        public string DIA_CHI { get; set; } = string.Empty;
        public string MATINH_CU_TRU { get; set; } = string.Empty;
        public string MAHUYEN_CU_TRU { get; set; } = string.Empty;
        public string MAXA_CU_TRU { get; set; } = string.Empty;
        public string? NGAYKD_HIV { get; set; }
        public string NOI_LAY_MAU_XN { get; set; } = string.Empty;
        public string NOI_XN_KD { get; set; } = string.Empty;
        public string NOI_BDDT_ARV { get; set; } = string.Empty;
        public string BDDT_ARV { get; set; } = string.Empty;
        public string MA_PHAC_DO_DIEU_TRI_BD { get; set; } = string.Empty;
        public int MA_BAC_PHAC_DO_BD { get; set; }
        public int MA_LYDO_DTRI { get; set; }
        public int LOAI_DTRI_LAO { get; set; }
        public int SANG_LOC_LAO { get; set; }
        public int PHACDO_DTRI_LAO { get; set; }
        public string NGAYBD_DTRI_LAO { get; set; } = string.Empty;
        public string? NGAYKT_DTRI_LAO { get; set; }
        public int KQ_DTRI_LAO { get; set; }
        public int MA_LYDO_XNTL_VR { get; set; }
        public string NGAY_XN_TLVR { get; set; } = string.Empty;
        public int KQ_XNTL_VR { get; set; }
        public string NGAY_KQ_XN_TLVR { get; set; } = string.Empty;
        public int MA_LOAI_BN { get; set; }
        public int GIAI_DOAN_LAM_SANG { get; set; }
        public int NHOM_DOI_TUONG { get; set; }
        public string MA_TINH_TRANG_DK { get; set; } = string.Empty;
        public int LAN_XN_PCR { get; set; }
        public string NGAY_XN_PCR { get; set; } = string.Empty;
        public string NGAY_KQ_XN_PCR { get; set; } = string.Empty;
        public int MA_KQ_XN_PCR { get; set; }
        public string NGAY_NHAN_TT_MANG_THAI { get; set; } = string.Empty;
        public string NGAY_BAT_DAU_DT_CTX { get; set; } = string.Empty;
        public string MA_XU_TRI { get; set; } = string.Empty;
        public string NGAY_BAT_DAU_XU_TRI { get; set; } = string.Empty;
        public string NGAY_KET_THUC_XU_TRI { get; set; } = string.Empty;
        public string MA_PHAC_DO_DIEU_TRI { get; set; } = string.Empty;
        public int MA_BAC_PHAC_DO { get; set; }
        public int SO_NGAY_CAP_THUOC_ARV { get; set; }
        public string NGAY_CHUYEN_PHAC_DO { get; set; } = string.Empty;
        public int LY_DO_CHUYEN_PHAC_DO { get; set; }
        public string MA_CSKCB { get; set; } = string.Empty;
        public string? DU_PHONG { get; set; }
    }
}
