namespace GoTRUST.EMR.Application.Features.EMRs.Models
{
    public class CreateEMRGiayChungSinhRequestModel
    {
        public string MA_LK { get; set; } = string.Empty;
        public string? MA_BHXH_NND { get; set; }
        public string? MA_THE_NND { get; set; }
        public string HO_TEN_NND { get; set; } = string.Empty;
        public string NGAYSINH_NND { get; set; } = string.Empty;
        public string MA_DANTOC_NND { get; set; } = string.Empty;
        public string SO_CCCD_NND { get; set; } = string.Empty;
        public string NGAYCAP_CCCD_NND { get; set; } = string.Empty;
        public string NOICAP_CCCD_NND { get; set; } = string.Empty;
        public string NOI_CU_TRU_NND { get; set; } = string.Empty;
        public string MA_QUOCTICH { get; set; } = string.Empty;
        public string MATINH_CU_TRU { get; set; } = string.Empty;
        public string MAHUYEN_CU_TRU { get; set; } = string.Empty;
        public string MAXA_CU_TRU { get; set; } = string.Empty;
        public string HO_TEN_CHA { get; set; } = string.Empty;
        public string MA_THE_TAM { get; set; } = string.Empty;
        public string? HO_TEN_CON { get; set; }
        public int GIOI_TINH_CON { get; set; }
        public int SO_CON { get; set; }
        public int LAN_SINH { get; set; }
        public int SO_CON_SONG { get; set; }
        public int CAN_NANG_CON { get; set; }
        public string NGAY_SINH_CON { get; set; } = string.Empty;
        public string NOI_SINH_CON { get; set; } = string.Empty;
        public string TINH_TRANG_CON { get; set; } = string.Empty;
        public int SINHCON_PHAUTHUAT { get; set; }
        public int SINHCON_DUOI32TUAN { get; set; }
        public string? GHI_CHU { get; set; }
        public string NGUOI_DO_DE { get; set; } = string.Empty;
        public string NGUOI_GHI_PHIEU { get; set; } = string.Empty;
        public string NGAY_CT { get; set; } = string.Empty;
        public string SO { get; set; } = string.Empty;
        public string QUYEN_SO { get; set; } = string.Empty;
        public string MA_TTDV { get; set; } = string.Empty;
        public string? DU_PHONG { get; set; }
    }
}
