namespace GoTRUST.EMR.Application.Features.EMRs.Models
{
    public class CreateEMRDVKTvaVTYTRequestModel
    {
        public string MA_LK { get; set; } = string.Empty;
        public int STT { get; set; }
        public string MA_DICH_VU { get; set; } = string.Empty;
        public string MA_PTTT_QT { get; set; } = string.Empty;
        public string? MA_VAT_TU { get; set; }
        public int MA_NHOM { get; set; }
        public string GOI_VTYT { get; set; } = string.Empty;
        public string TEN_VAT_TU { get; set; } = string.Empty;
        public string TEN_DICH_VU { get; set; } = string.Empty;
        public string MA_XANG_DAU { get; set; } = string.Empty;
        public string DON_VI_TINH { get; set; } = string.Empty;
        public int PHAM_VI { get; set; }
        public decimal SO_LUONG { get; set; }
        public decimal DON_GIA_BV { get; set; }
        public decimal DON_GIA_BH { get; set; }
        public string TT_THAU { get; set; } = string.Empty;
        public int TYLE_TT_DV { get; set; }
        public int TYLE_TT_BH { get; set; }
        public decimal THANH_TIEN_BV { get; set; }
        public decimal THANH_TIEN_BH { get; set; }
        public decimal? T_TRANTT { get; set; }
        public int MUC_HUONG { get; set; }
        public decimal T_NGUONKHAC_NSNN { get; set; }
        public decimal T_NGUONKHAC_VTNN { get; set; }
        public decimal T_NGUONKHAC_VTTN { get; set; }
        public decimal T_NGUONKHAC_CL { get; set; }
        public decimal T_NGUONKHAC { get; set; }
        public decimal T_BNTT { get; set; }
        public decimal T_BNCCT { get; set; }
        public decimal T_BHTT { get; set; }
        public string MA_KHOA { get; set; } = string.Empty;
        public string? MA_GIUONG { get; set; }
        public string MA_BAC_SI { get; set; } = string.Empty;
        public string NGUOI_THUC_HIEN { get; set; } = string.Empty;
        public string? MA_BENH { get; set; }
        public string? MA_BENH_YHCT { get; set; }
        public string NGAY_YL { get; set; } = string.Empty;
        public string NGAY_TH_YL { get; set; } = string.Empty;
        public string? NGAY_KQ { get; set; }
        public int MA_PTTT { get; set; }
        public int? VET_THUONG_TP { get; set; }
        public int? PP_VO_CAM { get; set; }
        public int? VI_TRI_TH_DVKT { get; set; }
        public string? MA_MAY { get; set; }
        public string? MA_HIEU_SP { get; set; }
        public int? TAI_SU_DUNG { get; set; }
        public string? DU_PHONG { get; set; }
    }
}
