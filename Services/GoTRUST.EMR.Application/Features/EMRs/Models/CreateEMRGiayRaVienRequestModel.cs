namespace GoTRUST.EMR.Application.Features.EMRs.Models
{
    public class CreateEMRGiayRaVienRequestModel
    {
        public string MA_LK { get; set; } = string.Empty;
        public string SO_LUU_TRU { get; set; } = string.Empty;
        public string MA_YTE { get; set; } = string.Empty;
        public string MA_KHOA_RV { get; set; } = string.Empty;
        public string NGAY_VAO { get; set; } = string.Empty;
        public string NGAY_RA { get; set; } = string.Empty;
        public int MA_DINH_CHI_THAI { get; set; }
        public string? NGUYENNHAN_DINHCHI { get; set; }
        public string? THOIGIAN_DINHCHI { get; set; }
        public int? TUOI_THAI { get; set; }
        public string CHAN_DOAN_RV { get; set; } = string.Empty;
        public string PP_DIEUTRI { get; set; } = string.Empty;
        public string GHI_CHU { get; set; } = string.Empty;
        public string MA_TTDV { get; set; } = string.Empty;
        public string MA_BS { get; set; } = string.Empty;
        public string TEN_BS { get; set; } = string.Empty;
        public string NGAY_CT { get; set; } = string.Empty;
        public string? MA_CHA { get; set; }
        public string? MA_ME { get; set; }
        public string MA_THE_TAM { get; set; } = string.Empty;
        public string? HO_TEN_CHA { get; set; }
        public string? HO_TEN_ME { get; set; }
        public int SO_NGAY_NGHI { get; set; }
        public string NGOAITRU_TUNGAY { get; set; } = string.Empty;
        public string NGOAITRU_DENNGAY { get; set; } = string.Empty;
        public string? DU_PHONG { get; set; }
    }
}
