namespace GoTRUST.EMR.Application.Features.EMRs.Models
{
    public class CreateEMRTongHopRequestModel
    {
        public string MA_LK { get; set; } = string.Empty;
        public int STT { get; set; }
        public string MA_BN { get; set; } = string.Empty;
        public string HO_TEN { get; set; } = string.Empty;
        public string SO_CCCD { get; set; } = string.Empty;
        public string NGAY_SINH { get; set; } = string.Empty;
        public int GIOI_TINH { get; set; }
        public string NHOM_MAU { get; set; } = string.Empty;
        public string MA_QUOCTICH { get; set; } = string.Empty;
        public string MA_DANTOC { get; set; } = string.Empty;
        public string MA_NGHE_NGHIEP { get; set; } = string.Empty;
        public string DIA_CHI { get; set; } = string.Empty;
        public string MATINH_CU_TRU { get; set; } = string.Empty;
        public string MAHUYEN_CU_TRU { get; set; } = string.Empty;
        public string MAXA_CU_TRU { get; set; } = string.Empty;
        public string? DIEN_THOAI { get; set; }
        public string? MA_THE_BHYT { get; set; }
        public string? MA_DKBD { get; set; }
        public string? GT_THE_TU { get; set; }
        public string? GT_THE_DEN { get; set; }
        public string? NGAY_MIEN_CCT { get; set; }
        public string? LY_DO_VV { get; set; }
        public string? LY_DO_VNT { get; set; }
        public string? MA_LY_DO_VNT { get; set; }
        public string? CHAN_DOAN_VAO { get; set; }
        public string? CHAN_DOAN_RV { get; set; }
        public string MA_BENH_CHINH { get; set; } = string.Empty;
        public string? MA_BENH_KT { get; set; }
        public string? MA_BENH_YHCT { get; set; }
        public string? MA_PTTT_QT { get; set; }
        public string MA_DOITUONG_KCB { get; set; } = string.Empty;
        public string? MA_NOI_DI { get; set; }
        public string? MA_NOI_DEN { get; set; }
        public int MA_TAI_NAN { get; set; }
        public string NGAY_VAO { get; set; } = string.Empty;
        public string? NGAY_VAO_NOI_TRU { get; set; }
        public string NGAY_RA { get; set; } = string.Empty;
        public string? GIAY_CHUYEN_TUYEN { get; set; }
        public int SO_NGAY_DTRI { get; set; }
        public string PP_DIEU_TRI { get; set; } = string.Empty;
        public int KET_QUA_DTRI { get; set; }
        public int MA_LOAI_RV { get; set; }
        public string? GHI_CHU { get; set; }
        public string? NGAY_TTOAN { get; set; }
        public decimal T_THUOC { get; set; }
        public decimal T_VTYT { get; set; }
        public decimal T_TONGCHI_BV { get; set; }
        public decimal T_TONGCHI_BH { get; set; }
        public decimal T_BNTT { get; set; }
        public decimal T_BNCCT { get; set; }
        public decimal T_BHTT { get; set; }
        public decimal T_NGUONKHAC { get; set; }
        public decimal T_BHTT_GDV { get; set; }
        public int NAM_QT { get; set; }
        public int THANG_QT { get; set; }
        public string MA_LOAI_KCB { get; set; } = string.Empty;
        public string MA_KHOA { get; set; } = string.Empty;
        public string MA_CSKCB { get; set; } = string.Empty;
        public string MA_KHUVUC { get; set; } = string.Empty;
        public string CAN_NANG { get; set; } = string.Empty;
        public string? CAN_NANG_CON { get; set; }
        public string NAM_NAM_LIEN_TUC { get; set; } = string.Empty;
        public string? NGAY_TAI_KHAM { get; set; }
        public string MA_HSBA { get; set; } = string.Empty;
        public string MA_TTDV { get; set; } = string.Empty;
        public string? DU_PHONG { get; set; }
    }
}
