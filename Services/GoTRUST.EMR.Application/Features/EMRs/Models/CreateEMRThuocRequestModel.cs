namespace GoTRUST.EMR.Application.Features.EMRs.Models
{
    public class CreateEMRThuocRequestModel
    {
        public string MA_LK { get; set; } = string.Empty;
        public int STT { get; set; }
        public string MA_THUOC { get; set; } = string.Empty;
        public string? MA_PP_CHEBIEN { get; set; }
        public string? MA_CSKCB_THUOC { get; set; }
        public int MA_NHOM { get; set; }
        public string TEN_THUOC { get; set; } = string.Empty;
        public string DON_VI_TINH { get; set; } = string.Empty;
        public string HAM_LUONG { get; set; } = string.Empty;
        public string DUONG_DUNG { get; set; } = string.Empty;
        public string DANG_BAO_CHE { get; set; } = string.Empty;
        public string LIEU_DUNG { get; set; } = string.Empty;
        public string CACH_DUNG { get; set; } = string.Empty;
        public string? SO_DANG_KY { get; set; }
        public string TT_THAU { get; set; } = string.Empty;
        public int PHAM_VI { get; set; }
        public int TYLE_TT_BH { get; set; }
        public decimal SO_LUONG { get; set; }
        public decimal DON_GIA { get; set; }
        public decimal THANH_TIEN_BV { get; set; }
        public decimal THANH_TIEN_BH { get; set; }
        public decimal T_NGUONKHAC_NSNN { get; set; }
        public decimal T_NGUONKHAC_VTNN { get; set; }
        public decimal T_NGUONKHAC_VTTN { get; set; }
        public decimal T_NGUONKHAC_CL { get; set; }
        public decimal T_NGUONKHAC { get; set; }
        public int MUC_HUONG { get; set; }
        public decimal T_BNTT { get; set; }
        public decimal T_BNCCT { get; set; }
        public decimal T_BHTT { get; set; }
        public string MA_KHOA { get; set; } = string.Empty;
        public string MA_BAC_SI { get; set; } = string.Empty;
        public string? MA_DICH_VU { get; set; }
        public string NGAY_YL { get; set; } = string.Empty;
        public string NGAY_TH_YL { get; set; } = string.Empty;
        public int MA_PTTT { get; set; }
        public int NGUON_CTRA { get; set; }
        public int? VET_THUONG_TP { get; set; }
        public string? DU_PHONG { get; set; }
    }
}
