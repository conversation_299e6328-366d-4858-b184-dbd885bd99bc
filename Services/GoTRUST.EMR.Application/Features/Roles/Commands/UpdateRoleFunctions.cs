﻿using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Mapster;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Roles.Commands;

public record UpdateRoleFunctionRequest(Guid Id, List<UpdateRoleFunctionReq> RoleFunctionList) : ICommand<Response<UpdateRoleFunctionResponse>>;

public record UpdateRoleFunctionReq(Guid FunctionId, string PermissionType, bool IsGranted);
public record UpdateRoleFunctionResponse(
    bool IsSuccess
);

public class UpdateRoleFunctionRequestHandler(
    IApplicationDbContext _context,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager,
    IHttpContextAccessor _httpContextAccessor
) : ICommandHandler<UpdateRoleFunctionRequest, Response<UpdateRoleFunctionResponse>>
{
    public async Task<Response<UpdateRoleFunctionResponse>> Handle(UpdateRoleFunctionRequest req, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await _userManager.FindByIdAsync(userId.ToString()) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var role = await _roleManager.Roles
            .FirstOrDefaultAsync(x => x.Id == req.Id && x.HospitalId == hospitalId, cancellationToken)
            ?? throw new NotFoundException("Role không tồn tại");

        var functionIds = req.RoleFunctionList
            .Where(x => x.IsGranted)
            .Select(x => x.FunctionId)
            .ToList();

        var functions = await _context.Functions
            .Where(f => functionIds.Contains(f.Id))
            .ToListAsync(cancellationToken);

        foreach (var item in req.RoleFunctionList.Where(x => x.IsGranted))
        {
            if (!functions.Any(f => f.Id == item.FunctionId))
                throw new NotFoundException($"Chức năng không tồn tại {item.FunctionId}");
        }

        var existingRoleFunctions = await _context.RoleFunctions
            .Where(rf => rf.RoleId == role.Id)
            .ToListAsync(cancellationToken);

        _context.RoleFunctions.RemoveRange(existingRoleFunctions);

        var newRoleFunctions = req.RoleFunctionList
            .Where(x => x.IsGranted)
            .Select(item => new RoleFunction
            {
                RoleId = role.Id,
                FunctionId = item.FunctionId,
                PermissionType = item.PermissionType
            });

        await _context.RoleFunctions.AddRangeAsync(newRoleFunctions, cancellationToken);
        role.AddDomainEvent(new RoleFunctionUpdateEvent(role));
        await _context.SaveChangesAsync(cancellationToken);

        var response = new UpdateRoleFunctionResponse(true);
        return new Response<UpdateRoleFunctionResponse>(response);
    }
}