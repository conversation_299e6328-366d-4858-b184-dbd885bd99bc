﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Events;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.Roles.Commands;

public record DeleteRoleRequest(Guid Id) : ICommand<Response<DeleteRoleResponse>>;

public record DeleteRoleResponse(bool IsSuccess);

public class DeleteRoleRequestValidator : AbstractValidator<DeleteRoleRequest>
{
    public DeleteRoleRequestValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteRoleRequestHandler(
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager,
    IApplicationDbContext _dbContext
) : ICommandHandler<DeleteRoleRequest, Response<DeleteRoleResponse>>
{
    public async Task<Response<DeleteRoleResponse>> Handle(DeleteRoleRequest req, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await _userManager.FindByIdAsync(userId.ToString()) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var role = await _roleManager.FindByIdAsync(req.Id.ToString());
        if (role == null || role.HospitalId != hospitalId)
            throw new NotFoundException("Không tìm thấy vai trò.");

        var usersInRole = await _userManager.GetUsersInRoleAsync(role.Name!);
        if (usersInRole.Any())
            throw new BadRequestException("Không thể xóa vai trò: đã có người dùng được gán vai trò này.");

        role.AddDomainEvent(new RoleDeleteEvent(role));
        await _dbContext.SaveChangesAsync(cancellationToken);

        var result = await _roleManager.DeleteAsync(role);
        if (!result.Succeeded)
            throw new InternalServerException(string.Join("; ", result.Errors.Select(e => e.Description)));

        return new Response<DeleteRoleResponse>(new DeleteRoleResponse(true));
    }
}
