﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Events;
using Mapster;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.Roles.Commands;

public record CreateRoleRequest(
    string Name
) : ICommand<Response<CreateRoleResponse>>;

public record CreateRoleResponse(
    Guid Id,
    string Name,
    string NormalizedName
);

public class CreateRoleRequestValidator : AbstractValidator<CreateRoleRequest>
{
    public CreateRoleRequestValidator()
    {
        RuleFor(x => x.Name).NotEmpty().MaximumLength(256);
    }
}

public class CreateRoleHandler(
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager,
    IApplicationDbContext _dbContext
) : ICommandHandler<CreateRoleRequest, Response<CreateRoleResponse>>
{
    public async Task<Response<CreateRoleResponse>> Handle(CreateRoleRequest req, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await _userManager.FindByIdAsync(userId.ToString()) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var normalizedRoleName = req.Name.ToUpperInvariant();
        var duplicateRole = _roleManager.Roles
            .Where(r => r.HospitalId == hospitalId && r.NormalizedName == normalizedRoleName)
            .FirstOrDefault();
        if (duplicateRole != null)
            throw new BadRequestException("Tên vai trò đã tồn tại.");

        var role = req.Adapt<Role>();
        role.HospitalId = hospitalId;
        role.NormalizedName = normalizedRoleName;

        var result = await _roleManager.CreateAsync(role);
        if (!result.Succeeded)
            throw new InternalServerException(string.Join("; ", result.Errors.Select(e => e.Description)));

        role.AddDomainEvent(new RoleCreateEvent(role));
        await _dbContext.SaveChangesAsync(cancellationToken);

        var response = role.Adapt<CreateRoleResponse>();
        return new Response<CreateRoleResponse>(response);
    }
}