﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Events;
using Mapster;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.Roles.Commands;

public record UpdateRoleRequest(
    Guid Id,
    string Name
) : ICommand<Response<UpdateRoleResponse>>;

public record UpdateRoleResponse(
    Guid Id,
    string Name,
    string NormalizedName
);

public class UpdateRoleRequestValidator : AbstractValidator<UpdateRoleRequest>
{
    public UpdateRoleRequestValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.Name).NotEmpty().MaximumLength(256);
    }
}

public class UpdateRoleHandler(
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager,
    IApplicationDbContext _dbContext
) : ICommandHandler<UpdateRoleRequest, Response<UpdateRoleResponse>>
{
    public async Task<Response<UpdateRoleResponse>> Handle(UpdateRoleRequest req, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await _userManager.FindByIdAsync(userId.ToString()) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var role = await _roleManager.FindByIdAsync(req.Id.ToString());
        if (role == null || role.HospitalId != hospitalId)
            throw new NotFoundException("Không tìm thấy vai trò.");

        var normalizedRoleName = req.Name.ToUpperInvariant();
        var duplicateRole = _roleManager.Roles
            .Where(r => r.HospitalId == hospitalId && r.NormalizedName == normalizedRoleName && r.Id != req.Id)
            .FirstOrDefault();
        if (duplicateRole != null)
            throw new BadRequestException("Tên vai trò đã tồn tại.");

        req.Adapt(role);
        role.NormalizedName = normalizedRoleName;

        var result = await _roleManager.UpdateAsync(role);
        if (!result.Succeeded)
            throw new InternalServerException(string.Join("; ", result.Errors.Select(e => e.Description)));

        role.AddDomainEvent(new RoleUpdateEvent(role));
        await _dbContext.SaveChangesAsync(cancellationToken);

        var response = role.Adapt<UpdateRoleResponse>();
        return new Response<UpdateRoleResponse>(response);
    }
}