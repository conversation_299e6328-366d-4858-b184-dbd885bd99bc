﻿using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Application.Helpers;
using Mapster;
using BuildingBlocks.Exceptions;

namespace GoTRUST.EMR.Application.Features.Roles.Queries;

public record GetRoleFunctionsQuery(Guid Id) : IQuery<Response<GetRoleFunctionsResponse>>;

public record GetRoleFunctionsResponse(
    IReadOnlyList<FunctionLabelRes> Functions,
    IReadOnlyList<PermissionLabelRes> Permissions,
    IReadOnlyList<RoleFunctionGrantedRes> RoleFunctions
);

public record RoleFunctionGrantedRes(
    Guid FunctionId,
    string PermissionCode,
    bool IsGranted
);

public record FunctionLabelRes(
    Guid Id,
    string Name,
    string Description
);

public record PermissionLabelRes(
    string Id,
    string Name
);

public class GetRoleFunctionsQueryHandler(
    IApplicationDbContext _context,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager,
    IHttpContextAccessor _httpContextAccessor
) : IQueryHandler<GetRoleFunctionsQuery, Response<GetRoleFunctionsResponse>>
{
    public async Task<Response<GetRoleFunctionsResponse>> Handle(GetRoleFunctionsQuery request, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await _userManager.FindByIdAsync(userId.ToString()) ?? throw new UserNotFoundException();
        var hospitalId = user.HospitalId;

        var role = await _roleManager.Roles
            .Where(x => x.Id == request.Id && x.HospitalId == hospitalId)
            .FirstOrDefaultAsync(cancellationToken);

        if (role == null)
            throw new NotFoundException("Role không tồn tại");

        var functions = await _context.Functions
            .ToListAsync(cancellationToken);

        var permissions = RolePermissionHelper.GetPermissions();

        var roleFunctions = await _context.RoleFunctions
            .Where(x => x.RoleId == request.Id)
            .ToListAsync(cancellationToken);

        var roleFunctionGrantedRes = new List<RoleFunctionGrantedRes>();
        foreach (var functionId in functions.Select(x => x.Id))
        {
            foreach (var permission in permissions)
            {
                roleFunctionGrantedRes.Add(new RoleFunctionGrantedRes(
                    functionId,
                    permission.Id,
                    roleFunctions.Any(x => x.FunctionId == functionId && x.PermissionType == permission.Id)
                ));
            }
        }

        var functionLabels = functions.Adapt<IReadOnlyList<FunctionLabelRes>>();
        var permissionLabels = permissions.Adapt<IReadOnlyList<PermissionLabelRes>>();

        var roleFunctionRes = new GetRoleFunctionsResponse(
            functionLabels,
            permissionLabels,
            roleFunctionGrantedRes
        );

        return new Response<GetRoleFunctionsResponse>(roleFunctionRes);
    }
}