using BuildingBlocks.Abstractions;
using Mapster;

namespace GoTRUST.EMR.Application.Features.Khoas.Queries;

public record GetKhoasRequest() : IQuery<Response<List<GetKhoasResponse>>>;

public record GetKhoasResponse(
    string Id,
    string? Name
);

public class GetKhoasRequestValidator : AbstractValidator<GetKhoasRequest>
{
    public GetKhoasRequestValidator()
    {
    }
}

public class GetKhoasHandler(
    IApplicationDbContext _dbContext
) : IQueryHandler<GetKhoasRequest, Response<List<GetKhoasResponse>>>
{
    public async Task<Response<List<GetKhoasResponse>>> Handle(GetKhoasRequest req, CancellationToken cancellationToken)
    {
        var query = _dbContext.Khoas.AsQueryable();

        var khoas = await query
            .ProjectToType<GetKhoasResponse>()
            .ToListAsync(cancellationToken);

        return new Response<List<GetKhoasResponse>>(khoas);
    }
}
