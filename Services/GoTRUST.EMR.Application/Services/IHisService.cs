using System.Runtime.CompilerServices;
using BuildingBlocks.Abstractions;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Services;

/// <summary>
/// HIS (Hospital Information System) Service Interface
/// Main interface for integrating with external HIS systems
/// </summary>
public interface IHisService
{
    /// <summary>
    /// L<PERSON>y danh sách các lần nhập viện của bệnh nhân từ HIS
    /// </summary>
    /// <param name="patientId">ID của bệnh nhân</param>
    /// <param name="hospitalId">ID của bệnh viện</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Danh sách các lần nhập viện</returns>
    Task<List<PatientAdmissionDto>> GetPatientAdmissionsAsync(
        Guid patientId,
        Guid hospitalId,
        CancellationToken cancellationToken = default);
}

public record PatientAdmissionDto(
    string AdmissionId,
    DateTime AdmissionDateTime,
    string Department,
    Guid DepartmentId,
    DateTime DischargeDate,
    int FilmCount,
    bool HasBHYT,
    string Note,
    string RecordCode,
    string StorageNumber,
    string Status,
    int StorageYears,
    AdmissionType AdmissionType,
    string BedNumber,
    string YTCode, // mã YT
    DateOnly BirthDate,
    string Age,
    string Occupation, // nghề nghiệp
    string OccupationCode, // mã nghề nghiệp
    string Ethnicity, // dân tộc
    string EthnicityCode, // mã dân tộc
    string Nationality, // ngoại kiều
    string NationalityCode, // mã ngoại kiều
    string HouseNumber, // số nhà
    string Street, // thôn, phố
    string Ward, // xã, phường
    string District, // huyện
    string DistrictCode, // mã huyện
    string Province, // tỉnh
    string ProvinceCode, // mã tỉnh
    string Workplace, // nơi làm việc
    int ObjectType, // đối tượng
    DateOnly BHYTExpirationDate, // BHYT giá trị đến
    string InsuranceNo, // Số thẻ BHYT
    string KinNameAddress, // họ tên, địa chỉ người nhà cần báo tin
    string PhoneNumber, // số điện thoại
    bool isDisabled = false
)
{
    public bool isDisabled { get; set; } = false;
};
