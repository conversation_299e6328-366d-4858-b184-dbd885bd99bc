using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;

namespace GoTRUST.EMR.Application.Services;

public class HisService(
    ILogger<HisService> logger) : IHisService
{
    private readonly ILogger<HisService> _logger = logger;

    public Task<List<PatientAdmissionDto>> GetPatientAdmissionsAsync(
        Guid patientId,
        Guid hospitalId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting HIS admissions for patient {PatientId} in hospital {HospitalId}",
            patientId, hospitalId);

        // TODO: Replace with real HIS API call
        // For now, return mock data - realistic Vietnamese hospital admissions
        var mockAdmissions = new List<PatientAdmissionDto>
        {
            new(
                AdmissionId: "8238438",
                AdmissionDateTime: new DateTime(2024, 12, 24, 14, 12, 0),
                Department: "Tim mạch",
                DepartmentId: new Guid("da7721f0-b73b-4931-9866-2b0c00f4d142"),
                DischargeDate: new DateTime(2024, 12, 25, 14, 12, 0),
                FilmCount: 1,
                HasBHYT: true,
                Note: "Bệnh nhân vào điều trị bệnh tim mạch",
                RecordCode: StringExtensions.GenerateRandomUppercaseCode(),
                StorageNumber: StringExtensions.GenerateRandomUppercaseCode(),
                Status: "Pending",
                StorageYears: 30,
                AdmissionType: AdmissionType.Inpatient,
                BedNumber: "TM-101",
                YTCode: "YT001",
                BirthDate: new DateOnly(1975, 5, 20),
                Age: "49",
                Occupation: "Giáo viên",
                OccupationCode: "01",
                Ethnicity: "Kinh",
                EthnicityCode: "01",
                Nationality: "Việt Nam",
                NationalityCode: "01",
                HouseNumber: "123",
                Street: "Nguyễn Trãi",
                Ward: "Thanh Xuân Trung",
                District: "Thanh Xuân",
                DistrictCode: "12",
                Province: "Hà Nội",
                ProvinceCode: "01",
                Workplace: "THPT Chu Văn An",
                ObjectType: 1,
                BHYTExpirationDate: new DateOnly(2025, 12, 31),
                InsuranceNo: "HN4751234567890",
                KinNameAddress: "Nguyễn Văn B - 456 Đường Láng, Đống Đa, Hà Nội",
                PhoneNumber: "**********"
            ),
            new(
                AdmissionId: "8238439",
                AdmissionDateTime: new DateTime(2024, 12, 24, 15, 30, 0),
                Department: "Nội tổng hợp",
                DepartmentId: new Guid("8ab54e77-e4f9-4349-bb59-5240f2020f2d"),
                DischargeDate: new DateTime(2024, 12, 25, 15, 30, 0),
                FilmCount: 2,
                HasBHYT: false,
                Note: "Khám tổng quát và tư vấn sức khỏe",
                RecordCode: StringExtensions.GenerateRandomUppercaseCode(),
                StorageNumber: StringExtensions.GenerateRandomUppercaseCode(),
                Status: "Pending",
                StorageYears: 30,
                AdmissionType: AdmissionType.Outpatient,
                BedNumber: "",
                YTCode: "YT002",
                BirthDate: new DateOnly(1985, 8, 15),
                Age: "39",
                Occupation: "Kỹ sư",
                OccupationCode: "02",
                Ethnicity: "Kinh",
                EthnicityCode: "01",
                Nationality: "Việt Nam",
                NationalityCode: "01",
                HouseNumber: "789",
                Street: "Lê Duẩn",
                Ward: "Đống Đa",
                District: "Đống Đa",
                DistrictCode: "11",
                Province: "Hà Nội",
                ProvinceCode: "01",
                Workplace: "Công ty TNHH ABC",
                ObjectType: 0,
                BHYTExpirationDate: new DateOnly(1900, 1, 1),
                InsuranceNo: "",
                KinNameAddress: "Trần Thị C - 321 Phố Huế, Hai Bà Trưng, Hà Nội",
                PhoneNumber: "**********"
            ),
            new(
                AdmissionId: "8238440",
                AdmissionDateTime: new DateTime(2024, 11, 15, 8, 45, 0),
                Department: "Ngoại tổng hợp",
                DepartmentId: new Guid("1447d4b2-e740-4ab8-a183-43664c361658"),
                DischargeDate: new DateTime(2024, 11, 16, 8, 45, 0),
                FilmCount: 3,
                HasBHYT: true,
                Note: "Phẫu thuật ngoại khoa cấp cứu",
                RecordCode: StringExtensions.GenerateRandomUppercaseCode(),
                StorageNumber: StringExtensions.GenerateRandomUppercaseCode(),
                Status: "Pending",
                StorageYears: 30,
                AdmissionType: AdmissionType.Inpatient,
                BedNumber: "NG-205",
                YTCode: "YT003",
                BirthDate: new DateOnly(1960, 3, 10),
                Age: "64",
                Occupation: "Nông dân",
                OccupationCode: "03",
                Ethnicity: "Kinh",
                EthnicityCode: "01",
                Nationality: "Việt Nam",
                NationalityCode: "01",
                HouseNumber: "45",
                Street: "Thôn Vân Canh",
                Ward: "Vân Canh",
                District: "Hoài Đức",
                DistrictCode: "17",
                Province: "Hà Nội",
                ProvinceCode: "01",
                Workplace: "Hợp tác xã nông nghiệp Vân Canh",
                ObjectType: 1,
                BHYTExpirationDate: new DateOnly(2025, 6, 30),
                InsuranceNo: "HN6034567890123",
                KinNameAddress: "Lê Văn D - Cùng địa chỉ",
                PhoneNumber: "**********"
            ),
            new(
                AdmissionId: "8238441",
                AdmissionDateTime: new DateTime(2024, 10, 5, 13, 20, 0),
                Department: "Sản phụ khoa",
                DepartmentId: new Guid("2a1bd940-440b-4a04-a1bb-82006f75c9f9"),
                DischargeDate: new DateTime(2024, 10, 6, 13, 20, 0),
                FilmCount: 4,
                HasBHYT: false,
                Note: "Sinh thường, mẹ và bé khỏe mạnh",
                RecordCode: StringExtensions.GenerateRandomUppercaseCode(),
                StorageNumber: StringExtensions.GenerateRandomUppercaseCode(),
                Status: "Pending",
                StorageYears: 30,
                AdmissionType: AdmissionType.Inpatient,
                BedNumber: "SP-301",
                YTCode: "YT004",
                BirthDate: new DateOnly(1992, 12, 25),
                Age: "31",
                Occupation: "Nhân viên văn phòng",
                OccupationCode: "04",
                Ethnicity: "Kinh",
                EthnicityCode: "01",
                Nationality: "Việt Nam",
                NationalityCode: "01",
                HouseNumber: "67",
                Street: "Trần Duy Hưng",
                Ward: "Trung Hòa",
                District: "Cầu Giấy",
                DistrictCode: "13",
                Province: "Hà Nội",
                ProvinceCode: "01",
                Workplace: "Công ty CP XYZ",
                ObjectType: 0,
                BHYTExpirationDate: new DateOnly(1900, 1, 1),
                InsuranceNo: "",
                KinNameAddress: "Phạm Văn E - Cùng địa chỉ",
                PhoneNumber: "**********"
            ),
            new(
                AdmissionId: "8238442",
                AdmissionDateTime: new DateTime(2024, 9, 12, 11, 0, 0),
                Department: "Nhi khoa",
                DepartmentId: new Guid("0ade05e3-a5c9-4ad3-a641-9b8486568520"),
                DischargeDate: new DateTime(2024, 9, 13, 11, 0, 0),
                FilmCount: 5,
                HasBHYT: true,
                Note: "Điều trị viêm phổi ở trẻ em",
                RecordCode: StringExtensions.GenerateRandomUppercaseCode(),
                StorageNumber: StringExtensions.GenerateRandomUppercaseCode(),
                Status: "Pending",
                StorageYears: 30,
                AdmissionType: AdmissionType.Outpatient,
                BedNumber: "",
                YTCode: "YT005",
                BirthDate: new DateOnly(2018, 7, 8),
                Age: "6",
                Occupation: "Học sinh",
                OccupationCode: "05",
                Ethnicity: "Kinh",
                EthnicityCode: "01",
                Nationality: "Việt Nam",
                NationalityCode: "01",
                HouseNumber: "234",
                Street: "Giải Phóng",
                Ward: "Bách Khoa",
                District: "Hai Bà Trưng",
                DistrictCode: "14",
                Province: "Hà Nội",
                ProvinceCode: "01",
                Workplace: "Trường Tiểu học Bách Khoa",
                ObjectType: 1,
                BHYTExpirationDate: new DateOnly(2025, 12, 31),
                InsuranceNo: "HN1823456789012",
                KinNameAddress: "Hoàng Thị F - Cùng địa chỉ",
                PhoneNumber: "**********"
            ),
        };

        _logger.LogInformation("Returned {Count} HIS admissions for patient {PatientId}",
            mockAdmissions.Count, patientId);

        return Task.FromResult(mockAdmissions);
    }
}
