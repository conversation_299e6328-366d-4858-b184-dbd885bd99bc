﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\BuildingBlocks\BuildingBlocks.Messaging\BuildingBlocks.Messaging.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\BuildingBlocks.Common\BuildingBlocks.Common.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\BuildingBlocks\BuildingBlocks.csproj" />
    <ProjectReference Include="..\GoTRUST.EMR.Domain\GoTRUST.EMR.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.1" />
    <PackageReference Include="Nanoid" Version="3.0.0" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.14" />
    <PackageReference Include="Hangfire.Mongo" Version="1.10.8" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Features\Authorizes\" />
    <Folder Include="Features\Orders\EventHandlers\Integration\" />
    <Folder Include="Intergrateds\" />
  </ItemGroup>

</Project>
