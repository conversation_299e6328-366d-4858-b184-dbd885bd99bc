using BuildingBlocks.Common.Interface;

namespace GoTRUST.EMR.Application.Jobs
{
    public class DeleteFileSystemNodeJob
    {
        private readonly IApplicationDbContext _dbContext;
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<DeleteFileSystemNodeJob> _logger;

        public DeleteFileSystemNodeJob(
            IApplicationDbContext dbContext,
            IFileStorageService fileStorageService,
            ILogger<DeleteFileSystemNodeJob> logger)
        {
            _dbContext = dbContext;
            _fileStorageService = fileStorageService;
            _logger = logger;
        }

        public async Task ExecuteAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("DeleteFileSystemNodeJob started at {Time}", DateTime.UtcNow);

            var filesToDelete = await _dbContext.FileSystemNodes
                .IgnoreQueryFilters()
                .Where(f => f.Is<PERSON>eleted && !f.DeleteS3At.HasValue && f.UpdatedAt <= DateTime.UtcNow.AddDays(-30))
                .ToListAsync(cancellationToken);

            if (filesToDelete.Count == 0)
            {
                _logger.LogInformation("No files to delete at this time.");
                return;
            }

            var keys = filesToDelete
                .Where(f => !string.IsNullOrEmpty(f.Path) && !string.IsNullOrEmpty(f.Name))
                .Select(f => $"{f.Path}/{f.Name}".Trim('/'))
                .ToList();

            if (keys.Count == 0)
            {
                _logger.LogWarning("No valid file keys found for deletion.");
                return;
            }

            var (success, message) = await _fileStorageService.DeleteFilesAsync(keys);

            if (!success)
            {
                _logger.LogError("Failed to delete files: {Message}", message);
                return;
            }

            _logger.LogInformation("Successfully deleted {Count} files.", keys.Count);
            filesToDelete.ForEach(f => f.DeleteS3At = DateTime.UtcNow);
            _dbContext.FileSystemNodes.UpdateRange(filesToDelete);
            await _dbContext.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("DeleteFileSystemNodeJob completed at {Time}", DateTime.UtcNow);
        }
    }
}