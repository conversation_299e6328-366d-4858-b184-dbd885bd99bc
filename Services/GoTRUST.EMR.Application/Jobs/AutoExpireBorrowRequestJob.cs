using GoTRUST.EMR.Domain.Enums;
using Microsoft.Extensions.Logging;

namespace GoTRUST.EMR.Application.Jobs
{
    public class AutoExpireBorrowRequestJob
    {
        private readonly IApplicationDbContext _dbContext;
        private readonly ILogger<AutoExpireBorrowRequestJob> _logger;

        public AutoExpireBorrowRequestJob(
            IApplicationDbContext dbContext,
            ILogger<AutoExpireBorrowRequestJob> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task ExecuteAsync(Guid borrowRequestId, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("AutoExpireBorrowRequestJob started for BorrowRequestId: {BorrowRequestId}", borrowRequestId);

            var borrowRequest = await _dbContext.BorrowRequests.FindAsync(borrowRequestId);
            if (borrowRequest == null)
            {
                _logger.LogWarning("BorrowRequest with ID {BorrowRequestId} not found.", borrowRequestId);
                throw new Exception($"BorrowRequest with ID {borrowRequestId} not found.");
            }

            borrowRequest.ApprovalStatus = BorrowRequestStatus.Returned;
            borrowRequest.ReturnedDate = DateTime.UtcNow;
            _dbContext.BorrowRequests.Update(borrowRequest);

            if (borrowRequest.Purpose == "2")
            {
                var medicalRecord = await _dbContext.MedicalRecords
                    .FindAsync(borrowRequest.MedicalRecordId, cancellationToken)
                    ?? throw new MedicalRecordNotFoundException();
                medicalRecord.Status = MedicalRecordStatus.Pending;
                _dbContext.MedicalRecords.Update(medicalRecord);
            }

            var result = await _dbContext.SaveChangesAsync(cancellationToken);
            if (result <= 0)
            {
                _logger.LogError("Failed to save changes to the database for BorrowRequestId: {BorrowRequestId}", borrowRequestId);
                throw new DatabaseSaveChangesException();
            }
        }
    }
}