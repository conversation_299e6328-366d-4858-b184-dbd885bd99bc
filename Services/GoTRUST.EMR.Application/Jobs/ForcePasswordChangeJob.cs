using GoTRUST.EMR.Domain.Constants;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;

namespace GoTRUST.EMR.Application.Jobs
{
    public class ForcePasswordChangeJob(
        UserManager<User> userManager,
        IConfiguration config,
        IWebHostEnvironment env,
        ILogger<ForcePasswordChangeJob> logger,
        IApplicationDbContext dbContext)
    {
        private readonly UserManager<User> _userManager = userManager;
        private readonly IConfiguration _config = config;
        private readonly IWebHostEnvironment _env = env;
        private readonly ILogger<ForcePasswordChangeJob> _logger = logger;
        private readonly IApplicationDbContext _dbContext = dbContext;
        private const int BatchSize = 50;

        public async Task ExecuteAsync(CancellationToken cancellationToken = default)
        {
            var users = await _userManager.Users
                .Include(u => u.Hospital)
                .ToListAsync(cancellationToken);
            var hospitalIds = users
                .Where(u => u.HospitalId.HasValue)
                .Select(u => u.HospitalId!.Value)
                .Distinct()
                .ToList();

            var configs = await _dbContext.HospitalConfigs
                .Where(c => hospitalIds.Contains(c.HospitalId) && c.Key == HospitalConfigConstants.ForcePasswordChangeDays)
                .ToListAsync(cancellationToken);

            var usersToNotify = new List<User>();

            foreach (var user in users)
            {
                if (string.IsNullOrEmpty(user.Email) || !user.IsActive || user.HospitalId == null)
                    continue;

                var config = configs.FirstOrDefault(c => c.HospitalId == user.HospitalId && c.Key == HospitalConfigConstants.ForcePasswordChangeDays);
                if (config == null || !int.TryParse(config.Value, out var days) || days <= 0)
                    continue;

                var lastChange = user.LastPasswordChangeAt;
                if (!lastChange.HasValue || lastChange.Value.AddDays(days) < DateTime.UtcNow)
                {
                    usersToNotify.Add(user);
                }
            }

            for (int i = 0; i < usersToNotify.Count; i += BatchSize)
            {
                var batch = usersToNotify.Skip(i).Take(BatchSize).ToList();
                var tasks = batch.Select(user => SendForgotPasswordEmailAsync(user, cancellationToken));
                await Task.WhenAll(tasks);
            }
        }

        private async Task SendForgotPasswordEmailAsync(User user, CancellationToken cancellationToken)
        {
            try
            {
                var expiresAt = DateTime.UtcNow.AddYears(1000);
                var session = new ForgotPasswordSession
                {
                    UserId = user.Id,
                    ExpiresAt = expiresAt
                };
                _dbContext.ForgotPasswordSessions.Add(session);
                await _dbContext.SaveChangesAsync(cancellationToken);

                var resetLink = $"{_config["ClientUrl"]}/tao-mat-khau?session={session.Id}&user={user.UserName}";
                var templatePath = Path.Combine(_env.WebRootPath, "Templates", "Mails", "EMRForgotPassword.html");
                var template = await File.ReadAllTextAsync(templatePath, cancellationToken);

                template = template.Replace("{{AccountName}}", user.FullName ?? user.UserName);
                template = template.Replace("{{CreatePassword}}", resetLink);

                _ = MailHelper.SendAsync(
                    int.TryParse(_config["EmailPort"], out int port) ? port : 587,
                    _config["EmailHost"]!,
                    _config["EmailPassword"]!,
                    _config["EmailFromMail"]!,
                    user.Email!,
                    _config["EmailDisplayName"]!,
                    "Yêu cầu đặt lại mật khẩu vì lý do bảo mật",
                    template
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending password change email to {Email}", user.Email);
            }
        }
    }
}