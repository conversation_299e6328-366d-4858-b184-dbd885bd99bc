﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Notifications;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace BuildingBlocks.Exceptions.Handler;

public class CustomExceptionHandler
    (ILogger<CustomExceptionHandler> logger)
    : IExceptionHandler
{
    public async ValueTask<bool> TryHandleAsync(
        HttpContext context,
        Exception exception,
        CancellationToken cancellationToken)
    {
        logger.LogError(
            "Error Message: {exceptionMessage}, Time of occurrence {time}",
            exception.Message, DateTime.UtcNow);

        var publisher = context.RequestServices.GetService<IPublisher>();
        if (publisher is not null)
        {
            try
            {
                var path = context.Features.Get<IExceptionHandlerPathFeature>()?.Path;
                var method = context.Request.Method;

                var failureEvent = new FailureLogEvent
                {
                    Path = path,
                    Method = method
                };

                await publisher.Publish(failureEvent, cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to publish exception log event");
            }
        }

        (string Detail, string Message, int StatusCode, string StatusCodeString) = exception switch
        {
            AppLogicException appLogicException =>
            (
                appLogicException.Message,
                appLogicException.Message,
                context.Response.StatusCode = StatusCodes.Status200OK,
                appLogicException.ErrorCode
            ),
            ValidationException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status400BadRequest,
                StatusCodes.Status400BadRequest.ToString()
            ),
            BadRequestException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status400BadRequest,
                StatusCodes.Status400BadRequest.ToString()
            ),
            UnAuthorizeException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status401Unauthorized,
                StatusCodes.Status401Unauthorized.ToString()
            ),
            ForbiddenException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status403Forbidden,
                StatusCodes.Status403Forbidden.ToString()
            ),
            NotFoundException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status404NotFound,
                StatusCodes.Status404NotFound.ToString()
            ),
            InternalServerException =>
            (
                exception.GetType().Name,
                exception.Message,
                StatusCode = StatusCodes.Status500InternalServerError,
                StatusCodes.Status500InternalServerError.ToString()
            ),
            MaintenanceException =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status503ServiceUnavailable,
                StatusCodes.Status503ServiceUnavailable.ToString()
            ),
            _ =>
            (
                exception.GetType().Name,
                exception.Message,
                context.Response.StatusCode = StatusCodes.Status500InternalServerError,
                StatusCodes.Status500InternalServerError.ToString()
            )
        };

        var problemDetails = new Response<object>
        {
            Message = Message,
            Errors = Detail,
            Code = StatusCodeString,
            TraceId = context.TraceIdentifier,
        };

        if (exception is ValidationException validationException)
        {
            problemDetails.Errors = validationException.Errors
                .Select(e => new { e.PropertyName, e.ErrorMessage })
                .ToList();
        }

        await context.Response.WriteAsJsonAsync(problemDetails, cancellationToken: cancellationToken);
        return true;
    }
}
