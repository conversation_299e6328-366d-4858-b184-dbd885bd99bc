﻿using System.Diagnostics;

namespace BuildingBlocks.Abstractions;
public class Response<TEntity> where TEntity : class
{
    public Response() { }

    public Response(TEntity data)
    {
        this.Data = data;
        this.Code = "000";
    }

    public Response(TEntity data, string message)
    {
        this.Data = data;
        this.Message = message;
        this.Code = "000";
    }

    public Response(TEntity data, string message, string code)
    {
        this.Data = data;
        this.Message = message;
        this.Code = code;
    }

    public string Code { get; set; } = "000";
    public string Message { get; set; } = string.Empty;
    public object? Errors { get; set; } = null;
    public string? TraceId { get; set; }
    public TEntity? Data { get; private set; }
}
