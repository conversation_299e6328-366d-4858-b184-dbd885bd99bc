using System.Globalization;
using ClosedXML.Excel;
using CsvHelper;
using Microsoft.AspNetCore.Http;

namespace BuildingBlocks.Common.Helper
{
    public class ImportExcelHelper
    {
        /// <summary>
        /// Đọc dữ liệu từ file Excel và trả về result wrapper
        /// </summary>
        /// <param name="file">File upload từ request</param>
        /// <param name="sheetIndex">Index của sheet cần đọc (mặc định là 0)</param>
        /// <param name="expectedHeaders">Danh sách keywords để detect header (required)</param>
        /// <returns>ImportResult chứa danh sách dictionary hoặc error message</returns>
        public static async Task<ImportResult<List<Dictionary<string, object>>>> ReadExcelFileAsync(
            IFormFile file,
            int sheetIndex = 0,
            List<string>? expectedHeaders = null)
        {
            if (file.Length == 0)
                return ImportResult<List<Dictionary<string, object>>>.Failure("File không hợp lệ hoặc rỗng");

            if (expectedHeaders == null || expectedHeaders.Count == 0)
                return ImportResult<List<Dictionary<string, object>>>.Failure("Header không được null hoặc rỗng");

            var data = new List<Dictionary<string, object>>();

            try
            {
                using var stream = file.OpenReadStream();
                using var workbook = new XLWorkbook(stream);

                if (workbook.Worksheets.Count <= sheetIndex)
                    return ImportResult<List<Dictionary<string, object>>>.Failure($"Sheet với index {sheetIndex} không tồn tại");

                var worksheet = workbook.Worksheet(sheetIndex + 1); // ClosedXML uses 1-based indexing
                var range = worksheet.RangeUsed();

                if (range == null || range.RowCount() < 1)
                    return ImportResult<List<Dictionary<string, object>>>.Failure("File Excel không có dữ liệu");

                // Tự động detect header row
                var headerRowInfo = DetectHeaderRow(worksheet, range, expectedHeaders);
                if (headerRowInfo.HeaderRow == -1)
                    return ImportResult<List<Dictionary<string, object>>>.Failure("Không tìm thấy header hợp lệ trong file Excel. " +
                    "Hãy đảm bảo file có chứa các cột cần thiết");

                var headers = headerRowInfo.Headers;
                var headerRow = headerRowInfo.HeaderRow;

                // Kiểm tra có dữ liệu sau header không
                if (headerRow >= range.RowCount())
                    return ImportResult<List<Dictionary<string, object>>>.Failure("File Excel chỉ có header mà không có dữ liệu");

                // Đọc dữ liệu từ row sau header
                for (var row = headerRow + 1; row <= range.RowCount(); row++)
                {
                    var rowData = new Dictionary<string, object>();
                    var hasData = false;

                    for (var col = 1; col <= range.ColumnCount() && col <= headers.Count; col++)
                    {
                        var cellValue = worksheet.Cell(row, col).Value;
                        var stringValue = cellValue.ToString().Trim();

                        if (!string.IsNullOrEmpty(stringValue))
                            hasData = true;

                        rowData[headers[col - 1]] = stringValue;
                    }

                    if (hasData)
                    {
                        data.Add(rowData);
                    }
                }

                return data.Count == 0 ? ImportResult<List<Dictionary<string, object>>>.Failure("File Excel không có dữ liệu hợp lệ sau header") : ImportResult<List<Dictionary<string, object>>>.Success(data);
            }
            catch (Exception ex)
            {
                return ImportResult<List<Dictionary<string, object>>>.Failure($"Lỗi đọc file Excel: {ex.Message}");
            }
        }

        /// <summary>
        /// Tự động detect header row trong Excel worksheet
        /// </summary>
        /// <param name="worksheet">Excel worksheet</param>
        /// <param name="range">Used range của worksheet</param>
        /// <param name="keywords">Danh sách keywords để identify header</param>
        /// <returns>HeaderRowInfo chứa vị trí header row và danh sách headers</returns>
        private static HeaderRowInfo DetectHeaderRow(IXLWorksheet worksheet, IXLRange range, List<string> keywords)
        {
            var maxRowsToScan = Math.Min(20, range.RowCount());
            var bestMatch = new
            {
                Row = -1,
                Score = 0.0,
                Headers = new List<string>()
            };

            for (int row = 1; row <= maxRowsToScan; row++)
            {
                var headers = new List<string>();
                var matchCount = 0;
                var totalCells = 0;

                // Đọc tất cả cells trong row này
                for (int col = 1; col <= range.ColumnCount(); col++)
                {
                    var cellValue = worksheet.Cell(row, col).GetString().Trim();
                    if (!string.IsNullOrEmpty(cellValue))
                    {
                        headers.Add(cellValue);
                        totalCells++;

                        // Check xem cell này có match với keywords không
                        if (IsHeaderKeywordMatch(cellValue, keywords))
                        {
                            matchCount++;
                        }
                    }
                    else
                    {
                        headers.Add($"Column{col}");
                    }
                }

                // Tính score: (số cell match / tổng số cell có dữ liệu)
                var score = totalCells > 0 ? (double)matchCount / totalCells : 0;

                // Điều kiện để coi là header row:
                // 1. Phải có ít nhất 2 keywords match
                // 2. Score phải >= 0.3 (30% cells match)
                // 3. Phải có ít nhất 3 cells có dữ liệu
                if (matchCount >= 2 && score >= 0.3 && totalCells >= 3 && score > bestMatch.Score)
                {
                    bestMatch = new
                    {
                        Row = row,
                        Score = score,
                        Headers = headers
                    };
                }
            }

            return new HeaderRowInfo
            {
                HeaderRow = bestMatch.Row,
                Headers = bestMatch.Headers,
                MatchScore = bestMatch.Score
            };
        }

        /// <summary>
        /// Check xem một cell value có match với header keywords không
        /// </summary>
        private static bool IsHeaderKeywordMatch(string cellValue, List<string> keywords)
        {
            var normalizedCell = cellValue.ToLower().Trim()
                .Replace(" ", "")
                .Replace("_", "")
                .Replace("-", "");

            foreach (var keyword in keywords)
            {
                var normalizedKeyword = keyword.ToLower().Trim()
                    .Replace(" ", "")
                    .Replace("_", "")
                    .Replace("-", "");

                // Exact match hoặc contains
                if (normalizedCell == normalizedKeyword ||
                    normalizedCell.Contains(normalizedKeyword) ||
                    normalizedKeyword.Contains(normalizedCell))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Class chứa thông tin về header row được detect
        /// </summary>
        private class HeaderRowInfo
        {
            public int HeaderRow { get; set; } = -1;
            public List<string> Headers { get; set; } = [];
            public double MatchScore { get; set; } = 0;
        }

        /// <summary>
        /// Đọc dữ liệu từ file CSV với auto-detect header và trả về result wrapper
        /// </summary>
        public static async Task<ImportResult<List<Dictionary<string, object>>>> ReadCsvFileAsync(
            IFormFile file,
            List<string> expectedHeaders)
        {
            if (file.Length == 0)
                return ImportResult<List<Dictionary<string, object>>>.Failure("File CSV không hợp lệ hoặc rỗng");

            if (expectedHeaders.Count == 0)
                return ImportResult<List<Dictionary<string, object>>>.Failure("Expected headers không được null hoặc rỗng");

            try
            {
                var data = new List<Dictionary<string, object>>();
                using var reader = new StreamReader(file.OpenReadStream());
                using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);
                var records = csv.GetRecords<dynamic>().ToList();

                foreach (var record in records)
                {
                    var rowData = new Dictionary<string, object>();
                    var hasData = false;
                    
                    var recordAsDict = (IDictionary<string, object>)record;
                    
                    foreach (var kvp in recordAsDict)
                    {
                        var value = kvp.Value.ToString() ?? string.Empty;
                        if (!string.IsNullOrEmpty(value))
                        {
                            hasData = true;
                        }
                        rowData[kvp.Key] = value; 
                    }
                    
                    if (hasData)
                    {
                        data.Add(rowData);
                    }
                }

                return data.Count == 0 ? ImportResult<List<Dictionary<string, object>>>.Failure("File CSV không có dữ liệu hợp lệ sau header") : ImportResult<List<Dictionary<string, object>>>.Success(data);
            }
            catch (Exception ex)
            {
                return ImportResult<List<Dictionary<string, object>>>.Failure($"Lỗi đọc file CSV: {ex.Message}");
            }
        }
    }
}