namespace BuildingBlocks.Common.Helper
{
    /// <summary>
    /// Result wrapper cho các operations có thể thất bại
    /// </summary>
    public class ImportResult<T>
    {
        public bool IsSuccess { get; set; }
        public T? Data { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> ValidationErrors { get; set; } = [];

        public static ImportResult<T> Success(T data)
        {
            return new ImportResult<T>
            {
                IsSuccess = true,
                Data = data
            };
        }

        public static ImportResult<T> Failure(string errorMessage)
        {
            return new ImportResult<T>
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }

        public static ImportResult<T> ValidationFailure(List<string> validationErrors)
        {
            return new ImportResult<T>
            {
                IsSuccess = false,
                ValidationErrors = validationErrors,
                ErrorMessage = "Validation failed"
            };
        }
    }
}
