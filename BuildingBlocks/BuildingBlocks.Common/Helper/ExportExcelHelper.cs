using System.Reflection;
using System.Text;
using ClosedXML.Excel;

namespace BuildingBlocks.Common.Helper
{
    public class ExportExcelHelper
    {
        public static Stream ExportListToExcel<T>(List<T> data, string sheetName = "Sheet1")
        {
            if (data == null || data.Count == 0)
                throw new ArgumentException("Data list is empty.");

            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add(sheetName);
            var type = typeof(T);
            var props = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            // Header
            for (int i = 0; i < props.Length; i++)
            {
                worksheet.Cell(1, i + 1).Value = props[i].Name;
            }

            // Data
            for (int row = 0; row < data.Count; row++)
            {
                for (int col = 0; col < props.Length; col++)
                {
                    var val = props[col].GetValue(data[row]);
                    worksheet.Cell(row + 2, col + 1).Value = XLCellValue.FromObject(val);
                }
            }

            using var ms = new MemoryStream();
            workbook.SaveAs(ms);
            ms.Position = 0; // Reset the stream position to the beginning
            return ms;
        }

        public static string ExportListToCsv<T>(List<T> data, char delimiter = ',')
        {
            if (data == null || !data.Any())
                throw new ArgumentException("Data list is empty.");

            var sb = new StringBuilder();
            var props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            // Header
            sb.AppendLine(string.Join(delimiter, props.Select(p => p.Name)));

            // Rows
            foreach (var item in data)
            {
                var values = props.Select(p =>
                {
                    var val = p.GetValue(item, null);
                    // Escape delimiters and quotes for CSV safety
                    var str = val?.ToString() ?? "";
                    if (str.Contains(delimiter) || str.Contains('"') || str.Contains('\n'))
                    {
                        str = "\"" + str.Replace("\"", "\"\"") + "\"";
                    }
                    return str;
                });
                sb.AppendLine(string.Join(delimiter, values));
            }

            return sb.ToString();
        }
    }
}