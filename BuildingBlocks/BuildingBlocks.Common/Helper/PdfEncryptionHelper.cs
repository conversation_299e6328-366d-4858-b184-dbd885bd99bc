using System.Security.Cryptography;
using Microsoft.AspNetCore.Http;
using PdfSharp.Pdf.IO;
using PdfSharp.Drawing;
using PdfSharp.Fonts;
using IOPath = System.IO.Path;

namespace BuildingBlocks.Common.Helper
{
    public class EncryptionResult
    {
        public required MemoryStream EncryptedData { get; set; }
        public required string IV { get; set; }
    }

    public class CustomFontResolver : IFontResolver
    {
        public string DefaultFontName => "Arial";

        public byte[] GetFont(string faceName)
        {
            return LoadFontData(faceName);
        }

        public FontResolverInfo ResolveTypeface(string familyName, bool isBold, bool isItalic)
        {
            return new FontResolverInfo(familyName, isBold, isItalic);
        }

        private static byte[] LoadFontData(string fontName)
        {            var systemFontPath = IOPath.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Fonts");
            var fontPath = IOPath.Combine(systemFontPath, fontName + ".ttf");
            
            if (File.Exists(fontPath))
            {
                return File.ReadAllBytes(fontPath);
            }

            // Nếu không tìm thấy font được yêu cầu, thử dùng Segoe UI
            fontPath = IOPath.Combine(systemFontPath, "segoeui.ttf");
            if (File.Exists(fontPath))
            {
                return File.ReadAllBytes(fontPath);
            }

            // Nếu không tìm thấy font nào, throw exception
            throw new FileNotFoundException($"Cannot find font {fontName}");
        }
    }

    /// <summary>
    /// Lớp tiện ích để mã hóa và giải mã tập tin PDF bằng AES-GCM
    /// Định dạng tập tin đã mã hóa: [Salt (16 bytes)][Tag (16 bytes)][Ciphertext]
    /// </summary>
    public static class PdfEncryptionHelper
    {
        // Các hằng số tham số mã hóa
        private const int SaltSize = 16; // 128 bits - kích thước salt
        private const int IvSize = 12; // 96 bits - kích thước vector khởi tạo, được khuyến nghị cho GCM
        private const int KeySize = 32; // 256 bits - kích thước khóa
        private const int TagSize = 16; // 128 bits - kích thước tag xác thực
        private const int Iterations = 100000; // Số vòng lặp PBKDF2, giá trị cao được khuyến nghị

        /// <summary>
        /// Mã hóa tập tin PDF sử dụng AES-GCM với khóa được sinh từ mật khẩu
        /// </summary>
        /// <param name="inputPdfFile">Tập tin PDF đầu vào cần mã hóa</param>
        /// <param name="secretKey">Khóa bí mật dùng để mã hóa</param>
        /// <returns>EncryptionResult chứa dữ liệu đã được mã hóa và IV dạng string</returns>
        public static async Task<EncryptionResult> EncryptPdfIFormFileAsync(IFormFile inputPdfFile, string secretKey)
        {
            // Đọc toàn bộ dữ liệu PDF từ IFormFile
            byte[] pdfData;
            using (var ms = new MemoryStream())
            using (var inputStream = inputPdfFile.OpenReadStream())
            {
                await inputStream.CopyToAsync(ms);
                pdfData = ms.ToArray();
            }

            // Tạo salt, key, iv
            byte[] salt = GenerateRandomBytes(SaltSize);
            byte[] key = GenerateKeyFromPassword(secretKey, salt);
            byte[] iv = GenerateRandomBytes(IvSize);

            // Mã hóa sử dụng AES-GCM
            using var aesGcm = new AesGcm(key, TagSize);
            byte[] encryptedData = new byte[pdfData.Length];
            byte[] tag = new byte[TagSize];

            aesGcm.Encrypt(iv, pdfData, encryptedData, tag);

            // Ghi ra memory stream theo format [Salt][Tag][CipherText]
            var outputStream = new MemoryStream();
            await outputStream.WriteAsync(salt, 0, salt.Length);
            await outputStream.WriteAsync(tag, 0, tag.Length);
            await outputStream.WriteAsync(encryptedData, 0, encryptedData.Length);

            outputStream.Position = 0; // Reset về đầu stream để đọc từ đầu sau này

            // Convert IV to base64 string
            string ivString = Convert.ToBase64String(iv);

            return new EncryptionResult
            {
                EncryptedData = outputStream,
                IV = ivString
            };
        }

        /// <summary>
        /// Mã hóa byte array PDF sử dụng AES-GCM với khóa được sinh từ mật khẩu
        /// </summary>
        /// <param name="pdfBytes">Byte array của PDF cần mã hóa</param>
        /// <param name="secretKey">Khóa bí mật dùng để mã hóa</param>
        /// <returns>EncryptionResult chứa dữ liệu đã được mã hóa và IV dạng string</returns>
        public static async Task<EncryptionResult> EncryptPdfBytesAsync(byte[] pdfBytes, string secretKey)
        {
            // Tạo salt, key, iv
            byte[] salt = GenerateRandomBytes(SaltSize);
            byte[] key = GenerateKeyFromPassword(secretKey, salt);
            byte[] iv = GenerateRandomBytes(IvSize);

            // Mã hóa sử dụng AES-GCM
            using var aesGcm = new AesGcm(key, TagSize);
            byte[] encryptedData = new byte[pdfBytes.Length];
            byte[] tag = new byte[TagSize];

            aesGcm.Encrypt(iv, pdfBytes, encryptedData, tag);

            // Ghi ra memory stream theo format [Salt][Tag][CipherText]
            var outputStream = new MemoryStream();
            await outputStream.WriteAsync(salt, 0, salt.Length);
            await outputStream.WriteAsync(tag, 0, tag.Length);
            await outputStream.WriteAsync(encryptedData, 0, encryptedData.Length);

            outputStream.Position = 0; // Reset về đầu stream để đọc từ đầu sau này

            // Convert IV to base64 string
            string ivString = Convert.ToBase64String(iv);

            return new EncryptionResult
            {
                EncryptedData = outputStream,
                IV = ivString
            };
        }

        /// <summary>
        /// Giải mã tập tin đã mã hóa về tập tin PDF gốc
        /// </summary>
        /// <param name="encryptedFile">Tập tin đã mã hóa</param>
        /// <param name="sercretKey">Mật khẩu đã sử dụng để mã hóa</param>
        /// <param name="ivString">IV dạng string đã được trả về khi mã hóa</param>
        /// <param name="watermarkText">Văn bản watermark</param>
        /// <param name="opacity">Độ mờ của watermark</param>
        /// <param name="watermarksPerPage">Số lượng watermark trên mỗi trang</param>
        /// <returns>MemoryStream chứa dữ liệu PDF đã giải mã</returns>
        public static async Task<MemoryStream> DecryptPdfIFormFileAsync(IFormFile encryptedFile, string sercretKey, string ivString, string watermarkText = "Watermark")
        {
            // Convert IV string back to bytes
            byte[] iv = Convert.FromBase64String(ivString);

            // Đọc toàn bộ dữ liệu từ IFormFile (file đã mã hóa)
            byte[] encryptedData;
            using (var ms = new MemoryStream())
            using (var inputStream = encryptedFile.OpenReadStream())
            {
                await inputStream.CopyToAsync(ms);
                encryptedData = ms.ToArray();
            }

            // Trích xuất salt (SaltSize bytes đầu tiên)
            byte[] salt = new byte[SaltSize];
            Buffer.BlockCopy(encryptedData, 0, salt, 0, SaltSize);

            // Trích xuất tag xác thực (TagSize bytes tiếp theo)
            byte[] tag = new byte[TagSize];
            Buffer.BlockCopy(encryptedData, SaltSize, tag, 0, TagSize);

            // Trích xuất dữ liệu mã hóa (phần còn lại của file)
            int ciphertextLength = encryptedData.Length - (SaltSize + TagSize);
            byte[] ciphertext = new byte[ciphertextLength];
            Buffer.BlockCopy(encryptedData, SaltSize + TagSize, ciphertext, 0, ciphertextLength);

            // Tạo khóa từ mật khẩu và salt
            byte[] key = GenerateKeyFromPassword(sercretKey, salt);

            // Giải mã AES-GCM với kích thước tag cụ thể
            using var aesGcm = new AesGcm(key, TagSize);
            byte[] plaintext = new byte[ciphertext.Length];

            aesGcm.Decrypt(iv, ciphertext, tag, plaintext);

            // Trả ra MemoryStream chứa PDF đã giải mã
            var outputStream = new MemoryStream(plaintext);
            outputStream.Position = 0; // Đảm bảo stream ở đầu để đọc
            // var result = AddWatermark(outputStream, watermarkText, IOPath.GetExtension(encryptedFile.FileName));
            // result.Position = 0;
            return outputStream;
        }

        /// <summary>
        /// Giải mã stream đã mã hóa về dữ liệu gốc
        /// </summary>
        /// <param name="encryptedStream">Stream chứa dữ liệu đã mã hóa</param>
        /// <param name="secretKey">Mật khẩu đã sử dụng để mã hóa</param>
        /// <param name="ivString">IV dạng string đã được trả về khi mã hóa</param>
        /// <returns>MemoryStream chứa dữ liệu đã giải mã</returns>
        public static async Task<MemoryStream> DecryptStreamAsync(Stream encryptedStream, string secretKey, string ivString)
        {
            // Convert IV string back to bytes
            byte[] iv = Convert.FromBase64String(ivString);

            // Đọc toàn bộ dữ liệu từ stream (dữ liệu đã mã hóa)
            byte[] encryptedData;
            using (var ms = new MemoryStream())
            {
                await encryptedStream.CopyToAsync(ms);
                encryptedData = ms.ToArray();
            }

            // Trích xuất salt (SaltSize bytes đầu tiên)
            byte[] salt = new byte[SaltSize];
            Buffer.BlockCopy(encryptedData, 0, salt, 0, SaltSize);

            // Trích xuất tag xác thực (TagSize bytes tiếp theo)
            byte[] tag = new byte[TagSize];
            Buffer.BlockCopy(encryptedData, SaltSize, tag, 0, TagSize);

            // Trích xuất dữ liệu mã hóa (phần còn lại của dữ liệu)
            int ciphertextLength = encryptedData.Length - (SaltSize + TagSize);
            byte[] ciphertext = new byte[ciphertextLength];
            Buffer.BlockCopy(encryptedData, SaltSize + TagSize, ciphertext, 0, ciphertextLength);

            // Tạo khóa từ mật khẩu và salt
            byte[] key = GenerateKeyFromPassword(secretKey, salt);

            // Giải mã AES-GCM với kích thước tag cụ thể
            using var aesGcm = new AesGcm(key, TagSize);
            byte[] plaintext = new byte[ciphertext.Length];

            aesGcm.Decrypt(iv, ciphertext, tag, plaintext);

            // Trả ra MemoryStream chứa dữ liệu đã giải mã
            var outputStream = new MemoryStream(plaintext);
            outputStream.Position = 0; // Đảm bảo stream ở đầu để đọc
            return outputStream;
        }

        /// <summary>
        /// Sinh khóa từ mật khẩu và salt sử dụng PBKDF2
        /// </summary>
        private static byte[] GenerateKeyFromPassword(string password, byte[] salt)
        {
            return Rfc2898DeriveBytes.Pbkdf2(
                password,
                salt,
                Iterations,
                HashAlgorithmName.SHA256,
                KeySize);
        }

        /// <summary>
        /// Sinh ngẫu nhiên các byte cho salt hoặc IV
        /// </summary>
        private static byte[] GenerateRandomBytes(int length)
        {
            var randomBytes = new byte[length];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomBytes);
            return randomBytes;
        }

        private static MemoryStream AddWatermark(MemoryStream sourceStream, string watermarkText, string fileExtension, double opacity = 0.15, int watermarksPerPage = 9)
        {
            // Đăng ký FontResolver nếu chưa được đăng ký
            if (GlobalFontSettings.FontResolver == null)
            {
                GlobalFontSettings.FontResolver = new CustomFontResolver();
            }

            var outputStream = new MemoryStream();
            fileExtension = fileExtension.ToLower();

            if (fileExtension == ".pdf")
            {
                // Load the PDF document
                sourceStream.Position = 0;
                var pdfDoc = PdfReader.Open(sourceStream, PdfDocumentOpenMode.Modify);

                // Add watermark to each page
                foreach (var page in pdfDoc.Pages)
                {
                    var gfx = XGraphics.FromPdfPage(page);

                    // Setup font và định dạng text
                    XFont font = new("Arial", 30);
                    double pageWidth = page.Width.Point;
                    double pageHeight = page.Height.Point;                    // Tạo brush với độ mờ tùy chỉnh
                    var color = XColor.FromArgb((int)(opacity * 255), 255, 0, 0); // Red với opacity
                    var brush = new XSolidBrush(color);

                    // Tính toán khoảng cách giữa các watermark
                    int rows = (int)Math.Sqrt(watermarksPerPage);
                    int cols = (int)Math.Ceiling(watermarksPerPage / (double)rows);
                    double cellWidth = pageWidth / cols;
                    double cellHeight = pageHeight / rows;

                    // Vẽ watermark trong mỗi ô
                    for (int row = 0; row < rows; row++)
                    {
                        for (int col = 0; col < cols; col++)
                        {
                            if (row * cols + col >= watermarksPerPage) break;

                            // Tính vị trí trung tâm của mỗi ô
                            double centerX = col * cellWidth + cellWidth / 2;
                            double centerY = row * cellHeight + cellHeight / 2;

                            // Lưu trạng thái transform hiện tại
                            var state = gfx.Save();

                            // Di chuyển đến tâm của ô
                            gfx.TranslateTransform(centerX, centerY);
                            // Xoay -45 độ
                            gfx.RotateTransform(-45);

                            // Tính kích thước của text để căn giữa
                            var size = gfx.MeasureString(watermarkText, font);
                            // Vẽ text ở giữa ô
                            gfx.DrawString(watermarkText, font, brush,
                                new XRect(-size.Width/2, -size.Height/2, size.Width, size.Height),
                                XStringFormats.Center);

                            // Khôi phục trạng thái transform
                            gfx.Restore(state);
                        }
                    }
                }

                // Save to output stream
                pdfDoc.Save(outputStream);
            }
            else
            {
                // For other file types, just return the original content
                sourceStream.CopyTo(outputStream);
            }

            outputStream.Position = 0;
            return outputStream;
        }
    }
}
