using BuildingBlocks.Common.PushNotification.Models;
using Refit;

namespace BuildingBlocks.Common.PushNotification.Interfaces;

/// <summary>
/// Interface API cho Push Notification Service
/// Hỗ trợ gửi cloud message đến mobile app
/// </summary>
public interface IPushNotificationApi
{
    /// <summary>
    /// API gửi cloud message đến mobile app
    /// </summary>
    /// <param name="request">Thông tin cloud message</param>
    /// <returns>HTTP Response</returns>
    [Post("users/send-cloud-message")]
    Task<HttpResponseMessage> SendCloudMessageAsync([Body] SendCloudMessageRequest request);

    /// <summary>
    /// API gửi sign notification đến mobile app
    /// </summary>
    /// <param name="request">CCCD của bệnh nhân</param>
    /// <returns>HTTP Response</returns>
    [Post("digital-signs/send-sign-notification")]
    Task<HttpResponseMessage> SendSignNotificationAsync([Body] SendNotificationRequest request);
}
