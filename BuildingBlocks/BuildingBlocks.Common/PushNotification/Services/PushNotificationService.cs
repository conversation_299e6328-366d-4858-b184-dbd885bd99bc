using BuildingBlocks.Common.PushNotification.Interfaces;
using BuildingBlocks.Common.PushNotification.Models;
using Microsoft.Extensions.Logging;

namespace BuildingBlocks.Common.PushNotification.Services;

/// <summary>
/// Service để gửi push notification đến mobile app
/// </summary>
public class PushNotificationService(
    IPushNotificationApi pushNotificationApi,
    ILogger<PushNotificationService> logger)
{
    private readonly IPushNotificationApi _pushNotificationApi = pushNotificationApi;
    private readonly ILogger<PushNotificationService> _logger = logger;

    /// <summary>
    /// Gửi OTP đến mobile app của bệnh nhân
    /// </summary>
    /// <param name="customerId">Số CMND của bệnh nhân</param>
    /// <param name="otp">Mã OTP</param>
    /// <param name="sessionId">Session ID để tra cứu</param>
    /// <returns>Kết quả gửi OTP</returns>
    public async Task<bool> SendNotificationAsync(string identityNo)
    {
        try
        {
            _logger.LogInformation("Sending notification to customer {CustomerId} for file to sign",
                identityNo);

            var request = new SendNotificationRequest
            {
                IdentityNo = identityNo,
            };

            var response = await _pushNotificationApi.SendSignNotificationAsync(request);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully sent notification to customer {CustomerId}. Status: {StatusCode}",
                    identityNo, response.StatusCode);
                return true;
            }
            else
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to send OTP to customer {CustomerId}. Status: {StatusCode}, Content: {Content}",
                    identityNo, response.StatusCode, responseContent);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to customer {CustomerId}",
                identityNo);
            return false;
        }
    }
    
    /// <summary>
    /// Gửi notification đến mobile app của bệnh nhân
    /// </summary>
    /// <param name="customerId">Số CMND của bệnh nhân</param>
    /// <param name="otp">Mã OTP</param>
    /// <param name="sessionId">Session ID để tra cứu</param>
    /// <returns>Kết quả gửi OTP</returns>
    public async Task<bool> SendOtpToMobileAsync(string customerId, string otp, Guid sessionId)
    {
        try
        {
            _logger.LogInformation("Sending OTP {OTP} to customer {CustomerId} for session {SessionId}", 
                otp, customerId, sessionId);

            var request = new SendCloudMessageRequest
            {
                CustomerIdentityNo = customerId,
                Notification = new NotificationInfo
                {
                    Title = "Mã xác thực tra cứu bệnh án",
                    Body = $"Mã OTP của bạn là: {otp}. Vui lòng nhập mã này để tra cứu lịch sử bệnh án."
                },
                Data = new Dictionary<string, string>
                {
                    ["otp"] = otp,
                    ["sessionId"] = sessionId.ToString(),
                    ["type"] = "medical_record_lookup",
                    ["timestamp"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ")
                },
                CreatedBy = "medipay_kiosk",
                TemplateCode = "medipay_kiosk_otp"
            };

            var response = await _pushNotificationApi.SendCloudMessageAsync(request);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully sent OTP to customer {CustomerId} for session {SessionId}. Status: {StatusCode}",
                    customerId, sessionId, response.StatusCode);
                return true;
            }
            else
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to send OTP to customer {CustomerId} for session {SessionId}. Status: {StatusCode}, Content: {Content}",
                    customerId, sessionId, response.StatusCode, responseContent);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending OTP to customer {CustomerId} for session {SessionId}", 
                customerId, sessionId);
            return false;
        }
    }
}
