using BuildingBlocks.Common.PushNotification.Interfaces;
using BuildingBlocks.Common.PushNotification.Models;
using BuildingBlocks.Common.PushNotification.Services;
using Microsoft.Extensions.DependencyInjection;
using Refit;
using System.Text.Json;

namespace BuildingBlocks.Common.PushNotification.Extensions;

/// <summary>
/// Extension methods cho việc đăng ký Push Notification services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Đăng ký Push Notification services với Dependency Injection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">C<PERSON>u hình Push Notification</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddPushNotification(this IServiceCollection services, PushNotificationConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);

        if (string.IsNullOrEmpty(configuration.BaseUrl))
            throw new ArgumentException("BaseUrl is required", nameof(configuration));

        // Đăng ký configuration
        services.AddSingleton(configuration);

        // Cấu hình Refit settings
        var refitSettings = new RefitSettings
        {
            ContentSerializer = new SystemTextJsonContentSerializer(new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            })
        };

        // Đăng ký Refit client với HttpClient
        services.AddRefitClient<IPushNotificationApi>(refitSettings)
                .ConfigureHttpClient(client =>
                {
                    client.BaseAddress = new Uri(configuration.BaseUrl);
                    client.Timeout = TimeSpan.FromSeconds(configuration.TimeoutSeconds);

                    // Thêm headers
                    client.DefaultRequestHeaders.Add("User-Agent", configuration.UserAgent);
                    client.DefaultRequestHeaders.Add("Accept", "*/*");
                    client.DefaultRequestHeaders.Add("Connection", "keep-alive");
                });

        // Đăng ký PushNotificationService
        services.AddScoped<PushNotificationService>();

        return services;
    }

    /// <summary>
    /// Đăng ký Push Notification services với cấu hình từ action
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Action để cấu hình</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddPushNotification(this IServiceCollection services, Action<PushNotificationConfiguration> configureOptions)
    {
        var configuration = new PushNotificationConfiguration();
        configureOptions(configuration);
        return services.AddPushNotification(configuration);
    }
}
