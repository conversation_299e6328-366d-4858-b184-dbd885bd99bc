using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.PushNotification.Models;

/// <summary>
/// Request để gửi cloud message đến mobile app
/// </summary>
public class SendCloudMessageRequest
{
    /// <summary>
    /// Số CMND của bệnh nhân
    /// </summary>
    [JsonPropertyName("customerIdentityNo")]
    public string CustomerIdentityNo { get; set; } = string.Empty;

    /// <summary>
    /// Thông tin notification hiển thị
    /// </summary>
    [JsonPropertyName("notification")]
    public NotificationInfo Notification { get; set; } = new();

    /// <summary>
    /// D<PERSON> liệu bổ sung (dictionary)
    /// </summary>
    [JsonPropertyName("data")]
    public Dictionary<string, string> Data { get; set; } = new();

    /// <summary>
    /// Người tạo request
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = "medipay_kiosk";

    /// <summary>
    /// Mã template
    /// </summary>
    [JsonPropertyName("templateCode")]
    public string TemplateCode { get; set; } = "medipay_kiosk_otp";
}

/// <summary>
/// Thông tin notification
/// </summary>
public class NotificationInfo
{
    /// <summary>
    /// Tiêu đề notification
    /// </summary>
    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Nội dung notification
    /// </summary>
    [JsonPropertyName("body")]
    public string Body { get; set; } = string.Empty;
}

/// <summary>
/// Response từ push notification service
/// </summary>
public class SendCloudMessageResponse
{
    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// Thông báo kết quả
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Dữ liệu trả về (nếu có)
    /// </summary>
    [JsonPropertyName("data")]
    public object? Data { get; set; }
}

public class SendNotificationRequest
{
    /// <summary>
    /// Số CMND của người nhận notification
    /// </summary>   
    [JsonPropertyName("identityNo")]
    public string IdentityNo { get; set; } = string.Empty;
}