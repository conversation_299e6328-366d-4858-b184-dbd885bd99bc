using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.EasySign.Models;

/// <summary>
/// Thông tin chứng thư số để ký
/// </summary>
public class TokenInfo
{
    /// <summary>
    /// Thông tin Serial chứng thư số (CTS)
    /// </summary>
    [JsonPropertyName("serial")]
    public string Serial { get; set; } = string.Empty;

    /// <summary>
    /// Mã PIN truy cập chứng thư số
    /// </summary>
    [JsonPropertyName("pin")]
    public string Pin { get; set; } = string.Empty;
}

/// <summary>
/// Tùy chọn ký số
/// </summary>
public class SigningOptional
{
    /// <summary>
    /// Kiểu băm (Mặc định: SHA1 – Giá trị nhận: SHA1, SHA256, SHA512)
    /// </summary>
    [JsonPropertyName("hashAlgorithm")]
    public string? HashAlgorithm { get; set; }

    /// <summary>
    /// Tr<PERSON> về nội dung đầu vào (Mặc định: false)
    /// </summary>
    [JsonPropertyName("returnInputData")]
    public bool? ReturnInputData { get; set; }

    /// <summary>
    /// Kiểu giải thuật ký số (Mặc định: RSA)
    /// </summary>
    [JsonPropertyName("signAlgorithm")]
    public string? SignAlgorithm { get; set; }

    /// <summary>
    /// Mã xác thực OTP (Nếu có cấu hình bật tính năng này)
    /// </summary>
    [JsonPropertyName("otpCode")]
    public string? OtpCode { get; set; }
}
