using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.EasySign.Models;

/// <summary>
/// Mẫu chữ ký
/// </summary>
public class SignatureTemplate
{
    /// <summary>
    /// Mã định danh mẫu chữ ký
    /// </summary>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// Mã định danh của user
    /// </summary>
    [JsonPropertyName("userId")]
    public int? UserId { get; set; }

    /// <summary>
    /// Username của tài khoản tạo mẫu chữ ký
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Thời gian tạo mẫu chữ ký
    /// </summary>
    [JsonPropertyName("createdDate")]
    public DateTime? CreatedDate { get; set; }

    /// <summary>
    /// Tên trình tạo thông tin trên mẫu chữ ký
    /// </summary>
    [JsonPropertyName("coreParser")]
    public string? CoreParser { get; set; }

    /// <summary>
    /// Họ tên của chủ sở hữu mẫu chữ ký
    /// </summary>
    [JsonPropertyName("fullName")]
    public string? FullName { get; set; }

    /// <summary>
    /// Chiều rộng của mẫu chữ ký (đơn vị: pixel)
    /// </summary>
    [JsonPropertyName("width")]
    public int Width { get; set; }

    /// <summary>
    /// Chiều cao của mẫu chữ ký (đơn vị: pixel)
    /// </summary>
    [JsonPropertyName("height")]
    public int Height { get; set; }

    /// <summary>
    /// Mẫu HTML của ảnh chữ ký
    /// </summary>
    [JsonPropertyName("htmlTemplate")]
    public string? HtmlTemplate { get; set; }

    /// <summary>
    /// Xác định mẫu chữ ký có thuộc dạng xóa phông hay không
    /// </summary>
    [JsonPropertyName("transparency")]
    public bool Transparency { get; set; }

    /// <summary>
    /// Base64 của ảnh mẫu chữ ký
    /// </summary>
    [JsonPropertyName("thumbnail")]
    public string? Thumbnail { get; set; }

    /// <summary>
    /// Tên mẫu chữ ký
    /// </summary>
    [JsonPropertyName("templateName")]
    public string? TemplateName { get; set; }

    /// <summary>
    /// Kiểu mẫu chữ ký: 0: CA_NHAN, 1: CA_NHAN_DAU_MOC, 2: TO_CHUC_DAU_MOC, 3: KY_NHAY
    /// </summary>
    [JsonPropertyName("type")]
    public int? Type { get; set; }
}
