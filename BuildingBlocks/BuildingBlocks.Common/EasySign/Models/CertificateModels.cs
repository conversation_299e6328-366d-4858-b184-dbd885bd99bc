using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.EasySign.Models;

/// <summary>
/// Y<PERSON>u cầu thay đổi PIN chứng thư số
/// </summary>
public class ChangeCertPinRequest
{
    /// <summary>
    /// Thông tin Serial chứng thư số (CTS)
    /// </summary>
    [JsonPropertyName("serial")]
    public string Serial { get; set; } = string.Empty;

    /// <summary>
    /// Mã PIN mới muốn đổi cho certificate
    /// </summary>
    [JsonPropertyName("newPIN")]
    public string NewPin { get; set; } = string.Empty;

    /// <summary>
    /// Mã PIN hiện tại của certificate muốn sửa
    /// </summary>
    [JsonPropertyName("oldPIN")]
    public string OldPin { get; set; } = string.Empty;

    /// <summary>
    /// Mã OTP (nếu hệ thống có bật tính năng <PERSON>TP)
    /// </summary>
    [JsonPropertyName("otpCode")]
    public string? OtpCode { get; set; }
}
