using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.EasySign.Models;

/// <summary>
/// Phần tử xác thực
/// </summary>
public class VerificationElement
{
    /// <summary>
    /// Base64 của nội dung chữ ký số
    /// </summary>
    [JsonPropertyName("base64Signature")]
    public string Base64Signature { get; set; } = string.Empty;

    /// <summary>
    /// Base64 của nội dung dữ liệu đầu vào
    /// </summary>
    [JsonPropertyName("base64OriginalData")]
    public string Base64OriginalData { get; set; } = string.Empty;

    /// <summary>
    /// Định danh dữ liệu
    /// </summary>
    [JsonPropertyName("key")]
    public string? Key { get; set; }
}

/// <summary>
/// Yêu cầu xác thực chữ ký Raw
/// </summary>
public class VerifyRawRequest
{
    /// <summary>
    /// Danh sách phần tử cần xác thực
    /// </summary>
    [JsonPropertyName("elements")]
    public List<VerificationElement> Elements { get; set; } = new();

    /// <summary>
    /// Kiểu băm (Mặc định: SHA1 – Giá trị nhận: SHA1, SHA256, SHA512)
    /// </summary>
    [JsonPropertyName("hashAlgorithm")]
    public string? HashAlgorithm { get; set; }

    /// <summary>
    /// Thông tin Serial chứng thư số (CTS)
    /// </summary>
    [JsonPropertyName("serial")]
    public string Serial { get; set; } = string.Empty;
}

/// <summary>
/// Yêu cầu xác thực chữ ký Hash
/// </summary>
public class VerifyHashRequest
{
    /// <summary>
    /// Danh sách phần tử cần xác thực
    /// </summary>
    [JsonPropertyName("elements")]
    public List<VerificationElement> Elements { get; set; } = new();

    /// <summary>
    /// Kiểu băm (Mặc định: SHA1 – Giá trị nhận: SHA1, SHA256, SHA512)
    /// </summary>
    [JsonPropertyName("hashAlgorithm")]
    public string? HashAlgorithm { get; set; }

    /// <summary>
    /// Thông tin Serial chứng thư số (CTS)
    /// </summary>
    [JsonPropertyName("serial")]
    public string Serial { get; set; } = string.Empty;
}

/// <summary>
/// Kết quả xác thực
/// </summary>
public class VerificationResult
{
    /// <summary>
    /// Định danh dữ liệu ký số
    /// </summary>
    [JsonPropertyName("key")]
    public string? Key { get; set; }

    /// <summary>
    /// Trạng thái xác thực dữ liệu (true: thành công – false: thất bại)
    /// </summary>
    [JsonPropertyName("result")]
    public bool Result { get; set; }
}

/// <summary>
/// Dữ liệu phản hồi xác thực
/// </summary>
public class VerificationResponseData
{
    /// <summary>
    /// Danh sách kết quả xác thực
    /// </summary>
    [JsonPropertyName("elements")]
    public List<VerificationResult>? Elements { get; set; }

    /// <summary>
    /// Base64 của chứng thư số
    /// </summary>
    [JsonPropertyName("certificate")]
    public string? Certificate { get; set; }
}
