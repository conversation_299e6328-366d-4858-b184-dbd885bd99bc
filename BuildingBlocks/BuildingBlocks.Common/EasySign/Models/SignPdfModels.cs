using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.EasySign.Models;

/// <summary>
/// Vị trí ký trên PDF
/// </summary>
public class PdfSignLocation
{
    /// <summary>
    /// Tọa độ X
    /// </summary>
    [JsonPropertyName("visibleX")]
    public int VisibleX { get; set; }

    /// <summary>
    /// Tọa độ Y
    /// </summary>
    [JsonPropertyName("visibleY")]
    public int VisibleY { get; set; }

    /// <summary>
    /// Kích thước chiều rộng ảnh chữ ký hiển thị (default = 150)
    /// </summary>
    [JsonPropertyName("visibleWidth")]
    public int VisibleWidth { get; set; } = 150;

    /// <summary>
    /// Kích thước chiều cao ảnh chữ ký hiển thị (default = 50)
    /// </summary>
    [JsonPropertyName("visibleHeight")]
    public int VisibleHeight { get; set; } = 50;
}

/// <summary>
/// Thông tin trang ký
/// </summary>
public class PdfExtraInfo
{
    /// <summary>
    /// Trang ký đặt chữ ký, default = page 1
    /// </summary>
    [JsonPropertyName("pageNum")]
    public int PageNum { get; set; } = 1;
}

/// <summary>
/// Nội dung yêu cầu ký PDF
/// </summary>
public class PdfSigningRequestContent
{
    /// <summary>
    /// Nội dung PDF cần ký đã được encode base64
    /// </summary>
    [JsonPropertyName("data")]
    public string Data { get; set; } = string.Empty;

    /// <summary>
    /// Tên định danh tài liệu
    /// </summary>
    [JsonPropertyName("documentName")]
    public string DocumentName { get; set; } = string.Empty;

    /// <summary>
    /// Vị trí ký (chỉ dùng cho ký hiện)
    /// </summary>
    [JsonPropertyName("location")]
    public PdfSignLocation? Location { get; set; }

    /// <summary>
    /// Thông tin trang ký (chỉ dùng cho ký hiện)
    /// </summary>
    [JsonPropertyName("extraInfo")]
    public PdfExtraInfo? ExtraInfo { get; set; }

    /// <summary>
    /// Ảnh chữ ký dạng base64 (chỉ dùng cho ký hiện)
    /// </summary>
    [JsonPropertyName("imageSignature")]
    public string? ImageSignature { get; set; }
}

/// <summary>
/// Yêu cầu ký PDF ẩn
/// </summary>
public class SignInvisiblePdfRequest
{
    /// <summary>
    /// Danh sách nội dung cần ký
    /// </summary>
    [JsonPropertyName("signingRequestContents")]
    public List<PdfSigningRequestContent> SigningRequestContents { get; set; } = new();

    /// <summary>
    /// Tùy chọn ký số
    /// </summary>
    [JsonPropertyName("optional")]
    public SigningOptional? Optional { get; set; }

    /// <summary>
    /// Thông tin chứng thư ký số
    /// </summary>
    [JsonPropertyName("tokenInfo")]
    public TokenInfo TokenInfo { get; set; } = new();
}

/// <summary>
/// Yêu cầu ký PDF hiện vị trí
/// </summary>
public class SignVisiblePdfRequest
{
    /// <summary>
    /// Danh sách nội dung cần ký
    /// </summary>
    [JsonPropertyName("signingRequestContents")]
    public List<PdfSigningRequestContent> SigningRequestContents { get; set; } = new();

    /// <summary>
    /// Tùy chọn ký số
    /// </summary>
    [JsonPropertyName("optional")]
    public SigningOptional? Optional { get; set; }

    /// <summary>
    /// Thông tin chứng thư ký số
    /// </summary>
    [JsonPropertyName("tokenInfo")]
    public TokenInfo TokenInfo { get; set; } = new();
}

/// <summary>
/// Nội dung phản hồi ký PDF
/// </summary>
public class PdfResponseContent
{
    /// <summary>
    /// Tên tài liệu
    /// </summary>
    [JsonPropertyName("documentName")]
    public string? DocumentName { get; set; }

    /// <summary>
    /// Chữ ký số riêng lẻ dưới dạng base64
    /// </summary>
    [JsonPropertyName("signatureOnly")]
    public string? SignatureOnly { get; set; }

    /// <summary>
    /// Base64 tệp PDF đầu vào kèm chữ ký số
    /// </summary>
    [JsonPropertyName("signedDocument")]
    public string? SignedDocument { get; set; }
}

/// <summary>
/// Dữ liệu phản hồi ký PDF
/// </summary>
public class SignPdfResponseData
{
    /// <summary>
    /// Danh sách nội dung phản hồi
    /// </summary>
    [JsonPropertyName("responseContentList")]
    public List<PdfResponseContent>? ResponseContentList { get; set; }

    /// <summary>
    /// Base64 của chứng thư số
    /// </summary>
    [JsonPropertyName("base64Certificate")]
    public string? Base64Certificate { get; set; }
}
