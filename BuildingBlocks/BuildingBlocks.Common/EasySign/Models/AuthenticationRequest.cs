using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.EasySign.Models;

/// <summary>
/// <PERSON><PERSON><PERSON> cầu đăng nhập hệ thống EasySign
/// </summary>
public class AuthenticationRequest
{
    /// <summary>
    /// Tên tà<PERSON>
    /// </summary>
    [JsonPropertyName("username")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// M<PERSON>t khẩu
    /// </summary>
    [JsonPropertyName("password")]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Ghi nhớ đăng nhập
    /// </summary>
    [JsonPropertyName("rememberMe")]
    public bool RememberMe { get; set; } = false;
}
