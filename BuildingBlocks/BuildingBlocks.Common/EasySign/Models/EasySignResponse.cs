using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.EasySign.Models;

/// <summary>
/// <PERSON>ản hồi chung từ EasySign API
/// </summary>
/// <typeparam name="T">Ki<PERSON>u dữ liệu của data</typeparam>
public class EasySignResponse<T>
{
    /// <summary>
    /// Trạng thái xử lý
    /// Status = 0: Thành công
    /// Status = -1: Lỗi chứng thư số (không tìm thấy, hết hạn, thu hồi)
    /// Status = 9: Lỗi serial hoặc pin
    /// Status = 10: Lỗi trong quá trình ký
    /// Status = 13: PIN không đúng
    /// </summary>
    [JsonPropertyName("status")]
    public int Status { get; set; }

    /// <summary>
    /// Thông tin chi tiết trạng thái
    /// </summary>
    [JsonPropertyName("msg")]
    public string? Msg { get; set; }

    /// <summary>
    /// Dữ liệu trả về
    /// </summary>
    [JsonPropertyName("data")]
    public T? Data { get; set; }
}
