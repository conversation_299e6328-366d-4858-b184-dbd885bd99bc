using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.EasySign.Models;

/// <summary>
/// Nội dung yêu cầu ký XML
/// </summary>
public class XmlSigningRequestContent
{
    /// <summary>
    /// Nội dung XML cần ký đã được encode base64
    /// </summary>
    [JsonPropertyName("data")]
    public string Data { get; set; } = string.Empty;

    /// <summary>
    /// Tên định danh tài liệu
    /// </summary>
    [JsonPropertyName("documentName")]
    public string DocumentName { get; set; } = string.Empty;
}

/// <summary>
/// Tùy chọn ký XML
/// </summary>
public class XmlSigningOptional
{
    /// <summary>
    /// Mã xác thực OTP (Nếu có cấu hình bật tính năng này)
    /// </summary>
    [JsonPropertyName("otpCode")]
    public string? OtpCode { get; set; }
}

/// <summary>
/// Y<PERSON><PERSON> cầu ký XML
/// </summary>
public class SignXmlRequest
{
    /// <summary>
    /// Danh sách nội dung cần ký
    /// </summary>
    [JsonPropertyName("signingRequestContents")]
    public List<XmlSigningRequestContent> SigningRequestContents { get; set; } = new();

    /// <summary>
    /// Tùy chọn ký số
    /// </summary>
    [JsonPropertyName("optional")]
    public XmlSigningOptional? Optional { get; set; }

    /// <summary>
    /// Thông tin chứng thư ký số
    /// </summary>
    [JsonPropertyName("tokenInfo")]
    public TokenInfo TokenInfo { get; set; } = new();
}

/// <summary>
/// Nội dung phản hồi ký XML
/// </summary>
public class XmlResponseContent
{
    /// <summary>
    /// Tên tài liệu
    /// </summary>
    [JsonPropertyName("documentName")]
    public string? DocumentName { get; set; }

    /// <summary>
    /// Chữ ký số riêng lẻ dưới dạng base64
    /// </summary>
    [JsonPropertyName("signatureOnly")]
    public string? SignatureOnly { get; set; }

    /// <summary>
    /// Base64 tệp XML đầu vào kèm chữ ký số
    /// </summary>
    [JsonPropertyName("signedDocument")]
    public string? SignedDocument { get; set; }
}

/// <summary>
/// Dữ liệu phản hồi ký XML
/// </summary>
public class SignXmlResponseData
{
    /// <summary>
    /// Danh sách nội dung phản hồi
    /// </summary>
    [JsonPropertyName("responseContentList")]
    public List<XmlResponseContent>? ResponseContentList { get; set; }

    /// <summary>
    /// Base64 của chứng thư số
    /// </summary>
    [JsonPropertyName("base64Certificate")]
    public string? Base64Certificate { get; set; }
}
