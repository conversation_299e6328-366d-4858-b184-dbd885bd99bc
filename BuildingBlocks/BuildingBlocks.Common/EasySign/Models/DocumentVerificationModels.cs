using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.EasySign.Models;

/// <summary>
/// Thông tin chứng thư số trong xác thực
/// </summary>
public class CertificateVerificationInfo
{
    /// <summary>
    /// Thông tin về tổ chức phát hành chữ ký
    /// </summary>
    [JsonPropertyName("issuer")]
    public string? Issuer { get; set; }

    /// <summary>
    /// Thông tin về đối tượng sử dụng chữ ký số
    /// </summary>
    [JsonPropertyName("subjectDn")]
    public string? SubjectDn { get; set; }

    /// <summary>
    /// Ngày phát hành chứng thư số
    /// </summary>
    [JsonPropertyName("validFrom")]
    public string? ValidFrom { get; set; }

    /// <summary>
    /// Ngày hết hạn chứng thư số
    /// </summary>
    [JsonPropertyName("validTo")]
    public string? ValidTo { get; set; }

    /// <summary>
    /// Thông tin về trạng thái thu hồi của chứng thư số
    /// </summary>
    [JsonPropertyName("revocationStatus")]
    public string? RevocationStatus { get; set; }

    /// <summary>
    /// Thông tin về trạng thái thời gian ký số
    /// </summary>
    [JsonPropertyName("signTimeStatus")]
    public string? SignTimeStatus { get; set; }

    /// <summary>
    /// Thông tin về trạng thái hiệu lực của chứng thư số
    /// </summary>
    [JsonPropertyName("currentStatus")]
    public string? CurrentStatus { get; set; }

    /// <summary>
    /// Xác thực chứng thư số của tổ chức cung cấp
    /// </summary>
    [JsonPropertyName("easyCACert")]
    public bool EasyCaCert { get; set; }
}

/// <summary>
/// Thông tin xác thực chữ ký
/// </summary>
public class SignatureVerificationInfo
{
    /// <summary>
    /// Trạng thái toàn vẹn nội dung văn bản
    /// </summary>
    [JsonPropertyName("coverWholeDocument")]
    public bool CoverWholeDocument { get; set; }

    /// <summary>
    /// Số thứ tự của chữ ký trong văn bản
    /// </summary>
    [JsonPropertyName("revision")]
    public int Revision { get; set; }

    /// <summary>
    /// Tổng số chữ ký có trong văn bản
    /// </summary>
    [JsonPropertyName("totalRevision")]
    public int TotalRevision { get; set; }

    /// <summary>
    /// Thời gian ký số dữ liệu
    /// </summary>
    [JsonPropertyName("signTime")]
    public string? SignTime { get; set; }

    /// <summary>
    /// Danh sách thông tin chứng thư số
    /// </summary>
    [JsonPropertyName("certificateVfDTOs")]
    public List<CertificateVerificationInfo>? CertificateVfDTOs { get; set; }

    /// <summary>
    /// Trạng thái toàn vẹn của ký số dữ liệu
    /// </summary>
    [JsonPropertyName("integrity")]
    public bool Integrity { get; set; }
}

/// <summary>
/// Dữ liệu phản hồi xác thực PDF/XML
/// </summary>
public class DocumentVerificationResponseData
{
    /// <summary>
    /// Danh sách thông tin xác thực chữ ký
    /// </summary>
    [JsonPropertyName("signatureVfDTOs")]
    public List<SignatureVerificationInfo>? SignatureVfDTOs { get; set; }
}
