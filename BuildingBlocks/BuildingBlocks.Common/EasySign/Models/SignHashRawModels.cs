using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.EasySign.Models;

/// <summary>
/// Phần tử dữ liệu cần ký (Hash/Raw)
/// </summary>
public class SignElement
{
    /// <summary>
    /// Nội dung cần ký đã được encode base64
    /// </summary>
    [JsonPropertyName("content")]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Tên định danh nội dung
    /// </summary>
    [JsonPropertyName("key")]
    public string Key { get; set; } = string.Empty;

    /// <summary>
    /// Ngày ký
    /// </summary>
    [JsonPropertyName("signDate")]
    public string? SignDate { get; set; }

    /// <summary>
    /// Tên người ký
    /// </summary>
    [JsonPropertyName("signer")]
    public string? Signer { get; set; }
}

/// <summary>
/// Y<PERSON><PERSON> cầu ký dữ liệu Hash
/// </summary>
public class SignHashRequest
{
    /// <summary>
    /// Danh sách phần tử chứa những dữ liệu cần ký
    /// </summary>
    [JsonPropertyName("elements")]
    public List<SignElement> Elements { get; set; } = [];

    /// <summary>
    /// Tùy chọn ký số
    /// </summary>
    [JsonPropertyName("optional")]
    public SigningOptional? Optional { get; set; }

    /// <summary>
    /// Thông tin chứng thư ký số
    /// </summary>
    [JsonPropertyName("tokenInfo")]
    public TokenInfo TokenInfo { get; set; } = new();
}

/// <summary>
/// Yêu cầu ký dữ liệu Raw
/// </summary>
public class SignRawRequest
{
    /// <summary>
    /// Danh sách phần tử chứa những dữ liệu cần ký
    /// </summary>
    [JsonPropertyName("elements")]
    public List<SignElement> Elements { get; set; } = [];

    /// <summary>
    /// Tùy chọn ký số
    /// </summary>
    [JsonPropertyName("optional")]
    public SigningOptional? Optional { get; set; }

    /// <summary>
    /// Thông tin chứng thư ký số
    /// </summary>
    [JsonPropertyName("tokenInfo")]
    public TokenInfo TokenInfo { get; set; } = new();
}

/// <summary>
/// Kết quả ký dữ liệu
/// </summary>
public class SignResult
{
    /// <summary>
    /// Chữ ký số tương ứng dữ liệu, dưới dạng base64
    /// </summary>
    [JsonPropertyName("base64Signature")]
    public string? Base64Signature { get; set; }

    /// <summary>
    /// Dữ liệu đầu vào cần ký số, dạng base64
    /// </summary>
    [JsonPropertyName("inputData")]
    public string? InputData { get; set; }

    /// <summary>
    /// Định danh dữ liệu ký số
    /// </summary>
    [JsonPropertyName("key")]
    public string? Key { get; set; }
}

/// <summary>
/// Dữ liệu phản hồi ký Hash/Raw
/// </summary>
public class SignHashRawResponseData
{
    /// <summary>
    /// Danh sách kết quả ký
    /// </summary>
    [JsonPropertyName("signResult")]
    public List<SignResult>? SignResult { get; set; }

    /// <summary>
    /// Base64 của chứng thư số
    /// </summary>
    [JsonPropertyName("certificate")]
    public string? Certificate { get; set; }
}
