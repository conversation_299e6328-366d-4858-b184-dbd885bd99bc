# EasySign Digital Signature Library

Thư viện tích hợp API EasySign cho việc ký số tập trung, hỗ trợ ký số các dữ liệu như: Raw, XML, PDF, WORD, HASH thông qua Web-API.

## Tính năng

- ✅ **X<PERSON>c thực JWT**: Hỗ trợ đăng nhập và quản lý token tự động
- ✅ **Ký số đa dạng**: Hash, Raw, PDF (ẩn/hiện), XML
- ✅ **X<PERSON><PERSON> thực chữ ký**: <PERSON><PERSON><PERSON> thự<PERSON> tính hợp lệ của chữ ký số
- ✅ **Quản lý chứng thư số**: <PERSON><PERSON><PERSON> thô<PERSON>, thay đổi PIN, reset PIN
- ✅ **Mẫu chữ ký**: L<PERSON>y danh sách và ảnh mẫu chữ ký
- ✅ **Dependency Injection**: Tích hợp sẵn với ASP.NET Core DI
- ✅ **Refit Client**: Sử dụng Refit cho HTTP client type-safe
- ✅ **Utility Methods**: <PERSON><PERSON><PERSON> phươ<PERSON> thức tiện ích cho chuyển đổi dữ liệu

## Cài đặt

Thư viện đã được tích hợp sẵn trong BuildingBlocks.Common, các package dependencies:

```xml
<PackageReference Include="Refit" Version="7.0.0" />
<PackageReference Include="Refit.HttpClientFactory" Version="7.0.0" />
```

## Cấu hình

### 1. Đăng ký trong Startup.cs hoặc Program.cs

```csharp
using BuildingBlocks.Common.EasySign.Extensions;

// Môi trường Demo
services.AddEasySignDemo();

// Hoặc môi trường Production
services.AddEasySignProduction("your_username", "your_password");

// Hoặc cấu hình custom
services.AddEasySign(config =>
{
    config.BaseUrl = "https://sign.easyca.vn";
    config.Username = "your_username";
    config.Password = "your_password";
    config.TimeoutSeconds = 30;
    config.AutoRefreshToken = true;
});
```

### 2. Thông tin hệ thống

**Demo Environment:**
- URL: `http://demosign.easyca.vn:8080`
- Username: `demo_easysign`
- Password: `demo_easysign`
- Serial CTS Demo: `540110beffa622f3ca84bd2f93f0122c`
- PIN Demo: `12345678`

**Production Environment:**
- URL: `https://sign.easyca.vn`

## Sử dụng

### 1. Inject service vào Controller/Service

```csharp
using BuildingBlocks.Common.EasySign.Services;

public class DocumentController : ControllerBase
{
    private readonly EasySignService _easySignService;

    public DocumentController(EasySignService easySignService)
    {
        _easySignService = easySignService;
    }
}
```

### 2. Ký văn bản đơn giản

```csharp
public async Task<IActionResult> SignDocument()
{
    try
    {
        const string serial = "540110beffa622f3ca84bd2f93f0122c";
        const string pin = "12345678";
        const string content = "Nội dung cần ký số";

        var result = await _easySignService.SignTextAsync(
            text: content,
            key: "doc_001",
            serial: serial,
            pin: pin,
            signer: "Nguyễn Văn A"
        );

        if (EasySignService.IsSuccess(result))
        {
            var signature = result.Data?.SignResult?.FirstOrDefault()?.Base64Signature;
            return Ok(new { signature });
        }
        else
        {
            return BadRequest(EasySignService.GetErrorMessage(result));
        }
    }
    catch (Exception ex)
    {
        return StatusCode(500, ex.Message);
    }
}
```

### 3. Ký PDF với vị trí hiển thị

```csharp
public async Task<IActionResult> SignPdfVisible(IFormFile pdfFile)
{
    try
    {
        const string serial = "540110beffa622f3ca84bd2f93f0122c";
        const string pin = "12345678";

        // Chuyển đổi file upload thành base64
        using var memoryStream = new MemoryStream();
        await pdfFile.CopyToAsync(memoryStream);
        var pdfBase64 = Convert.ToBase64String(memoryStream.ToArray());

        // Lấy ảnh chữ ký
        var imageResult = await _easySignService.GetCertificateImageAsync(serial, pin);
        var signatureImage = EasySignService.IsSuccess(imageResult) ? imageResult.Data : null;

        // Ký PDF
        var result = await _easySignService.SignVisiblePdfAsync(
            pdfBase64: pdfBase64,
            documentName: pdfFile.FileName,
            serial: serial,
            pin: pin,
            x: 100,      // Tọa độ X
            y: 100,      // Tọa độ Y
            width: 200,  // Chiều rộng chữ ký
            height: 80,  // Chiều cao chữ ký
            pageNum: 1,  // Trang số 1
            imageSignature: signatureImage
        );

        if (EasySignService.IsSuccess(result))
        {
            var signedPdfBytes = Convert.FromBase64String(result.Data!);
            return File(signedPdfBytes, "application/pdf", $"signed_{pdfFile.FileName}");
        }
        else
        {
            return BadRequest(EasySignService.GetErrorMessage(result));
        }
    }
    catch (Exception ex)
    {
        return StatusCode(500, ex.Message);
    }
}
```

### 4. Ký PDF ẩn (không hiển thị chữ ký)

```csharp
public async Task<IActionResult> SignPdfInvisible(IFormFile pdfFile)
{
    try
    {
        const string serial = "540110beffa622f3ca84bd2f93f0122c";
        const string pin = "12345678";

        using var memoryStream = new MemoryStream();
        await pdfFile.CopyToAsync(memoryStream);
        var pdfBase64 = Convert.ToBase64String(memoryStream.ToArray());

        var result = await _easySignService.SignInvisiblePdfAsync(
            pdfBase64: pdfBase64,
            documentName: pdfFile.FileName,
            serial: serial,
            pin: pin
        );

        if (EasySignService.IsSuccess(result))
        {
            var signedDocument = result.Data?.ResponseContentList?.FirstOrDefault()?.SignedDocument;
            if (!string.IsNullOrEmpty(signedDocument))
            {
                var signedPdfBytes = Convert.FromBase64String(signedDocument);
                return File(signedPdfBytes, "application/pdf", $"signed_{pdfFile.FileName}");
            }
            return BadRequest("Không có dữ liệu PDF đã ký");
        }
        else
        {
            return BadRequest(EasySignService.GetErrorMessage(result));
        }
    }
    catch (Exception ex)
    {
        return StatusCode(500, ex.Message);
    }
}
```

### 5. Ký XML

```csharp
public async Task<IActionResult> SignXml(IFormFile xmlFile)
{
    try
    {
        const string serial = "540110beffa622f3ca84bd2f93f0122c";
        const string pin = "12345678";

        using var memoryStream = new MemoryStream();
        await xmlFile.CopyToAsync(memoryStream);
        var xmlBase64 = Convert.ToBase64String(memoryStream.ToArray());

        var result = await _easySignService.SignXmlAsync(
            xmlBase64: xmlBase64,
            documentName: xmlFile.FileName,
            serial: serial,
            pin: pin
        );

        if (EasySignService.IsSuccess(result))
        {
            var signedDocument = result.Data?.ResponseContentList?.FirstOrDefault()?.SignedDocument;
            if (!string.IsNullOrEmpty(signedDocument))
            {
                var signedXmlBytes = Convert.FromBase64String(signedDocument);
                return File(signedXmlBytes, "application/xml", $"signed_{xmlFile.FileName}");
            }
            return BadRequest("Không có dữ liệu XML đã ký");
        }
        else
        {
            return BadRequest(EasySignService.GetErrorMessage(result));
        }
    }
    catch (Exception ex)
    {
        return StatusCode(500, ex.Message);
    }
}
```

### 6. Xác thực chữ ký PDF

```csharp
public async Task<IActionResult> VerifyPdf(IFormFile signedPdfFile)
{
    try
    {
        using var stream = signedPdfFile.OpenReadStream();
        var result = await _easySignService.VerifyPdfAsync(stream, signedPdfFile.FileName);

        if (EasySignService.IsSuccess(result))
        {
            var signatures = result.Data?.SignatureVfDTOs;
            return Ok(new
            {
                isValid = signatures?.Any(s => s.Integrity) == true,
                signatures = signatures?.Select(s => new
                {
                    revision = $"{s.Revision}/{s.TotalRevision}",
                    signTime = s.SignTime,
                    integrity = s.Integrity,
                    coverWholeDocument = s.CoverWholeDocument,
                    certificates = s.CertificateVfDTOs?.Select(c => new
                    {
                        subject = c.SubjectDn,
                        issuer = c.Issuer,
                        validFrom = c.ValidFrom,
                        validTo = c.ValidTo,
                        status = c.CurrentStatus
                    })
                })
            });
        }
        else
        {
            return BadRequest(EasySignService.GetErrorMessage(result));
        }
    }
    catch (Exception ex)
    {
        return StatusCode(500, ex.Message);
    }
}
```

### 7. Quản lý chứng thư số

```csharp
// Lấy thông tin chứng thư số
public async Task<IActionResult> GetCertificateInfo(string serial, string pin)
{
    var result = await _easySignService.GetCertificateAsync(serial, pin);
    if (EasySignService.IsSuccess(result))
    {
        return Ok(new { certificate = result.Data });
    }
    return BadRequest(EasySignService.GetErrorMessage(result));
}

// Lấy ảnh chữ ký
public async Task<IActionResult> GetSignatureImage(string serial, string pin)
{
    var result = await _easySignService.GetCertificateImageAsync(serial, pin);
    if (EasySignService.IsSuccess(result))
    {
        var imageBytes = Convert.FromBase64String(result.Data!);
        return File(imageBytes, "image/png");
    }
    return BadRequest(EasySignService.GetErrorMessage(result));
}

// Thay đổi PIN
public async Task<IActionResult> ChangePin(string serial, string oldPin, string newPin)
{
    var result = await _easySignService.ChangeCertificatePinAsync(serial, oldPin, newPin);
    if (EasySignService.IsSuccess(result))
    {
        return Ok(new { message = "Thay đổi PIN thành công" });
    }
    return BadRequest(EasySignService.GetErrorMessage(result));
}
```

## Utility Methods

### Chuyển đổi dữ liệu

```csharp
// File to Base64
var base64 = await EasySignService.FileToBase64Async("path/to/file.pdf");

// Base64 to File
await EasySignService.SaveBase64ToFileAsync(base64String, "path/to/output.pdf");

// Byte array to Base64
var base64 = EasySignService.ToBase64String(byteArray);

// Base64 to Byte array
var bytes = EasySignService.FromBase64String(base64String);
```

### Kiểm tra kết quả

```csharp
// Kiểm tra thành công
if (EasySignService.IsSuccess(result))
{
    // Xử lý thành công
}

// Lấy thông báo lỗi
var errorMessage = EasySignService.GetErrorMessage(result);
```

## Mã trạng thái

- **Status = 0**: Thành công
- **Status = -1**: Lỗi chứng thư số (không tìm thấy, hết hạn, thu hồi)
- **Status = 9**: Lỗi serial hoặc PIN chứng thư số
- **Status = 10**: Lỗi trong quá trình ký
- **Status = 13**: PIN không đúng

## Lưu ý

1. **File XML**: Cần đặt ID cho thẻ cần ký trước khi thực hiện base64
2. **Token Management**: Thư viện tự động quản lý token và refresh khi cần
3. **Error Handling**: Luôn kiểm tra `IsSuccess()` trước khi sử dụng dữ liệu
4. **File Size**: Lưu ý giới hạn kích thước file khi upload
5. **Timeout**: Có thể cấu hình timeout cho các request

## Tham khảo

- [Tài liệu API EasySign](ChuKySoEasySign.md)
- [Postman Collection](https://drive.google.com/file/d/1VYfdWlLc3lYHYDCcbRQOwwTWo6kyQiJW/view?usp=sharing)
