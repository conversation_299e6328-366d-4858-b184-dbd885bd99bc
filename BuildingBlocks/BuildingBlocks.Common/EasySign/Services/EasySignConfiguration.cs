namespace BuildingBlocks.Common.EasySign.Services;

/// <summary>
/// <PERSON><PERSON><PERSON> hình kết nối EasySign
/// </summary>
public class EasySignConfiguration
{
    /// <summary>
    /// URL API EasySign
    /// <PERSON><PERSON> thống thật: https://sign.easyca.vn
    /// Hệ thống demo: http://demosign.easyca.vn:8080
    /// </summary>
    public string BaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// Timeout cho các request (giây)
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Tài khoản đăng nhập
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// Mật khẩu đăng nhập
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// Tự động refresh token khi hết hạn
    /// </summary>
    public bool AutoRefreshToken { get; set; } = true;
}
