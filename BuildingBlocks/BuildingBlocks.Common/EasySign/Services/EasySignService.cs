using BuildingBlocks.Common.EasySign.Interfaces;
using BuildingBlocks.Common.EasySign.Models;
using BuildingBlocks.Common.Interface;
using Refit;
using System.Text;

namespace BuildingBlocks.Common.EasySign.Services;

/// <summary>
/// Service wrapper cho EasySign API với các tính năng bổ sung
/// </summary>
public class EasySignService(IEasySignApi api, EasySignConfiguration configuration) : ISignService
{
    private readonly IEasySignApi _api = api ?? throw new ArgumentNullException(nameof(api));
    private readonly EasySignConfiguration _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    private string? _cachedToken;
    private DateTime _tokenExpiration;

    #region Authentication

    /// <summary>
    /// Đăng nhập và lấy token xác thực
    /// </summary>
    /// <param name="username">Tên đăng nhập (tùy chọn, nếu không truyền sẽ dùng từ configuration)</param>
    /// <param name="password"><PERSON><PERSON><PERSON> khẩu (tù<PERSON> chọn, nếu không truyền sẽ dùng từ configuration)</param>
    /// <param name="rememberMe">Ghi nhớ đăng nhập</param>
    /// <returns>Token xác thực</returns>
    public async Task<string> AuthenticateAsync(string? username = null, string? password = null, bool rememberMe = false)
    {
        var request = new AuthenticationRequest
        {
            Username = username ?? _configuration.Username ?? throw new ArgumentException("Username is required"),
            Password = password ?? _configuration.Password ?? throw new ArgumentException("Password is required"),
            RememberMe = rememberMe
        };

        var response = await _api.AuthenticateAsync(request);

        _cachedToken = response.IdToken;

        // JWT tokens thường có thời hạn 30 ngày, tính toán thời gian hết hạn
        _tokenExpiration = DateTime.UtcNow.AddDays(30);

        return response.IdToken;
    }

    /// <summary>
    /// Lấy token hiện tại hoặc refresh nếu cần
    /// </summary>
    /// <returns>Token hợp lệ</returns>
    public async Task<string> GetValidTokenAsync()
    {
        if (string.IsNullOrEmpty(_cachedToken) || DateTime.UtcNow >= _tokenExpiration)
        {
            if (_configuration.AutoRefreshToken)
            {
                await AuthenticateAsync();
            }
            else
            {
                throw new InvalidOperationException("Token hết hạn! Vui lòng đăng nhập lại.");
            }
        }

        return $"Bearer {_cachedToken}";
    }

    #endregion

    #region Digital Signature - Hash/Raw

    /// <summary>
    /// Ký dữ liệu hash
    /// </summary>
    /// <param name="request">Yêu cầu ký hash</param>
    /// <returns>Kết quả ký hash</returns>
    public async Task<EasySignResponse<SignHashRawResponseData>> SignHashAsync(SignHashRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.SignHashAsync(request, token);
    }

    /// <summary>
    /// Ký dữ liệu hash đơn giản
    /// </summary>
    /// <param name="hashContent">Nội dung hash đã encode base64</param>
    /// <param name="key">Tên định danh nội dung</param>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="pin">PIN chứng thư số</param>
    /// <param name="hashAlgorithm">Thuật toán hash (SHA1, SHA256, SHA512)</param>
    /// <param name="signer">Tên người ký</param>
    /// <param name="otpCode">Mã OTP (nếu có)</param>
    /// <returns>Kết quả ký hash</returns>
    public async Task<EasySignResponse<SignHashRawResponseData>> SignHashAsync(
        string hashContent,
        string key,
        string serial,
        string pin,
        string hashAlgorithm = "SHA256",
        string? signer = null,
        string? otpCode = null)
    {
        var request = new SignHashRequest
        {
            Elements =
            [
                new SignElement
                {
                    Content = hashContent,
                    Key = key,
                    Signer = signer
                }
            ],
            Optional = new SigningOptional
            {
                HashAlgorithm = hashAlgorithm,
                OtpCode = otpCode,
                ReturnInputData = true
            },
            TokenInfo = new TokenInfo
            {
                Serial = serial,
                Pin = pin
            }
        };

        return await SignHashAsync(request);
    }

    /// <summary>
    /// Ký dữ liệu raw
    /// </summary>
    /// <param name="request">Yêu cầu ký raw</param>
    /// <returns>Kết quả ký raw</returns>
    public async Task<EasySignResponse<SignHashRawResponseData>> SignRawAsync(SignRawRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.SignRawAsync(request, token);
    }

    /// <summary>
    /// Ký dữ liệu raw đơn giản
    /// </summary>
    /// <param name="rawContent">Nội dung raw đã encode base64</param>
    /// <param name="key">Tên định danh nội dung</param>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="pin">PIN chứng thư số</param>
    /// <param name="hashAlgorithm">Thuật toán hash (SHA1, SHA256, SHA512)</param>
    /// <param name="signer">Tên người ký</param>
    /// <param name="otpCode">Mã OTP (nếu có)</param>
    /// <returns>Kết quả ký raw</returns>
    public async Task<EasySignResponse<SignHashRawResponseData>> SignRawAsync(
        string rawContent,
        string key,
        string serial,
        string pin,
        string hashAlgorithm = "SHA256",
        string? signer = null,
        string? otpCode = null)
    {
        var request = new SignRawRequest
        {
            Elements =
            [
                new SignElement
                {
                    Content = rawContent,
                    Key = key,
                    Signer = signer
                }
            ],
            Optional = new SigningOptional
            {
                HashAlgorithm = hashAlgorithm,
                OtpCode = otpCode,
                ReturnInputData = true
            },
            TokenInfo = new TokenInfo
            {
                Serial = serial,
                Pin = pin
            }
        };

        return await SignRawAsync(request);
    }

    /// <summary>
    /// Ký chuỗi text đơn giản (tự động chuyển đổi sang base64)
    /// </summary>
    /// <param name="text">Nội dung text cần ký</param>
    /// <param name="key">Tên định danh nội dung</param>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="pin">PIN chứng thư số</param>
    /// <param name="hashAlgorithm">Thuật toán hash (SHA1, SHA256, SHA512)</param>
    /// <param name="signer">Tên người ký</param>
    /// <param name="otpCode">Mã OTP (nếu có)</param>
    /// <returns>Kết quả ký text</returns>
    public async Task<EasySignResponse<SignHashRawResponseData>> SignTextAsync(
        string text,
        string key,
        string serial,
        string pin,
        string hashAlgorithm = "SHA256",
        string? signer = null,
        string? otpCode = null)
    {
        var base64Content = Convert.ToBase64String(Encoding.UTF8.GetBytes(text));
        return await SignRawAsync(base64Content, key, serial, pin, hashAlgorithm, signer, otpCode);
    }

    #endregion

    #region Digital Signature - PDF

    /// <summary>
    /// Ký PDF ẩn
    /// </summary>
    /// <param name="request">Yêu cầu ký PDF ẩn</param>
    /// <returns>Kết quả ký PDF ẩn</returns>
    public async Task<EasySignResponse<SignPdfResponseData>> SignInvisiblePdfAsync(SignInvisiblePdfRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.SignInvisiblePdfAsync(request, token);
    }

    /// <summary>
    /// Ký PDF ẩn đơn giản
    /// </summary>
    /// <param name="pdfBase64">Nội dung PDF đã encode base64</param>
    /// <param name="documentName">Tên tài liệu</param>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="pin">PIN chứng thư số</param>
    /// <param name="otpCode">Mã OTP (nếu có)</param>
    /// <returns>Kết quả ký PDF ẩn</returns>
    public async Task<EasySignResponse<SignPdfResponseData>> SignInvisiblePdfAsync(
        string pdfBase64,
        string documentName,
        string serial,
        string pin,
        string? otpCode = null)
    {
        var request = new SignInvisiblePdfRequest
        {
            SigningRequestContents = new List<PdfSigningRequestContent>
            {
                new PdfSigningRequestContent
                {
                    Data = pdfBase64,
                    DocumentName = documentName
                }
            },
            Optional = new SigningOptional
            {
                OtpCode = otpCode
            },
            TokenInfo = new TokenInfo
            {
                Serial = serial,
                Pin = pin
            }
        };

        return await SignInvisiblePdfAsync(request);
    }

    /// <summary>
    /// Ký PDF từ file path ẩn
    /// </summary>
    /// <param name="pdfFilePath">Đường dẫn file PDF</param>
    /// <param name="documentName">Tên tài liệu</param>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="pin">PIN chứng thư số</param>
    /// <param name="otpCode">Mã OTP (nếu có)</param>
    /// <returns>Kết quả ký PDF ẩn</returns>
    public async Task<EasySignResponse<SignPdfResponseData>> SignInvisiblePdfFromFileAsync(
        string pdfFilePath,
        string documentName,
        string serial,
        string pin,
        string? otpCode = null)
    {
        var pdfBytes = await File.ReadAllBytesAsync(pdfFilePath);
        var pdfBase64 = Convert.ToBase64String(pdfBytes);
        return await SignInvisiblePdfAsync(pdfBase64, documentName, serial, pin, otpCode);
    }

    /// <summary>
    /// Ký PDF hiện vị trí
    /// </summary>
    /// <param name="request">Yêu cầu ký PDF hiện</param>
    /// <returns>Kết quả ký PDF hiện</returns>
    public async Task<EasySignResponse<string>> SignVisiblePdfAsync(SignVisiblePdfRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.SignVisiblePdfAsync(request, token);
    }

    /// <summary>
    /// Ký PDF hiện vị trí đơn giản
    /// </summary>
    /// <param name="pdfBase64">Nội dung PDF đã encode base64</param>
    /// <param name="documentName">Tên tài liệu</param>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="pin">PIN chứng thư số</param>
    /// <param name="x">Tọa độ X</param>
    /// <param name="y">Tọa độ Y</param>
    /// <param name="width">Chiều rộng chữ ký</param>
    /// <param name="height">Chiều cao chữ ký</param>
    /// <param name="pageNum">Số trang</param>
    /// <param name="imageSignature">Ảnh chữ ký base64 (tùy chọn)</param>
    /// <param name="otpCode">Mã OTP (nếu có)</param>
    /// <returns>Kết quả ký PDF hiện</returns>
    public async Task<EasySignResponse<string>> SignVisiblePdfAsync(
    string pdfBase64,
    string documentName,
    string serial,
    string pin,
    int x,
    int y,
    int width = 150,
    int height = 50,
    int pageNum = 1,
    string? imageSignature = null,
    string? otpCode = null)
    {
        var request = new SignVisiblePdfRequest
        {
            SigningRequestContents = new List<PdfSigningRequestContent>
            {
                new PdfSigningRequestContent
                {
                    Data = pdfBase64,
                    DocumentName = documentName,
                    Location = new PdfSignLocation
                    {
                        VisibleX = x,
                        VisibleY = y,
                        VisibleWidth = width,
                        VisibleHeight = height
                    },
                    ExtraInfo = new PdfExtraInfo
                    {
                        PageNum = pageNum
                    },
                    ImageSignature = imageSignature
                }
            },
            Optional = new SigningOptional
            {
                OtpCode = otpCode
            },
            TokenInfo = new TokenInfo
            {
                Serial = serial,
                Pin = pin
            }
        };

        return await SignVisiblePdfAsync(request);
    }

    #endregion

    #region Digital Signature - XML

    /// <summary>
    /// Ký XML
    /// </summary>
    /// <param name="request">Yêu cầu ký XML</param>
    /// <returns>Kết quả ký XML</returns>
    public async Task<EasySignResponse<SignXmlResponseData>> SignXmlAsync(SignXmlRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.SignXmlAsync(request, token);
    }

    /// <summary>
    /// Ký XML đơn giản
    /// </summary>
    /// <param name="xmlBase64">Nội dung XML đã encode base64</param>
    /// <param name="documentName">Tên tài liệu</param>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="pin">PIN chứng thư số</param>
    /// <param name="otpCode">Mã OTP (nếu có)</param>
    /// <returns>Kết quả ký XML</returns>
    public async Task<EasySignResponse<SignXmlResponseData>> SignXmlAsync(
        string xmlBase64,
        string documentName,
        string serial,
        string pin,
        string? otpCode = null)
    {
        var request = new SignXmlRequest
        {
            SigningRequestContents = new List<XmlSigningRequestContent>
            {
                new XmlSigningRequestContent
                {
                    Data = xmlBase64,
                    DocumentName = documentName
                }
            },
            Optional = new XmlSigningOptional
            {
                OtpCode = otpCode
            },
            TokenInfo = new TokenInfo
            {
                Serial = serial,
                Pin = pin
            }
        };

        return await SignXmlAsync(request);
    }

    /// <summary>
    /// Ký XML từ file path
    /// </summary>
    /// <param name="xmlFilePath">Đường dẫn file XML</param>
    /// <param name="documentName">Tên tài liệu</param>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="pin">PIN chứng thư số</param>
    /// <param name="otpCode">Mã OTP (nếu có)</param>
    /// <returns>Kết quả ký XML</returns>
    public async Task<EasySignResponse<SignXmlResponseData>> SignXmlFromFileAsync(
        string xmlFilePath,
        string documentName,
        string serial,
        string pin,
        string? otpCode = null)
    {
        var xmlBytes = await File.ReadAllBytesAsync(xmlFilePath);
        var xmlBase64 = Convert.ToBase64String(xmlBytes);
        return await SignXmlAsync(xmlBase64, documentName, serial, pin, otpCode);
    }

    #endregion

    #region Verification

    /// <summary>
    /// Xác thực chữ ký raw
    /// </summary>
    /// <param name="request">Yêu cầu xác thực raw</param>
    /// <returns>Kết quả xác thực raw</returns>
    public async Task<EasySignResponse<VerificationResponseData>> VerifyRawAsync(VerifyRawRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.VerifyRawAsync(request, token);
    }

    /// <summary>
    /// Xác thực chữ ký hash
    /// </summary>
    /// <param name="request">Yêu cầu xác thực hash</param>
    /// <returns>Kết quả xác thực hash</returns>
    public async Task<EasySignResponse<VerificationResponseData>> VerifyHashAsync(VerifyHashRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.VerifyHashAsync(request, token);
    }

    /// <summary>
    /// Xác thực PDF
    /// </summary>
    /// <param name="pdfStream">Stream PDF cần xác thực</param>
    /// <param name="fileName">Tên file</param>
    /// <returns>Kết quả xác thực PDF</returns>
    public async Task<EasySignResponse<DocumentVerificationResponseData>> VerifyPdfAsync(Stream pdfStream, string fileName)
    {
        var token = await GetValidTokenAsync();
        var streamPart = new StreamPart(pdfStream, fileName, "application/pdf");
        return await _api.VerifyPdfAsync(streamPart, token);
    }

    /// <summary>
    /// Xác thực PDF từ file path
    /// </summary>
    /// <param name="pdfFilePath">Đường dẫn file PDF</param>
    /// <returns>Kết quả xác thực PDF</returns>
    public async Task<EasySignResponse<DocumentVerificationResponseData>> VerifyPdfFromFileAsync(string pdfFilePath)
    {
        using var fileStream = File.OpenRead(pdfFilePath);
        var fileName = Path.GetFileName(pdfFilePath);
        return await VerifyPdfAsync(fileStream, fileName);
    }

    /// <summary>
    /// Xác thực XML
    /// </summary>
    /// <param name="xmlStream">Stream XML cần xác thực</param>
    /// <param name="fileName">Tên file</param>
    /// <returns>Kết quả xác thực XML</returns>
    public async Task<EasySignResponse<DocumentVerificationResponseData>> VerifyXmlAsync(Stream xmlStream, string fileName)
    {
        var token = await GetValidTokenAsync();
        var streamPart = new StreamPart(xmlStream, fileName, "application/xml");
        return await _api.VerifyXmlAsync(streamPart, token);
    }

    /// <summary>
    /// Xác thực XML từ file path
    /// </summary>
    /// <param name="xmlFilePath">Đường dẫn file XML</param>
    /// <returns>Kết quả xác thực XML</returns>
    public async Task<EasySignResponse<DocumentVerificationResponseData>> VerifyXmlFromFileAsync(string xmlFilePath)
    {
        using var fileStream = File.OpenRead(xmlFilePath);
        var fileName = Path.GetFileName(xmlFilePath);
        return await VerifyXmlAsync(fileStream, fileName);
    }

    #endregion

    #region Certificate Management

    /// <summary>
    /// Lấy ảnh chữ ký số
    /// </summary>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="pin">PIN chứng thư số</param>
    /// <param name="position">Thông tin chức vụ (tùy chọn)</param>
    /// <param name="timeSign">Thông tin thời gian ký (tùy chọn)</param>
    /// <returns>Base64 ảnh chữ ký</returns>
    public async Task<EasySignResponse<string>> GetCertificateImageAsync(string serial, string pin, string? position = null, string? timeSign = null)
    {
        var token = await GetValidTokenAsync();
        return await _api.GetCertificateImageAsync(serial, pin, position, timeSign, token);
    }

    /// <summary>
    /// Lấy ảnh chữ ký theo template
    /// </summary>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="pin">PIN chứng thư số</param>
    /// <param name="templateId">ID template (tùy chọn)</param>
    /// <returns>Base64 ảnh chữ ký</returns>
    public async Task<EasySignResponse<string>> GetCertificateImageByTemplateIdAsync(string serial, string pin, int? templateId = null)
    {
        var token = await GetValidTokenAsync();
        return await _api.GetCertificateImageByTemplateIdAsync(serial, pin, templateId, token);
    }

    /// <summary>
    /// Thay đổi PIN chứng thư số
    /// </summary>
    /// <param name="request">Yêu cầu thay đổi PIN</param>
    /// <returns>Kết quả thay đổi PIN</returns>
    public async Task<EasySignResponse<object>> ChangeCertificatePinAsync(ChangeCertPinRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.ChangeCertificatePinAsync(request, token);
    }

    /// <summary>
    /// Thay đổi PIN chứng thư số đơn giản
    /// </summary>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="oldPin">PIN cũ</param>
    /// <param name="newPin">PIN mới</param>
    /// <param name="otpCode">Mã OTP (nếu có)</param>
    /// <returns>Kết quả thay đổi PIN</returns>
    public async Task<EasySignResponse<object>> ChangeCertificatePinAsync(string serial, string oldPin, string newPin, string? otpCode = null)
    {
        var request = new ChangeCertPinRequest
        {
            Serial = serial,
            OldPin = oldPin,
            NewPin = newPin,
            OtpCode = otpCode
        };

        return await ChangeCertificatePinAsync(request);
    }

    /// <summary>
    /// Reset PIN chứng thư số
    /// </summary>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="masterKey">Master key</param>
    /// <returns>PIN mới</returns>
    public async Task<EasySignResponse<string>> ResetCertificatePinAsync(string serial, string masterKey)
    {
        var token = await GetValidTokenAsync();
        return await _api.ResetCertificatePinAsync(serial, masterKey, token);
    }

    /// <summary>
    /// Lấy thông tin chứng thư số
    /// </summary>
    /// <param name="serial">Serial chứng thư số</param>
    /// <param name="pin">PIN chứng thư số</param>
    /// <returns>Base64 chứng thư số</returns>
    public async Task<EasySignResponse<string>> GetCertificateAsync(string serial, string pin)
    {
        var token = await GetValidTokenAsync();
        return await _api.GetCertificateAsync(serial, pin, token);
    }

    /// <summary>
    /// Lấy danh sách mẫu chữ ký
    /// </summary>
    /// <param name="username">Username</param>
    /// <returns>Danh sách mẫu chữ ký</returns>
    public async Task<EasySignResponse<List<SignatureTemplate>>> GetSignatureTemplatesByUsernameAsync(string username)
    {
        var token = await GetValidTokenAsync();
        return await _api.GetSignatureTemplatesByUsernameAsync(username, token);
    }

    #endregion

    #region Utility Methods

    /// <summary>
    /// Kiểm tra trạng thái phản hồi có thành công không
    /// </summary>
    /// <param name="response">Phản hồi từ API</param>
    /// <returns>True nếu thành công</returns>
    public static bool IsSuccess<T>(EasySignResponse<T> response)
    {
        return response.Status == 0;
    }

    /// <summary>
    /// Lấy thông tin lỗi từ phản hồi
    /// </summary>
    /// <param name="response">Phản hồi từ API</param>
    /// <returns>Chuỗi mô tả lỗi</returns>
    public static string GetErrorMessage<T>(EasySignResponse<T> response)
    {
        return response.Status switch
        {
            0 => "Thành công",
            -1 => response.Msg ?? "Lỗi chứng thư số (không tìm thấy, hết hạn, thu hồi)",
            9 => "Lỗi serial hoặc PIN chứng thư số",
            10 => response.Msg ?? "Lỗi trong quá trình ký",
            13 => "PIN không đúng",
            _ => response.Msg ?? $"Lỗi không xác định (Status: {response.Status})"
        };
    }

    /// <summary>
    /// Chuyển đổi byte array sang base64 string
    /// </summary>
    /// <param name="bytes">Byte array</param>
    /// <returns>Base64 string</returns>
    public static string ToBase64String(byte[] bytes)
    {
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// Chuyển đổi base64 string sang byte array
    /// </summary>
    /// <param name="base64">Base64 string</param>
    /// <returns>Byte array</returns>
    public static byte[] FromBase64String(string base64)
    {
        return Convert.FromBase64String(base64);
    }

    /// <summary>
    /// Đọc file và chuyển đổi sang base64
    /// </summary>
    /// <param name="filePath">Đường dẫn file</param>
    /// <returns>Base64 string</returns>
    public static async Task<string> FileToBase64Async(string filePath)
    {
        var bytes = await File.ReadAllBytesAsync(filePath);
        return ToBase64String(bytes);
    }

    /// <summary>
    /// Lưu base64 string thành file
    /// </summary>
    /// <param name="base64">Base64 string</param>
    /// <param name="filePath">Đường dẫn file đích</param>
    public static async Task SaveBase64ToFileAsync(string base64, string filePath)
    {
        var bytes = FromBase64String(base64);
        await File.WriteAllBytesAsync(filePath, bytes);
    }

    #endregion
}
