using BuildingBlocks.Common.EasySign.Models;
using Refit;

namespace BuildingBlocks.Common.EasySign.Interfaces;

/// <summary>
/// Interface API cho hệ thống EasySign - Ký số tập trung
/// Hỗ trợ ký số các dữ liệu như: Raw, XML, PDF, WORD, HASH thông qua Web-API
/// </summary>
public interface IEasySignApi
{
    #region Authentication

    /// <summary>
    /// API đăng nhập hệ thống EasySign
    /// Lấy ra token để có thể truy cập được vào các API nghiệp vụ khác
    /// </summary>
    /// <param name="request">Thông tin đăng nhập</param>
    /// <returns>Token xác thực JWT</returns>
    [Post("/api/authenticate")]
    Task<AuthenticationResponse> AuthenticateAsync([Body] AuthenticationRequest request);

    #endregion

    #region Digital Signature - Hash/Raw

    /// <summary>
    /// API ký dữ liệu hash
    /// Ký số với dữ liệu đầu vào là chuỗi hash, trong một lần request có thể ký nhiều dữ liệu
    /// </summary>
    /// <param name="request">Yêu cầu ký hash</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Kết quả ký hash</returns>
    [Post("/api/sign/hash")]
    Task<EasySignResponse<SignHashRawResponseData>> SignHashAsync(
        [Body] SignHashRequest request,
        [Header("Authorization")] string authorization);

    /// <summary>
    /// API ký số dữ liệu raw
    /// Ký số với dữ liệu đầu vào là chuỗi dữ liệu bất kỳ, trong một lần request có thể ký nhiều dữ liệu
    /// </summary>
    /// <param name="request">Yêu cầu ký raw</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Kết quả ký raw</returns>
    [Post("/api/sign/raw")]
    Task<EasySignResponse<SignHashRawResponseData>> SignRawAsync(
        [Body] SignRawRequest request,
        [Header("Authorization")] string authorization);

    #endregion

    #region Digital Signature - PDF

    /// <summary>
    /// API ký số dữ liệu PDF (ký ẩn)
    /// Ký số với dữ liệu PDF với tính năng ký ẩn – không hiển thị hình ký số trên tệp
    /// </summary>
    /// <param name="request">Yêu cầu ký PDF ẩn</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Kết quả ký PDF ẩn</returns>
    [Post("/api/sign/invisiblePdf")]
    Task<EasySignResponse<SignPdfResponseData>> SignInvisiblePdfAsync(
        [Body] SignInvisiblePdfRequest request,
        [Header("Authorization")] string authorization);

    /// <summary>
    /// API ký số dữ liệu PDF (ký hiện vị trí)
    /// Ký số với dữ liệu PDF với tính năng ký hiện vị trí ảnh chữ ký với vị trí do mình tùy chọn
    /// </summary>
    /// <param name="request">Yêu cầu ký PDF hiện</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Kết quả ký PDF hiện</returns>
    [Post("/api/sign/pdf")]
    Task<EasySignResponse<string>> SignVisiblePdfAsync(
        [Body] SignVisiblePdfRequest request,
        [Header("Authorization")] string authorization);

    #endregion

    #region Digital Signature - XML

    /// <summary>
    /// API ký số dữ liệu XML
    /// Ký số với dữ liệu XML với tính năng ký file XML và xác thực otpCode
    /// Lưu ý: File XML cần được đặt ID cho thẻ cần ký trước khi thực hiện base64
    /// </summary>
    /// <param name="request">Yêu cầu ký XML</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Kết quả ký XML</returns>
    [Post("/api/sign/xml")]
    Task<EasySignResponse<SignXmlResponseData>> SignXmlAsync(
        [Body] SignXmlRequest request,
        [Header("Authorization")] string authorization);

    #endregion

    #region Verification

    /// <summary>
    /// API xác thực chữ ký số với dữ liệu raw
    /// Xác thực tính hợp lệ của chữ ký số với dữ liệu raw ban đầu
    /// </summary>
    /// <param name="request">Yêu cầu xác thực raw</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Kết quả xác thực raw</returns>
    [Post("/api/verification/raw")]
    Task<EasySignResponse<VerificationResponseData>> VerifyRawAsync(
        [Body] VerifyRawRequest request,
        [Header("Authorization")] string authorization);

    /// <summary>
    /// API xác thực chữ ký số với dữ liệu hash
    /// Xác thực tính hợp lệ của chữ ký số với dữ liệu hash ban đầu
    /// </summary>
    /// <param name="request">Yêu cầu xác thực hash</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Kết quả xác thực hash</returns>
    [Post("/api/verification/hash")]
    Task<EasySignResponse<VerificationResponseData>> VerifyHashAsync(
        [Body] VerifyHashRequest request,
        [Header("Authorization")] string authorization);

    /// <summary>
    /// API verify PDF (Sau khi ký thành công)
    /// Xác minh chữ ký PDF
    /// </summary>
    /// <param name="file">File PDF đã ký cần xác thực</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Kết quả xác thực PDF</returns>
    [Multipart]
    [Post("/api/verification/pdf")]
    Task<EasySignResponse<DocumentVerificationResponseData>> VerifyPdfAsync(
        [AliasAs("file")] StreamPart file,
        [Header("Authorization")] string authorization);

    /// <summary>
    /// API Xác thực chữ ký số File XML đã ký
    /// Xác thực tính hợp lệ chữ ký số đã ký lên file XML
    /// </summary>
    /// <param name="file">File XML đã ký cần xác thực</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Kết quả xác thực XML</returns>
    [Multipart]
    [Post("/api/verification/xml")]
    Task<EasySignResponse<DocumentVerificationResponseData>> VerifyXmlAsync(
        [AliasAs("file")] StreamPart file,
        [Header("Authorization")] string authorization);

    #endregion

    #region Certificate Management

    /// <summary>
    /// API Lấy ảnh chữ ký số
    /// Lấy hình ảnh chữ ký số
    /// </summary>
    /// <param name="serial">Thông tin Serial chứng thư số (CTS)</param>
    /// <param name="pin">Mã PIN truy cập CTS</param>
    /// <param name="position">Thông tin truyền động chức vụ (tùy chọn)</param>
    /// <param name="timeSign">Thông tin truyền động thời gian ký (tùy chọn)</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Dữ liệu base64 hình ảnh chữ ký số</returns>
    [Get("/api/certificate/getImage")]
    Task<EasySignResponse<string>> GetCertificateImageAsync(
        [Query] string serial,
        [Query] string pin,
        [Query] string? position = null,
        [Query] string? timeSign = null,
        [Header("Authorization")] string authorization = "");

    /// <summary>
    /// API Lấy ảnh mẫu chữ ký theo mã mẫu chữ ký
    /// Lấy ảnh mẫu chữ ký theo templateId
    /// </summary>
    /// <param name="serial">Serial của chứng thư số</param>
    /// <param name="pin">Mã PIN của chứng thư số</param>
    /// <param name="templateId">Mã ảnh mẫu chữ ký (tùy chọn)</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Base64 của ảnh mẫu chữ ký</returns>
    [Get("/api/certificate/getImageByTemplateId")]
    Task<EasySignResponse<string>> GetCertificateImageByTemplateIdAsync(
        [Query] string serial,
        [Query] string pin,
        [Query] int? templateId = null,
        [Header("Authorization")] string authorization = "");

    /// <summary>
    /// API Change Certificate Pin
    /// Thay đổi PIN certificate (serial) cho người dùng
    /// </summary>
    /// <param name="request">Yêu cầu thay đổi PIN</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Kết quả thay đổi PIN</returns>
    [Post("/api/certificate/changeCertPIN")]
    Task<EasySignResponse<object>> ChangeCertificatePinAsync(
        [Body] ChangeCertPinRequest request,
        [Header("Authorization")] string authorization);

    /// <summary>
    /// API reset mã PIN chứng thư số
    /// Reset mã PIN chứng thư số không yêu cầu mã PIN cũ
    /// </summary>
    /// <param name="serial">Serial của chứng thư số cần reset PIN</param>
    /// <param name="masterKey">Key bí mật được cấp riêng</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Mã PIN mới cho chứng thư số</returns>
    [Post("/api/certificate/resetHsmCertPin")]
    Task<EasySignResponse<string>> ResetCertificatePinAsync(
        [Query] string serial,
        [Query] string masterKey,
        [Header("Authorization")] string authorization);

    /// <summary>
    /// API lấy thông tin chứng thư số bằng serial
    /// Lấy thông tin chứng thư số bằng serial và PIN
    /// </summary>
    /// <param name="serial">Serial của chứng thư số cần lấy</param>
    /// <param name="pin">Mã PIN của chứng thư số cần lấy thông tin</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Base64 của chứng thư số được tìm thấy</returns>
    [Get("/api/certificate/get")]
    Task<EasySignResponse<string>> GetCertificateAsync(
        [Query] string serial,
        [Query] string pin,
        [Header("Authorization")] string authorization);

    #endregion

    #region Signature Templates

    /// <summary>
    /// API Lấy danh sách mẫu chữ ký của username
    /// Lấy danh sách mẫu chữ ký theo username truyền vào
    /// </summary>
    /// <param name="username">Tên username của người cần lấy danh sách mẫu chữ ký</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Danh sách mẫu chữ ký</returns>
    [Get("/api/signature-templates/getByUserName")]
    Task<EasySignResponse<List<SignatureTemplate>>> GetSignatureTemplatesByUsernameAsync(
        [Query] string username,
        [Header("Authorization")] string authorization);

    #endregion
}
