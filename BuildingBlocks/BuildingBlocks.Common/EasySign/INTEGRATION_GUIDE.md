# Tích hợp EasySign vào GoTRUST EMR

Hướng dẫn tích hợp thư viện EasySign Digital Signature vào hệ thống GoTRUST EMR để ký số hồ sơ bệnh án điện tử.

## 1. <PERSON><PERSON><PERSON> hình dự án

### Thêm vào Program.cs hoặc DependencyInjection.cs

### Cấu hình appsettings.json

```json
{
  "EasySign": {
    "Username": "demo_easysign",
    "Password": "demo_easysign",
    "BaseUrl": "http://demosign.easyca.vn:8080",
    "TimeoutSeconds": 30,
    "DemoCredentials": {
      "Serial": "540110beffa622f3ca84bd2f93f0122c",
      "Pin": "12345678"
    }
  }
}
```

## 2. Tích hợp vào Domain/Application Layer

### Tạo Digital Signature Domain Service

```csharp
// Services/GoTRUST.EMR.Domain/Services/IDigitalSignatureService.cs
using BuildingBlocks.Common.EasySign.Models;

namespace GoTRUST.EMR.Domain.Services;

public interface IDigitalSignatureService
{
    Task<string> SignMedicalRecordAsync(
        byte[] pdfData, 
        string patientId, 
        string doctorSerial, 
        string doctorPin, 
        SignaturePosition? position = null);
    
    Task<string> SignPrescriptionAsync(
        string prescriptionXml, 
        string patientId, 
        string doctorSerial, 
        string doctorPin);
    
    Task<DigitalSignatureVerificationResult> VerifyDocumentAsync(Stream documentStream, string fileName);
    
    Task<string> GetDoctorSignatureImageAsync(string doctorSerial, string doctorPin);
}

public class DigitalSignatureVerificationResult
{
    public bool IsValid { get; set; }
    public List<SignatureInfo> Signatures { get; set; } = new();
    public DateTime VerificationTime { get; set; }
}

public class SignatureInfo
{
    public string DoctorName { get; set; } = string.Empty;
    public string SignTime { get; set; } = string.Empty;
    public bool IsIntact { get; set; }
    public string CertificateStatus { get; set; } = string.Empty;
}
```

### Implementation trong Infrastructure Layer

```csharp
// Services/GoTRUST.EMR.Infrastructure/Services/DigitalSignatureService.cs
using BuildingBlocks.Common.EasySign.Services;
using GoTRUST.EMR.Domain.Services;

namespace GoTRUST.EMR.Infrastructure.Services;

public class DigitalSignatureService : IDigitalSignatureService
{
    private readonly EasySignService _easySignService;
    private readonly ILogger<DigitalSignatureService> _logger;

    public DigitalSignatureService(
        EasySignService easySignService,
        ILogger<DigitalSignatureService> logger)
    {
        _easySignService = easySignService;
        _logger = logger;
    }

    public async Task<string> SignMedicalRecordAsync(
        byte[] pdfData, 
        string patientId, 
        string doctorSerial, 
        string doctorPin, 
        SignaturePosition? position = null)
    {
        try
        {
            var pdfBase64 = Convert.ToBase64String(pdfData);
            
            var result = await _easySignService.SignVisiblePdfAsync(
                pdfBase64: pdfBase64,
                documentName: $"MedicalRecord_{patientId}_{DateTime.Now:yyyyMMdd}.pdf",
                serial: doctorSerial,
                pin: doctorPin,
                x: position?.X ?? 400,
                y: position?.Y ?? 100,
                width: position?.Width ?? 200,
                height: position?.Height ?? 80,
                pageNum: position?.PageNum ?? 1
            );

            if (EasySignService.IsSuccess(result))
            {
                return result.Data!;
            }
            
            throw new InvalidOperationException($"Ký PDF thất bại: {EasySignService.GetErrorMessage(result)}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi ký hồ sơ bệnh án {PatientId}", patientId);
            throw;
        }
    }

    public async Task<string> SignPrescriptionAsync(
        string prescriptionXml, 
        string patientId, 
        string doctorSerial, 
        string doctorPin)
    {
        try
        {
            var xmlBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(prescriptionXml));
            
            var result = await _easySignService.SignXmlAsync(
                xmlBase64: xmlBase64,
                documentName: $"Prescription_{patientId}_{DateTime.Now:yyyyMMdd}.xml",
                serial: doctorSerial,
                pin: doctorPin
            );

            if (EasySignService.IsSuccess(result))
            {
                var signedDocument = result.Data?.ResponseContentList?.FirstOrDefault()?.SignedDocument;
                if (!string.IsNullOrEmpty(signedDocument))
                {
                    return signedDocument;
                }
            }
            
            throw new InvalidOperationException($"Ký XML thất bại: {EasySignService.GetErrorMessage(result)}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi ký đơn thuốc {PatientId}", patientId);
            throw;
        }
    }

    public async Task<DigitalSignatureVerificationResult> VerifyDocumentAsync(Stream documentStream, string fileName)
    {
        try
        {
            var result = await _easySignService.VerifyPdfAsync(documentStream, fileName);
            
            if (EasySignService.IsSuccess(result))
            {
                var signatures = result.Data?.SignatureVfDTOs ?? new List<SignatureVerificationInfo>();
                
                return new DigitalSignatureVerificationResult
                {
                    IsValid = signatures.Any(s => s.Integrity),
                    Signatures = signatures.Select(s => new SignatureInfo
                    {
                        DoctorName = ExtractDoctorName(s.CertificateVfDTOs?.FirstOrDefault()?.SubjectDn),
                        SignTime = s.SignTime ?? "N/A",
                        IsIntact = s.Integrity,
                        CertificateStatus = s.CertificateVfDTOs?.FirstOrDefault()?.CurrentStatus ?? "Unknown"
                    }).ToList(),
                    VerificationTime = DateTime.Now
                };
            }
            
            throw new InvalidOperationException($"Xác thực thất bại: {EasySignService.GetErrorMessage(result)}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi xác thực tài liệu {FileName}", fileName);
            throw;
        }
    }

    public async Task<string> GetDoctorSignatureImageAsync(string doctorSerial, string doctorPin)
    {
        try
        {
            var result = await _easySignService.GetCertificateImageAsync(doctorSerial, doctorPin);
            
            if (EasySignService.IsSuccess(result) && !string.IsNullOrEmpty(result.Data))
            {
                return result.Data;
            }
            
            throw new InvalidOperationException($"Lấy ảnh chữ ký thất bại: {EasySignService.GetErrorMessage(result)}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi lấy ảnh chữ ký {DoctorSerial}", doctorSerial);
            throw;
        }
    }

    private string ExtractDoctorName(string? subjectDn)
    {
        if (string.IsNullOrEmpty(subjectDn)) return "N/A";
        
        var cnIndex = subjectDn.IndexOf("CN=", StringComparison.OrdinalIgnoreCase);
        if (cnIndex >= 0)
        {
            var cnValue = subjectDn.Substring(cnIndex + 3);
            var commaIndex = cnValue.IndexOf(',');
            if (commaIndex > 0)
            {
                cnValue = cnValue.Substring(0, commaIndex);
            }
            return cnValue.Trim().Replace("\\", "");
        }
        
        return "N/A";
    }
}
```

### Đăng ký trong DependencyInjection.cs

```csharp
// Services/GoTRUST.EMR.Infrastructure/DependencyInjection.cs
public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
{
    // ... existing code ...
    
    // Đăng ký Digital Signature Service
    services.AddScoped<IDigitalSignatureService, DigitalSignatureService>();
    
    // Đăng ký EasySign
    services.AddEasySignServices(configuration);
    
    return services;
}
```

## 3. Sử dụng trong Application Layer

### Medical Record Commands

```csharp
// Services/GoTRUST.EMR.Application/Features/MedicalRecords/Commands/SignMedicalRecordCommand.cs
using GoTRUST.EMR.Domain.Services;
using MediatR;

namespace GoTRUST.EMR.Application.Features.MedicalRecords.Commands;

public record SignMedicalRecordCommand(
    string PatientId,
    byte[] PdfData,
    string DoctorSerial,
    string DoctorPin,
    SignaturePosition? Position = null
) : IRequest<string>;

public class SignMedicalRecordCommandHandler : IRequestHandler<SignMedicalRecordCommand, string>
{
    private readonly IDigitalSignatureService _digitalSignatureService;
    private readonly ILogger<SignMedicalRecordCommandHandler> _logger;

    public SignMedicalRecordCommandHandler(
        IDigitalSignatureService digitalSignatureService,
        ILogger<SignMedicalRecordCommandHandler> logger)
    {
        _digitalSignatureService = digitalSignatureService;
        _logger = logger;
    }

    public async Task<string> Handle(SignMedicalRecordCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Ký hồ sơ bệnh án cho bệnh nhân: {PatientId}", request.PatientId);
        
        var signedPdfBase64 = await _digitalSignatureService.SignMedicalRecordAsync(
            request.PdfData,
            request.PatientId,
            request.DoctorSerial,
            request.DoctorPin,
            request.Position
        );
        
        _logger.LogInformation("Ký hồ sơ bệnh án thành công cho bệnh nhân: {PatientId}", request.PatientId);
        
        return signedPdfBase64;
    }
}
```

### Prescription Commands

```csharp
// Services/GoTRUST.EMR.Application/Features/Prescriptions/Commands/SignPrescriptionCommand.cs
public record SignPrescriptionCommand(
    string PatientId,
    string PrescriptionXml,
    string DoctorSerial,
    string DoctorPin
) : IRequest<string>;

public class SignPrescriptionCommandHandler : IRequestHandler<SignPrescriptionCommand, string>
{
    private readonly IDigitalSignatureService _digitalSignatureService;
    private readonly ILogger<SignPrescriptionCommandHandler> _logger;

    public SignPrescriptionCommandHandler(
        IDigitalSignatureService digitalSignatureService,
        ILogger<SignPrescriptionCommandHandler> logger)
    {
        _digitalSignatureService = digitalSignatureService;
        _logger = logger;
    }

    public async Task<string> Handle(SignPrescriptionCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Ký đơn thuốc cho bệnh nhân: {PatientId}", request.PatientId);
        
        var signedXmlBase64 = await _digitalSignatureService.SignPrescriptionAsync(
            request.PrescriptionXml,
            request.PatientId,
            request.DoctorSerial,
            request.DoctorPin
        );
        
        _logger.LogInformation("Ký đơn thuốc thành công cho bệnh nhân: {PatientId}", request.PatientId);
        
        return signedXmlBase64;
    }
}
```

## 4. Controller Implementation

```csharp
// Services/GoTRUST.EMR.API/Endpoints/DigitalSignatureEndpoints.cs
using GoTRUST.EMR.Application.Features.MedicalRecords.Commands;
using GoTRUST.EMR.Application.Features.Prescriptions.Commands;
using MediatR;

namespace GoTRUST.EMR.API.Endpoints;

public static class DigitalSignatureEndpoints
{
    public static IEndpointRouteBuilder MapDigitalSignatureEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/digital-signature")
            .WithTags("Digital Signature")
            .WithOpenApi();

        group.MapPost("/sign-medical-record", SignMedicalRecord)
            .WithName("SignMedicalRecord")
            .WithSummary("Ký hồ sơ bệnh án");

        group.MapPost("/sign-prescription", SignPrescription)
            .WithName("SignPrescription")
            .WithSummary("Ký đơn thuốc");

        group.MapPost("/verify-document", VerifyDocument)
            .WithName("VerifyDocument")
            .WithSummary("Xác thực chữ ký số");

        return app;
    }

    private static async Task<IResult> SignMedicalRecord(
        SignMedicalRecordRequest request,
        IMediator mediator)
    {
        try
        {
            var command = new SignMedicalRecordCommand(
                request.PatientId,
                Convert.FromBase64String(request.PdfBase64),
                request.DoctorSerial,
                request.DoctorPin,
                request.Position
            );

            var result = await mediator.Send(command);
            var signedPdfBytes = Convert.FromBase64String(result);

            return Results.File(
                signedPdfBytes, 
                "application/pdf", 
                $"Signed_MedicalRecord_{request.PatientId}.pdf"
            );
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "Signing Error",
                detail: ex.Message,
                statusCode: 500
            );
        }
    }

    private static async Task<IResult> SignPrescription(
        SignPrescriptionRequest request,
        IMediator mediator)
    {
        try
        {
            var command = new SignPrescriptionCommand(
                request.PatientId,
                request.PrescriptionXml,
                request.DoctorSerial,
                request.DoctorPin
            );

            var result = await mediator.Send(command);
            var signedXmlBytes = Convert.FromBase64String(result);

            return Results.File(
                signedXmlBytes, 
                "application/xml", 
                $"Signed_Prescription_{request.PatientId}.xml"
            );
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "Signing Error",
                detail: ex.Message,
                statusCode: 500
            );
        }
    }

    private static async Task<IResult> VerifyDocument(
        IFormFile file,
        IDigitalSignatureService digitalSignatureService)
    {
        try
        {
            using var stream = file.OpenReadStream();
            var result = await digitalSignatureService.VerifyDocumentAsync(stream, file.FileName);

            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "Verification Error",
                detail: ex.Message,
                statusCode: 500
            );
        }
    }
}

// Request models
public record SignMedicalRecordRequest(
    string PatientId,
    string PdfBase64,
    string DoctorSerial,
    string DoctorPin,
    SignaturePosition? Position = null
);

public record SignPrescriptionRequest(
    string PatientId,
    string PrescriptionXml,
    string DoctorSerial,
    string DoctorPin
);
```

## 5. Đăng ký Endpoints

```csharp
// Services/GoTRUST.EMR.API/Program.cs
using GoTRUST.EMR.API.Endpoints;

var app = builder.Build();

// ... existing middleware ...

// Map endpoints
app.MapDigitalSignatureEndpoints();

app.Run();
```

## 6. Testing

### Unit Tests

```csharp
// Tests/GoTRUST.EMR.Application.Tests/Features/DigitalSignatureServiceTests.cs
using BuildingBlocks.Common.EasySign.Services;
using GoTRUST.EMR.Infrastructure.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

public class DigitalSignatureServiceTests
{
    private readonly Mock<EasySignService> _mockEasySignService;
    private readonly Mock<ILogger<DigitalSignatureService>> _mockLogger;
    private readonly DigitalSignatureService _service;

    public DigitalSignatureServiceTests()
    {
        _mockEasySignService = new Mock<EasySignService>();
        _mockLogger = new Mock<ILogger<DigitalSignatureService>>();
        _service = new DigitalSignatureService(_mockEasySignService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task SignMedicalRecordAsync_ShouldReturnSignedPdf_WhenSuccessful()
    {
        // Arrange
        var patientId = "P001";
        var pdfData = System.Text.Encoding.UTF8.GetBytes("sample pdf");
        var doctorSerial = "test_serial";
        var doctorPin = "test_pin";

        // Act & Assert
        // Add test implementation
    }
}
```

## 7. Monitoring và Logging

### Custom Logging

```csharp
// Services/GoTRUST.EMR.Infrastructure/Logging/DigitalSignatureLogger.cs
public static class DigitalSignatureLogger
{
    private static readonly Action<ILogger, string, string, Exception?> _signMedicalRecordStarted =
        LoggerMessage.Define<string, string>(
            LogLevel.Information,
            EventIds.SignMedicalRecordStarted,
            "Bắt đầu ký hồ sơ bệnh án - PatientId: {PatientId}, DoctorSerial: {DoctorSerial}");

    private static readonly Action<ILogger, string, Exception?> _signMedicalRecordCompleted =
        LoggerMessage.Define<string>(
            LogLevel.Information,
            EventIds.SignMedicalRecordCompleted,
            "Hoàn thành ký hồ sơ bệnh án - PatientId: {PatientId}");

    public static void SignMedicalRecordStarted(this ILogger logger, string patientId, string doctorSerial)
        => _signMedicalRecordStarted(logger, patientId, doctorSerial, null);

    public static void SignMedicalRecordCompleted(this ILogger logger, string patientId)
        => _signMedicalRecordCompleted(logger, patientId, null);
}

public static class EventIds
{
    public static readonly EventId SignMedicalRecordStarted = new(1001, "SignMedicalRecordStarted");
    public static readonly EventId SignMedicalRecordCompleted = new(1002, "SignMedicalRecordCompleted");
    // Add more event IDs as needed
}
```

## 8. Deployment Notes

### Docker Support

Đảm bảo thêm vào Dockerfile nếu cần:

```dockerfile
# Install certificate stores for digital signature validation
RUN apt-get update && apt-get install -y ca-certificates
```

### Health Checks

```csharp
// Services/GoTRUST.EMR.API/HealthChecks/EasySignHealthCheck.cs
public class EasySignHealthCheck : IHealthCheck
{
    private readonly EasySignService _easySignService;

    public EasySignHealthCheck(EasySignService easySignService)
    {
        _easySignService = easySignService;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            await _easySignService.AuthenticateAsync();
            return HealthCheckResult.Healthy("EasySign service is healthy");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("EasySign service is unhealthy", ex);
        }
    }
}

// Đăng ký trong Program.cs
builder.Services.AddHealthChecks()
    .AddCheck<EasySignHealthCheck>("easysign");
```

Với hướng dẫn này, bạn có thể tích hợp hoàn chỉnh thư viện EasySign vào hệ thống GoTRUST EMR để ký số hồ sơ bệnh án điện tử một cách an toàn và hiệu quả.
