using BuildingBlocks.Common.EasySign.Interfaces;
using BuildingBlocks.Common.EasySign.Services;
using BuildingBlocks.Common.Interface;
using Microsoft.Extensions.DependencyInjection;
using Refit;
using System.Text.Json;

namespace BuildingBlocks.Common.EasySign.Extensions;

/// <summary>
/// Extension methods cho việc đăng ký EasySign services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Đăng ký EasySign services với Dependency Injection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Cấu hình EasySign</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddEasySign(this IServiceCollection services, EasySignConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);

        if (string.IsNullOrEmpty(configuration.BaseUrl))
            throw new ArgumentException("BaseUrl is required", nameof(configuration));

        // Đăng ký configuration
        services.AddSingleton(configuration);

        // C<PERSON><PERSON> hình Refit settings
        var refitSettings = new RefitSettings
        {
            ContentSerializer = new SystemTextJsonContentSerializer(new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            })
        };

        // Đăng ký Refit client với HttpClient
        services.AddRefitClient<IEasySignApi>(refitSettings)
                .ConfigureHttpClient(client =>
                {
                    client.BaseAddress = new Uri(configuration.BaseUrl);
                    client.Timeout = TimeSpan.FromSeconds(configuration.TimeoutSeconds);

                    // Thêm User-Agent header
                    client.DefaultRequestHeaders.Add("User-Agent", "EasySign-Client/1.0");
                });

        // Đăng ký EasySignService
        services.AddScoped<ISignService, EasySignService>();
        services.AddScoped<EasySignService>();

        return services;
    }

    /// <summary>
    /// Đăng ký EasySign services với cấu hình từ action
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Action để cấu hình</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddEasySign(this IServiceCollection services, Action<EasySignConfiguration> configureOptions)
    {
        var configuration = new EasySignConfiguration();
        configureOptions(configuration);
        return services.AddEasySign(configuration);
    }
}
