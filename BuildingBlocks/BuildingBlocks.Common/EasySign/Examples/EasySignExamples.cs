using BuildingBlocks.Common.EasySign.Models;
using BuildingBlocks.Common.EasySign.Services;

namespace BuildingBlocks.Common.EasySign.Examples;

/// <summary>
/// <PERSON><PERSON>c ví dụ sử dụng EasySign API
/// </summary>
public class EasySignExamples(EasySignService easySignService)
{
    private readonly EasySignService _easySignService = easySignService;

    /// <summary>
    /// Ví dụ ký văn bản đơn giản
    /// </summary>
    public async Task<string> SignDocumentExample()
    {
        try
        {
            // Thông tin chứng thư số demo
            const string serial = "540110beffa622f3ca84bd2f93f0122c";
            const string pin = "12345678";

            // Nội dung cần ký
            const string content = "Đây là nội dung cần ký số";

            // Ký văn bản
            var result = await _easySignService.SignTextAsync(
                text: content,
                key: "doc_001",
                serial: serial,
                pin: pin,
                signer: "Nguyễn <PERSON>n <PERSON>"
            );

            if (EasySignService.IsSuccess(result))
            {
                var signature = result.Data?.SignResult?.FirstOrDefault()?.Base64Signature;
                return signature ?? "Không có chữ ký";
            }
            else
            {
                throw new Exception($"Ký thất bại: {EasySignService.GetErrorMessage(result)}");
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"Lỗi khi ký tài liệu: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Ví dụ ký PDF với vị trí hiển thị
    /// </summary>
    public async Task<string> SignPdfWithVisibleSignatureExample(string pdfFilePath)
    {
        try
        {
            // Thông tin chứng thư số demo
            const string serial = "540110beffa622f3ca84bd2f93f0122c";
            const string pin = "12345678";

            // Đọc file PDF
            var pdfBase64 = await EasySignService.FileToBase64Async(pdfFilePath);

            // Lấy ảnh chữ ký
            var imageResult = await _easySignService.GetCertificateImageAsync(serial, pin);
            var signatureImage = EasySignService.IsSuccess(imageResult) ? imageResult.Data : null;

            // Ký PDF với vị trí hiển thị
            var result = await _easySignService.SignVisiblePdfAsync(
                pdfBase64: pdfBase64,
                documentName: "Contract.pdf",
                serial: serial,
                pin: pin,
                x: 100,
                y: 100,
                width: 200,
                height: 80,
                pageNum: 1,
                imageSignature: signatureImage
            );

            if (EasySignService.IsSuccess(result))
            {
                // Lưu file PDF đã ký
                var signedPdfPath = Path.ChangeExtension(pdfFilePath, ".signed.pdf");
                await EasySignService.SaveBase64ToFileAsync(result.Data!, signedPdfPath);
                return signedPdfPath;
            }
            else
            {
                throw new Exception($"Ký PDF thất bại: {EasySignService.GetErrorMessage(result)}");
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"Lỗi khi ký PDF: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Ví dụ ký PDF ẩn (không hiển thị chữ ký)
    /// </summary>
    public async Task<string> SignPdfInvisibleExample(string pdfFilePath)
    {
        try
        {
            // Thông tin chứng thư số demo
            const string serial = "540110beffa622f3ca84bd2f93f0122c";
            const string pin = "12345678";

            // Ký PDF ẩn
            var result = await _easySignService.SignInvisiblePdfFromFileAsync(
                pdfFilePath: pdfFilePath,
                documentName: "Document.pdf",
                serial: serial,
                pin: pin
            );

            if (EasySignService.IsSuccess(result))
            {
                var signedDocument = result.Data?.ResponseContentList?.FirstOrDefault()?.SignedDocument;
                if (!string.IsNullOrEmpty(signedDocument))
                {
                    // Lưu file PDF đã ký
                    var signedPdfPath = Path.ChangeExtension(pdfFilePath, ".invisible.signed.pdf");
                    await EasySignService.SaveBase64ToFileAsync(signedDocument, signedPdfPath);
                    return signedPdfPath;
                }
                throw new Exception("Không có dữ liệu PDF đã ký");
            }
            else
            {
                throw new Exception($"Ký PDF ẩn thất bại: {EasySignService.GetErrorMessage(result)}");
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"Lỗi khi ký PDF ẩn: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Ví dụ ký XML
    /// </summary>
    public async Task<string> SignXmlExample(string xmlFilePath)
    {
        try
        {
            // Thông tin chứng thư số demo
            const string serial = "540110beffa622f3ca84bd2f93f0122c";
            const string pin = "12345678";

            // Ký XML
            var result = await _easySignService.SignXmlFromFileAsync(
                xmlFilePath: xmlFilePath,
                documentName: "Data.xml",
                serial: serial,
                pin: pin
            );

            if (EasySignService.IsSuccess(result))
            {
                var signedDocument = result.Data?.ResponseContentList?.FirstOrDefault()?.SignedDocument;
                if (!string.IsNullOrEmpty(signedDocument))
                {
                    // Lưu file XML đã ký
                    var signedXmlPath = Path.ChangeExtension(xmlFilePath, ".signed.xml");
                    await EasySignService.SaveBase64ToFileAsync(signedDocument, signedXmlPath);
                    return signedXmlPath;
                }
                throw new Exception("Không có dữ liệu XML đã ký");
            }
            else
            {
                throw new Exception($"Ký XML thất bại: {EasySignService.GetErrorMessage(result)}");
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"Lỗi khi ký XML: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Ví dụ xác thực chữ ký PDF
    /// </summary>
    public async Task<bool> VerifyPdfExample(string signedPdfFilePath)
    {
        try
        {
            var result = await _easySignService.VerifyPdfFromFileAsync(signedPdfFilePath);

            if (EasySignService.IsSuccess(result))
            {
                var signatures = result.Data?.SignatureVfDTOs;
                if (signatures != null && signatures.Any())
                {
                    foreach (var signature in signatures)
                    {
                        Console.WriteLine($"Chữ ký số {signature.Revision}/{signature.TotalRevision}:");
                        Console.WriteLine($"- Thời gian ký: {signature.SignTime}");
                        Console.WriteLine($"- Toàn vẹn: {(signature.Integrity ? "Hợp lệ" : "Không hợp lệ")}");
                        Console.WriteLine($"- Bao phủ toàn tài liệu: {(signature.CoverWholeDocument ? "Có" : "Không")}");

                        if (signature.CertificateVfDTOs != null)
                        {
                            foreach (var cert in signature.CertificateVfDTOs)
                            {
                                Console.WriteLine($"- Chứng thư số: {cert.SubjectDn}");
                                Console.WriteLine($"- Trạng thái: {cert.CurrentStatus}");
                                Console.WriteLine($"- Hiệu lực: {cert.ValidFrom} đến {cert.ValidTo}");
                            }
                        }
                    }
                    return true;
                }
                return false;
            }
            else
            {
                throw new Exception($"Xác thực PDF thất bại: {EasySignService.GetErrorMessage(result)}");
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"Lỗi khi xác thực PDF: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Ví dụ lấy thông tin chứng thư số
    /// </summary>
    public async Task<string> GetCertificateInfoExample()
    {
        try
        {
            // Thông tin chứng thư số demo
            const string serial = "540110beffa622f3ca84bd2f93f0122c";
            const string pin = "12345678";

            var result = await _easySignService.GetCertificateAsync(serial, pin);

            if (EasySignService.IsSuccess(result))
            {
                return result.Data ?? "Không có dữ liệu chứng thư số";
            }
            else
            {
                throw new Exception($"Lấy thông tin chứng thư số thất bại: {EasySignService.GetErrorMessage(result)}");
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"Lỗi khi lấy thông tin chứng thư số: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Ví dụ thay đổi PIN chứng thư số
    /// </summary>
    public async Task<bool> ChangePinExample(string serial, string oldPin, string newPin)
    {
        try
        {
            var result = await _easySignService.ChangeCertificatePinAsync(serial, oldPin, newPin);

            if (EasySignService.IsSuccess(result))
            {
                Console.WriteLine("Thay đổi PIN thành công!");
                return true;
            }
            else
            {
                throw new Exception($"Thay đổi PIN thất bại: {EasySignService.GetErrorMessage(result)}");
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"Lỗi khi thay đổi PIN: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Ví dụ lấy danh sách mẫu chữ ký
    /// </summary>
    public async Task<List<SignatureTemplate>> GetSignatureTemplatesExample(string username)
    {
        try
        {
            var result = await _easySignService.GetSignatureTemplatesByUsernameAsync(username);

            if (EasySignService.IsSuccess(result))
            {
                return result.Data ?? new List<SignatureTemplate>();
            }
            else
            {
                throw new Exception($"Lấy danh sách mẫu chữ ký thất bại: {EasySignService.GetErrorMessage(result)}");
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"Lỗi khi lấy danh sách mẫu chữ ký: {ex.Message}", ex);
        }
    }
}
