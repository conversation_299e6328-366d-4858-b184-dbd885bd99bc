TÀI LIỆU KỸ THUẬT TÍCH HỢP EASY-SIGN
MỤC LỤC
I. Giới thiệu
II. Đặc điểm kỹ thuật
III. Điểm lưu ý
IV. Phụ lục
V. Quy trình gọi API
VI. Các đầu API
VI.1. API đăng nhập hệ thống
VI.2. API ký dữ liệu hash
VI.3. API ký số dữ liệu raw
VI.4. API ký số dữ liệu PDF (ký ẩn)
VI.5. API xác thực chữ ký số với dữ liệu raw
VI.6. API xác thực chữ ký số với dữ liệu hash
VI.7. API Lấy ảnh chữ ký số
VI.8. API ký số dữ liệu PDF (ký hiện vị trí)
VI.9. API verify PDF (Sau khi kí thành công)
VI.10. API Change Certificate Pin
VI.11. API ký số dữ liệu XML
VI.12. API reset mã pin chứng thư số
VI.13. API lấy thông tin chứng thư số bằng serial
VI.14. API Xác thực chữ ký số File XML đã ký.
VI.15. API Lấy danh sách mẫu chữ ký của username.
VI.16. API Lấy ảnh mẫu chữ ký theo mã mẫu chữ ký.

I. Giới thiệu
EasySign là hệ thống ký số tập trung cho phép ký số các dữ liệu như: Raw, XML, PDF, WORD, HASH thông qua Web-API.

Tài liệu này cung cấp cách thức kết nối đến hệ thống EasySign và sử dụng các hàm nghiệp vụ ký số.

II. Đặc điểm kỹ thuật
Cổng tích hợp được xây dựng bằng Restful API.

Kiểu dữ liệu (ContentType): Application/json.

Xác thực bằng mô hình JWT.

Thông tin hệ thống thật: https://sign.easyca.vn

Thông tin hệ thống demo: http://demosign.easyca.vn:8080

Tài khoản/Mật khẩu: demo_easysign / demo_easysign

Serial CTS/PIN: 540110beffa622f3ca84bd2f93f0122c / 12345678

III. Điểm lưu ý
Các ví dụ đầu vào và đầu ra trong tài liệu này chỉ mô tả về cấu trúc của dữ liệu, không hoàn toàn chính xác về nội dung dữ liệu nên có thể không hoạt động được trên môi trường test/demo/production.
Tham khảo thêm mẫu ví dụ khi chạy trên postman tại đường dẫn này: https://drive.google.com/file/d/1VYfdWlLc3lYHYDCcbRQOwwTWo6kyQiJW/view?usp=sharing

IV. Phụ lục
Mô tả trạng thái kết quả ký số
Status = 0: Message: Ký file pdf thành công

Status = -1:

Trường hợp không tìm thấy chứng thư số: Message: The certificate is not found

Trường hợp cts hết hạn: Certificate has error (chứng thư số thu hồi, hết hạn)

Trường hợp không tìm thấy key trên thiết bị HSM: Private key is not found (không tìm thấy private key trên thiết bị HSM)

Status = 9: Certificate has error! Please check serial and pin

Status = 10: Message: Sign PDF occurs error + exp cụ thể (xảy ra trong quá trình ký bị lỗi)

Status = 13: Trường hợp nhập mã pin sai: Keystore is not initialized, please check PIN number

Mô tả trạng thái kết quả verify chữ ký số
Status=0: Msg=Hợp lệ

Status=1: Msg=Hết hạn

Status=2: Msg=Thu hồi

V. Quy trình gọi API
Hệ thống EasySign sử dụng cơ chế xác thực JWT (Json Web Token).
Mỗi khi gọi các đầu API xử lý nghiệp vụ, client cần gửi kèm thêm một giá trị token để có quyền truy cập vào hệ thống. Token được lấy thông qua “API đăng nhập hệ thống” (mục V. 1).
Sau khi có token, các request gọi API nghiệp vụ bắt buộc kèm theo Header, với:

Key: Authorization

Value: Bearer (khoảng trắng) id_token
Ví dụ:

Key: Authorization

Value: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJidmFucGh1b2MiLCJhdXRoIjoiUk9MRV9VU0VSIiwiZXhwIjoxNjExNzE1NjYyfQ.Wp9lHKnM3-CX1fr8afYvxWuyA0sdbsR2eZ03kn4saBGNztyal0jQH7s7K3i5GMjVzrkoQURcNgUBsKgKQ3nC3g

VI. Các đầu API
VI.1. API đăng nhập hệ thống
Mô tả chung

Mô tả: API lấy ra token, là đầu ra để có thể truy cập được vào các API nghiệp vụ khác.

Yêu cầu xác thực: Không

API: /api/authenticate

Method: POST

Header: ContentType: Application/json

Thuộc tính đầu vào
| STT | Tên        | Kiểu dữ liệu | Bắt buộc | Mô tả       |
|-----|------------|--------------|----------|-------------|
| 1   | username | String     | x      | Tên tài khoản |
| 2   | password | String     | x      | Mật khẩu    |
| 3   | rememberMe | Boolean    |          |             |

Ví dụ đầu vào:

{
    "username": "{{username}}",
    "password": "{{password}}",
    "rememberMe": false
}

Thuộc tính đầu ra
| STT | Tên        | Kiểu dữ liệu | Bắt buộc | Mô tả                   |
|-----|------------|--------------|----------|-------------------------|
| 1   | id_token | String     | x      | Giá trị token dùng để xác thực |

Ví dụ đầu ra:

{
    "id_token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJidmFucGh1b2MiLCJhdXRoIjoiUk9MRV9VU0VSIiwiZXhwIjoxNjExNzE4NDI2fQ.alE0V03dSjSAqW412d06uJS41KceepwLa_vw7hvAlhTTpuhAt0qMN07YR4jamVrojWh4nvXsB1D5jMwQP614RA"
}

VI.2. API ký dữ liệu hash
Mô tả chung

Mô tả: Ký số với dữ liệu đầu vào là chuỗi hash, trong một lần request có thể ký nhiều dữ liệu.

Yêu cầu xác thực: Có

API: /api/sign/hash

Method: POST

Header: ContentType: Application/json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên            | Kiểu dữ liệu | Bắt buộc | Mô tả                                                               |
|-----|----------------|--------------|----------|---------------------------------------------------------------------|
| 1   | elements     | Object List| x      | Phần tử chứa những dữ liệu cần ký                                  |
| 1.1 | content      | String     | x      | Nội dung hash cần ký đã được encode base64                          |
| 1.2 | key          | String     | x | o    | Tên định danh nội dung                                              |
| 1.3 | signDate     | String     |          | Ngày ký                                                             |
| 1.4 | signer       | String     | o      | Tên người ký                                                        |
| 2   | optional     | Object     |          | Phần tử chứa những tùy chọn ký số                                  |
| 2.1 | hashAlgorithm| String     | o      | Kiểu băm (Mặc định: SHA1 – Giá trị nhận: SHA1, SHA256, SHA512)      |
| 2.2 | returnInputData| Boolean    | o      | Trả về nội dung đầu vào (Mặc định: false)                           |
| 2.3 | signAlgorithm| String     | o      | Kiểu giải thuật ký số (Mặc định: RSA)                               |
| 2.4 | otpCode      | String     | o      | Mã xác thực OTP (Nếu có cấu hình bật tính năng này)                 |
| 3   | tokenInfo    | Object     | x      | Phần tử chứa thông tin chứng thư ký số                              |
| 3.1 | serial       | String     | x      | Thông tin Serial chứng thư số (CTS)                                |
| 3.2 | pin          | String     | x      | Mã pin truy cập CTS                                                 |

Ví dụ đầu vào:

{
    "elements": [
        {
            "content": "aGFoYWhhIGRzZA==",
            "key": "123",
            "signDate": "",
            "signer": "thanhld"
        }
    ],
    "optional": {
        "hashAlgorithm": "sha256",
        "returnInputData": true,
        "signAlgorithm": "rsa",
        "otpCode": "998866"
    },
    "tokenInfo": {
        "pin": "123456",
        "serial": "26f1"
    }
}

Thuộc tính đầu ra
| STT | Tên              | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|------------------|--------------|----------|-----------------------------------------|
| 1   | status         | int        | x      | Trạng thái xử lý                        |
| 2   | msg            | String     |          | Thông tin chi tiết trạng thái           |
| 3   | data           | Object     |          | Phần tử chứa thông tin ký số            |
| 3.1 | signResult     | Object List|          | Phần tử chứa danh sách dữ liệu ký số    |
| 3.1.1 | base64Signature| String     |          | Chữ ký số tương ứng dữ liệu, dưới dạng base64 |
| 3.1.2 | inputData    | String     |          | Dữ liệu đầu vào cần ký số, dạng base64    |
| 3.1.3 | key          | String     |          | Định danh dữ liệu ký số                 |
| 3.2 | certificate    | String     |          | Base64 của chứng thư số                 |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": null,
    "data": {
        "signResult": [
            {
                "base64Signature": "wSIzloAVnK4YtUwOk3Hg9ZpCaObWFbCiEHfMUcOXCj3HHDrqIih0CH8geQ3k8bjYw0xACgs8gQvr0ONymOVFMK5/YZSRXqHD0FG7+xdlYURFltKtIwPKXyKmlz61MAwu58X7DIgDBh3HkBWbMJFa6FIiCE+phmJafjUFxcLjVJc=",
                "inputData": "dGhhbmhsZA==",
                "key": "123"
            }
        ],
        "certificate": "MIIEGzCCAwOgAwIBAgIQVAT//rcDP7MW1nIgG4DTmDANBgkqhkiG9w0BAQUFADBOMQswCQYDVQQGEwJWTjESMBAGA1UEBwwJSMOgIE7hu5lpMRYwFAYDVQQKEw1WaWV0dGVsIEdyb3VwMRMwEQYDVQQDEwpWaWV0dGVsLUNBMB4XDTIwMDMwNTA4MzI1OVoXDTIxMDMwNTA4MzI1OVowgZYxHjAcBgoJkiaJk/IsZAEBDA5NU1Q6MDEwNTk4NzQzMjFTMFEGA1UEAwxKQ8OUTkcgVFkgQ+G7lCBQSOG6pk4gxJDhuqZVIFTGryBDw5RORyBOR0jhu4YgVsOAIFRIxq/GoE5HIE3huqBJIFNPRlREUkVBTVMxEjAQBgNVBAcMCUjDgCBO4buYSTELMAkGA1UEBhMCVk4wgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBAN45MIHLnjVj1vpa4WJ2tYtyxDrIwkREjDqTaKgGPryrxxcipfQpZdqCSWIkPUf6K44I5jcK8s1YeoC6hADjVKrpsz8baQz/dBSYy5oJxdiMweTJQq9QlbNw+kx15W9aEi2k2DOb+i3yBTQDkc+u/ylLGF5F7njajQhoKR8PxuvXAgMBAAGjggEuMIIBKjA1BggrBgEFBQcBAQQpMCcwJQYIKwYBBQUHMAGGGWh0dHA6Ly9vY3NwLnZpZXR0ZWwtY2Eudm4wHQYDVR0OBBYEFAz4ANgRsBDOZXFKGBWg0tOa8nHSMAwGA1UdEwEB/wQCMAAwHwYDVR0jBBgwFoAU/stBFOldp9kyDeSyCFzxXOy2srUwgZIGA1UdHwSBijCBhzCBhKAuoCyGKmh0dHA6Ly9jcmwudmlldHRlbC1jYS52bi9WaWV0dGVsLUNBLXYzLmNybKJSpFAwTjETMBEGA1UEAwwKVmlldHRlbC1DQTEWMBQGA1UECgwNVmlldHRlbCBHcm91cDESMBAGA1UEBwwJSMOgIE7hu5lpMQswCQYDVQQGEwJWTjAOBgNVHQ8BAf8EBAMCBeAwDQYJKoZIhvcNAQEFBQADggEBAJGwolmW8aFUp7cViSErVDxhMfgB6mOd5bW+jBphpULpezPJ7vNteuKjKhtGVOkGuwOyuCKR2IK2uRNlMGi6kE9jUV5W4R5/DVM5oFRmTgs9Q7W1Sy/RytUyJXVtvehDY2hwS3YhtfWJ57Cw0zmPj28a7vgOy7Pzbx7YAoR2UTrP5gmVuyIAFJ1r+r0BNDcyK8uHeq29h6hKXuRc5K8kUZ3cnIl7WeNuLCWULB+k5DpxpajDSvSJR7rZlgvg4i64p3lsvSucndM9iD1vEE03VEMYIMZEWh6LYvQ7f/Ah9V98MTdkRN2CpmtptrMsBDzb6+UDzrE0rqFyZFfICsaGrZ4="
    }
}

VI.3. API ký số dữ liệu raw
Mô tả chung

Mô tả: Ký số với dữ liệu đầu vào là chuỗi dữ liệu bất kỳ, trong một lần request có thể ký nhiều dữ liệu.

Yêu cầu xác thực: Có

API: /api/sign/raw

Method: POST

Header: ContentType: Application/json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên            | Kiểu dữ liệu | Bắt buộc | Mô tả                                                               |
|-----|----------------|--------------|----------|---------------------------------------------------------------------|
| 1   | elements     | Object List| x      | Phần tử chứa những dữ liệu cần ký                                  |
| 1.1 | content      | String     | x      | Nội dung cần ký đã được encode base64                               |
| 1.2 | key          | String     | x | o    | Tên định danh nội dung                                              |
| 1.3 | signDate     | String     |          | Ngày ký                                                             |
| 1.4 | signer       | String     | o      | Tên người ký                                                        |
| 2   | optional     | Object     |          | Phần tử chứa những tùy chọn ký số                                  |
| 2.1 | hashAlgorithm| String     | o      | Kiểu băm (Mặc định: SHA1 – Giá trị nhận: SHA1, SHA256, SHA512)      |
| 2.2 | returnInputData| Boolean    | o      | Trả về nội dung đầu vào (Mặc định: false)                           |
| 2.3 | signAlgorithm| String     | o      | Kiểu giải thuật ký số (Mặc định: RSA)                               |
| 2.4 | otpCode      | String     | o      | Mã OTP (nếu hệ thống có bật tính năng OTP)                          |
| 3   | tokenInfo    | Object     | x      | Phần tử chứa thông tin chứng thư ký số                              |
| 3.1 | serial       | String     | x      | Thông tin Serial chứng thư số (CTS)                                |
| 3.2 | pin          | String     | x      | Mã pin truy cập CTS                                                 |

Ví dụ đầu vào:

{
    "elements": [
        {
            "content": "aGFoYWhhIGRzZA==",
            "key": "123",
            "signDate": "",
            "signer": "thanhld"
        }
    ],
    "optional": {
        "hashAlgorithm": "sha256",
        "returnInputData": true,
        "signAlgorithm": "rsa",
        "otpCode": "998866"
    },
    "tokenInfo": {
        "pin": "123456",
        "serial": "26f1"
    }
}

Thuộc tính đầu ra
| STT | Tên              | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|------------------|--------------|----------|-----------------------------------------|
| 1   | status         | int        | x      | Trạng thái xử lý                        |
| 2   | msg            | String     |          | Thông tin chi tiết trạng thái           |
| 3   | data           | Object     |          | Phần tử chứa thông tin ký số            |
| 3.1 | signResult     | Object List|          | Phần tử chứa danh sách dữ liệu ký số    |
| 3.1.1 | base64Signature| String     |          | Chữ ký số tương ứng dữ liệu, dưới dạng base64 |
| 3.1.2 | inputData    | String     |          | Dữ liệu đầu vào cần ký số, dạng base64    |
| 3.1.3 | key          | String     |          | Định danh dữ liệu ký số                 |
| 3.2 | certificate    | String     |          | Base64 của chứng thư số                 |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": null,
    "data": {
        "signResult": [
            {
                "base64Signature": "wSIzloAVnK4YtUwOk3Hg9ZpCaObWFbCiEHfMUcOXCj3HHDrqIih0CH8geQ3k8bjYw0xACgs8gQvr0ONymOVFMK5/YZSRXqHD0FG7+xdlYURFltKtIwPKXyKmlz61MAwu58X7DIgDBh3HkBWbMJFa6FIiCE+phmJafjUFxcLjVJc=",
                "inputData": "dGhhbmhsZA==",
                "key": "123"
            }
        ],
        "certificate": "MIIEGzCCAwOgAwIBAgIQVAT//rcDP7MW1nIgG4DTmDANBgkqhkiG9w0BAQUFADBOMQswCQYDVQQGEwJWTjESMBAGA1UEBwwJSMOgIE7hu5lpMRYwFAYDVQQKEw1WaWV0dGVsIEdyb3VwMRMwEQYDVQQDEwpWaWV0dGVsLUNBMB4XDTIwMDMwNTA4MzI1OVoXDTIxMDMwNTA4MzI1OVowgZYxHjAcBgoJkiaJk/IsZAEBDA5NU1Q6MDEwNTk4NzQzMjFTMFEGA1UEAwxKQ8OUTkcgVFkgQ+G7lCBQSOG6pk4gxJDhuqZVIFTGryBDw5RORyBOR0jhu4YgVsOAIFRIxq/GoE5HIE3huqBJIFNPRlREUkVBTVMxEjAQBgNVBAcMCUjDgCBO4buYSTELMAkGA1UEBhMCVk4wgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBAN45MIHLnjVj1vpa4WJ2tYtyxDrIwkREjDqTaKgGPryrxxcipfQpZdqCSWIkPUf6K44I5jcK8s1YeoC6hADjVKrpsz8baQz/dBSYy5oJxdiMweTJQq9QlbNw+kx15W9aEi2k2DOb+i3yBTQDkc+u/ylLGF5F7njajQhoKR8PxuvXAgMBAAGjggEuMIIBKjA1BggrBgEFBQcBAQQpMCcwJQYIKwYBBQUHMAGGGWh0dHA6Ly9vY3NwLnZpZXR0ZWwtY2Eudm4wHQYDVR0OBBYEFAz4ANgRsBDOZXFKGBWg0tOa8nHSMAwGA1UdEwEB/wQCMAAwHwYDVR0jBBgwFoAU/stBFOldp9kyDeSyCFzxXOy2srUwgZIGA1UdHwSBijCBhzCBhKAuoCyGKmh0dHA6Ly9jcmwudmlldHRlbC1jYS52bi9WaWV0dGVsLUNBLXYzLmNybKJSpFAwTjETMBEGA1UEAwwKVmlldHRlbC1DQTEWMBQGA1UECgwNVmlldHRlbCBHcm91cDESMBAGA1UEBwwJSMOgIE7hu5lpMQswCQYDVQQGEwJWTjAOBgNVHQ8BAf8EBAMCBeAwDQYJKoZIhvcNAQEFBQADggEBAJGwolmW8aFUp7cViSErVDxhMfgB6mOd5bW+jBphpULpezPJ7vNteuKjKhtGVOkGuwOyuCKR2IK2uRNlMGi6kE9jUV5W4R5/DVM5oFRmTgs9Q7W1Sy/RytUyJXVtvehDY2hwS3YhtfWJ57Cw0zmPj28a7vgOy7Pzbx7YAoR2UTrP5gmVuyIAFJ1r+r0BNDcyK8uHeq29h6hKXuRc5K8kUZ3cnIl7WeNuLCWULB+k5DpxpajDSvSJR7rZlgvg4i64p3lsvSucndM9iD1vEE03VEMYIMZEWh6LYvQ7f/Ah9V98MTdkRN2CpmtptrMsBDzb6+UDzrE0rqFyZFfICsaGrZ4="
    }
}

VI.4. API ký số dữ liệu PDF (ký ẩn)
Mô tả chung

Mô tả: Ký số với dữ liệu PDF với tính năng ký ẩn – không hiển thị hình ký số trên tệp, trong một lần request có thể ký nhiều dữ liệu.

Yêu cầu xác thực: Có

API: /api/sign/invisiblePdf

Method: POST

Header: ContentType: Application/json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên                    | Kiểu dữ liệu | Bắt buộc | Mô tả                                                               |
|-----|------------------------|--------------|----------|---------------------------------------------------------------------|
| 1   | signingRequestContents| Object List| x      | Phần tử chứa những dữ liệu cần ký                                  |
| 1.1 | data                 | String     | x      | Nội dung cần ký đã được encode base64                               |
| 1.2 | documentName         | String     | x | o    | Tên định danh nội dung                                              |
| 2   | optional             | Object     |          | Phần tử chứa những tùy chọn ký số                                  |
| 2.1 | hashAlgorithm        | String     | o      | Kiểu băm (Mặc định: SHA1 – Giá trị nhận: SHA1, SHA256, SHA512)      |
| 2.2 | returnInputData      | Boolean    | o      | Trả về nội dung đầu vào (Mặc định: false)                           |
| 2.3 | signAlgorithm        | String     | o      | Kiểu giải thuật ký số (Mặc định: RSA)                               |
| 2.4 | otpCode              | String     | o      | Mã OTP (nếu hệ thống có bật tính năng OTP)                          |
| 3   | tokenInfo            | Object     | x      | Phần tử chứa thông tin chứng thư ký số                              |
| 3.1 | serial               | String     | x      | Thông tin Serial chứng thư số (CTS)                                |
| 3.2 | pin                  | String     | x      | Mã pin truy cập CTS                                                 |

Ví dụ đầu vào:

{
    "signingRequestContents": [
        {
            "data": "JVBERi0xLjQNCiWqq6ytDQoxIDAgb2JqDQo8PA0KL1BhZ2VzIDIgMCBSDQovUGFnZUxheW91dCAvT25lQ29sdW1uDQovVHlwZSAvQ2F0YWxvZw0KL091dHB1dEludGVudHM",
            "documentName": "123"
        }
    ],
    "tokenInfo": {
        "pin": "079073009568",
        "serial": "540110000b4525650231e39369660895"
    },
    "optional": {
        "otpCode": "317588"
    }
}

Thuộc tính đầu ra
| STT | Tên                  | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|----------------------|--------------|----------|-----------------------------------------|
| 1   | status             | int        | x      | Trạng thái xử lý                        |
| 2   | msg                | String     |          | Thông tin chi tiết trạng thái           |
| 3   | data               | Object     |          | Phần tử chứa thông tin ký số            |
| 3.1 | responseContentList| Object List|          | Phần tử chứa danh sách dữ liệu ký số    |
| 3.1.1 | documentName     | String     |          | Định danh dữ liệu ký số                 |
| 3.1.2 | signatureOnly    | String     |          | Chữ ký số tương ứng dữ liệu, dưới dạng base64 |
| 3.1.3 | signedDocument   | String     |          | Base64 tệp PDF đầu vào kèm chữ ký số    |
| 3.2 | base64Certificate  | String     |          | Base64 của chứng thư số                 |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": null,
    "data": {
        "responseContentList": [
            {
                "signatureOnly": "xdlYURFltKtIwPKXyKmlz61MAwu58X7DIgDBh3HkBWbMJFa6FIiCE+phmJafjUFxcLjVJc=",
                "signedDocument": "wSIzloAVnK4YtUwOk3Hg9ZpCaObWFbCiEHfMUcOXCj3HHDrqIih0CH8geQ3k8bjYw0xACgs8gQvr0ONymOVFMK5/YZSRXqHD0FG7+xdlYURFltKtIwPKXyKmlz61MAwu58X7DIgDBh3HkBWbMJFa6FIiCE+phmJafjUFxcLjVJc=",
                "documentName": "tepPdf.pdf"
            }
        ],
        "certificate": "MIIEGzCCAwOgAwIBAgIQVAT/stBFOldp9kyDeSyCFzxXOy2srUwgZIGA1UdHwSBijCBhzCBhKAuoCyGKmh0dHA6Ly9jcmwudmlldHRlbC1jYS52bi9WaWV0dGVsLUNBLXYzLmNybKJSpFAwTjETMBEGA1UEAwwKVmlldHRlbC1DQTEWMBQGA1UECgwNVmlldHRlbCBHcm91cDESMBAGA1UEBwwJSMOgIE7hu5lpMQswCQYDVQQGEwJWTjAOBgNVHQ8BAf8EBAMCBeAwDQYJKoZIhvcNAQEFBQADggEBAJGwolmW8aFUp7cViSErVDxhMfgB6mOd5bW+jBphpULpezPJ7vNteuKjKhtGVOkGuwOyuCKR2IK2uRNlMGi6kE9jUV5W4R5/DVM5oFRmTgs9Q7W1Sy/RytUyJXVtvehDY2hwS3YhtfWJ57Cw0zmPj28a7vgOy7Pzbx7YAoR2UTrP5gmVuyIAFJ1r+r0BNDcyK8uHeq29h6hKXuRc5K8kUZ3cnIl7WeNuLCWULB+k5DpxpajDSvSJR7rZlgvg4i64p3lsvSucndM9iD1vEE03VEMYIMZEWh6LYvQ7f/Ah9V98MTdkRN2CpmtptrMsBDzb6+UDzrE0rqFyZFfICsaGrZ4="
    }
}

VI.5. API xác thực chữ ký số với dữ liệu raw
Mô tả chung

Mô tả: Xác thực tính hợp lệ của chữ ký số với dữ liệu raw ban đầu.

Yêu cầu xác thực: Có

API: /api/verification/raw

Method: POST

Header: ContentType: Application/json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên                | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|--------------------|--------------|----------|-----------------------------------------|
| 1   | elements         | Object List| x      | Phần tử chứa những dữ liệu cần xác thực |
| 1.1 | base64Signature  | String     | x      | Base64 của nội dung chữ ký số           |
| 1.2 | base64OriginalData| String     | x      | Base64 của nội dung dữ liệu đầu vào     |
| 1.3 | key              | Object     |          | Định danh dữ liệu                       |
| 2   | hashAlgorithm    | String     |          | Kiểu băm (Mặc định: SHA1 – Giá trị nhận: SHA1, SHA256, SHA512) |
| 3   | serial           | String     | x      | Thông tin Serial chứng thư số (CTS)     |

Ví dụ đầu vào:

{
    "elements": [
        {
            "base64Signature": "Fwhk1T3+nLw96toAj51DJjAyNv2bh8N9Ssi3AcaU0ve7FycvcPSJGrTK5tZZxaeBtWZxS+mlADBRXMeNFihEo5ooomB92BV855QcDMdgXyNDQ1l7LKOXZXioijjNKr/olkfZm+UTbxz04+HSN4jdfWRH/lm8ayDp/gjMO5740Yg=",
            "key": "123",
            "base64OriginalData": "a2trMg=="
        }
    ],
    "hashAlgorithm": "SHA1",
    "serial": "5404fffeb7033fb316d672201b80d398"
}

Thuộc tính đầu ra
| STT | Tên          | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|--------------|--------------|----------|-----------------------------------------|
| 1   | status     | int        | x      | Trạng thái xử lý                        |
| 2   | msg        | String     |          | Thông tin chi tiết trạng thái           |
| 3   | data       | Object     |          | Phần tử chứa thông tin xác thực trả về |
| 3.1 | elements   | Object List|          | Phần tử chứa danh sách xác thực        |
| 3.1.1 | key      | String     |          | Định danh dữ liệu ký số                 |
| 3.1.2 | result   | Boolean    |          | Trạng thái xác thực dữ liệu theo key tương ứng (true: thành công – false: thất bại) |
| 3.2 | certificate| String     |          | Base64 của chứng thư số                 |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": null,
    "data": {
        "certificate": "MIIEGzCCAwOgAwIBAgIQVAT//rcDP7MW1nIgG4DTmDANBgkqhkiG9w0BAQUFADBOMQswCQYDVQQGEwJWTjESMBAGA1UEBwwJSMOgIE7hu5lpMRYwFAYDVQQKEw1WaWV0dGVsIEdyb3VwMRMwEQYDVQQDEwpWaWV0dGVsLUNBMB4XDTIwMDMwNTA4MzI1OVoXDTIxMDMwNTA4MzI1OVowgZYxHjAcBgoJkiaJk/IsZAEBDA5NU1Q6MDEwNTk4NzQzMjFTMFEGA1UEAwxKQ8OUTkcgVFkgQ+G7lCBQSOG6pk4gxJDhuqZVIFTGryBDw5RORyBOR0jhu4YgVsOAIFRIxq/GoE5HIE3huqBJIFNPRlREUkVBTVMxEjAQBgNVBAcMCUjDgCBO4buYSTELMAkGA1UEBhMCVk4wgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBAN45MIHLnjVj1vpa4WJ2tYtyxDrIwkREjDqTaKgGPryrxxcipfQpZdqCSWIkPUf6K44I5jcK8s1YeoC6hADjVKrpsz8baQz/dBSYy5oJxdiMweTJQq9QlbNw+kx15W9aEi2k2DOb+i3yBTQDkc+u/ylLGF5F7njajQhoKR8PxuvXAgMBAAGjggEuMIIBKjA1BggrBgEFBQcBAQQpMCcwJQYIKwYBBQUHMAGGGWh0dHA6Ly9vY3NwLnZpZXR0ZWwtY2Eudm4wHQYDVR0OBBYEFAz4ANgRsBDOZXFKGBWg0tOa8nHSMAwGA1UdEwEB/wQCMAAwHwYDVR0jBBgwFoAU/stBFOldp9kyDeSyCFzxXOy2srUwgZIGA1UdHwSBijCBhzCBhKAuoCyGKmh0dHA6Ly9jcmwudmlldHRlbC1jYS52bi9WaWV0dGVsLUNBLXYzLmNybKJSpFAwTjETMBEGA1UEAwwKVmlldHRlbC1DQTEWMBQGA1UECgwNVmlldHRlbCBHcm91cDESMBAGA1UEBwwJSMOgIE7hu5lpMQswCQYDVQQGEwJWTjAOBgNVHQ8BAf8EBAMCBeAwDQYJKoZIhvcNAQEFBQADggEBAJGwolmW8aFUp7cViSErVDxhMfgB6mOd5bW+jBphpULpezPJ7vNteuKjKhtGVOkGuwOyuCKR2IK2uRNlMGi6kE9jUV5W4R5/DVM5oFRmTgs9Q7W1Sy/RytUyJXVtvehDY2hwS3YhtfWJ57Cw0zmPj28a7vgOy7Pzbx7YAoR2UTrP5gmVuyIAFJ1r+r0BNDcyK8uHeq29h6hKXuRc5K8kUZ3cnIl7WeNuLCWULB+k5DpxpajDSvSJR7rZlgvg4i64p3lsvSucndM9iD1vEE03VEMYIMZEWh6LYvQ7f/Ah9V98MTdkRN2CpmtptrMsBDzb6+UDzrE0rqFyZFfICsaGrZ4=",
        "elements": [
            {
                "key": "123",
                "result": false
            }
        ]
    }
}

VI.6. API xác thực chữ ký số với dữ liệu hash
Mô tả chung

Mô tả: Xác thực tính hợp lệ của chữ ký số với dữ liệu raw ban đầu.

Yêu cầu xác thực: Có

API: /api/verification/hash

Method: POST

Header: ContentType: Application/json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên                | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|--------------------|--------------|----------|-----------------------------------------|
| 1   | elements         | Object List| x      | Phần tử chứa những dữ liệu cần xác thực |
| 1.1 | base64Signature  | String     | x      | Base64 của nội dung chữ ký số           |
| 1.2 | base64OriginalData| String     | x      | Base64 của nội dung dữ liệu đầu vào     |
| 1.3 | key              | Object     |          | Định danh dữ liệu                       |
| 2   | hashAlgorithm    | String     |          | Kiểu băm (Mặc định: SHA1 – Giá trị nhận: SHA1, SHA256, SHA512) |
| 3   | serial           | String     | x      | Thông tin Serial chứng thư số (CTS)     |

Ví dụ đầu vào:

{
    "elements": [
        {
            "base64Signature": "Fwhk1T3+nLw96toAj51DJjAyNv2bh8N9Ssi3AcaU0ve7FycvcPSJGrTK5tZZxaeBtWZxS+mlADBRXMeNFihEo5ooomB92BV855QcDMdgXyNDQ1l7LKOXZXioijjNKr/olkfZm+UTbxz04+HSN4jdfWRH/lm8ayDp/gjMO5740Yg=",
            "key": "123",
            "base64OriginalData": "a2trMg=="
        }
    ],
    "hashAlgorithm": "SHA1",
    "serial": "5404fffeb7033fb316d672201b80d398"
}

Thuộc tính đầu ra
| STT | Tên          | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|--------------|--------------|----------|-----------------------------------------|
| 1   | status     | int        | x      | Trạng thái xử lý                        |
| 2   | msg        | String     |          | Thông tin chi tiết trạng thái           |
| 3   | data       | Object     |          | Phần tử chứa thông tin xác thực trả về |
| 3.1 | elements   | Object List|          | Phần tử chứa danh sách xác thực        |
| 3.1.1 | key      | String     |          | Định danh dữ liệu ký số                 |
| 3.1.2 | result   | Boolean    |          | Trạng thái xác thực dữ liệu theo key tương ứng (true: thành công – false: thất bại) |
| 3.2 | certificate| String     |          | Base64 của chứng thư số                 |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": null,
    "data": {
        "certificate": "MIIEGzCCAwOgAwIBAgIQVAT//rcDP7MW1nIgG4DTmDANBgkqhkiG9w0BAQUFADBOMQswCQYDVQQGEwJWTjESMBAGA1UEBwwJSMOgIE7hu5lpMRYwFAYDVQQKEw1WaWV0dGVsIEdyb3VwMRMwEQYDVQQDEwpWaWV0dGVsLUNBMB4XDTIwMDMwNTA4MzI1OVoXDTIxMDMwNTA4MzI1OVowgZYxHjAcBgoJkiaJk/IsZAEBDA5NU1Q6MDEwNTk4NzQzMjFTMFEGA1UEAwxKQ8OUTkcgVFkgQ+G7lCBQSOG6pk4gxJDhuqZVIFTGryBDw5RORyBOR0jhu4YgVsOAIFRIxq/GoE5HIE3huqBJIFNPRlREUkVBTVMxEjAQBgNVBAcMCUjDgCBO4buYSTELMAkGA1UEBhMCVk4wgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBAN45MIHLnjVj1vpa4WJ2tYtyxDrIwkREjDqTaKgGPryrxxcipfQpZdqCSWIkPUf6K44I5jcK8s1YeoC6hADjVKrpsz8baQz/dBSYy5oJxdiMweTJQq9QlbNw+kx15W9aEi2k2DOb+i3yBTQDkc+u/ylLGF5F7njajQhoKR8PxuvXAgMBAAGjggEuMIIBKjA1BggrBgEFBQcBAQQpMCcwJQYIKwYBBQUHMAGGGWh0dHA6Ly9vY3NwLnZpZXR0ZWwtY2Eudm4wHQYDVR0OBBYEFAz4ANgRsBDOZXFKGBWg0tOa8nHSMAwGA1UdEwEB/wQCMAAwHwYDVR0jBBgwFoAU/stBFOldp9kyDeSyCFzxXOy2srUwgZIGA1UdHwSBijCBhzCBhKAuoCyGKmh0dHA6Ly9jcmwudmlldHRlbC1jYS52bi9WaWV0dGVsLUNBLXYzLmNybKJSpFAwTjETMBEGA1UEAwwKVmlldHRlbC1DQTEWMBQGA1UECgwNVmlldHRlbCBHcm91cDESMBAGA1UEBwwJSMOgIE7hu5lpMQswCQYDVQQGEwJWTjAOBgNVHQ8BAf8EBAMCBeAwDQYJKoZIhvcNAQEFBQADggEBAJGwolmW8aFUp7cViSErVDxhMfgB6mOd5bW+jBphpULpezPJ7vNteuKjKhtGVOkGuwOyuCKR2IK2uRNlMGi6kE9jUV5W4R5/DVM5oFRmTgs9Q7W1Sy/RytUyJXVtvehDY2hwS3YhtfWJ57Cw0zmPj28a7vgOy7Pzbx7YAoR2UTrP5gmVuyIAFJ1r+r0BNDcyK8uHeq29h6hKXuRc5K8kUZ3cnIl7WeNuLCWULB+k5DpxpajDSvSJR7rZlgvg4i64p3lsvSucndM9iD1vEE03VEMYIMZEWh6LYvQ7f/Ah9V98MTdkRN2CpmtptrMsBDzb6+UDzrE0rqFyZFfICsaGrZ4=",
        "elements": [
            {
                "key": "123",
                "result": false
            }
        ]
    }
}

VI.7. API Lấy ảnh chữ ký số
Mô tả chung

Mô tả: Lấy hình ảnh chữ ký số.

Yêu cầu xác thực: Có

API: /api/certificate/getImage

Method: GET

Header: ContentType: Application/json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên        | Kiểu dữ liệu | Bắt buộc | Mô tả                                                               |
|-----|------------|--------------|----------|---------------------------------------------------------------------|
| 1   | serial   | String     | x      | Thông tin Serial chứng thư số (CTS)                                |
| 2   | pin      | String     | x      | Mã pin truy cập CTS                                                 |
| 3   | position | String     | x | o    | Thông tin truyền động chức vụ. Để trống api sẽ build theo parser đã setup trước đó |
| 4   | timeSign | String     | x | o    | Thông tin truyền động thời gian ký. Để trống api sẽ build theo parser đã setup trước đó |

Ví dụ đầu vào (URL):
/api/certificate/getImage?serial=5401100041302CAD2FBEF0D8D64B7161&pin=12345678

Thuộc tính đầu ra
| STT | Tên    | Kiểu dữ liệu | Bắt buộc | Mô tả                                |
|-----|--------|--------------|----------|--------------------------------------|
| 1   | status | int        | x      | Trạng thái xử lý                     |
| 2   | msg    | String     |          | Thông tin chi tiết trạng thái        |
| 3   | data   | String     |          | Dữ liệu base64 hình ảnh chữ ký số    |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": null,
    "data": "iVBORw0KGgoAAAANSUhEUgAAAZAAAACWCAIAAAB/…"
}

VI.8. API ký số dữ liệu PDF (ký hiện vị trí)
Mô tả chung

Mô tả: Ký số với dữ liệu PDF với tính năng ký hiện vị trí ảnh chữ ký với vị trí do mình tùy chọn trên hệ thống EasySign.

Yêu cầu xác thực: Có

API: /api/sign/pdf

Method: POST

Header: ContentType: Application/json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên                    | Kiểu dữ liệu | Bắt buộc | Mô tả                                                               |
|-----|------------------------|--------------|----------|---------------------------------------------------------------------|
| 1   | signingRequestContents| Object List| x      | Phần tử chứa những dữ liệu cần ký                                  |
| 1.1 | data                 | String     | x      | Nội dung cần ký đã được encode base64                               |
| 1.2 | documentName         | String     | x | o    | Tên định danh nội dung                                              |
| 1.3 | location             | object     | x | o    | Vị trí ký                                                           |
| 1.3.1 | visibleX           | Int        | x | o    | Tọa độ x                                                            |
| 1.3.2 | visibleY           | Int        | x | o    | Tọa độ y                                                            |
| 1.3.3 | visibleWidth       | Int        | x | o    | Kích thước chiều rộng ảnh chữ ký hiển thị (default = 150)           |
| 1.3.4 | visibleHeight      | Int        | x | o    | Kích thước chiều cao ảnh chữ ký hiện thị (default = 50)             |
| 1.4 | extraInfo            | object     | x | o    | Thông tin trang ký                                                  |
| 1.4.1 | pageNum            | Int        | x | o    | Trang ký đặt chữ ký, default = page 1                               |
| 1.5 | imageSignature       | String (base64)| o      | ảnh chữ ký dạng base64                                              |
| 2   | optional             | Object     |          | Phần tử chứa những tùy chọn ký số                                  |
| 2.1 | hashAlgorithm        | String     | o      | Kiểu băm (Mặc định: SHA1 – Giá trị nhận: SHA1, SHA256, SHA512)      |
| 2.2 | returnInputData      | Boolean    | o      | Trả về nội dung đầu vào (Mặc định: false)                           |
| 2.3 | signAlgorithm        | String     | o      | Kiểu giải thuật ký số (Mặc định: RSA)                               |
| 2.4 | otpCode              | String     | o      | Mã OTP (nếu hệ thống có bật tính năng OTP)                          |
| 3   | tokenInfo            | Object     | x      | Phần tử chứa thông tin chứng thư ký số                              |
| 3.1 | serial               | String     | x      | Thông tin Serial chứng thư số (CTS)                                |
| 3.2 | pin                  | String     | x      | Mã pin truy cập CTS                                                 |

Ví dụ đầu vào:

{
    "signingRequestContents": [
        {
            "data": "JVBERi0xLjQNCiWqq6ytDQoxIDAgb2JqDQo8PA0KL1BhZ2VzIDIgMCBSDQovUGFnZUxheW91dCAvT25lQ29sdW1uDQovVHlwZSAvQ2F0YW...................",
            "documentName": "123",
            "location": {
                "visibleX": 355,
                "visibleY": 315,
                "visibleWidth": 266,
                "visibleHeight": 98
            },
            "extraInfo": {
                "pageNum": 1
            },
            "imageSignature": "iVBORw0KGgoAAAANSUhEUgAAAQQAAAB7CAIAAAAdaWn9AAAACXBIWXMAAC4jAAAuIwF4pT92AAAF92lUW.........."
        }
    ],
    "tokenInfo": {
        "pin": "363203",
        "serial": "54011000693fbda9f7dc411570f82f79"
    },
    "optional": {
        "otpCode": "037856"
    }
}

Thuộc tính đầu ra
| STT | Tên    | Kiểu dữ liệu | Bắt buộc | Mô tả                                |
|-----|--------|--------------|----------|--------------------------------------|
| 1   | status | int        | x      | Trạng thái xử lý                     |
| 2   | msg    | String     |          | Thông tin chi tiết trạng thái        |
| 3   | data   | Object     |          | Phần tử chứa thông tin ký số         |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": "Ký tệp pdf thành công",
    "data": "JVBERi0xLjQNCiWqq6ytDQoxIDAgb2JqDQo8PA0KL1BhZ2VzIDIgMCBSDQovUGFnZUxheW91dCAvT25lQ29sdW1uDQovVHlwZSAvQ2F0YWxvZ…."
}

VI.9. API verify PDF (Sau khi kí thành công)
Mô tả chung

Mô tả: Xác minh chữ kí PDF.

Yêu cầu xác thực: Có

API: /api/verification/pdf

Method: POST

Header: ContentType: Application/json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Mô tả                                                                                                  |
|-----|--------------------------------------------------------------------------------------------------------|
| 1   | Truyền vào file PDF đã ký ở Body, mục form-data. Chọn key dạng file, với giá trị của KEY là “file”, giá trị của VALUE là file PDF đã ký. |

Thuộc tính đầu ra
| STT | Tên                  | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|----------------------|--------------|----------|-----------------------------------------|
| 1   | status             | int        | x      | Trạng thái xử lý                        |
| 2   | msg                | String     |          | Thông tin chi tiết trạng thái           |
| 3   | data               | Object     |          | Phần tử chứa thông tin ký số            |
| 3.1 | signatureVfDTOs    | Object List|          | Phần tử chứa danh sách dữ liệu ký số    |
| 3.1.1 | coverWholeDocument| Boolean    |          | Trạng thái toàn vẹn nội dung văn bản   |
| 3.1.2 | revision         | int        |          | Số thứ tự của chữ kí trong văn bản      |
| 3.1.3 | totalRevision    | int        |          | Tổng số chữ ký có trong văn bản         |
| 3.1.4 | signTime         | Date       |          | Thời gian ký số dữ liệu                 |
| 3.1.5 | certificateVfDTOs| Object List|          | Phần tử chứa danh sách dữ liệu chứng thư số |
| 3.1.5.1 | issuer         | String     |          | Thông tin về tổ chức phát hành chữ ký   |
| 3.1.5.2 | subjectDn      | String     |          | Thông tin về đối tượng sử dụng chữ ký số |
| 3.1.5.3 | validFrom      | Date       |          | Ngày phát hành chứng thư số             |
| 3.1.5.4 | validTo        | Date       |          | Ngày hết hạn chứng thư số               |
| 3.1.5.5 | revocationStatus| String     |          | Thông tin về trạng thái thu hồi của chứng thư số |
| 3.1.5.6 | signTimeStatus | String     |          | Thông tin về trạng thái thời gian ký số |
| 3.1.5.7 | currentStatus  | String     |          | Thông tin về trạng thái hiệu lực của chứng thư số |
| 3.1.5.8 | easyCACert     | Boolean    |          | Xác thực chứng thư số của tổ chức cung cấp |
| 3.1.6 | integrity        | Boolean    |          | Trạng thái toàn vẹn của ký số dữ liệu   |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": null,
    "data": {
        "signatureVfDTOs": [
            {
                "coverWholeDocument": true,
                "revision": 1,
                "totalRevision": 1,
                "signTime": "11:15:53 02/11/2022",
                "certificateVfDTOs": [
                    {
                        "issuer": "C=VN,O=SOFTDREAMS TECHNOLOGY INVESTMENT AND TRADING JSC,OU=EASYCA,CN=EASYCA",
                        "subjectDn": "C=VN,ST=TP Hồ Chí Minh,O=Bệnh viện Quận 11,T=BS CK II,CN=Phạm Quốc Dũng\\, A101.0001 - 003848/HCM-CCHN,UID=MST:0301824642,UID=CMND:079073009568",
                        "validFrom": "07:33:00 19/11/2020",
                        "validTo": "07:33:00 19/11/2023",
                        "revocationStatus": "REVOKED",
                        "signTimeStatus": "VALID",
                        "currentStatus": "VALID",
                        "easyCACert": true
                    }
                ],
                "integrity": true
            }
        ]
    }
}

VI.10. API Change Certificate Pin
Mô tả chung

Mô tả: Thay đổi pin certificate (serial) cho người dùng.

Yêu cầu xác thực: Có

API: /api/certificate/changeCertPIN

Method: POST

Header: ContentType: Application/json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên      | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|----------|--------------|----------|-----------------------------------------|
| 1   | serial   | String     | x      | Thông tin Serial chứng thư số (CTS)     |
| 2   | newPin   | String     | x      | Mã pin mới muốn đổi cho certificate     |
| 3   | oldPin   | String     | x      | Mã pin hiện tại của certificate muốn sửa |
| 4   | otpCode  | String     | o      | Mã OTP (nếu hệ thống có bật tính năng OTP) |

Ví dụ đầu vào:

{
    "serial": "540110000b4525650231e39369660895",
    "newPIN": "12345678",
    "oldPIN": "079073009568",
    "otpCode": ""
}

Thuộc tính đầu ra
| STT | Tên    | Kiểu dữ liệu | Bắt buộc | Mô tả                                |
|-----|--------|--------------|----------|--------------------------------------|
| 1   | status | int        | x      | Trạng thái xử lý                     |
| 2   | msg    | String     |          | Thông tin chi tiết trạng thái        |
| 3   | data   | Object     |          | Phần tử chứa thông tin ký số         |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": "Change Certificate PIN successfully!",
    "data": null
}

VI.11. API ký số dữ liệu XML
Mô tả chung

Mô tả: Ký số với dữ liệu XML với tính năng ký file XML và xác thực otpCode.

Yêu cầu xác thực: Có

API: /api/sign/xml

Method: POST

Header: ContentType: Application/json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên                    | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|------------------------|--------------|----------|-----------------------------------------|
| 1   | signingRequestContents| Object List| x      | Phần tử chứa những dữ liệu cần ký       |
| 1.1 | data                 | String     | x      | Nội dung cần ký đã được encode base64   |
| 1.2 | documentName         | String     | x | o    | Tên định danh nội dung                  |
| 2   | optional             | Object     |          | Phần tử chứa những tùy chọn ký số       |
| 2.1 | otpCode              | String     | o      | Mã OTP (nếu hệ thống có bật tính năng OTP) |
| 3   | tokenInfo            | Object     | x      | Phần tử chứa thông tin chứng thư ký số  |
| 3.1 | serial               | String     | x      | Thông tin Serial chứng thư số (CTS)     |
| 3.2 | pin                  | String     | x      | Mã pin truy cập CTS                     |

Ví dụ đầu vào:

{
    "signingRequestContents": [
        {
            "data": "PERhdGE+DQoJPENvbnRlbnQgSWQ9IlNpZ25pbmdEYXRhIj4NCgl7DQogICAgICAgICJzdWIiOiAiYnZuaGlkb25nIiwNCiAgICAgICAgImF1dGgiOiAiUk9MRV9BRE1JTiINCiAgICB9DQoJPC9Db250ZW50Pg0KPC9EYXRhPg==",
            "documentName": "123"
        }
    ],
    "tokenInfo": {
        "pin": "582989",
        "serial": "5401100029c95b56bfea831713cee000"
    },
    "optional": {
        "otpCode": "037856"
    }
}

Lưu ý: File Xml cần được đặt ID cho thẻ cần ký trước khi thực hiện base64.
Vd: cần ký thẻ Root

Thuộc tính đầu ra
| STT | Tên                  | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|----------------------|--------------|----------|-----------------------------------------|
| 1   | status             | int        | x      | Trạng thái xử lý                        |
| 2   | msg                | String     |          | Thông tin chi tiết trạng thái           |
| 3   | data               | Object     |          | Phần tử chứa thông tin ký số            |
| 3.1 | responseContentList| Object List|          | Phần tử chứa danh sách dữ liệu ký số    |
| 3.1.1 | documentName     | String     |          | Định danh dữ liệu ký số                 |
| 3.1.2 | signatureOnly    | String     |          | Chữ ký số tương ứng dữ liệu, dưới dạng base64 |
| 3.1.3 | signedDocument   | String     |          | Base64 tệp PDF đầu vào kèm chữ ký số    |
| 3.2 | base64Certificate  | String     |          | Base64 của chứng thư số                 |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": null,
    "data": {
        "responseContentList": [
            {
                "documentName": "123",
                "signatureOnly": null,
                "signedDocument": "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"
            }
        ],
        "base64Certificate": "MIIFVzCCBD+gAwIBAgIQVAEQACnJW1a/6oMXE87gADANBgkqhkiG9w0BAQsFADBqMQswCQYDVQQGEwJWTjE5MDcGA1UECgwwU09GVERSRUFNUyBURUNITk9MT0dZIElOVkVTVE1FTlQgQU5EIFRSQURJTkcgSlNDMQ8wDQYDVQQLDAZFQVNZQ0ExDzANBgNVBAMMBkVBU1lDQTAeFw0yMDEyMjMwNzQ4MTdaFw0yMTEyMDUxMDI2MDBaMIHqMQswCQYDVQQGEwJWTjEaMBgGA1UECAwRVFAgSOG7kyBDaMOtIE1pbmgxIDAeBgNVBAoMF0Lhu4duaCB2aeG7h24gUXXhuq1uIDExMRMwEQYDVQQLDApLaG9hIE3huq90MQwwCgYDVQQMDANCUy4xNzA1BgNVBAMMLkjhu5MgUXXhu5FjIFRow6FpLCBBMjE3LjA0NTggLSAwMzE4MjYvSENNLUNDSE4xHjAcBgoJkiaJk/IsZAEBDA5NU1Q6MDMwMTgyNDY0MjEhMB8GCgmSJomT8ixkAQEMEUNDQ0Q6MDg3MDgyMDAwMzIxMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtvWQ7Xmk473x9kcdxKShWc90fff97GVo6AdsgL0hF357ANI540iUgCRU1DvHNg0A6kEP5/nPHN2qWEunroj81+L/4t/N6aKdHiGDjIQ2BEhFY6lqYHea6ZS5L8jM9a+SK+urnYe871pbxwJjBUaR1IaEgGlCFELm0m8dLX5HHcwB79i8FS2OdFsx0o7n1AD3TCcaugTva2PZ+aD1rxxbCS3ygqMK2UxjBmtkvbzpUEOf0pgHkHffqLiBaBICH4GlARO2Thdekhz80zOBsXGbC2EIB/Gmijgd70MCJO3hfnorVZnIXkpfA+dhp/McUp3UGaKke1Adyl9StgXiZvFrpQIDAQABo4IBdjCCAXIwDAYDVR0TAQH/BAIwADAfBgNVHSMEGDAWgBRft+dSEFAWYjLfbc7CrXhLEdATNjA2BggrBgEFBQcBAQQqMCgwJgYIKwYBBQUHMAGGGmh0dHA6Ly9vY3NwLmVhc3ljYS52bi9vY3NwMDQGA1UdJQQtMCsGCCsGAQUFBwMCBggrBgEFBQcDBAYKKwYBBAGCNwoDDAYJKoZIhvcvAQEFMIGjBgNVHR8EgZswgZgwgZWgI6Ahhh9odHRwOi8vY3JsLmVhc3ljYS52bi9lYXN5Y2EuY3Jsom6kbDBqMQ8wDQYDVQQDDAZFQVNZQ0ExDzANBgNVBAsMBkVBU1lDQTE5MDcGA1UECgwwU09GVERSRUFNUyBURUNITk9MT0dZIElOVkVTVE1FTlQgQU5EIFRSQURJTkcgSlNDMQswCQYDVQQGEwJWTjAdBgNVHQ4EFgQUvHhdIlHPuUZ8Y9y2xW9IiyCZEU0wDgYDVR0PAQH/BAQDAgTwMA0GCSqGSIb3DQEBCwUAA4IBAQAgEFemPIvgl28jHDXPJEEWGol+VeV28dM1gwz8tQqpQLPzF1zQBlMWO9cTDd78Rj5sDnvJ7gL7nNgRHKEr+UfhndWeqa0BcvkYVM3/yjJZhNr93DeAttO2kawGv8kbhDpRJUZfdsEXu+I+JSgy4+m5BRLGkDrdrvrk5NsoKEgJrUWJkK3NvPWobZgiayX0+e9sJ1NG3OV+ZuRe0bM2MfNpY4qCiS66OsGwQ6LFcVm61ahFqSdPuXUEQR51hGqvhNNVfyslI8o6mygtZe6Dp0Qp05n4SeIFYFOKwiTp98UE26ffFvbxsRi8l9eZ3UTjPO08mxHiMwQ4RLaXl0HshZBf"
    }
}

VI.12. API reset mã pin chứng thư số
Mô tả chung

Mô tả: Reset mã pin chứng thư số không yêu cầu mã pin cũ.

Yêu cầu xác thực: Có

API: /api/certificate/resetHsmCertPin

Method: POST

Header: Authorization: “Bearer” + id_token

Thuộc tính đầu vào dưới dạng Request Param
| STT | Tên        | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|------------|--------------|----------|-----------------------------------------|
| 1   | Serial   | string     | x      | Serial của chứng thư số cần reset pin   |
| 2   | masterKey| string     | x      | Key bí mật được cấp riêng               |

Thuộc tính đầu ra
| STT | Tên    | Kiểu dữ liệu | Bắt buộc | Mô tả                                |
|-----|--------|--------------|----------|--------------------------------------|
| 1   | status | int        | x      | Trạng thái xử lý                     |
| 2   | msg    | string     | o      | Thông tin lỗi                        |
| 3   | data   | string     | x      | Mã pin mới cho chứng thư số          |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": null,
    "data": "260650"
}

VI.13. API lấy thông tin chứng thư số bằng serial
Mô tả chung

Mô tả: Lấy thông tin chứng thư số bằng serial và pin.

Yêu cầu xác thực: Có

API: /api/certificate/get

Method: GET

Header: ContentType: Application/json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên    | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|--------|--------------|----------|-----------------------------------------|
| 1   | pin    | String     | x      | Mã pin của chứng thư số cần lấy thông tin |
| 2   | serial | String     | x      | Serial của chứng thư số cần lấy         |

Thuộc tính đầu ra
| STT | Tên    | Kiểu dữ liệu | Bắt buộc | Mô tả                                |
|-----|--------|--------------|----------|--------------------------------------|
| 1   | status | int        | x      | Trạng thái xử lý                     |
| 2   | msg    | String     |          | Thông tin chi tiết trạng thái        |
| 3   | data   | String     |          | Base64 của chứng thư số được tìm thấy |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": null,
    "data": "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"
}

VI.14. API Xác thực chữ ký số File XML đã ký.
Mô tả chung

Mô tả: Xác thực tính hợp lệ chữ ký số đã ký lên file XML.

Yêu cầu xác thực: Có

API: /api/verification/xml

Method: POST

Header: ContentType: Multipart/form-data, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên  | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|------|--------------|----------|-----------------------------------------|
| 1   | file | file       | x      | File XML đã ký cần xác thực chữ ký số   |

Thuộc tính đầu ra
| STT | Tên                  | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|----------------------|--------------|----------|-----------------------------------------|
| 1   | status             | int        | x      | Trạng thái xử lý                        |
| 2   | msg                | String     |          | Thông tin chi tiết trạng thái           |
| 3   | data               | Object     |          | Phần tử chứa thông tin ký số            |
| 3.1 | signatureVfDTOs    | Object List|          | Phần tử chứa danh sách dữ liệu ký số    |
| 3.1.1 | coverWholeDocument| Boolean    |          | Trạng thái toàn vẹn nội dung văn bản   |
| 3.1.2 | revision         | int        |          | Số thứ tự của chữ kí trong văn bản      |
| 3.1.3 | totalRevision    | int        |          | Tổng số chữ ký có trong văn bản         |
| 3.1.4 | signTime         | Date       |          | Thời gian ký số dữ liệu                 |
| 3.1.5 | certificateVfDTOs| Object List|          | Phần tử chứa danh sách dữ liệu chứng thư số |
| 3.1.5.1 | issuer         | String     |          | Thông tin về tổ chức phát hành chữ ký   |
| 3.1.5.2 | subjectDn      | String     |          | Thông tin về đối tượng sử dụng chữ ký số |
| 3.1.5.3 | validFrom      | Date       |          | Ngày phát hành chứng thư số             |
| 3.1.5.4 | validTo        | Date       |          | Ngày hết hạn chứng thư số               |
| 3.1.5.5 | revocationStatus| String     |          | Thông tin về trạng thái thu hồi của chứng thư số |
| 3.1.5.6 | signTimeStatus | String     |          | Thông tin về trạng thái thời gian ký số |
| 3.1.5.7 | currentStatus  | String     |          | Thông tin về trạng thái hiệu lực của chứng thư số |
| 3.1.5.8 | easyCACert     | Boolean    |          | Xác thực chứng thư số của tổ chức cung cấp |
| 3.1.6 | integrity        | Boolean    |          | Trạng thái toàn vẹn của ký số dữ liệu   |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": null,
    "data": {
        "signatureVfDTOs": [
            {
                "coverWholeDocument": false,
                "revision": 0,
                "totalRevision": 0,
                "signTime": null,
                "certificateVfDTOs": [
                    {
                        "issuer": "CN=EASYCA",
                        "subjectDn": "UID=CMND:121766134, UID=MST:0105987432, CN=\"Nguyễn Văn Test, NV000418 - 0190A7/HCM-CCHN\", T=BS, OU=Xét nghiệm, O=CÔNG TY CỔ PHẦN ĐẦU TƯ CÔNG NGHỆ VÀ THƯƠNG MẠI TEST, ST=Hà Nội, C=VN",
                        "validFrom": "01:20:54 11/04/2022",
                        "validTo": "01:20:54 11/04/2023",
                        "revocationStatus": "UNCHECKED",
                        "signTimeStatus": "VALID",
                        "currentStatus": "VALID",
                        "easyCACert": false
                    }
                ],
                "integrity": true
            },
            {
                "coverWholeDocument": false,
                "revision": 0,
                "totalRevision": 0,
                "signTime": null,
                "certificateVfDTOs": [
                    {
                        "issuer": "CN=EASYCA",
                        "subjectDn": "UID=CMND:121766134, UID=MST:0105987432, CN=\"Nguyễn Văn Test, NV000418 - 0190A7/HCM-CCHN\", T=BS, OU=Xét nghiệm, O=CÔNG TY CỔ PHẦN ĐẦU TƯ CÔNG NGHỆ VÀ THƯƠNG MẠI TEST, ST=Hà Nội, C=VN",
                        "validFrom": "01:20:54 11/04/2022",
                        "validTo": "01:20:54 11/04/2023",
                        "revocationStatus": "UNCHECKED",
                        "signTimeStatus": "VALID",
                        "currentStatus": "VALID",
                        "easyCACert": false
                    }
                ],
                "integrity": true
            },
            {
                "coverWholeDocument": false,
                "revision": 0,
                "totalRevision": 0,
                "signTime": null,
                "certificateVfDTOs": [
                    {
                        "issuer": "CN=EASYCA, OU=EASYCA, O=SOFTDREAMS TECHNOLOGY INVESTMENT AND TRADING JSC, C=VN",
                        "subjectDn": "UID=CMND:079073009568, UID=MST:0301824642, CN=\"Phạm Quốc Dũng, A101.0001 - 003848/HCM-CCHN\", T=BS CK II, O=Bệnh viện Quận 11, ST=TP Hồ Chí Minh, C=VN",
                        "validFrom": "07:33:00 19/11/2020",
                        "validTo": "07:33:00 19/11/2023",
                        "revocationStatus": "REVOKED",
                        "signTimeStatus": "VALID",
                        "currentStatus": "VALID",
                        "easyCACert": false
                    }
                ],
                "integrity": true
            }
        ]
    }
}

VI.15. API Lấy danh sách mẫu chữ ký của username.
Mô tả chung

Mô tả: Lấy danh sách mẫu chữ ký theo username truyền vào.

Yêu cầu xác thực: Có

API: /api/signature-templates/

Method: GET

Header: ContentType: Application_Json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên        | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|------------|--------------|----------|-----------------------------------------|
| 1   | username | String     | x      | tên username của người cần lấy danh sách mẫu chữ ký |

Ví dụ đầu vào (URL):
/api/signature-templates/getByUserName?username=demo_easysign

Thuộc tính đầu ra
| STT | Tên            | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|----------------|--------------|----------|-----------------------------------------|
| 1   | status       | int        | x      | Trạng thái xử lý                        |
| 2   | msg          | String     |          | Thông tin chi tiết trạng thái           |
| 3   | data         | Object     |          | Phần tử chứa thông tin ký số            |
| 3.1 | id           | int        |          | Mã định danh mẫu chữ ký                 |
| 3.2 | userId       | int        |          | Mã định danh của user lấy danh sách mẫu chữ ký |
| 3.3 | createdBy    | string     |          | username của tài khoản tạo mẫu chữ ký   |
| 3.4 | createdDate  | Date       |          | Thời gian tạo mẫu chữ ký                |
| 3.5 | coreParser   | String     |          | Tên trình tạo thông tin trên mẫu chữ ký |
| 3.6 | fullName     | String     |          | Họ tên của chủ sở hữu mẫu chữ ký (Họ tên của tài khoản có username truyền vào) |
| 3.7 | width        | int        |          | Chiều rộng của mẫu chữ ký (đơn vị: pixel) |
| 3.8 | height       | int        |          | Chiều cao của mẫu chữ ký (đơn vị: pixel) |
| 3.9 | htmlTemplate | String     |          | Mẫu HTML của ảnh chữ ký                 |
| 3.10 | transparency| boolean    |          | Xác định mẫu chữ ký có thuộc dạng xóa phông hay không |
| 3.11 | thumbnail   | String     |          | Base64 của ảnh mẫu chữ ký               |
| 3.12 | templateName| String     |          | tên mẫu chữ ký                          |
| 3.13 | type        | int        |          | Kiểu mẫu chữ ký: 0: CA_NHAN, 1: CA_NHAN_DAU_MOC, 2: TO_CHUC_DAU_MOC, 3: KY_NHAY |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": "",
    "data": [
        {
            "id": 18,
            "userId": 264,
            "createdBy": "demo_easysign",
            "createdDate": "2022-04-13T15:38:04.663",
            "coreParser": "Full_Info_Version",
            "fullName": null,
            "width": 320,
            "height": 280,
            "htmlTemplate": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    ...",
            "transparency": true,
            "thumbnail": "iVBORw0KGgoAAAANSUhEUgAAAUAAAACmCAYAAACr+laB...",
            "templateName": null,
            "type": null
        }
    ]
}

VI.16. API Lấy ảnh mẫu chữ ký theo mã mẫu chữ ký.
Mô tả chung

Mô tả: Lấy danh sách mẫu chữ ký theo username truyền vào.

Yêu cầu xác thực: Có

API: /api/certificate/getImageByTemplateId

Method: GET

Header: ContentType: Application_Json, Authorization: “Bearer” + id_token

Thuộc tính đầu vào
| STT | Tên        | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
|-----|------------|--------------|----------|-----------------------------------------|
| 1   | serial   | String     | x      | Serial của chứng thư số                 |
| 2   | pin      | string     | x      | Mã pin của chứng thư số                 |
| 3   | templateId| int        | o      | Mã ảnh mẫu chữ ký. Mặc định k truyền sẽ get ảnh theo mẫu mặc định của hệ thống |

Ví dụ đầu vào (URL):
/api/certificate/getImageByTemplateId?serial=5401100041302CAD2FBEF0D8D64B7161&pin=12345678&templateId=18

Thuộc tính đầu ra
| STT | Tên    | Kiểu dữ liệu | Bắt buộc | Mô tả                                |
|-----|--------|--------------|----------|--------------------------------------|
| 1   | status | int        | x      | Trạng thái xử lý                     |
| 2   | msg    | String     |          | Thông tin chi tiết trạng thái        |
| 3   | data   | Object     |          | Base64 của ảnh mẫu chữ ký            |

Ví dụ đầu ra:

{
    "status": 0,
    "msg": "",
    "data": "iVBORw0KGgoAAAANSUhEUg..."
}