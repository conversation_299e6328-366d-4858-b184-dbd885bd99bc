using BuildingBlocks.Common.GoTrustApi.Interfaces;
using BuildingBlocks.Common.GoTrustApi.Services;
using Microsoft.Extensions.DependencyInjection;
using Refit;
using System.Text.Json;

namespace BuildingBlocks.Common.GoTrustApi.Extensions;

/// <summary>
/// Extension methods for registering GoTrustApi services.
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Registers GoTrustApi services with Dependency Injection.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configuration">The GoTrustApi configuration.</param>
    /// <returns>The service collection.</returns>
    public static IServiceCollection AddGoTrustApi(this IServiceCollection services, GoTrustApiConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);

        if (string.IsNullOrEmpty(configuration.BaseUrl))
            throw new ArgumentException("BaseUrl is required", nameof(configuration));

        // Register configuration
        services.AddSingleton(configuration);

        // Configure Refit settings
        var refitSettings = new RefitSettings
        {
            ContentSerializer = new SystemTextJsonContentSerializer(new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            })
        };

        // Register Refit client with HttpClient
        services.AddRefitClient<IGoTrustApi>(refitSettings)
                .ConfigureHttpClient(client =>
                {
                    client.BaseAddress = new Uri(configuration.BaseUrl);
                    client.Timeout = TimeSpan.FromSeconds(configuration.TimeoutSeconds);
                    client.DefaultRequestHeaders.Add("User-Agent", "GoTrust-EMR-Client/1.0");
                });

        return services;
    }

    /// <summary>
    /// Registers GoTrustApi services with configuration from an action.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configureOptions">The action to configure.</param>
    /// <returns>The service collection.</returns>
    public static IServiceCollection AddGoTrustApi(this IServiceCollection services, Action<GoTrustApiConfiguration> configureOptions)
    {
        var configuration = new GoTrustApiConfiguration();
        configureOptions(configuration);
        return services.AddGoTrustApi(configuration);
    }
}
