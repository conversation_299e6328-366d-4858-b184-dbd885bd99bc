using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.GoTrustApi.Models;

public class PdfConversionOptionsDto
{
    [JsonPropertyName("format")]
    public string Format { get; set; } = "A4";

    [JsonPropertyName("marginTop")]
    public string MarginTop { get; set; } = "20px";

    [JsonPropertyName("marginRight")]
    public string MarginRight { get; set; } = "20px";

    [JsonPropertyName("marginBottom")]
    public string MarginBottom { get; set; } = "20px";

    [JsonPropertyName("marginLeft")]
    public string MarginLeft { get; set; } = "20px";

    [JsonPropertyName("landscape")]
    public bool Landscape { get; set; } = false;
}
