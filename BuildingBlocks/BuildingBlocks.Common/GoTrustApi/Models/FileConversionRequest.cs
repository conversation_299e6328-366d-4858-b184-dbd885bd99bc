using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.GoTrustApi.Models;

public class FileConversionRequest
{
    [JsonPropertyName("htmlContent")]
    public string HtmlContent { get; set; } = string.Empty;

    [JsonPropertyName("filename")]
    public string Filename { get; set; } = string.Empty;

    [JsonPropertyName("options")]
    public PdfConversionOptionsDto Options { get; set; } = new();
}
