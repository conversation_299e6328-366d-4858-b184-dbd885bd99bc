# CAService Integration Guide

Hướng dẫn tích hợp thư viện CAService vào dự án GoTRUST EMR.

## 1. Cài đặt Dependencies

Thư viện CAService sử dụng các package sau:
- `Refit` - HTTP client generator
- `Microsoft.Extensions.DependencyInjection` - DI container
- `System.Text.Json` - JSON serialization

Các dependencies này đã được include sẵn trong `BuildingBlocks.Common.csproj`.

## 2. Cấu hình trong DI Container

### Option 1: Sử dụng Configuration từ appsettings.json

```json
{
  "CAService": {
    "BaseUrl": "https://ca-api.gotrust.vn",
    "Account": "your-account",
    "Password": "your-password",
    "TimeoutSeconds": 60,
    "AutoRefreshToken": true,
    "TokenCacheMinutes": 30
  }
}
```

```csharp
// Trong Program.cs
var caConfig = builder.Configuration.GetSection("CAService").Get<CAServiceConfiguration>();
builder.Services.AddCAService(caConfig);
```

### Option 2: C<PERSON>u hình trực tiếp

```csharp
// Trong Program.cs
builder.Services.AddCAServiceProduction(
    baseUrl: "https://ca-api.gotrust.vn",
    account: "your-account",
    password: "your-password");
```

### Option 3: Sử dụng Environment Variables

```csharp
builder.Services.AddCAService(new CAServiceConfiguration
{
    BaseUrl = Environment.GetEnvironmentVariable("CA_SERVICE_BASE_URL") ?? "https://ca-api.gotrust.vn",
    Account = Environment.GetEnvironmentVariable("CA_SERVICE_ACCOUNT"),
    Password = Environment.GetEnvironmentVariable("CA_SERVICE_PASSWORD"),
    TimeoutSeconds = 60,
    AutoRefreshToken = true,
    TokenCacheMinutes = 30
});
```

## 3. Sử dụng trong CQRS Commands/Queries

### Command Handler Example

```csharp
using BuildingBlocks.Common.CAService.Services;
using GoTRUST.EMR.Application.Features.DigitalSignature.Commands;

public class SignDocumentCommandHandler : ICommandHandler<SignDocumentCommand, SignDocumentResponse>
{
    private readonly CAService _caService;
    private readonly IApplicationDbContext _context;

    public SignDocumentCommandHandler(CAService caService, IApplicationDbContext context)
    {
        _caService = caService;
        _context = context;
    }

    public async Task<SignDocumentResponse> Handle(SignDocumentCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Tìm khách hàng bằng khuôn mặt
            var customerResponse = await _caService.FindCustomersByFaceAsync(request.FaceImageBase64);
            
            if (!CAService.IsSuccess(customerResponse) || !customerResponse.Data.Faces.Any())
            {
                throw new BadRequestException("Không tìm thấy khách hàng phù hợp");
            }

            var customer = customerResponse.Data.Faces.First();
            var customerId = Guid.Parse(customer.CustomerId);

            // Tạo request ký PDF
            var createSignResponse = await _caService.CreateSignPdfRequestAsync(
                request.FileName, 
                customerId);

            if (!CAService.IsSuccess(createSignResponse))
            {
                throw new InternalServerException($"Tạo request ký thất bại: {CAService.GetErrorMessage(createSignResponse)}");
            }

            // Ký PDF
            var signResponse = await _caService.SignPdfAsync(
                createSignResponse.Data.SignedPDFId,
                request.FileBase64,
                request.KeyBase64,
                request.SignaturePosition.X,
                request.SignaturePosition.Y,
                request.SignaturePosition.Width,
                request.SignaturePosition.Height,
                request.PageNumber);

            if (!CAService.IsSuccess(signResponse))
            {
                throw new InternalServerException($"Ký PDF thất bại: {CAService.GetErrorMessage(signResponse)}");
            }

            // Lưu thông tin vào database
            var signedDocument = new SignedDocument
            {
                Id = Guid.NewGuid(),
                SignedPdfId = signResponse.Data.SignedPDFId,
                CustomerId = customerId,
                FileName = request.FileName,
                Status = "Signed",
                CreatedAt = DateTime.UtcNow
            };

            _context.SignedDocuments.Add(signedDocument);
            await _context.SaveChangesAsync(cancellationToken);

            return new SignDocumentResponse
            {
                SignedPdfId = signResponse.Data.SignedPDFId,
                CustomerId = customerId,
                IsSuccess = true,
                Message = "Ký tài liệu thành công"
            };
        }
        catch (Exception ex)
        {
            return new SignDocumentResponse
            {
                IsSuccess = false,
                Message = ex.Message
            };
        }
    }
}
```

### Query Handler Example

```csharp
public class GetSignedDocumentQueryHandler : IQueryHandler<GetSignedDocumentQuery, GetSignedDocumentResponse>
{
    private readonly CAService _caService;
    private readonly IApplicationDbContext _context;

    public GetSignedDocumentQueryHandler(CAService caService, IApplicationDbContext context)
    {
        _caService = caService;
        _context = context;
    }

    public async Task<GetSignedDocumentResponse> Handle(GetSignedDocumentQuery request, CancellationToken cancellationToken)
    {
        var document = await _context.SignedDocuments
            .FirstOrDefaultAsync(x => x.Id == request.DocumentId, cancellationToken);

        if (document == null)
        {
            throw new NotFoundException("Không tìm thấy tài liệu");
        }

        // Lấy thông tin PDF từ CA Service
        var pdfResponse = await _caService.GetSignedPdfAsync(document.SignedPdfId);
        
        if (!CAService.IsSuccess(pdfResponse))
        {
            throw new InternalServerException($"Lấy thông tin PDF thất bại: {CAService.GetErrorMessage(pdfResponse)}");
        }

        return new GetSignedDocumentResponse
        {
            DocumentId = document.Id,
            SignedPdfId = document.SignedPdfId,
            FileName = document.FileName,
            DownloadUrl = pdfResponse.Data.DownloadUrl,
            Status = pdfResponse.Data.Status,
            CustomerId = document.CustomerId
        };
    }
}
```

## 4. Carter Endpoint Integration

```csharp
public class DigitalSignatureModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("api/digital-signature")
                      .WithTags("Digital Signature")
                      .RequireAuthorization();

        group.MapPost("/sign-document", SignDocumentAsync)
             .WithOpenApi(operation =>
             {
                 operation.Summary = "Ký tài liệu PDF";
                 operation.Description = "Ký tài liệu PDF sử dụng CA Service";
                 return operation;
             });

        group.MapGet("/signed-document/{documentId}", GetSignedDocumentAsync)
             .WithOpenApi();
    }

    private static async Task<IResult> SignDocumentAsync(
        SignDocumentCommand command,
        IMediator mediator)
    {
        var result = await mediator.Send(command);
        
        if (result.IsSuccess)
        {
            return Results.Ok(Response<SignDocumentResponse>.Success(result));
        }
        
        return Results.BadRequest(Response<SignDocumentResponse>.Failure(result.Message));
    }

    private static async Task<IResult> GetSignedDocumentAsync(
        Guid documentId,
        IMediator mediator)
    {
        var query = new GetSignedDocumentQuery { DocumentId = documentId };
        var result = await mediator.Send(query);
        
        return Results.Ok(Response<GetSignedDocumentResponse>.Success(result));
    }
}
```

## 5. Background Job Integration (Hangfire)

```csharp
public class DocumentSigningBackgroundService : IBackgroundService
{
    private readonly CAService _caService;
    private readonly ILogger<DocumentSigningBackgroundService> _logger;

    public DocumentSigningBackgroundService(
        CAService caService, 
        ILogger<DocumentSigningBackgroundService> logger)
    {
        _caService = caService;
        _logger = logger;
    }

    [AutomaticRetry(Attempts = 3)]
    public async Task ProcessBatchSigningAsync(List<DocumentSigningRequest> requests)
    {
        foreach (var request in requests)
        {
            try
            {
                _logger.LogInformation("Processing document signing for {CustomerId}", request.CustomerId);

                var createSignResponse = await _caService.CreateSignPdfRequestAsync(
                    request.FileName, 
                    request.CustomerId);

                if (CAService.IsSuccess(createSignResponse))
                {
                    var signResponse = await _caService.SignPdfAsync(
                        createSignResponse.Data.SignedPDFId,
                        request.FileBase64,
                        request.KeyBase64,
                        request.X, request.Y, 
                        request.Width, request.Height,
                        request.PageNumber);

                    if (CAService.IsSuccess(signResponse))
                    {
                        _logger.LogInformation("Document signed successfully: {SignedPdfId}", 
                            signResponse.Data.SignedPDFId);
                    }
                    else
                    {
                        _logger.LogError("Failed to sign document: {Error}", 
                            CAService.GetErrorMessage(signResponse));
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing document signing");
                throw; // Re-throw để Hangfire retry
            }
        }
    }
}
```

## 6. Error Handling và Logging

```csharp
public class CAServiceErrorHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<CAServiceErrorHandlingMiddleware> _logger;

    public CAServiceErrorHandlingMiddleware(RequestDelegate next, ILogger<CAServiceErrorHandlingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex) when (ex.Message.Contains("CAService"))
        {
            _logger.LogError(ex, "CAService integration error");
            
            context.Response.StatusCode = 500;
            await context.Response.WriteAsync(JsonSerializer.Serialize(new
            {
                error = "CAService integration error",
                message = ex.Message,
                traceId = Activity.Current?.Id
            }));
        }
    }
}
```

## 7. Testing

### Unit Test Example

```csharp
[Test]
public async Task SignDocument_ShouldReturnSuccess_WhenValidRequest()
{
    // Arrange
    var mockApi = new Mock<ICAServiceApi>();
    var config = new CAServiceConfiguration 
    { 
        BaseUrl = "https://test-api.com",
        AutoRefreshToken = false 
    };
    
    var caService = new CAService(mockApi.Object, config);
    
    mockApi.Setup(x => x.CreateSignPdfRequestAsync(It.IsAny<CreateSignPdfRequest>(), It.IsAny<string>()))
           .ReturnsAsync(new CAServiceResponse<CreateSignPdfResponseData>
           {
               Code = "200",
               Data = new CreateSignPdfResponseData { SignedPDFId = Guid.NewGuid() }
           });

    // Act
    var result = await caService.CreateSignPdfRequestAsync("test.pdf", Guid.NewGuid());

    // Assert
    Assert.IsTrue(CAService.IsSuccess(result));
    Assert.IsNotNull(result.Data);
}
```

## 8. Configuration Best Practices

### Production Environment

```json
{
  "CAService": {
    "BaseUrl": "https://ca-api.gotrust.vn",
    "TimeoutSeconds": 60,
    "AutoRefreshToken": true,
    "TokenCacheMinutes": 30
  }
}
```

### Development Environment

```json
{
  "CAService": {
    "BaseUrl": "https://ca-api-dev.gotrust.vn",
    "Account": "dev-account",
    "Password": "dev-password",
    "TimeoutSeconds": 30,
    "AutoRefreshToken": true,
    "TokenCacheMinutes": 60
  }
}
```

## 9. Monitoring và Metrics

```csharp
// Thêm vào Program.cs
builder.Services.AddHttpClient<ICAServiceApi>()
    .AddPolicyHandler(GetRetryPolicy())
    .AddPolicyHandler(GetCircuitBreakerPolicy());

static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
{
    return HttpPolicyExtensions
        .HandleTransientHttpError()
        .WaitAndRetryAsync(3, retryAttempt =>
            TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
}

static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
{
    return HttpPolicyExtensions
        .HandleTransientHttpError()
        .CircuitBreakerAsync(3, TimeSpan.FromMinutes(1));
}
```

Với hướng dẫn này, bạn có thể tích hợp CAService một cách hoàn chỉnh vào hệ thống GoTRUST EMR.
