using BuildingBlocks.Common.CAService.Interfaces;
using BuildingBlocks.Common.CAService.Models;
using BuildingBlocks.Common.CAService.Services;
using Microsoft.Extensions.DependencyInjection;
using Refit;
using System.Text.Json;

namespace BuildingBlocks.Common.CAService.Extensions;

/// <summary>
/// Extension methods cho việc đăng ký CAService services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Đăng ký CAService services với Dependency Injection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration"><PERSON><PERSON><PERSON> hình CAService</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddCAService(this IServiceCollection services, CAServiceConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);

        if (string.IsNullOrEmpty(configuration.BaseUrl))
            throw new ArgumentException("BaseUrl is required", nameof(configuration));

        // Đăng ký configuration
        services.AddSingleton(configuration);

        // Cấu hình Refit settings
        var refitSettings = new RefitSettings
        {
            ContentSerializer = new SystemTextJsonContentSerializer(new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            })
        };

        // Đăng ký Refit client với HttpClient
        services.AddRefitClient<ICAServiceApi>(refitSettings)
                .ConfigureHttpClient(client =>
                {
                    client.BaseAddress = new Uri(configuration.BaseUrl);
                    client.Timeout = TimeSpan.FromSeconds(configuration.TimeoutSeconds);

                    // Thêm User-Agent header
                    client.DefaultRequestHeaders.Add("User-Agent", "CAService-Client/1.0");
                    client.DefaultRequestHeaders.Add("Accept", "application/json");
                });

        // Đăng ký CAService
        services.AddScoped<Services.CAService>();

        return services;
    }

    /// <summary>
    /// Đăng ký CAService services với cấu hình từ action
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Action để cấu hình</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddCAService(this IServiceCollection services, Action<CAServiceConfiguration> configureOptions)
    {
        var configuration = new CAServiceConfiguration();
        configureOptions(configuration);
        return services.AddCAService(configuration);
    }
}
