using BuildingBlocks.Common.CAService.Interfaces;
using BuildingBlocks.Common.CAService.Models;

namespace BuildingBlocks.Common.CAService.Services;

/// <summary>
/// Service wrapper cho CA API với các tính năng bổ sung
/// </summary>
public class CAService(ICAServiceApi api, CAServiceConfiguration configuration)
{
    private readonly ICAServiceApi _api = api ?? throw new ArgumentNullException(nameof(api));
    private readonly CAServiceConfiguration _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    private string? _cachedToken;
    private DateTime _tokenExpiration;

    #region Authentication

    /// <summary>
    /// Đăng nhập và lấy token xác thực
    /// </summary>
    /// <param name="account">Tài khoản đăng nhập (tùy chọn, nếu không truyền sẽ dùng từ configuration)</param>
    /// <param name="password"><PERSON><PERSON><PERSON> khẩu (tùy chọn, nếu không truyền sẽ dùng từ configuration)</param>
    /// <returns>Thông tin đăng nhập</returns>
    public async Task<CAServiceResponse<LoginResponseData>> LoginAsync(string? account = null, string? password = null)
    {
        var request = new LoginRequest
        {
            Account = account ?? _configuration.Account ?? throw new ArgumentException("Account is required"),
            Password = password ?? _configuration.Password ?? throw new ArgumentException("Password is required")
        };

        var response = await _api.LoginAsync(request);

        if (response.IsSuccess && response.Data?.UserInfo?.Token != null)
        {
            _cachedToken = response.Data.UserInfo.Token;
            // Sử dụng thời gian hết hạn từ API hoặc cache time từ config
            _tokenExpiration = response.Data.UserInfo.TokenExpiresAt != default
                ? response.Data.UserInfo.TokenExpiresAt
                : DateTime.UtcNow.AddMinutes(_configuration.TokenCacheMinutes);
        }

        return response;
    }

    /// <summary>
    /// Lấy token hiện tại hoặc refresh nếu cần
    /// </summary>
    /// <returns>Token hợp lệ</returns>
    public async Task<string> GetValidTokenAsync()
    {
        if (string.IsNullOrEmpty(_cachedToken) || DateTime.UtcNow >= _tokenExpiration)
        {
            if (_configuration.AutoRefreshToken)
            {
                var loginResponse = await LoginAsync();
                if (!loginResponse.IsSuccess)
                {
                    throw new InvalidOperationException($"Login failed: {loginResponse.Message}");
                }
            }
            else
            {
                throw new InvalidOperationException("Token hết hạn! Vui lòng đăng nhập lại.");
            }
        }

        return $"Bearer {_cachedToken}";
    }

    #endregion

    #region Customer Management

    /// <summary>
    /// Tạo khách hàng mới
    /// </summary>
    /// <param name="request">Thông tin khách hàng</param>
    /// <returns>Kết quả tạo khách hàng</returns>
    public async Task<CAServiceResponse<CreateCustomerResponseData>> CreateCustomerAsync(CreateCustomerRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.CreateCustomerAsync(request, token);
    }

    /// <summary>
    /// Tạo khách hàng mới đơn giản
    /// </summary>
    /// <param name="identityNo">Số CMND/CCCD</param>
    /// <param name="email">Email</param>
    /// <param name="phoneNumber">Số điện thoại</param>
    /// <param name="fullName">Họ và tên</param>
    /// <param name="faceImgBase64">Ảnh khuôn mặt Base64</param>
    /// <returns>Kết quả tạo khách hàng</returns>
    public async Task<CAServiceResponse<CreateCustomerResponseData>> CreateCustomerAsync(
        string identityNo,
        string email,
        string phoneNumber,
        string fullName,
        string faceImgBase64)
    {
        var request = new CreateCustomerRequest
        {
            IdentityNo = identityNo,
            Email = email,
            PhoneNumber = phoneNumber,
            FullName = fullName,
            FaceImgBase64 = faceImgBase64
        };

        return await CreateCustomerAsync(request);
    }

    /// <summary>
    /// Tìm kiếm khách hàng bằng khuôn mặt
    /// </summary>
    /// <param name="request">Thông tin khuôn mặt</param>
    /// <returns>Danh sách khách hàng khớp</returns>
    public async Task<CAServiceResponse<FindCustomerByFaceResponseData>> FindCustomersByFaceAsync(FindCustomerByFaceRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.FindCustomersByFaceAsync(request, token);
    }

    /// <summary>
    /// Tìm kiếm khách hàng bằng khuôn mặt đơn giản
    /// </summary>
    /// <param name="faceImgBase64">Ảnh khuôn mặt Base64</param>
    /// <returns>Danh sách khách hàng khớp</returns>
    public async Task<CAServiceResponse<FindCustomerByFaceResponseData>> FindCustomersByFaceAsync(string faceImgBase64)
    {
        var request = new FindCustomerByFaceRequest
        {
            FaceImgBase64 = faceImgBase64
        };

        return await FindCustomersByFaceAsync(request);
    }

    /// <summary>
    /// Lấy PIN khách hàng
    /// </summary>
    /// <param name="request">Thông tin xác thực</param>
    /// <returns>PIN khách hàng</returns>
    public async Task<CAServiceResponse<GetCustomerPinResponseData>> GetCustomerPinAsync(GetCustomerPinRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.GetCustomerPinAsync(request, token);
    }

    /// <summary>
    /// Lấy PIN khách hàng đơn giản
    /// </summary>
    /// <param name="faceImgBase64">Ảnh khuôn mặt Base64</param>
    /// <param name="customerId">ID khách hàng</param>
    /// <returns>PIN khách hàng</returns>
    public async Task<CAServiceResponse<GetCustomerPinResponseData>> GetCustomerPinAsync(string faceImgBase64, Guid customerId)
    {
        var request = new GetCustomerPinRequest
        {
            FaceImgBase64 = faceImgBase64,
            CustomerId = customerId
        };

        return await GetCustomerPinAsync(request);
    }

    #endregion

    #region Digital Signature

    /// <summary>
    /// Lấy thông tin PDF đã ký
    /// </summary>
    /// <param name="signedPdfId">ID của PDF đã ký</param>
    /// <returns>Thông tin PDF đã ký</returns>
    public async Task<CAServiceResponse<SignedPdf>> GetSignedPdfAsync(Guid signedPdfId)
    {
        var token = await GetValidTokenAsync();
        return await _api.GetSignedPdfAsync(signedPdfId, token);
    }

    /// <summary>
    /// Tạo request ký PDF
    /// </summary>
    /// <param name="request">Thông tin request ký PDF</param>
    /// <returns>ID của PDF đã tạo</returns>
    public async Task<CAServiceResponse<CreateSignPdfResponseData>> CreateSignPdfRequestAsync(CreateSignPdfRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.CreateSignPdfRequestAsync(request, token);
    }

    /// <summary>
    /// Tạo request ký PDF đơn giản
    /// </summary>
    /// <param name="fileName">Tên file</param>
    /// <param name="customerId">ID khách hàng</param>
    /// <returns>ID của PDF đã tạo</returns>
    public async Task<CAServiceResponse<CreateSignPdfResponseData>> CreateSignPdfRequestAsync(string fileName, Guid customerId)
    {
        var request = new CreateSignPdfRequest
        {
            FileName = fileName,
            CustomerId = customerId
        };

        return await CreateSignPdfRequestAsync(request);
    }

    /// <summary>
    /// Ký PDF
    /// </summary>
    /// <param name="request">Thông tin ký PDF</param>
    /// <returns>Kết quả ký PDF</returns>
    public async Task<CAServiceResponse<SignPdfResponseData>> SignPdfAsync(SignPdfRequest request)
    {
        var token = await GetValidTokenAsync();
        return await _api.SignPdfAsync(request, token);
    }

    /// <summary>
    /// Ký PDF đơn giản
    /// </summary>
    /// <param name="signedPdfId">ID của PDF đã ký</param>
    /// <param name="fileBase64">Nội dung file PDF Base64</param>
    /// <param name="keyBase64">Khóa Base64</param>
    /// <param name="x">Tọa độ X</param>
    /// <param name="y">Tọa độ Y</param>
    /// <param name="width">Chiều rộng</param>
    /// <param name="height">Chiều cao</param>
    /// <param name="pageNumber">Số trang</param>
    /// <returns>Kết quả ký PDF</returns>
    public async Task<CAServiceResponse<SignPdfResponseData>> SignPdfAsync(
        Guid signedPdfId,
        string fileBase64,
        string keyBase64,
        int x,
        int y,
        int width,
        int height,
        int pageNumber = 1)
    {
        var request = new SignPdfRequest
        {
            SignedPDFId = signedPdfId,
            FileBase64 = fileBase64,
            KeyBase64 = keyBase64,
            X = x,
            Y = y,
            Width = width,
            Height = height,
            PageNumber = pageNumber
        };

        return await SignPdfAsync(request);
    }

    /// <summary>
    /// Ký PDF từ file path
    /// </summary>
    /// <param name="signedPdfId">ID của PDF đã ký</param>
    /// <param name="pdfFilePath">Đường dẫn file PDF</param>
    /// <param name="keyBase64">Khóa Base64</param>
    /// <param name="x">Tọa độ X</param>
    /// <param name="y">Tọa độ Y</param>
    /// <param name="width">Chiều rộng</param>
    /// <param name="height">Chiều cao</param>
    /// <param name="pageNumber">Số trang</param>
    /// <returns>Kết quả ký PDF</returns>
    public async Task<CAServiceResponse<SignPdfResponseData>> SignPdfFromFileAsync(
        Guid signedPdfId,
        string pdfFilePath,
        string keyBase64,
        int x,
        int y,
        int width,
        int height,
        int pageNumber = 1)
    {
        var pdfBytes = await File.ReadAllBytesAsync(pdfFilePath);
        var fileBase64 = Convert.ToBase64String(pdfBytes);

        return await SignPdfAsync(signedPdfId, fileBase64, keyBase64, x, y, width, height, pageNumber);
    }

    #endregion

    #region Utility Methods

    /// <summary>
    /// Kiểm tra trạng thái phản hồi có thành công không
    /// </summary>
    /// <param name="response">Phản hồi từ API</param>
    /// <returns>True nếu thành công</returns>
    public static bool IsSuccess<T>(CAServiceResponse<T> response)
    {
        return response.IsSuccess;
    }

    /// <summary>
    /// Lấy thông tin lỗi từ phản hồi
    /// </summary>
    /// <param name="response">Phản hồi từ API</param>
    /// <returns>Chuỗi mô tả lỗi</returns>
    public static string GetErrorMessage<T>(CAServiceResponse<T> response)
    {
        if (response.IsSuccess)
            return "Thành công";

        return response.Code switch
        {
            "400" => response.Message ?? "Bad Request",
            "401" => "Unauthorized - Token không hợp lệ hoặc hết hạn",
            "403" => "Forbidden - Không có quyền truy cập",
            "404" => "Not Found - Không tìm thấy tài nguyên",
            "500" => "Internal Server Error - Lỗi máy chủ",
            _ => response.Message ?? $"Lỗi không xác định (Code: {response.Code})"
        };
    }

    /// <summary>
    /// Chuyển đổi byte array sang base64 string
    /// </summary>
    /// <param name="bytes">Byte array</param>
    /// <returns>Base64 string</returns>
    public static string ToBase64String(byte[] bytes)
    {
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// Chuyển đổi base64 string sang byte array
    /// </summary>
    /// <param name="base64">Base64 string</param>
    /// <returns>Byte array</returns>
    public static byte[] FromBase64String(string base64)
    {
        return Convert.FromBase64String(base64);
    }

    /// <summary>
    /// Đọc file và chuyển đổi sang base64
    /// </summary>
    /// <param name="filePath">Đường dẫn file</param>
    /// <returns>Base64 string</returns>
    public static async Task<string> FileToBase64Async(string filePath)
    {
        var bytes = await File.ReadAllBytesAsync(filePath);
        return ToBase64String(bytes);
    }

    /// <summary>
    /// Lưu base64 string thành file
    /// </summary>
    /// <param name="base64">Base64 string</param>
    /// <param name="filePath">Đường dẫn file đích</param>
    public static async Task SaveBase64ToFileAsync(string base64, string filePath)
    {
        var bytes = FromBase64String(base64);
        await File.WriteAllBytesAsync(filePath, bytes);
    }

    #endregion
}
