using BuildingBlocks.Common.CAService.Models;
using Refit;

namespace BuildingBlocks.Common.CAService.Interfaces;

/// <summary>
/// Interface API cho hệ thống CA Service
/// Hỗ trợ quản lý khách hàng và ký số PDF thông qua Web-API
/// </summary>
public interface ICAServiceApi
{
    #region Authentication

    /// <summary>
    /// API đăng nhập hệ thống CA
    /// </summary>
    /// <param name="request">Thông tin đăng nhập</param>
    /// <returns>Thông tin đăng nhập</returns>
    [Post("/authenticates/login")]
    Task<CAServiceResponse<LoginResponseData>> LoginAsync([Body] LoginRequest request);

    #endregion

    #region Customer Management

    /// <summary>
    /// API tạo khách hàng mới
    /// </summary>
    /// <param name="request">Thông tin khách hàng</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Thông tin khách hàng đã tạo</returns>
    [Post("/customers")]
    Task<CAServiceResponse<CreateCustomerResponseData>> CreateCustomerAsync(
        [Body] CreateCustomerRequest request,
        [Header("Authorization")] string authorization);

    /// <summary>
    /// API tìm kiếm khách hàng bằng khuôn mặt
    /// </summary>
    /// <param name="request">Thông tin khuôn mặt</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Danh sách khách hàng khớp</returns>
    [Post("/customers/ids-by-face")]
    Task<CAServiceResponse<FindCustomerByFaceResponseData>> FindCustomersByFaceAsync(
        [Body] FindCustomerByFaceRequest request,
        [Header("Authorization")] string authorization);

    /// <summary>
    /// API lấy PIN khách hàng
    /// </summary>
    /// <param name="request">Thông tin xác thực</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>PIN khách hàng</returns>
    [Post("/customers/pin")]
    Task<CAServiceResponse<GetCustomerPinResponseData>> GetCustomerPinAsync(
        [Body] GetCustomerPinRequest request,
        [Header("Authorization")] string authorization);

    #endregion

    #region Digital Signature

    /// <summary>
    /// API lấy thông tin PDF đã ký
    /// </summary>
    /// <param name="signedPdfId">ID của PDF đã ký</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Thông tin PDF đã ký</returns>
    [Get("/signatures/signed-pdf/{signedPdfId}")]
    Task<CAServiceResponse<SignedPdf>> GetSignedPdfAsync(
        Guid signedPdfId,
        [Header("Authorization")] string authorization);

    /// <summary>
    /// API tạo request ký PDF
    /// </summary>
    /// <param name="request">Thông tin request ký PDF</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>ID của PDF đã tạo</returns>
    [Post("/signatures/sign-pdf-request")]
    Task<CAServiceResponse<CreateSignPdfResponseData>> CreateSignPdfRequestAsync(
        [Body] CreateSignPdfRequest request,
        [Header("Authorization")] string authorization);

    /// <summary>
    /// API ký PDF
    /// </summary>
    /// <param name="request">Thông tin ký PDF</param>
    /// <param name="authorization">Bearer token</param>
    /// <returns>Kết quả ký PDF</returns>
    [Post("/signatures/sign-pdf")]
    Task<CAServiceResponse<SignPdfResponseData>> SignPdfAsync(
        [Body] SignPdfRequest request,
        [Header("Authorization")] string authorization);

    #endregion
}
