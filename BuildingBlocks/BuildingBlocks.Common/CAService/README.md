# CAService Library

Thư viện tích hợp với CA Service API để quản lý khách hàng và ký số PDF.

## Tính năng

- **Authentication**: Đ<PERSON><PERSON> nhập và quản lý token tự động
- **Customer Management**: <PERSON><PERSON><PERSON> hàng, t<PERSON><PERSON> kiếm bằng khuôn mặt, l<PERSON>y PIN
- **Digital Signature**: Ký PDF với chữ ký số
- **Token Management**: Tự động refresh token khi hết hạn
- **Error Handling**: Xử lý lỗi thống nhất

## Cài đặt

### 1. Đăng ký service trong DI Container

```csharp
// Trong Program.cs hoặc Startup.cs
using BuildingBlocks.Common.CAService.Extensions;

// Môi trường demo/development
services.AddCAServiceDemo("https://ca-api.example.com", "your-account", "your-password");

// Hoặc môi trường production với cấu hình chi tiết
services.AddCAServiceProduction(
    baseUrl: "https://ca-api.example.com",
    account: "your-account", 
    password: "your-password",
    timeoutSeconds: 60,
    tokenCacheMinutes: 30);

// Hoặc cấu hình thủ công
var configuration = new CAServiceConfiguration
{
    BaseUrl = "https://ca-api.example.com",
    Account = "your-account",
    Password = "your-password",
    TimeoutSeconds = 30,
    AutoRefreshToken = true,
    TokenCacheMinutes = 60
};
services.AddCAService(configuration);
```

### 2. Sử dụng trong Controller hoặc Service

```csharp
using BuildingBlocks.Common.CAService.Services;

public class ExampleController : ControllerBase
{
    private readonly CAService _caService;

    public ExampleController(CAService caService)
    {
        _caService = caService;
    }

    // Các method sử dụng service...
}
```

## Sử dụng

### Authentication

```csharp
// Đăng nhập thủ công (service sẽ tự động đăng nhập khi cần)
var loginResponse = await _caService.LoginAsync("account", "password");
if (CAService.IsSuccess(loginResponse))
{
    Console.WriteLine("Đăng nhập thành công");
}
```

### Customer Management

```csharp
// Tạo khách hàng mới
var createResponse = await _caService.CreateCustomerAsync(
    identityNo: "*********",
    email: "<EMAIL>",
    phoneNumber: "**********",
    fullName: "Nguyễn Văn A",
    faceImgBase64: "base64-encoded-face-image");

if (CAService.IsSuccess(createResponse))
{
    var customerId = createResponse.Data.CustomerId;
    Console.WriteLine($"Tạo khách hàng thành công: {customerId}");
}

// Tìm kiếm khách hàng bằng khuôn mặt
var findResponse = await _caService.FindCustomersByFaceAsync("base64-encoded-face-image");
if (CAService.IsSuccess(findResponse))
{
    foreach (var customer in findResponse.Data.Faces)
    {
        Console.WriteLine($"Tìm thấy: {customer.FullName} - {customer.CustomerId}");
    }
}

// Lấy PIN khách hàng
var pinResponse = await _caService.GetCustomerPinAsync("base64-encoded-face-image", customerId);
if (CAService.IsSuccess(pinResponse))
{
    var pin = pinResponse.Data.Pin;
    Console.WriteLine($"PIN: {pin}");
}
```

### Digital Signature

```csharp
// Tạo request ký PDF
var createSignResponse = await _caService.CreateSignPdfRequestAsync("document.pdf", customerId);
if (CAService.IsSuccess(createSignResponse))
{
    var signedPdfId = createSignResponse.Data.SignedPDFId;
    
    // Ký PDF
    var signResponse = await _caService.SignPdfAsync(
        signedPdfId: signedPdfId,
        fileBase64: "base64-encoded-pdf-content",
        keyBase64: "base64-encoded-key",
        x: 100,
        y: 200,
        width: 150,
        height: 50,
        pageNumber: 1);
    
    if (CAService.IsSuccess(signResponse))
    {
        Console.WriteLine("Ký PDF thành công");
        
        // Lấy thông tin PDF đã ký
        var pdfInfo = await _caService.GetSignedPdfAsync(signedPdfId);
        if (CAService.IsSuccess(pdfInfo))
        {
            Console.WriteLine($"Download URL: {pdfInfo.Data.DownloadUrl}");
        }
    }
}

// Ký PDF từ file
var fileSignResponse = await _caService.SignPdfFromFileAsync(
    signedPdfId,
    @"C:\path\to\document.pdf",
    "base64-encoded-key",
    100, 200, 150, 50);
```

### Error Handling

```csharp
var response = await _caService.CreateCustomerAsync(request);

if (!CAService.IsSuccess(response))
{
    var errorMessage = CAService.GetErrorMessage(response);
    Console.WriteLine($"Lỗi: {errorMessage}");
    
    // Xử lý theo mã lỗi
    switch (response.Code)
    {
        case "400":
            // Xử lý Bad Request
            break;
        case "401":
            // Xử lý Unauthorized
            break;
        case "404":
            // Xử lý Not Found
            break;
        default:
            // Xử lý lỗi khác
            break;
    }
}
```

### Utility Methods

```csharp
// Chuyển file thành base64
var base64Content = await CAService.FileToBase64Async(@"C:\path\to\file.pdf");

// Lưu base64 thành file
await CAService.SaveBase64ToFileAsync(base64Content, @"C:\path\to\output.pdf");

// Chuyển đổi byte array
var bytes = new byte[] { 1, 2, 3, 4, 5 };
var base64 = CAService.ToBase64String(bytes);
var backToBytes = CAService.FromBase64String(base64);
```

## Configuration Options

| Property | Description | Default |
|----------|-------------|---------|
| `BaseUrl` | URL cơ sở của API | Required |
| `Account` | Tài khoản đăng nhập mặc định | null |
| `Password` | Mật khẩu mặc định | null |
| `TimeoutSeconds` | Thời gian timeout cho request (giây) | 30 |
| `AutoRefreshToken` | Tự động refresh token khi hết hạn | true |
| `TokenCacheMinutes` | Thời gian cache token (phút) | 60 |

## Error Codes

| Code | Description |
|------|-------------|
| 200/0 | Thành công |
| 400 | Bad Request - Dữ liệu đầu vào không hợp lệ |
| 401 | Unauthorized - Token không hợp lệ hoặc hết hạn |
| 403 | Forbidden - Không có quyền truy cập |
| 404 | Not Found - Không tìm thấy tài nguyên |
| 500 | Internal Server Error - Lỗi máy chủ |

## Thread Safety

Service này được thiết kế để thread-safe và có thể sử dụng trong môi trường multi-threaded. Token sẽ được cache và tự động refresh khi cần thiết.
