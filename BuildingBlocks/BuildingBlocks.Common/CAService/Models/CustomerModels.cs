using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.CAService.Models;

/// <summary>
/// Y<PERSON>u cầu tạo khách hàng mới
/// </summary>
public class CreateCustomerRequest
{
    /// <summary>
    /// Số CMND/CCCD
    /// </summary>
    [JsonPropertyName("identityNo")]
    public string IdentityNo { get; set; } = string.Empty;

    /// <summary>
    /// Email
    /// </summary>
    [JsonPropertyName("email")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Số điện thoại
    /// </summary>
    [JsonPropertyName("phoneNumber")]
    public string PhoneNumber { get; set; } = string.Empty;

    /// <summary>
    /// Họ và tên
    /// </summary>
    [JsonPropertyName("fullName")]
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Ảnh khuôn mặt dạng Base64
    /// </summary>
    [JsonPropertyName("faceImgBase64")]
    public string FaceImgBase64 { get; set; } = string.Empty;
}

/// <summary>
/// Dữ liệu phản hồi tạo khách hàng
/// </summary>
public class CreateCustomerResponseData
{
    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    [JsonPropertyName("isSuccess")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// ID khách hàng
    /// </summary>
    [JsonPropertyName("customerId")]
    public Guid? CustomerId { get; set; }
}

/// <summary>
/// Yêu cầu tìm kiếm khách hàng bằng khuôn mặt
/// </summary>
public class FindCustomerByFaceRequest
{
    /// <summary>
    /// Ảnh khuôn mặt dạng Base64
    /// </summary>
    [JsonPropertyName("faceImgBase64")]
    public string FaceImgBase64 { get; set; } = string.Empty;
}

/// <summary>
/// Thông tin khách hàng được tìm thấy
/// </summary>
public class CustomerFaceMatch
{
    /// <summary>
    /// ID khách hàng
    /// </summary>
    [JsonPropertyName("customerId")]
    public string CustomerId { get; set; } = string.Empty;

    /// <summary>
    /// Họ và tên
    /// </summary>
    [JsonPropertyName("fullName")]
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Số điện thoại
    /// </summary>
    [JsonPropertyName("phone")]
    public string Phone { get; set; } = string.Empty;

    /// <summary>
    /// Email
    /// </summary>
    [JsonPropertyName("email")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Số CMND/CCCD
    /// </summary>
    [JsonPropertyName("identityNo")]
    public string IdentityNo { get; set; } = string.Empty;
}

/// <summary>
/// Dữ liệu phản hồi tìm kiếm khách hàng bằng khuôn mặt
/// </summary>
public class FindCustomerByFaceResponseData
{
    /// <summary>
    /// Danh sách khách hàng tìm thấy
    /// </summary>
    [JsonPropertyName("faces")]
    public List<CustomerFaceMatch> Faces { get; set; } = new();
}

/// <summary>
/// Yêu cầu lấy PIN khách hàng
/// </summary>
public class GetCustomerPinRequest
{
    /// <summary>
    /// Ảnh khuôn mặt dạng Base64
    /// </summary>
    [JsonPropertyName("faceImgBase64")]
    public string FaceImgBase64 { get; set; } = string.Empty;

    /// <summary>
    /// ID khách hàng
    /// </summary>
    [JsonPropertyName("customerId")]
    public Guid? CustomerId { get; set; }
}

/// <summary>
/// Dữ liệu phản hồi lấy PIN khách hàng
/// </summary>
public class GetCustomerPinResponseData
{
    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    [JsonPropertyName("isSuccess")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// ID khách hàng
    /// </summary>
    [JsonPropertyName("customerId")]
    public Guid? CustomerId { get; set; }

    /// <summary>
    /// PIN khách hàng
    /// </summary>
    [JsonPropertyName("pin")]
    public string Pin { get; set; } = string.Empty;
}
