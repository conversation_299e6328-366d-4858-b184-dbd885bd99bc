namespace BuildingBlocks.Common.CAService.Models;

/// <summary>
/// C<PERSON>u hình cho CAService API
/// </summary>
public class CAServiceConfiguration
{
    /// <summary>
    /// URL cơ sở của API
    /// </summary>
    public string BaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// Tài khoản đăng nhập mặc định
    /// </summary>
    public string? Account { get; set; }

    /// <summary>
    /// Mật khẩu mặc định
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// Thời gian timeout cho request (giây)
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Tự động refresh token khi hết hạn
    /// </summary>
    public bool AutoRefreshToken { get; set; } = true;

    /// <summary>
    /// Thời gian cache token (phút)
    /// </summary>
    public int TokenCacheMinutes { get; set; } = 60;
}
