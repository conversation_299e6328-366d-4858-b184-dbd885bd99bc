using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.CAService.Models;

/// <summary>
/// Wrapper response chung cho tất cả API của CAService
/// </summary>
/// <typeparam name="T">Ki<PERSON>u dữ liệu của data</typeparam>
public class CAServiceResponse<T>
{
    /// <summary>
    /// Mã lỗi
    /// </summary>
    [JsonPropertyName("code")]
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Thông báo
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Chi tiết lỗi
    /// </summary>
    [JsonPropertyName("errors")]
    public string? Errors { get; set; }

    /// <summary>
    /// Trace ID
    /// </summary>
    [JsonPropertyName("traceId")]
    public string? TraceId { get; set; }

    /// <summary>
    /// Dữ liệu phản hồi
    /// </summary>
    [JsonPropertyName("data")]
    public T? Data { get; set; }

    /// <summary>
    /// Kiểm tra phản hồi có thành công không
    /// </summary>
    public bool IsSuccess => Code == "000" || Code == "0";
}
