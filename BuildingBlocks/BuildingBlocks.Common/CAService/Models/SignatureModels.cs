using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.CAService.Models;

/// <summary>
/// Thông tin PDF đã ký
/// </summary>
public class SignedPdf
{
    /// <summary>
    /// ID của PDF đã ký
    /// </summary>
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }

    /// <summary>
    /// ID khách hàng
    /// </summary>
    [JsonPropertyName("customerId")]
    public Guid? CustomerId { get; set; }

    /// <summary>
    /// Tên file
    /// </summary>
    [JsonPropertyName("fileName")]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// URL tải xuống
    /// </summary>
    [JsonPropertyName("downloadUrl")]
    public string DownloadUrl { get; set; } = string.Empty;

    /// <summary>
    /// Trạng thái
    /// </summary>
    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Khóa mã hóa
    /// </summary>
    [JsonPropertyName("encryptionKey")]
    public string EncryptionKey { get; set; } = string.Empty;
}

/// <summary>
/// Yêu cầu tạo request ký PDF
/// </summary>
public class CreateSignPdfRequest
{
    /// <summary>
    /// Tên file
    /// </summary>
    [JsonPropertyName("fileName")]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// ID khách hàng
    /// </summary>
    [JsonPropertyName("customerId")]
    public Guid? CustomerId { get; set; }
}

/// <summary>
/// Dữ liệu phản hồi tạo request ký PDF
/// </summary>
public class CreateSignPdfResponseData
{
    /// <summary>
    /// ID của PDF đã ký
    /// </summary>
    [JsonPropertyName("signedPDFId")]
    public Guid? SignedPDFId { get; set; }
}

/// <summary>
/// Yêu cầu ký PDF
/// </summary>
public class SignPdfRequest
{
    /// <summary>
    /// ID của PDF đã ký
    /// </summary>
    [JsonPropertyName("signedPDFId")]
    public Guid? SignedPDFId { get; set; }

    /// <summary>
    /// Nội dung file PDF dạng Base64
    /// </summary>
    [JsonPropertyName("fileBase64")]
    public string FileBase64 { get; set; } = string.Empty;

    /// <summary>
    /// Khóa dạng Base64
    /// </summary>
    [JsonPropertyName("keyBase64")]
    public string KeyBase64 { get; set; } = string.Empty;

    /// <summary>
    /// Tọa độ X
    /// </summary>
    [JsonPropertyName("x")]
    public int X { get; set; }

    /// <summary>
    /// Tọa độ Y
    /// </summary>
    [JsonPropertyName("y")]
    public int Y { get; set; }

    /// <summary>
    /// Chiều rộng
    /// </summary>
    [JsonPropertyName("width")]
    public int Width { get; set; }

    /// <summary>
    /// Chiều cao
    /// </summary>
    [JsonPropertyName("height")]
    public int Height { get; set; }

    /// <summary>
    /// Số trang
    /// </summary>
    [JsonPropertyName("pageNumber")]
    public int PageNumber { get; set; }
}

/// <summary>
/// Dữ liệu phản hồi ký PDF
/// </summary>
public class SignPdfResponseData
{
    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    [JsonPropertyName("isSuccess")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// ID của PDF đã ký
    /// </summary>
    [JsonPropertyName("signedPDFId")]
    public Guid? SignedPDFId { get; set; }
}
