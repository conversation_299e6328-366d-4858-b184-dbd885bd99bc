using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.CAService.Models;

/// <summary>
/// Yêu cầu đăng nhập hệ thống CA
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// Tài khoản đăng nhập
    /// </summary>
    [JsonPropertyName("account")]
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// Mật khẩu
    /// </summary>
    [JsonPropertyName("password")]
    public string Password { get; set; } = string.Empty;
}

/// <summary>
/// Thông tin người dùng
/// </summary>
public class UserInfo
{
    /// <summary>
    /// Tên đăng nhập
    /// </summary>
    [JsonPropertyName("username")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Số điện thoại
    /// </summary>
    [JsonPropertyName("phone")]
    public string Phone { get; set; } = string.Empty;

    /// <summary>
    /// Email
    /// </summary>
    [JsonPropertyName("email")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Họ và tên
    /// </summary>
    [JsonPropertyName("fullName")]
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Token truy cập
    /// </summary>
    [JsonPropertyName("token")]
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Refresh token
    /// </summary>
    [JsonPropertyName("refreshToken")]
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// Tên vai trò
    /// </summary>
    [JsonPropertyName("roleName")]
    public string RoleName { get; set; } = string.Empty;

    /// <summary>
    /// Thời gian hết hạn token
    /// </summary>
    [JsonPropertyName("tokenExpiresAt")]
    public DateTime TokenExpiresAt { get; set; }
}

/// <summary>
/// Dữ liệu phản hồi đăng nhập
/// </summary>
public class LoginResponseData
{
    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    [JsonPropertyName("isSuccess")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Email
    /// </summary>
    [JsonPropertyName("email")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Session ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public Guid? SessionId { get; set; }

    /// <summary>
    /// Thông tin người dùng
    /// </summary>
    [JsonPropertyName("userInfo")]
    public UserInfo? UserInfo { get; set; }

    /// <summary>
    /// Yêu cầu xác thực 2 bước
    /// </summary>
    [JsonPropertyName("isTwoFactorRequired")]
    public bool IsTwoFactorRequired { get; set; }
}
