﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.3" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.310.5" />
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="CsvHelper" Version="33.1.0" />
    <PackageReference Include="itext7" Version="7.2.5" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="PDFsharp" Version="6.2.0" />
    <PackageReference Include="Portable.BouncyCastle" Version="1.8.10" />
    <PackageReference Include="SkiaSharp" Version="3.119.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.50-g3f6a898e04" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.6" />
    <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
  </ItemGroup>
</Project>
