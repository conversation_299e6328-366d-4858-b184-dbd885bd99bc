using Newtonsoft.Json;

namespace BuildingBlocks.Common.EidCAGtel.DTOs;

/// <summary>
/// Request for document signature confirmation
/// </summary>
public class DocumentSignatureRequest
{
    [JsonProperty("code")]
    public string Code { get; set; } = string.Empty;

    [JsonProperty("id_number")]
    public string Id_Number { get; set; } = null!;

    [JsonProperty("transaction_code")]
    public string Transaction_Code { get; set; } = string.Empty;

    [JsonProperty("token_sign")]
    public string Token_Sign { get; set; } = string.Empty;

    [JsonProperty("info")]
    public SignatureContactInfo Info { get; set; } = new();

    [JsonProperty("doc_signs")]
    public List<DocumentSignature> Doc_Signs { get; set; } = [];
}

public class SignatureContactInfo
{
    [JsonProperty("image")]
    public string Image { get; set; } = string.Empty;
}

public class DocumentSignature
{
    [JsonProperty("doc_id")]
    public string Doc_Id { get; set; } = string.Empty;

    [JsonProperty("signature")]
    public string Signature { get; set; } = string.Empty;
}

/// <summary>
/// Response for document signature
/// </summary>
public class DocumentSignatureResponse : BaseResponse<DocumentSignatureData>
{
}

public class DocumentSignatureData
{
    [JsonProperty("transaction_code")]
    public string? TransactionCode { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("token")]
    public string? Token { get; set; }

    [JsonProperty("interval")]
    public int? Interval { get; set; }

    [JsonProperty("expires_at")]
    public int? ExpiresAt { get; set; }

    [JsonProperty("signed_docs")]
    public List<SignedDoc>? SignedDocs { get; set; }
}

public class SignedDoc
{
    [JsonProperty("doc_id")]
    public string? DocId { get; set; }

    [JsonProperty("doc_name")]
    public string? DocName { get; set; }

    [JsonProperty("ca_signature")]
    public string? CaSignature { get; set; }

    [JsonProperty("sign_at")]
    public int? SignAt { get; set; }

    [JsonProperty("expire_at")]
    public int? ExpireAt { get; set; }

    [JsonProperty("doc_hash")]
    public string? DocHash { get; set; }
}
