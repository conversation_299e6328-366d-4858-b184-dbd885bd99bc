using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace BuildingBlocks.Common.EidCAGtel.DTOs;

/// <summary>
/// Response for document signing challenge
/// </summary>
public class DocumentSignChallengeResponse : BaseResponse<DocumentSignChallengeData>
{
}

public class DocumentSignChallengeData
{
    [JsonProperty("transaction_code")]
    public string? TransactionCode { get; set; }

    [JsonProperty("token_sign")]
    public string? TokenSign { get; set; }

    [JsonProperty("docs")]
    public List<Doc>? Docs { get; set; }
}

public class Doc
{
    [JsonProperty("doc_id")]
    public string? DocId { get; set; }

    [JsonProperty("doc_name")]
    public string? DocName { get; set; }

    [JsonProperty("doc_type")]
    public string? DocType { get; set; }

    [JsonProperty("doc_challenge")]
    public string? DocChallenge { get; set; }
}