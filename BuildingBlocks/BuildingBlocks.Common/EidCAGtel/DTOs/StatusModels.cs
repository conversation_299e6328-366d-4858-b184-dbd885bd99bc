using Newtonsoft.Json;

namespace BuildingBlocks.Common.EidCAGtel.DTOs;

/// <summary>
/// Request for checking certificate status
/// </summary>
public class CheckStatusRequest
{
    [JsonProperty("code")]
    public string Code { get; set; } = string.Empty;

    [JsonProperty("transaction_code")]
    public string Transaction_Code { get; set; } = string.Empty;

    [JsonProperty("token_signature")]
    public string Token_Signature { get; set; } = string.Empty;
}

/// <summary>
/// Response for status check
/// </summary>
public class CheckStatusResponse : BaseResponse<StatusData>
{
}

public class GetExistCaResponse : BaseResponse<ExistCaData>
{
}

public class ExistCaData
{
        [JsonProperty("serial_number")]
        public string? SerialNumber { get; set; }

        [JsonProperty("id_number")]
        public string? IdNumber { get; set; }

        [JsonProperty("full_name")]
        public string? FullName { get; set; }

        [JsonProperty("phone")]
        public string? Phone { get; set; }

        [JsonProperty("email")]
        public string? Email { get; set; }

        [JsonProperty("cert")]
        public string? Cert { get; set; }

        [JsonProperty("date_issue")]
        public DateTime? DateIssue { get; set; }

        [JsonProperty("date_expire")]
        public DateTime? DateExpire { get; set; }
}

public class CheckIntervalResponse : BaseResponse<CheckIntervalData>
{
}

public class CheckIntervalData
{
    [JsonProperty("transaction_code")]
    public string? TransactionCode { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("cert_info")]
    public CertInfo? CertInfo { get; set; }
}

public class CertInfo
{
    [JsonProperty("serial_number")]
    public string? SerialNumber { get; set; }

    [JsonProperty("id_number")]
    public string? IdNumber { get; set; }

    [JsonProperty("full_name")]
    public string? FullName { get; set; }

    [JsonProperty("phone")]
    public string? Phone { get; set; }

    [JsonProperty("email")]
    public string? Email { get; set; }

    [JsonProperty("cert")]
    public string? Cert { get; set; }

    [JsonProperty("date_issue")]
    public DateTime? DateIssue { get; set; }

    [JsonProperty("date_expire")]
    public DateTime? DateExpire { get; set; }
}

public class StatusData
{
    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;

    [JsonProperty("certificate")]
    public CertificateInfo? Certificate { get; set; }
}

public class CertificateInfo
{
    [JsonProperty("serial")]
    public string Serial { get; set; } = string.Empty;

    [JsonProperty("issued_at")]
    public DateTime Issued_At { get; set; }

    [JsonProperty("expired_at")]
    public DateTime Expired_At { get; set; }

    [JsonProperty("subject")]
    public string Subject { get; set; } = string.Empty;

    [JsonProperty("issuer")]
    public string Issuer { get; set; } = string.Empty;
}
