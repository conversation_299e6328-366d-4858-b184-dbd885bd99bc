using Newtonsoft.Json;

namespace BuildingBlocks.Common.EidCAGtel.DTOs;

/// <summary>
/// Request for getting challenge token
/// </summary>
public class ChallengeRequest
{
    /// <summary>
    /// Customer code
    /// </summary>
    [JsonProperty("code")]
    public string Code { get; set; } = string.Empty;
    
    /// <summary>
    /// ID number from citizen card
    /// </summary>
    [JsonProperty("id_number")]
    public string Id_Number { get; set; } = string.Empty;
}

/// <summary>
/// Response for challenge request
/// </summary>
public class ChallengeResponse : BaseResponse<ChallengeData>
{
}

public class ChallengeData
{
    [JsonProperty("transaction_code")]
    public string Transaction_Code { get; set; } = string.Empty;
    
    [JsonProperty("token_challenge")]
    public string Token_Challenge { get; set; } = string.Empty;
}