using System;
using Newtonsoft.Json;

namespace BuildingBlocks.Common.EidCAGtel.DTOs;

/// <summary>
/// Request for signature verification
/// </summary>
public class SignatureRequest
{
    [JsonProperty("code")]
    public string Code { get; set; } = string.Empty;

    [JsonProperty("transaction_code")]
    public string Transaction_Code { get; set; } = string.Empty;

    [JsonProperty("token_challenge")]
    public string Token_Challenge { get; set; } = string.Empty;

    [JsonProperty("raw_data")]
    public RawData Raw_Data { get; set; } = new();

    [JsonProperty("info")]
    public ContactInfo Info { get; set; } = new();

    [JsonProperty("signature")]
    public string Signature { get; set; } = string.Empty;
}

public class RawData
{
    [JsonProperty("sod")]
    public string Sod { get; set; } = string.Empty;

    [JsonProperty("dg1")]
    public string Dg1 { get; set; } = string.Empty;

    [JsonProperty("dg2")]
    public string Dg2 { get; set; } = string.Empty;

    [JsonProperty("dg13")]
    public string Dg13 { get; set; } = string.Empty;

    [JsonProperty("dg15")]
    public string Dg15 { get; set; } = string.Empty;
}

public class ContactInfo
{
    [JsonProperty("phone")]
    public string? Phone { get; set; }

    [JsonProperty("email")]
    public string? Email { get; set; }

    [JsonProperty("image")]
    public string Image { get; set; } = string.Empty;
}

/// <summary>
/// Response for signature request
/// </summary>
public class SignatureResponse : BaseResponse<SignatureData>
{
}

public class SignatureData
{
    [JsonProperty("transaction_code")]
    public string? Transaction_code { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("interval")]
    public string? Interval { get; set; }

    [JsonProperty("expired_at")]
    public string? Expired_at { get; set; }

    [JsonProperty("token_signature")]
    public string? Token_signature { get; set; }
}
