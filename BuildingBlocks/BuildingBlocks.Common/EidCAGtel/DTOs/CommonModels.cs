using Newtonsoft.Json;

namespace BuildingBlocks.Common.EidCAGtel.DTOs
{
    public class BaseResponse<T>
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("error")]
        public ErrorObject? Error { get; set; }

        [JsonProperty("data")]
        public T? Data { get; set; }
    }

    public class ErrorObject
    {
        [JsonProperty("code")]
        public string Code { get; set; } = null!;
        
        [JsonProperty("message")]
        public string? Message { get; set; }
    }
} 