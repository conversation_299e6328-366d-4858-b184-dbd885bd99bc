using BuildingBlocks.Common.EidCAGtel.DTOs;
using Microsoft.AspNetCore.Http;
using Refit;

namespace BuildingBlocks.Common.EidCAGtel.Interface;

/// <summary>
/// Refit API interface for EIDCA service
/// </summary>
public interface IEidcaApiClient
{
    // Personal Onboarding APIs
    [Get("/ca/api/eid-personal/{idNumber}")]
    Task<GetExistCaResponse> GetExistCaAsync(
        string idNumber, 
        [Header("x-api-key")] string apiKey,
        [Header("code")] string code);
    
    [Post("/ca/api/eid-personal/challenge")]
    Task<ChallengeResponse> GetChallengeAsync([Body] ChallengeRequest request, [Header("x-api-key")] string apiKey);
    
    [Post("/ca/api/eid-personal/signature")]
    Task<SignatureResponse> SendSignatureAsync([Body] SignatureRequest request, [Header("x-api-key")] string apiKey);
    
    [Post("/ca/api/eid-personal/check")]
    Task<CheckIntervalResponse> CheckPersonalStatusAsync([Body] CheckStatusRequest request, [Header("x-api-key")] string apiKey);
    
    // Document Signing APIs
    [Multipart]
    [Post("/ca/api/sign/challenge")]
    Task<DocumentSignChallengeResponse> SendDocumentForSigningAsync(
        [AliasAs("documents")] StreamPart document,
        [AliasAs("id_number")] string idNumber,
        [AliasAs("security_level")] string securityLevel,
        [AliasAs("sign_props")] string? signProps,
        [AliasAs("doc_name")] string? docName,
        [Header("x-api-key")] string apiKey,
        [Header("code")] string code
        );
    
    [Post("/ca/api/sign/signature")]
    Task<DocumentSignatureResponse> ConfirmDocumentSignatureAsync([Body] DocumentSignatureRequest request, [Header("x-api-key")] string apiKey,[Header("code")] string code);
    
    [Post("/ca/api/sign/check")]
    Task<CheckStatusResponse> CheckDocumentSigningStatusAsync([Body] CheckStatusRequest request, [Header("x-api-key")] string apiKey);
    
    // Download signed file API
    [Get("/ca/api/sign/download/{docId}")]
    Task<HttpResponseMessage> DownloadSignedFileAsync(
        string docId,
        [Header("x-api-key")] string apiKey,
        [Header("code")] string code,
        [Header("transaction-code")] string transactionCode,
        [Header("token-sign")] string tokenSign,
        [Header("os-type")] string osType = "mobile"
        );
}
