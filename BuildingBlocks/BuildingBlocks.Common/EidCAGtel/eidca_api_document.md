# EIDCA - API Document

## 1. EID PERSONAL ONBOARDING API

### Flow Overview

1. EU đặt thẻ lên thiết bị -> Thiết bị đọc chip và xác minh tính toàn vẹn 2A. Toàn vẹn -> gửi CCCD để lấy Challenge 2B. Không toàn vẹn -> yê<PERSON> cầu EU thực hiện lại
2. <PERSON><PERSON>i CCCD -> tạo Challenge, Trans ID
3. Trả token challenge (timeout > 60s)
4. Token challenge được gửi về 6A. Nếu lỗi -> yêu cầu đặt lại thẻ 6B. <PERSON><PERSON><PERSON> tiếp và sinh challenge signature
5. Thiết bị stream hình ảnh
6. EU chụp hình và gửi lên HUB
7. Gửi payload RAW, signature, SĐT/Email 10A. eKYC pass -> lưu AA Signature, tạo CSR, request cấp CTS 10B. eKYC failed -> yêu cầu làm lại
8. Gửi payload đăng ký CTS
9. T<PERSON><PERSON> mã tra cứu và link form đăng ký 13A. Thành công -> tr<PERSON> mã giao dịch 13B. Thất bại -> yê<PERSON> cầu EU xác nhận lại 14A. Prompt xác nhận 14B. CKS failed -> yêu cầu thực hiện lại

---

### STEP 1: Lấy Challenge

- **Endpoint:** `{{api-base-url}}/ca/api/eid-personal/challenge`
- **Method:** POST
- **Headers:**
  - Content-type: application/json
  - x-api-key: APIKey value
- **Body:**

```json
{
  "code": "string",
  "id_number": "string"
}
```

- **Response:**

```json
{
  "success": true,
  "data": {
    "transaction_code": "string",
    "token_challenge": "string"
  },
  "error": {
    "code": "string",
    "message": "string"
  }
}
```

### STEP 2: Gửi signature, ảnh selfie và thông tin liên hệ

- **Endpoint:** `{{api-base-url}}/ca/api/eid-personal/signature`
- **Method:** POST
- **Headers:** như trên
- **Body:**

```json
{
  "code": "string",
  "transaction_code": "string",
  "token_challenge": "string",
  "raw_data": {
    "sod": "string",
    "dg1": "string",
    "dg2": "string",
    "dg13": "string",
    "dg15": "string"
  },
  "info": {
    "phone": "string",
    "email": "string",
    "image": "string"
  },
  "signature": "string"
}
```

- **Response:** bao gồm token\_signature, status, interval, expired\_at

### STEP 3: Kiểm tra trạng thái cấp CTS

- **Endpoint:** `{{api-base-url}}/ca/api/eid-personal/check`
- **Method:** POST
- **Body:**

```json
{
  "code": "string",
  "transaction_code": "string",
  "token_signature": "string"
}
```

- **Response:** gồm trạng thái CTS, thông tin chứng thư số (serial, ngày cấp, ngày hết hạn,...)

---

## 2. EID SIGN DOCUMENT API

### Flow Overview

1. EU gửi tài liệu và đặt thẻ chip 2A. Toàn vẹn -> gửi CCCD và tài liệu 2B. Không toàn vẹn -> yêu cầu làm lại
2. Tạo challenge từ tài liệu
3. Gửi challenge
4. Thiết bị nhận challenge 6A. Lỗi -> đặt lại thẻ 6B. Đọc tiếp
5. Stream hình ảnh EU
6. Chụp hình để eKYC
7. Gửi hình ảnh và token challenge 10A. eKYC Pass -> gửi signature + document\_id để ký 10B. eKYC Fail -> yêu cầu làm lại
8. Trả kết quả ký
9. Hiển thị vị trí ký
10. EU confirm

### STEP 1: Gửi tài liệu để ký

- **Endpoint:** `{{api-base-url}}/ca/api/sign/challenge`
- **Method:** POST
- **Headers:** như trên
- **Form Params:**
  - documents: File (PNG/JPG/XML/PDF/DOC, < 100MB)
  - id\_number: String
  - security\_level: LEVEL\_2
  - doc\_name: Optional
- **Response:** trả `transaction_code`, `token_sign`, danh sách tài liệu (doc\_id, challenge,...)

### STEP 2: Gửi ảnh selfie và chữ ký để xác nhận ký

- **Endpoint:** `{{api-base-url}}/ca/api/sign/signature`
- **Method:** POST
- **Body:**

```json
{
  "code": "string",
  "transaction_code": "string",
  "token_sign": "string",
  "info": {
    "image": "string"
  },
  "doc_signs": [
    {
      "doc_id": "string",
      "signature": "string"
    }
  ]
}
```

- **Response:** trạng thái ký, danh sách file đã ký (doc\_id, ca\_signature, timestamp,...)

### STEP 3: Kiểm tra trạng thái ký

- **Endpoint:** `{{api-base-url}}/ca/api/eid-personal/check`
- **Method:** POST
- **Body:** tương tự Step 2 với `token_sign`
- **Response:** giống step 2, trạng thái `signing`, `completed`, `failed`

---

## 3. EMBED SIGNATURES PDF

### STEP: Nhúng chữ ký vào PDF

- **Endpoint:** `{{api-base-url}}/ca/api/sign/get-file`
- **Method:** POST
- **Body:**

```json
{
  "code": "string",
  "id_number": "string",
  "property": {
    "lLx": 0,
    "lLy": 0,
    "width": 100,
    "height": 50,
    "location": "string",
    "reason": "string",
    "contact": "string",
    "font_size": 12,
    "page": 1,
    "text_color": "black",
    "password": "string",
    "font_name": "arial.ttf",
    "sign_visible_type": 1
  }
}
```

- **Response:**

```json
{
  "success": true,
  "data": {
    "transaction_code": "string",
    "responds": [
      {
        "singed_data_base64": "string"
      }
    ]
  },
  "error": {
    "code": "string",
    "message": "string"
  }
}
```

