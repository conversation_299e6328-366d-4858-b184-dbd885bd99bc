namespace BuildingBlocks.Common.EidCAGtel.Helper;

/// <summary>
/// Helper class for EIDCA operations
/// </summary>
public static class EidcaHelper
{
    /// <summary>
    /// Validate ID number format
    /// </summary>
    /// <param name="idNumber">ID number to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    public static bool IsValidIdNumber(string idNumber)
    {
        if (string.IsNullOrWhiteSpace(idNumber))
            return false;

        // Vietnamese ID card is 12 digits
        return idNumber.Length == 12 && idNumber.All(char.IsDigit);
    }

    /// <summary>
    /// Validate email format
    /// </summary>
    /// <param name="email">Email to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    public static bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Validate phone number format
    /// </summary>
    /// <param name="phone">Phone number to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    public static bool IsValidPhoneNumber(string phone)
    {
        if (string.IsNullOrWhiteSpace(phone))
            return false;

        // Remove common phone number separators
        var cleanPhone = phone.Replace("-", "").Replace(" ", "").Replace("(", "").Replace(")", "");
        
        // Vietnamese phone number validation
        return cleanPhone.Length >= 10 && cleanPhone.Length <= 11 && cleanPhone.All(char.IsDigit);
    }

    /// <summary>
    /// Check if certificate status is completed
    /// </summary>
    /// <param name="status">Status to check</param>
    /// <returns>True if completed, false otherwise</returns>
    public static bool IsCertificateStatusCompleted(string status)
    {
        return status.Equals("completed", StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Check if document signing status is completed
    /// </summary>
    /// <param name="status">Status to check</param>
    /// <returns>True if completed, false otherwise</returns>
    public static bool IsDocumentSigningCompleted(string status)
    {
        return status.Equals("completed", StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Check if process is still in progress
    /// </summary>
    /// <param name="status">Status to check</param>
    /// <returns>True if in progress, false otherwise</returns>
    public static bool IsProcessInProgress(string status)
    {
        return status.Equals("processing", StringComparison.OrdinalIgnoreCase) ||
               status.Equals("signing", StringComparison.OrdinalIgnoreCase) ||
               status.Equals("pending", StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Check if process has failed
    /// </summary>
    /// <param name="status">Status to check</param>
    /// <returns>True if failed, false otherwise</returns>
    public static bool IsProcessFailed(string status)
    {
        return status.Equals("failed", StringComparison.OrdinalIgnoreCase) ||
               status.Equals("error", StringComparison.OrdinalIgnoreCase) ||
               status.Equals("cancelled", StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Convert Base64 string to byte array
    /// </summary>
    /// <param name="base64String">Base64 string</param>
    /// <returns>Byte array</returns>
    public static byte[] ConvertBase64ToBytes(string base64String)
    {
        if (string.IsNullOrWhiteSpace(base64String))
            return Array.Empty<byte>();

        try
        {
            return Convert.FromBase64String(base64String);
        }
        catch
        {
            return Array.Empty<byte>();
        }
    }

    /// <summary>
    /// Convert byte array to Base64 string
    /// </summary>
    /// <param name="bytes">Byte array</param>
    /// <returns>Base64 string</returns>
    public static string ConvertBytesToBase64(byte[] bytes)
    {
        if (bytes == null || bytes.Length == 0)
            return string.Empty;

        try
        {
            return Convert.ToBase64String(bytes);
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary>
    /// Get supported file extensions for document signing
    /// </summary>
    /// <returns>List of supported extensions</returns>
    public static List<string> GetSupportedDocumentExtensions()
    {
        return new List<string> { ".pdf", ".png", ".jpg", ".jpeg", ".xml", ".doc", ".docx" };
    }

    /// <summary>
    /// Check if file extension is supported
    /// </summary>
    /// <param name="fileName">File name</param>
    /// <returns>True if supported, false otherwise</returns>
    public static bool IsFileExtensionSupported(string fileName)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            return false;

        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return GetSupportedDocumentExtensions().Contains(extension);
    }

    /// <summary>
    /// Get MIME type for file extension
    /// </summary>
    /// <param name="fileName">File name</param>
    /// <returns>MIME type</returns>
    public static string GetMimeType(string fileName)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            return "application/octet-stream";

        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".pdf" => "application/pdf",
            ".png" => "image/png",
            ".jpg" or ".jpeg" => "image/jpeg",
            ".xml" => "application/xml",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            _ => "application/octet-stream"
        };
    }
}
