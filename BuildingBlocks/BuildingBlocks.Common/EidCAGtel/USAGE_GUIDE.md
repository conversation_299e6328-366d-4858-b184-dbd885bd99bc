# EIDCA Digital Signature Library - Usage Guide

## Overview
This library provides a comprehensive interface for integrating with the EIDCA digital signature system using Vietnamese citizen ID cards. It supports both personal onboarding and document signing workflows.

## Installation & Configuration

### 1. Add to appsettings.json
```json
{
  "EidcaConfig": {
    "BaseUrl": "https://api-sandbox.jth.vn",
    "ApiKey": "your-api-key-here",
    "CustomerCode": "your-customer-code",
    "TimeoutSeconds": 60,
    "MaxRetryAttempts": 3,
    "EnableLogging": true
  }
}
```

### 2. Register services in Program.cs or Startup.cs
```csharp
using BuildingBlocks.Common.EidCAGtel.Extensions;

// In Program.cs
builder.Services.AddEidcaServices(builder.Configuration);

// In Startup.cs
services.AddEidcaServices(Configuration);
```

## Usage Examples

### Personal Onboarding Workflow

```csharp
using BuildingBlocks.Common.EidCAGtel.Interface;
using BuildingBlocks.Common.EidCAGtel.DTOs;
using BuildingBlocks.Common.EidCAGtel.Helper;

public class PersonalOnboardingService
{
    private readonly IEidcaService _eidcaService;
    
    public PersonalOnboardingService(IEidcaService eidcaService)
    {
        _eidcaService = eidcaService;
    }
    
    public async Task<bool> OnboardPersonAsync(string idNumber, string phone, string email, string selfieBase64, SignatureData signatureData)
    {
        // Step 1: Validate input
        if (!EidcaHelper.IsValidIdNumber(idNumber))
            throw new ArgumentException("Invalid ID number format");
            
        if (!EidcaHelper.IsValidEmail(email))
            throw new ArgumentException("Invalid email format");
            
        if (!EidcaHelper.IsValidPhoneNumber(phone))
            throw new ArgumentException("Invalid phone number format");
        
        // Step 2: Get challenge
        var challengeResponse = await _eidcaService.GetChallengeAsync(idNumber);
        if (!challengeResponse.Success)
        {
            throw new Exception($"Failed to get challenge: {challengeResponse.Error?.Message}");
        }
        
        // Step 3: Send signature and personal info
        var signatureRequest = new SignatureRequest
        {
            Transaction_Code = challengeResponse.Data.Transaction_Code,
            Token_Challenge = challengeResponse.Data.Token_Challenge,
            Raw_Data = new RawData
            {
                Sod = signatureData.Sod,
                Dg1 = signatureData.Dg1,
                Dg2 = signatureData.Dg2,
                Dg13 = signatureData.Dg13,
                Dg15 = signatureData.Dg15
            },
            Info = new ContactInfo
            {
                Phone = phone,
                Email = email,
                Image = selfieBase64
            },
            Signature = signatureData.Signature
        };
        
        var signatureResponse = await _eidcaService.SendSignatureAsync(signatureRequest);
        if (!signatureResponse.Success)
        {
            throw new Exception($"Failed to send signature: {signatureResponse.Error?.Message}");
        }
        
        // Step 4: Poll for certificate status
        var tokenSignature = signatureResponse.Data.Token_Signature;
        var transactionCode = challengeResponse.Data.Transaction_Code;
        
        while (true)
        {
            var statusResponse = await _eidcaService.CheckCertificateStatusAsync(transactionCode, tokenSignature);
            if (!statusResponse.Success)
            {
                throw new Exception($"Failed to check status: {statusResponse.Error?.Message}");
            }
            
            if (EidcaHelper.IsCertificateStatusCompleted(statusResponse.Data.Status))
            {
                // Certificate issued successfully
                return true;
            }
            else if (EidcaHelper.IsProcessFailed(statusResponse.Data.Status))
            {
                // Process failed
                return false;
            }
            
            // Wait before polling again
            await Task.Delay(5000);
        }
    }
}
```

### Document Signing Workflow

```csharp
public class DocumentSigningService
{
    private readonly IEidcaService _eidcaService;
    
    public DocumentSigningService(IEidcaService eidcaService)
    {
        _eidcaService = eidcaService;
    }
    
    public async Task<byte[]> SignDocumentAsync(string idNumber, IFormFile document, string selfieBase64, List<DocumentSignatureData> signatures)
    {
        // Step 1: Validate input
        if (!EidcaHelper.IsValidIdNumber(idNumber))
            throw new ArgumentException("Invalid ID number format");
            
        if (!EidcaHelper.IsFileExtensionSupported(document.FileName))
            throw new ArgumentException("File type not supported");
        
        // Step 2: Send document for signing
        var challengeResponse = await _eidcaService.SendDocumentForSigningAsync(idNumber, document);
        if (!challengeResponse.Success)
        {
            throw new Exception($"Failed to send document: {challengeResponse.Error?.Message}");
        }
        
        // Step 3: Confirm signatures
        var docSigns = signatures.Select(s => new DocumentSignature
        {
            Doc_Id = s.DocId,
            Signature = s.Signature
        }).ToList();
        
        var signatureRequest = new DocumentSignatureRequest
        {
            Transaction_Code = challengeResponse.Data.Transaction_Code,
            Token_Sign = challengeResponse.Data.Token_Sign,
            Info = new SignatureContactInfo
            {
                Image = selfieBase64
            },
            Doc_Signs = docSigns
        };
        
        var signatureResponse = await _eidcaService.ConfirmDocumentSignatureAsync(signatureRequest);
        if (!signatureResponse.Success)
        {
            throw new Exception($"Failed to confirm signature: {signatureResponse.Error?.Message}");
        }
        
        // Step 4: Poll for completion
        var tokenSign = challengeResponse.Data.Token_Sign;
        var transactionCode = challengeResponse.Data.Transaction_Code;
        
        while (true)
        {
            var statusResponse = await _eidcaService.CheckDocumentSigningStatusAsync(transactionCode, tokenSign);
            if (!statusResponse.Success)
            {
                throw new Exception($"Failed to check status: {statusResponse.Error?.Message}");
            }
            
            if (EidcaHelper.IsDocumentSigningCompleted(statusResponse.Data.Status))
            {
                // Step 5: Embed signature and get final document
                var embedRequest = new EmbedSignatureRequest
                {
                    Id_Number = idNumber,
                    Property = new SignatureProperty
                    {
                        LLx = 100,
                        LLy = 100,
                        Width = 200,
                        Height = 50,
                        Location = "Vietnam",
                        Reason = "Document signing",
                        Contact = "<EMAIL>",
                        Page = 1,
                        Sign_Visible_Type = 1
                    }
                };
                
                var embedResponse = await _eidcaService.EmbedSignatureAsync(embedRequest);
                if (!embedResponse.Success)
                {
                    throw new Exception($"Failed to embed signature: {embedResponse.Error?.Message}");
                }
                
                // Return signed document as bytes
                var signedDataBase64 = embedResponse.Data.Responds.First().Singed_Data_Base64;
                return EidcaHelper.ConvertBase64ToBytes(signedDataBase64);
            }
            else if (EidcaHelper.IsProcessFailed(statusResponse.Data.Status))
            {
                throw new Exception("Document signing failed");
            }
            
            // Wait before polling again
            await Task.Delay(5000);
        }
    }
}
```

### Simple Usage in Controller

```csharp
[ApiController]
[Route("api/[controller]")]
public class DigitalSignatureController : ControllerBase
{
    private readonly IEidcaService _eidcaService;
    
    public DigitalSignatureController(IEidcaService eidcaService)
    {
        _eidcaService = eidcaService;
    }
    
    [HttpPost("onboard")]
    public async Task<IActionResult> OnboardPerson([FromBody] OnboardRequest request)
    {
        try
        {
            var challengeResponse = await _eidcaService.GetChallengeAsync(request.IdNumber);
            
            if (!challengeResponse.Success)
            {
                return BadRequest(challengeResponse.Error);
            }
            
            return Ok(challengeResponse.Data);
        }
        catch (Exception ex)
        {
            return StatusCode(500, ex.Message);
        }
    }
    
    [HttpPost("sign-document")]
    public async Task<IActionResult> SignDocument(IFormFile document, [FromForm] string idNumber)
    {
        try
        {
            var response = await _eidcaService.SendDocumentForSigningAsync(idNumber, document);
            
            if (!response.Success)
            {
                return BadRequest(response.Error);
            }
            
            return Ok(response.Data);
        }
        catch (Exception ex)
        {
            return StatusCode(500, ex.Message);
        }
    }
}
```

## Error Handling

```csharp
try
{
    var result = await _eidcaService.GetChallengeAsync(idNumber);
}
catch (EidcaValidationException ex)
{
    // Handle validation errors
    logger.LogWarning("Validation error: {Message}", ex.Message);
}
catch (EidcaApiException ex)
{
    // Handle API errors
    logger.LogError("API error {StatusCode}: {Message}", ex.StatusCode, ex.Message);
}
catch (EidcaTimeoutException ex)
{
    // Handle timeout errors
    logger.LogError("Timeout error: {Message}", ex.Message);
}
catch (EidcaException ex)
{
    // Handle other EIDCA errors
    logger.LogError("EIDCA error {ErrorCode}: {Message}", ex.ErrorCode, ex.Message);
}
```

## Helper Functions

```csharp
// Validation helpers
bool isValidId = EidcaHelper.IsValidIdNumber("123456789012");
bool isValidEmail = EidcaHelper.IsValidEmail("<EMAIL>");
bool isValidPhone = EidcaHelper.IsValidPhoneNumber("0123456789");

// Status checking helpers
bool isCompleted = EidcaHelper.IsCertificateStatusCompleted("completed");
bool isInProgress = EidcaHelper.IsProcessInProgress("processing");
bool isFailed = EidcaHelper.IsProcessFailed("failed");

// File handling helpers
bool isSupported = EidcaHelper.IsFileExtensionSupported("document.pdf");
string mimeType = EidcaHelper.GetMimeType("document.pdf");
byte[] fileBytes = EidcaHelper.ConvertBase64ToBytes(base64String);
```

## Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| BaseUrl | EIDCA API base URL | Required |
| ApiKey | API key for authentication | Required |
| CustomerCode | Customer code | Required |
| TimeoutSeconds | HTTP request timeout | 60 |
| MaxRetryAttempts | Max retry attempts for failed requests | 3 |
| EnableLogging | Enable detailed logging | true |

## Supported File Types

- PDF (.pdf)
- PNG (.png)
- JPEG (.jpg, .jpeg)
- XML (.xml)
- Microsoft Word (.doc, .docx)

## Error Codes

- `VALIDATION_ERROR`: Input validation failed
- `API_ERROR`: API request failed
- `CONFIGURATION_ERROR`: Configuration error
- `CERTIFICATE_ERROR`: Certificate related error
- `SIGNATURE_ERROR`: Signature related error
- `TIMEOUT_ERROR`: Request timeout
- `INTERNAL_ERROR`: Internal system error
