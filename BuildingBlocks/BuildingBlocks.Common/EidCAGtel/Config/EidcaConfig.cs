namespace BuildingBlocks.Common.EidCAGtel.Config;

/// <summary>
/// Configuration for EIDCA API
/// </summary>
public class EidcaConfig
{
    public const string SectionName = "EidcaConfig";
    
    /// <summary>
    /// Base URL for EIDCA API
    /// </summary>
    public string BaseUrl { get; set; } = string.Empty;
    
    /// <summary>
    /// API Key for authentication
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;
    
    /// <summary>
    /// Customer code
    /// </summary>
    public string CustomerCode { get; set; } = string.Empty;
    
    /// <summary>
    /// Request timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 60;
    
    /// <summary>
    /// Max retry attempts
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;
    
    /// <summary>
    /// Enable logging
    /// </summary>
    public bool EnableLogging { get; set; } = true;
}
