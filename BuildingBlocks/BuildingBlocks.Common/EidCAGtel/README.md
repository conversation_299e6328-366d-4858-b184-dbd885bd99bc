# EIDCA Digital Signature Library

A comprehensive .NET library for integrating with the EIDCA digital signature system using Vietnamese citizen ID cards.

## Features

- **Personal Onboarding**: Complete digital certificate issuance workflow
- **Document Signing**: Sign documents with digital signatures
- **Status Monitoring**: Real-time status checking with polling
- **Error Handling**: Comprehensive error handling and retry mechanisms
- **Validation**: Built-in validation for Vietnamese ID numbers, emails, and phone numbers
- **Logging**: Configurable logging for monitoring and debugging
- **Async/Await**: Full async support with cancellation tokens

## Quick Start

### 1. Configuration

Add to your `appsettings.json`:

```json
{
  "EidcaConfig": {
    "BaseUrl": "https://api-sandbox.jth.vn",
    "ApiKey": "your-api-key",
    "CustomerCode": "your-customer-code",
    "TimeoutSeconds": 60,
    "MaxRetryAttempts": 3,
    "EnableLogging": true
  }
}
```

### 2. Service Registration

In `Program.cs`:

```csharp
using BuildingBlocks.Common.EidCAGtel.Extensions;

builder.Services.AddEidcaServices(builder.Configuration);
```

### 3. Usage

```csharp
public class MyService
{
    private readonly IDigitalSignatureService _digitalSignatureService;
    
    public MyService(IDigitalSignatureService digitalSignatureService)
    {
        _digitalSignatureService = digitalSignatureService;
    }
    
    public async Task<bool> OnboardUserAsync(string idNumber, string phone, string email)
    {
        // Get challenge
        var challenge = await _digitalSignatureService.GetChallengeAsync(idNumber);
        
        // Complete onboarding (after getting signature data from card)
        var success = await _digitalSignatureService.CompletePersonalOnboardingAsync(
            idNumber, phone, email, selfieBase64, rawData, signature);
            
        return success;
    }
    
    public async Task<byte[]> SignDocumentAsync(string idNumber, IFormFile document)
    {
        var signatures = new List<DocumentSignature>
        {
            new DocumentSignature { Doc_Id = "doc1", Signature = "signature_data" }
        };
        
        var signedDocument = await _digitalSignatureService.SignDocumentAsync(
            idNumber, document, selfieBase64, signatures);
            
        return signedDocument;
    }
}
```

## API Reference

### Services

#### `IDigitalSignatureService`
High-level service for common digital signature operations.

- `GetChallengeAsync(idNumber)` - Get challenge token
- `CompletePersonalOnboardingAsync(...)` - Complete onboarding workflow
- `SignDocumentAsync(...)` - Sign document workflow
- `CheckStatusAsync(...)` - Check operation status

#### `IEidcaService`
Low-level service for direct API access.

- `GetChallengeAsync(idNumber)` - Get challenge token
- `SendSignatureAsync(request)` - Send signature data
- `CheckCertificateStatusAsync(...)` - Check certificate status
- `SendDocumentForSigningAsync(...)` - Send document for signing
- `ConfirmDocumentSignatureAsync(...)` - Confirm document signature
- `CheckDocumentSigningStatusAsync(...)` - Check document signing status
- `EmbedSignatureAsync(...)` - Embed signature in PDF

### Data Models

#### Personal Onboarding
- `ChallengeRequest` / `ChallengeResponse`
- `SignatureRequest` / `SignatureResponse`
- `CheckStatusRequest` / `CheckStatusResponse`

#### Document Signing
- `DocumentSignChallengeRequest` / `DocumentSignChallengeResponse`
- `DocumentSignatureRequest` / `DocumentSignatureResponse`
- `EmbedSignatureRequest` / `EmbedSignatureResponse`

### Helper Functions

#### `EidcaHelper`
Utility functions for validation and data conversion.

- `IsValidIdNumber(idNumber)` - Validate Vietnamese ID number
- `IsValidEmail(email)` - Validate email format
- `IsValidPhoneNumber(phone)` - Validate phone number
- `IsCertificateStatusCompleted(status)` - Check if certificate is ready
- `IsDocumentSigningCompleted(status)` - Check if document signing is done
- `IsProcessFailed(status)` - Check if process failed
- `ConvertBase64ToBytes(base64)` - Convert Base64 to bytes
- `IsFileExtensionSupported(fileName)` - Check supported file types

## Error Handling

The library provides specific exception types:

- `EidcaException` - Base exception
- `EidcaValidationException` - Validation errors
- `EidcaApiException` - API errors
- `EidcaConfigurationException` - Configuration errors
- `EidcaCertificateException` - Certificate errors
- `EidcaSignatureException` - Signature errors
- `EidcaTimeoutException` - Timeout errors

## Supported File Types

- PDF (.pdf)
- PNG (.png)
- JPEG (.jpg, .jpeg)
- XML (.xml)
- Microsoft Word (.doc, .docx)

## Workflows

### Personal Onboarding Workflow
1. Get challenge token with ID number
2. Read data from citizen ID card
3. Take selfie photo
4. Send signature data + selfie + contact info
5. Poll for certificate status
6. Certificate issued when status = "completed"

### Document Signing Workflow
1. Send document file with ID number
2. Get challenge tokens for each document
3. Read data from citizen ID card
4. Take selfie photo
5. Send signatures + selfie
6. Poll for signing completion
7. Embed signature in PDF
8. Download signed document

## Configuration Options

| Property | Description | Default | Required |
|----------|-------------|---------|----------|
| BaseUrl | EIDCA API endpoint | - | Yes |
| ApiKey | API authentication key | - | Yes |
| CustomerCode | Customer identifier | - | Yes |
| TimeoutSeconds | Request timeout | 60 | No |
| MaxRetryAttempts | Max retry count | 3 | No |
| EnableLogging | Enable detailed logging | true | No |

## Logging

The library uses Microsoft.Extensions.Logging for comprehensive logging:

- Information: Normal operations
- Warning: Retry attempts
- Error: Failures and exceptions
- Debug: Detailed debugging info

## Examples

See `USAGE_GUIDE.md` for detailed examples and integration patterns.

## Dependencies

- Microsoft.Extensions.DependencyInjection
- Microsoft.Extensions.Configuration
- Microsoft.Extensions.Logging
- Microsoft.Extensions.Options
- Microsoft.AspNetCore.Http.Features
- Refit.HttpClientFactory
- System.Text.Json

## License

This library is part of the GoTRUST.EMR project building blocks.
