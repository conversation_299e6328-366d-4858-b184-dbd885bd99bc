using BuildingBlocks.Common.EidCAGtel.Config;
using BuildingBlocks.Common.EidCAGtel.Interface;
using BuildingBlocks.Common.EidCAGtel.Service;
using Microsoft.Extensions.DependencyInjection;
using Refit;
using System.Text.Json;

namespace BuildingBlocks.Common.EidCAGtel.Extensions;

/// <summary>
/// Extension methods for dependency injection
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Add EIDCA services to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddEidca(this IServiceCollection services, Action<EidcaConfig> configureOptions)
    {
        var config = new EidcaConfig();
        configureOptions(config);

        if (string.IsNullOrEmpty(config.BaseUrl))
            throw new ArgumentException("BaseUrl is required", nameof(config));

        services.AddSingleton(config);

        // Register Refit HTTP client
        services.AddRefitClient<IEidcaApiClient>(new RefitSettings
        {
            ContentSerializer = new SystemTextJsonContentSerializer(new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
                WriteIndented = true,
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
            })
        })
        .ConfigureHttpClient(client =>
        {
            client.BaseAddress = new Uri(config.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(config.TimeoutSeconds);
        });

        // Register services
        services.AddScoped<IEidcaService, EidcaService>();
        services.AddScoped<EidcaService>();

        return services;
    }
}
