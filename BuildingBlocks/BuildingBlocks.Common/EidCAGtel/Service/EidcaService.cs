using BuildingBlocks.Common.EidCAGtel.Config;
using BuildingBlocks.Common.EidCAGtel.DTOs;
using BuildingBlocks.Common.EidCAGtel.Interface;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Refit;
using System.Net;

namespace BuildingBlocks.Common.EidCAGtel.Service;

/// <summary>
/// Implementation of EIDCA digital signature service
/// </summary>
public class EidcaService(
    IEidcaApiClient apiClient,
    EidcaConfig config,
    ILogger<EidcaService> logger) : IEidcaService
{
    public async Task<ChallengeResponse> GetChallengeAsync(string idNumber, CancellationToken cancellationToken = default)
    {
        var request = new ChallengeRequest
        {
            Code = config.CustomerCode,
            Id_Number = idNumber
        };

        return await ExecuteWithRetryAsync<ChallengeResponse, ChallengeData>(
            async () => await apiClient.GetChallengeAsync(request, config.ApiKey),
            nameof(GetChallengeAsync),
            $"ID: {idNumber}");
    }

    public async Task<SignatureResponse> SendSignatureAsync(SignatureRequest request, CancellationToken cancellationToken = default)
    {
        request.Code = config.CustomerCode;
        return await ExecuteWithRetryAsync<SignatureResponse, SignatureData>(
            async () => await apiClient.SendSignatureAsync(request, config.ApiKey),
            nameof(SendSignatureAsync),
            $"Transaction: {request.Transaction_Code}");
    }

    public async Task<CheckIntervalResponse> CheckPersonalStatusAsync(string transactionCode, string tokenSignature, CancellationToken cancellationToken = default)
    {
        var request = new CheckStatusRequest
        {
            Code = config.CustomerCode,
            Transaction_Code = transactionCode,
            Token_Signature = tokenSignature
        };
        
        return await ExecuteWithRetryAsync<CheckIntervalResponse, CheckIntervalData>(
            async () => await apiClient.CheckPersonalStatusAsync(request, config.ApiKey),
            nameof(CheckPersonalStatusAsync),
            $"Transaction: {transactionCode}");
    }

    public async Task<DocumentSignChallengeResponse> SendDocumentForSigningAsync(string idNumber, IFormFile document, string? signProps, string? docName, string securityLevel = "LEVEL_2", CancellationToken cancellationToken = default)
    {
        await using var stream = document.OpenReadStream();
        var streamPart = new StreamPart(stream, document.FileName, document.ContentType);

        return await ExecuteWithRetryAsync<DocumentSignChallengeResponse, DocumentSignChallengeData>(
            async () => await apiClient.SendDocumentForSigningAsync(streamPart, idNumber, securityLevel, signProps, docName, config.ApiKey, config.CustomerCode),
            nameof(SendDocumentForSigningAsync),
            $"ID: {idNumber}, Document: {docName ?? document.FileName}");
    }

    public async Task<DocumentSignatureResponse> ConfirmDocumentSignatureAsync(DocumentSignatureRequest request, CancellationToken cancellationToken = default)
    {
        request.Code = config.CustomerCode;
        return await ExecuteWithRetryAsync<DocumentSignatureResponse, DocumentSignatureData>(
            async () => await apiClient.ConfirmDocumentSignatureAsync(request, config.ApiKey, config.CustomerCode),
            nameof(ConfirmDocumentSignatureAsync),
            $"Transaction: {request.Transaction_Code}");
    }

    public async Task<CheckStatusResponse> CheckDocumentSigningStatusAsync(string transactionCode, string tokenSign, CancellationToken cancellationToken = default)
    {
        var request = new CheckStatusRequest
        {
            Code = config.CustomerCode,
            Transaction_Code = transactionCode,
            Token_Signature = tokenSign
        };

        return await ExecuteWithRetryAsync<CheckStatusResponse, StatusData>(
            async () => await apiClient.CheckDocumentSigningStatusAsync(request, config.ApiKey),
            nameof(CheckDocumentSigningStatusAsync),
            $"Transaction: {transactionCode}");
    }

    public async Task<Stream?> DownloadSignedFileAsync(string docId, string transactionCode, string tokenSign, string osType = "mobile", CancellationToken cancellationToken = default)
    {
        if (config.EnableLogging)
        {
            logger.LogInformation("EIDCA: Downloading signed file with docId: {docId}", docId);
        }
        
        var response = await apiClient.DownloadSignedFileAsync(docId, config.ApiKey, config.CustomerCode, transactionCode, tokenSign, osType);

        if (!response.IsSuccessStatusCode)
        {
            return null;
        }
        
        if (config.EnableLogging)
        {
            logger.LogInformation("EIDCA: File download completed for docId: {docId}", docId);
        }

        return await response.Content.ReadAsStreamAsync(cancellationToken);
    }
    
    public async Task<GetExistCaResponse> GetExistCaAsync(string idNumber, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithRetryAsync<GetExistCaResponse, ExistCaData>(
            async () => await apiClient.GetExistCaAsync(idNumber, config.ApiKey, config.CustomerCode),
            nameof(GetExistCaAsync),
            $"ID: {idNumber}");
    }
    
    private async Task<T> ExecuteWithRetryAsync<T, TData>(Func<Task<T>> operation, string callingMethod, string logContext) where T : BaseResponse<TData>, new()
    {
        if (config.EnableLogging)
        {
            logger.LogInformation("EIDCA: Starting {CallingMethod} for {LogContext}", callingMethod, logContext);
        }

        var attempt = 0;
        while (true)
        {
            try
            {
                var response = await operation();
                if (config.EnableLogging)
                {
                    if (!response.Success)
                    {
                        logger.LogWarning("EIDCA API returned an error for {CallingMethod}. Code: {ErrorCode}, Message: {ErrorMessage}", callingMethod, response.Error?.Code, response.Error?.Message);
                    }
                    else
                    {
                        logger.LogInformation("EIDCA: {CallingMethod} completed successfully for {LogContext}", callingMethod, logContext);
                    }
                }
                return response;
            }
            catch (ApiException ex) when (ex.StatusCode == HttpStatusCode.TooManyRequests || ex.StatusCode == HttpStatusCode.RequestTimeout)
            {
                attempt++;
                if (attempt >= config.MaxRetryAttempts)
                {
                    logger.LogError(ex, "EIDCA: {CallingMethod} failed after {MaxAttempts} attempts due to API exception for {LogContext}.", callingMethod, config.MaxRetryAttempts, logContext);
                    return new T
                    {
                        Success = false,
                        Error = new ErrorObject { Code = ex.StatusCode.ToString(), Message = $"API error after retries: {ex.Message}" }
                    };
                }
                var delay = TimeSpan.FromSeconds(Math.Pow(2, attempt));
                logger.LogWarning(ex, "EIDCA: API exception in {CallingMethod}. Retrying in {Delay}s. Attempt {Attempt}/{MaxAttempts}", callingMethod, delay.TotalSeconds, attempt, config.MaxRetryAttempts);
                await Task.Delay(delay, CancellationToken.None);
            }
            catch (ApiException ex)
            {
                logger.LogError(ex, "EIDCA: API exception occurred in {CallingMethod} for {LogContext}. Status: {StatusCode}, Content: {Content}",
                    callingMethod, logContext, ex.StatusCode, ex.Content);

                var errorMessage = ExtractErrorMessage(ex.Content) ?? ex.Message;

                return new T
                {
                    Success = false,
                    Error = new ErrorObject
                    {
                        Code = ex.StatusCode.ToString(),
                        Message = errorMessage
                    }
                };
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "EIDCA: An unexpected exception occurred in {CallingMethod} for {LogContext}.", callingMethod, logContext);
                return new T
                {
                    Success = false,
                    Error = new ErrorObject { Code = "INTERNAL_ERROR", Message = $"An unexpected error occurred: {ex.Message}" }
                };
            }
        }
    }

    private static string? ExtractErrorMessage(string? content)
    {
        if (string.IsNullOrEmpty(content))
            return null;

        try
        {
            var errorResponse = JsonConvert.DeserializeObject<dynamic>(content);

            if (errorResponse?.error?.message != null)
            {
                return errorResponse.error.message.ToString();
            }

            if (errorResponse?.message != null)
            {
                return errorResponse.message.ToString();
            }

            return null;
        }
        catch
        {
            return null;
        }
    }
}
