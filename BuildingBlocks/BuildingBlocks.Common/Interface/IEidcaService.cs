using BuildingBlocks.Common.EidCAGtel.DTOs;
using Microsoft.AspNetCore.Http;

namespace BuildingBlocks.Common.EidCAGtel.Interface;

/// <summary>
/// Interface for EIDCA digital signature service
/// </summary>
public interface IEidcaService
{
    // Personal Onboarding APIs
    /// <summary>
    /// Step 1: Get challenge token for personal onboarding
    /// </summary>
    /// <param name="idNumber">ID number from citizen card</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Challenge response</returns>
    Task<ChallengeResponse> GetChallengeAsync(string idNumber, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Step 2: Send signature and selfie for personal onboarding
    /// </summary>
    /// <param name="request">Signature request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Signature response</returns>
    Task<SignatureResponse> SendSignatureAsync(SignatureRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Step 3: Check certificate status
    /// </summary>
    /// <param name="transactionCode">Transaction code</param>
    /// <param name="tokenSignature">Token signature</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Status response</returns>
    Task<CheckIntervalResponse> CheckPersonalStatusAsync(string transactionCode, string tokenSignature, CancellationToken cancellationToken = default);
    
    // Document Signing APIs
    /// <summary>
    /// Step 1: Send document for signing challenge
    /// </summary>
    /// <param name="idNumber">ID number from citizen card</param>
    /// <param name="document">Document file to sign</param>
    /// <param name="signProps">Signing properties in JSON format</param>
    /// <param name="docName">Optional document name</param>
    /// <param name="securityLevel">Security level (default: LEVEL_2)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document sign challenge response</returns>
    Task<DocumentSignChallengeResponse> SendDocumentForSigningAsync(string idNumber, IFormFile document, string? signProps, string? docName, string securityLevel = "LEVEL_2", CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Step 2: Confirm document signature
    /// </summary>
    /// <param name="request">Document signature request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document signature response</returns>
    Task<DocumentSignatureResponse> ConfirmDocumentSignatureAsync(DocumentSignatureRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Step 3: Check document signing status
    /// </summary>
    /// <param name="transactionCode">Transaction code</param>
    /// <param name="tokenSign">Token sign</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Status response</returns>
    Task<CheckStatusResponse> CheckDocumentSigningStatusAsync(string transactionCode, string tokenSign, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Download signed file
    /// </summary>
    /// <param name="docId">Document Id</param>
    /// <param name="transactionCode">Transaction code</param>
    /// <param name="tokenSign">Token sign</param>
    /// <param name="osType">OS type (default: mobile)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File stream</returns>
    Task<Stream?> DownloadSignedFileAsync(string docId, string transactionCode, string tokenSign, string osType = "mobile", CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get existing Certificate Authority
    /// </summary>
    /// <param name="idNumber">ID number</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>CA Status</returns>
    Task<GetExistCaResponse> GetExistCaAsync(string idNumber, CancellationToken cancellationToken = default);
}
