using BuildingBlocks.Common.EasySign.Models;

namespace BuildingBlocks.Common.Interface
{
    public interface ISignService
    {
        Task<string> AuthenticateAsync(string? username = null, string? password = null, bool rememberMe = false);
        Task<string> GetValidTokenAsync();
        Task<EasySignResponse<SignHashRawResponseData>> SignHashAsync(SignHashRequest request);
        Task<EasySignResponse<SignHashRawResponseData>> SignHashAsync(
            string hashContent,
            string key,
            string serial,
            string pin,
            string hashAlgorithm = "SHA256",
            string? signer = null,
            string? otpCode = null);
        Task<EasySignResponse<SignHashRawResponseData>> SignRawAsync(SignRawRequest request);
        Task<EasySignResponse<SignHashRawResponseData>> SignRawAsync(
            string rawContent,
            string key,
            string serial,
            string pin,
            string hashAlgorithm = "SHA256",
            string? signer = null,
            string? otpCode = null);
        Task<EasySignResponse<SignHashRawResponseData>> SignTextAsync(
            string text,
            string key,
            string serial,
            string pin,
            string hashAlgorithm = "SHA256",
            string? signer = null,
            string? otpCode = null);
        Task<EasySignResponse<SignPdfResponseData>> SignInvisiblePdfAsync(SignInvisiblePdfRequest request);
        Task<EasySignResponse<SignPdfResponseData>> SignInvisiblePdfAsync(
            string pdfBase64,
            string documentName,
            string serial,
            string pin,
            string? otpCode = null);
        Task<EasySignResponse<SignPdfResponseData>> SignInvisiblePdfFromFileAsync(
            string pdfFilePath,
            string documentName,
            string serial,
            string pin,
            string? otpCode = null);
        Task<EasySignResponse<string>> SignVisiblePdfAsync(SignVisiblePdfRequest request);
        Task<EasySignResponse<string>> SignVisiblePdfAsync(
            string pdfBase64,
            string documentName,
            string serial,
            string pin,
            int x,
            int y,
            int width,
            int height,
            int pageNum,
            string? imageSignature = null,
            string? otpCode = null);
        Task<EasySignResponse<SignXmlResponseData>> SignXmlAsync(SignXmlRequest request);
        Task<EasySignResponse<SignXmlResponseData>> SignXmlAsync(
            string xmlBase64,
            string documentName,
            string serial,
            string pin,
            string? otpCode = null);
        Task<EasySignResponse<SignXmlResponseData>> SignXmlFromFileAsync(
            string xmlFilePath,
            string documentName,
            string serial,
            string pin,
            string? otpCode = null);
        Task<EasySignResponse<VerificationResponseData>> VerifyRawAsync(VerifyRawRequest request);
        Task<EasySignResponse<VerificationResponseData>> VerifyHashAsync(VerifyHashRequest request);
        Task<EasySignResponse<DocumentVerificationResponseData>> VerifyPdfAsync(Stream pdfStream, string fileName);
        Task<EasySignResponse<DocumentVerificationResponseData>> VerifyPdfFromFileAsync(string pdfFilePath);
        Task<EasySignResponse<DocumentVerificationResponseData>> VerifyXmlAsync(Stream xmlStream, string fileName);
        Task<EasySignResponse<DocumentVerificationResponseData>> VerifyXmlFromFileAsync(string xmlFilePath);
        Task<EasySignResponse<string>> GetCertificateImageAsync(string serial, string pin, string? position = null, string? timeSign = null);
        Task<EasySignResponse<string>> GetCertificateImageByTemplateIdAsync(string serial, string pin, int? templateId = null);
        Task<EasySignResponse<object>> ChangeCertificatePinAsync(ChangeCertPinRequest request);
        Task<EasySignResponse<object>> ChangeCertificatePinAsync(string serial, string oldPin, string newPin, string? otpCode = null);
        Task<EasySignResponse<string>> ResetCertificatePinAsync(string serial, string masterKey);
        Task<EasySignResponse<string>> GetCertificateAsync(string serial, string pin);
        Task<EasySignResponse<List<SignatureTemplate>>> GetSignatureTemplatesByUsernameAsync(string username);
    }
}