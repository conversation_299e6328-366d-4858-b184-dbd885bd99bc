namespace BuildingBlocks.Common.Interface
{
    public interface IFileStorageService
    {
        /// <summary>
        /// Uploads a file to storage.
        /// </summary>
        /// <param name="data">The byte array containing the file data.</param>
        /// <param name="key">The key under which to store the file.</param>
        /// <returns>A tuple containing (success status, URL, error message if any).</returns>
        Task<(bool Success, string Message, string FileUrl)> UploadFileAsync(MemoryStream data, string key);
        Task<(bool Success, string Message, string FileUrl)> UploadFileAsync(FileStream data, string key);
        Task<(bool Success, string Message)> DeleteFileAsync(string key);
        Task<(bool Success, string Message)> DeleteFilesAsync(IEnumerable<string> keys);
    }
}
