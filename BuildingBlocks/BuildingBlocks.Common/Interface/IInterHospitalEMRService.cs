using BuildingBlocks.Common.EMRApi.Models;

namespace BuildingBlocks.Common.Interface;

/// <summary>
/// Interface cho Inter-Hospital EMR Service
/// Dành riêng cho việc tra cứu bệnh án liên viện từ hệ thống EMR bên ngoài
/// </summary>
public interface IInterHospitalEMRService
{
    /// <summary>
    /// Tra cứu thông tin bệnh án liên viện từ hệ thống EMR bên ngoài
    /// </summary>
    /// <param name="request">Request chứa thông tin truy vấn (HospitalId, PatientId)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns><PERSON><PERSON> sách hồ sơ bệnh án từ hệ thống liên viện</returns>
    Task<EMRApiResponse<List<HoSoBenhAn>>> GetInterHospitalEMRAsync(
        GetEMRRequest request,
        CancellationToken cancellationToken = default);
}
