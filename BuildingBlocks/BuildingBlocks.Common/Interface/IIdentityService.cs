using BuildingBlocks.Common.IdentityClient.Models;

namespace BuildingBlocks.Common.Interface
{
    /// <summary>
    /// Interface cho dịch vụ xác thực khuôn mặt và định danh CCCD
    /// Hỗ trợ các chức năng: Face Matching, EID Verification, Liveness Detection, Face Search
    /// </summary>
    public interface IIdentityService
    {

        #region LEEON Identity Verification

        /// <summary>
        /// Xác thực eID (CCCD gắn chip) thông qua LEEON
        /// </summary>
        /// <param name="request">Thông tin xác thực eID</param>
        /// <returns>Kết quả xác thực eID</returns>
        Task<IdentityResponse<LeeoneIDVerificationResponse>> VerifyEIDAsync(LeeoneIDVerificationRequest request);

        /// <summary>
        /// Xác thực tính sống (liveness detection) thông qua LEEON
        /// </summary>
        /// <param name="request">Thông tin ảnh để xác thực tính sống</param>
        /// <returns>Kết quả xác thực tính sống</returns>
        Task<IdentityResponse<LivenessVerificationResponse>> VerifyLivenessAsync(LeeonLivenessVerificationRequest request);

        /// <summary>
        /// So khớp khuôn mặt (face matching) thông qua LEEON
        /// </summary>
        /// <param name="request">Thông tin 2 ảnh để so khớp</param>
        /// <returns>Kết quả so khớp khuôn mặt</returns>
        Task<IdentityResponse<LeeonFaceMatchingVerificationResponse>> FaceMatchingAsync(LeeonFaceMatchingVerificationRequest request);

        #endregion

        #region LEEON Face Search

        /// <summary>
        /// Thêm khuôn mặt vào hệ thống tìm kiếm
        /// </summary>
        /// <param name="request">Thông tin ảnh khuôn mặt và metadata</param>
        /// <returns>Kết quả thêm khuôn mặt</returns>
        Task<IdentityResponse<AddFaceResponse>> AddFaceAsync(AddFaceRequest request);

        /// <summary>
        /// Tìm kiếm khuôn mặt trong hệ thống
        /// </summary>
        /// <param name="request">Thông tin ảnh để tìm kiếm</param>
        /// <returns>Danh sách khuôn mặt tương tự</returns>
        Task<IdentityResponse<FindFaceResponse>> FindFaceAsync(FindFaceRequest request);

        #endregion

        #region Utility Methods

        /// <summary>
        /// Kiểm tra kết quả response có thành công hay không
        /// </summary>
        /// <typeparam name="T">Kiểu dữ liệu response</typeparam>
        /// <param name="response">Response cần kiểm tra</param>
        /// <returns>True nếu thành công, False nếu thất bại</returns>
        bool IsSuccess<T>(IdentityResponse<T> response) where T : class;

        /// <summary>
        /// Lấy thông báo lỗi từ response
        /// </summary>
        /// <typeparam name="T">Kiểu dữ liệu response</typeparam>
        /// <param name="response">Response cần lấy thông báo lỗi</param>
        /// <returns>Thông báo lỗi</returns>
        string GetErrorMessage<T>(IdentityResponse<T> response) where T : class;

        #endregion
    }
}
