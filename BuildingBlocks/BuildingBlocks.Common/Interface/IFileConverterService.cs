namespace BuildingBlocks.Common.Interface;

public interface IFileConverterService
{
    Task<(bool Success, byte[]? Bytes, string? ErrorMessage)> ConvertHtmlToPdfBytesAsync(string htmlContent, string fileName, PdfConversionOptions? options = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Options for PDF conversion
/// </summary>
public class PdfConversionOptions
{
    /// <summary>
    /// PDF page format (default: A4)
    /// </summary>
    public string Format { get; set; } = "A4";

    /// <summary>
    /// Top margin (default: 0.5cm)
    /// </summary>
    public string MarginTop { get; set; } = "0.5cm";

    /// <summary>
    /// Right margin (default: 0.5cm)
    /// </summary>
    public string MarginRight { get; set; } = "0.5cm";

    /// <summary>
    /// Bottom margin (default: 0.5cm)
    /// </summary>
    public string MarginBottom { get; set; } = "0.5cm";

    /// <summary>
    /// Left margin (default: 0.5cm)
    /// </summary>
    public string MarginLeft { get; set; } = "0.5cm";

    /// <summary>
    /// Landscape orientation (default: false)
    /// </summary>
    public bool Landscape { get; set; } = false;

    /// <summary>
    /// Document filename for the converter API (default: "emr-document")
    /// </summary>
    public string DocumentFilename { get; set; } = "emr-document";
}