using BuildingBlocks.Common.EMRApi.Interfaces;
using BuildingBlocks.Common.EMRApi.Services;
using BuildingBlocks.Common.Interface;
using Microsoft.Extensions.DependencyInjection;
using Refit;
using System.Text.Json;

namespace BuildingBlocks.Common.EMRApi.Extensions;

/// <summary>
/// Extension methods cho việc đăng ký Inter-Hospital EMR API services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Đăng ký Inter-Hospital EMR API services với Dependency Injection
    /// Dành cho tra cứu bệnh án liên viện từ hệ thống EMR bên ngoài
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Cấu hình EMR API</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddInterHospitalEMRApi(this IServiceCollection services, EMRConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);

        if (string.IsNullOrEmpty(configuration.BaseUrl))
            throw new ArgumentException("BaseUrl is required", nameof(configuration));

        // Đăng ký configuration (không cần inject vào EMRService)
        services.AddSingleton(configuration);

        // Cấu hình Refit settings
        var refitSettings = new RefitSettings
        {
            ContentSerializer = new SystemTextJsonContentSerializer(new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            })
        };

        // Register Refit client with HttpClient
        services.AddRefitClient<IEMRApi>(refitSettings)
                .ConfigureHttpClient(client =>
                {
                    client.BaseAddress = new Uri(configuration.BaseUrl);
                    client.Timeout = TimeSpan.FromSeconds(configuration.TimeoutSeconds);
                    client.DefaultRequestHeaders.Add("User-Agent", "GoTrust-EMR-Client/1.0");
                });

        // Đăng ký InterHospitalEMRService cho tra cứu bệnh án liên viện
        services.AddScoped<IInterHospitalEMRService, InterHospitalEMRService>();
        services.AddScoped<InterHospitalEMRService>();

        return services;
    }

    /// <summary>
    /// Đăng ký Inter-Hospital EMR API services với cấu hình từ action
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Action để cấu hình</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddInterHospitalEMRApi(this IServiceCollection services, Action<EMRConfiguration> configureOptions)
    {
        var configuration = new EMRConfiguration();
        configureOptions(configuration);
        return services.AddInterHospitalEMRApi(configuration);
    }
}
