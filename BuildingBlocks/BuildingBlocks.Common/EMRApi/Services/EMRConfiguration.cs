namespace BuildingBlocks.Common.EMRApi.Services;

/// <summary>
/// C<PERSON><PERSON> hình cho Inter-Hospital EMR API client
/// Dành cho tra cứu bệnh án liên viện từ hệ thống EMR bên ngoài
/// </summary>
public class EMRConfiguration
{
    /// <summary>
    /// Base URL của EMR API
    /// Ví dụ: https://api-dev.gotrust.vn/emr-management
    /// </summary>
    public string BaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// Timeout cho HTTP requests (giây)
    /// Mặc định: 30 giây
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;
}
