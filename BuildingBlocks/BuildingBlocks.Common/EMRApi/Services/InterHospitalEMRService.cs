using BuildingBlocks.Common.EMRApi.Interfaces;
using BuildingBlocks.Common.EMRApi.Models;
using BuildingBlocks.Common.Interface;
using Microsoft.Extensions.Logging;

namespace BuildingBlocks.Common.EMRApi.Services;

/// <summary>
/// Service implementation cho tra cứu bệnh án liên viện
/// Wrapper cho EMR API để truy xuất dữ liệu từ hệ thống EMR bên ngoài
/// </summary>
public class InterHospitalEMRService(IEMRApi api, ILogger<InterHospitalEMRService> logger) : IInterHospitalEMRService
{
    private readonly IEMRApi _api = api ?? throw new ArgumentNullException(nameof(api));
    private readonly ILogger<InterHospitalEMRService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    /// <summary>
    /// Tra cứu thông tin bệnh án liên viện từ hệ thống EMR bên ngoài
    /// </summary>
    /// <param name="request">Request chứa thông tin truy vấn (HospitalId, PatientId)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Danh sách hồ sơ bệnh án từ hệ thống liên viện</returns>
    public async Task<EMRApiResponse<List<HoSoBenhAn>>> GetInterHospitalEMRAsync(
        GetEMRRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Tra cứu bệnh án liên viện cho HospitalId: {HospitalId}, PatientId: {PatientId}",
            request.HospitalId, request.PatientId);

        var response = await _api.GetEMRAsync(request);

        if (response.IsSuccess)
        {
            _logger.LogInformation("Tra cứu thành công {Count} bệnh án liên viện cho HospitalId: {HospitalId}, PatientId: {PatientId}",
                response.Data?.Count ?? 0, request.HospitalId, request.PatientId);
        }
        else
        {
            _logger.LogWarning("Tra cứu bệnh án liên viện thất bại cho HospitalId: {HospitalId}, PatientId: {PatientId}. Code: {Code}, Message: {Message}",
                request.HospitalId, request.PatientId, response.Code, response.Message);
        }

        return response;
    }
}
