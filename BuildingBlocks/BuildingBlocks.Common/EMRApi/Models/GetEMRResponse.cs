namespace BuildingBlocks.Common.EMRApi.Models;

public class HoSoBenhAn
{
    public ThongTinBenhNhan? ThongTinBenhNhan { get; set; }
    public ThongTinVaoVien? ThongTinVaoVien { get; set; }
    public ThongTinDieuTri? ThongTinDieuTri { get; set; }
    public YLenhThuocVatTu? YLenhThuocVatTu { get; set; }
    public PhieuChiDinh? PhieuChiDinh { get; set; }
    public KetQuaChanDoanHinhAnh? KetQuaChanDoanHinhAnh { get; set; }
    public KetQuaXetNghiem? KetQuaXetNghiem { get; set; }
    public GiayChuyenVien? GiayChuyenVien { get; set; }
    public HoSoCapCuu? HoSoCapCuu { get; set; }
    public PhieuThuThuat? PhieuThuThuat { get; set; }
    public PhieuPhauThuat? PhieuPhauThuat { get; set; }
}

public class ThongTinBenh<PERSON>han
{
    public string loaiba { get; set; } = string.Empty;
    public string sovaovien { get; set; } = string.Empty;
    public string soba { get; set; } = string.Empty;
    public string soluutru { get; set; } = string.Empty;
    public string makhoa { get; set; } = string.Empty;
    public string tenkhoa { get; set; } = string.Empty;
    public string thoigianvaovien { get; set; } = string.Empty;
    public string mabenhnhan { get; set; } = string.Empty;
    public string buong { get; set; } = string.Empty;
    public string giuong { get; set; } = string.Empty;
    public string cccd_so { get; set; } = string.Empty;
    public string hochieu_so { get; set; } = string.Empty;
    public string hoten { get; set; } = string.Empty;
    public string ngaysinh { get; set; } = string.Empty;
    public string tuoi { get; set; } = string.Empty;
    public string gioitinh { get; set; } = string.Empty;
    public string nghenghiep { get; set; } = string.Empty;
    public string nghenghiep_ma { get; set; } = string.Empty;
    public string dantoc { get; set; } = string.Empty;
    public string dantoc_ma { get; set; } = string.Empty;
    public string ngoaikieu { get; set; } = string.Empty;
    public string ngoaikieu_ma { get; set; } = string.Empty;
    public string tungaybhyt { get; set; } = string.Empty;
    public string denngaybhyt { get; set; } = string.Empty;
    public string mabhyt { get; set; } = string.Empty;
    public string noidangkykcbbd { get; set; } = string.Empty;
    public string diachi { get; set; } = string.Empty;
    public string sonha { get; set; } = string.Empty;
    public string thonpho { get; set; } = string.Empty;
    public string xaphuong { get; set; } = string.Empty;
    public string quanhuyen { get; set; } = string.Empty;
    public string quanhuyen_ma { get; set; } = string.Empty;
    public string tinhthanh { get; set; } = string.Empty;
    public string tinhthanh_ma { get; set; } = string.Empty;
    public string noilamviec { get; set; } = string.Empty;
    public string sodienthoai { get; set; } = string.Empty;
    public string nhommau { get; set; } = string.Empty;
    public string yeutorh { get; set; } = string.Empty;
    public string doituongbn_loai { get; set; } = string.Empty;
    public string hotennguoithan { get; set; } = string.Empty;
    public string diachinguoithan { get; set; } = string.Empty;
    public string sodienthoainguoithan { get; set; } = string.Empty;
    public string lydotiepnhan { get; set; } = string.Empty;
    public string noigioithieu_loai { get; set; } = string.Empty;
    public List<TiensuBenhTat>? tiensubenhtatcuabanthan { get; set; }
    public string tiensubenhtatcuagiadinh { get; set; } = string.Empty;
    public TienSuSanPhuKhoa? tiensusanphukhoa { get; set; }
    public TienSuSanKhoa? tiensusankhoa { get; set; }
}

public class TiensuBenhTat
{
    public string thongtin_ma { get; set; } = string.Empty;
    public string thongtin_noidung { get; set; } = string.Empty;
    public string thongtin_giatri { get; set; } = string.Empty;
    public string thongtin_ghichu { get; set; } = string.Empty;
}

public class TienSuSanPhuKhoa
{
    public string tuoibatdaukinh { get; set; } = string.Empty;
    public string tinhchatkinhnguyet { get; set; } = string.Empty;
    public string chukykinhnguyet { get; set; } = string.Empty;
    public string songaythaykinh { get; set; } = string.Empty;
    public string luongkinh { get; set; } = string.Empty;
    public string kinhlancuoingay { get; set; } = string.Empty;
    public string daubungkinh { get; set; } = string.Empty;
    public string laychongnam { get; set; } = string.Empty;
    public string hetkinhnam { get; set; } = string.Empty;
    public string benhphukhoadadieutri { get; set; } = string.Empty;
}

public class TienSuSanKhoa
{
    public string PARA { get; set; } = string.Empty;
}

public class ThongTinVaoVien
{
    public string sovaovien { get; set; } = string.Empty;
    public string sophieu { get; set; } = string.Empty;
    public string phongkham { get; set; } = string.Empty;
    public string maphongkham { get; set; } = string.Empty;
    public string bacsikhambenh { get; set; } = string.Empty;
    public string mabacsikhambenh { get; set; } = string.Empty;
    public string chandoancuanoigioithieu { get; set; } = string.Empty;
    public string chandoansobo { get; set; } = string.Empty;
    public string chandoanvaovien { get; set; } = string.Empty;
    public string khambenh_chandoanvaovienmaicd { get; set; } = string.Empty;
    public string chieucao { get; set; } = string.Empty;
    public string dieutritaikhoa { get; set; } = string.Empty;
    public string madieutritaikhoa { get; set; } = string.Empty;
    public string chuky_hoten { get; set; } = string.Empty;
    public string denkhambenhluc { get; set; } = string.Empty;
    public string lydovaovien { get; set; } = string.Empty;
    public string quatrinhbenhly { get; set; } = string.Empty;
    public string mach { get; set; } = string.Empty;
    public string nhietdo { get; set; } = string.Empty;
    public string nhiptho { get; set; } = string.Empty;
    public string huyetap_tamthu { get; set; } = string.Empty;
    public string huyetap_tamtruong { get; set; } = string.Empty;
    public string cannang { get; set; } = string.Empty;
    public string khambenh_toanthan { get; set; } = string.Empty;
    public Khambenh_CacCoQuan? khambenh_caccoquan { get; set; }
}

public class Khambenh_CacCoQuan
{
    public Khambenh_TuanHoan? tuanhoan { get; set; }
    public Khambenh_HoHap? hohap { get; set; }
    public List<Khambenh_TieuHoa>? tieuhoa { get; set; }
    public Khambenh_ThanTietNieu? thantietnieu { get; set; }
    public Khambenh_ThanKinh? thankinh { get; set; }
    public Khambenh_CoXuongKhop? coxuongkhop { get; set; }
    public Khambenh_TaiMuiHong? taimuihong { get; set; }
    public Khambenh_RangHamMat? ranghammat { get; set; }
    public Khambenh_Mat? mat { get; set; }
    public Khambenh_NoiTietDinhDuong? noitiet_dinhduong_benhlikhac { get; set; }
    public Khambenh_PhuKhoa? phukhoa { get; set; }
    public Khambenh_SanKhoa? sankhoa { get; set; }
}

public class Khambenh_TuanHoan
{
    public DauchungMa? dauchung_ma { get; set; }
    public string dauchung { get; set; } = string.Empty;
    public string dauchung_ghichu { get; set; } = string.Empty;
}

public class Khambenh_HoHap
{
    public DauchungMa? dauchung_ma { get; set; }
    public string dauchung { get; set; } = string.Empty;
    public string dauchung_ghichu { get; set; } = string.Empty;
}

public class Khambenh_TieuHoa
{
    public DauchungMa? dauchung_ma { get; set; }
    public string dauchung { get; set; } = string.Empty;
    public string dauchung_ghichu { get; set; } = string.Empty;
}

public class Khambenh_ThanTietNieu
{
    public DauchungMa? dauchung_ma { get; set; }
    public string dauchung { get; set; } = string.Empty;
    public string dauchung_ghichu { get; set; } = string.Empty;
}

public class Khambenh_ThanKinh
{
    public DauchungMa? dauchung_ma { get; set; }
    public string dauchung { get; set; } = string.Empty;
    public string dauchung_ghichu { get; set; } = string.Empty;
}

public class Khambenh_CoXuongKhop
{
    public DauchungMa? dauchung_ma { get; set; }
    public string dauchung { get; set; } = string.Empty;
    public string dauchung_ghichu { get; set; } = string.Empty;
}

public class Khambenh_TaiMuiHong
{
    public DauchungMa? dauchung_ma { get; set; }
    public string dauchung { get; set; } = string.Empty;
    public string dauchung_ghichu { get; set; } = string.Empty;
}

public class Khambenh_RangHamMat
{
    public DauchungMa? dauchung_ma { get; set; }
    public string dauchung { get; set; } = string.Empty;
    public string dauchung_ghichu { get; set; } = string.Empty;
}

public class Khambenh_Mat
{
    public MatBenh? matphai { get; set; }
    public MatBenh? mattrai { get; set; }
}

public class MatBenh
{
    public string nhanapvaovien { get; set; } = string.Empty;
    public string thilucvaovien_cokinh { get; set; } = string.Empty;
    public string thilucvaovien_khongkinh { get; set; } = string.Empty;
    public DauchungMa? dauchung_ma { get; set; }
    public string dauchung { get; set; } = string.Empty;
    public string dauchung_ghichu { get; set; } = string.Empty;
}

public class Khambenh_NoiTietDinhDuong
{
    public DauchungMa? dauchung_ma { get; set; }
    public string dauchung { get; set; } = string.Empty;
    public string dauchung_ghichu { get; set; } = string.Empty;
}

public class Khambenh_PhuKhoa
{
    public Khambenh_PhuKhoa_KhamNgoai? khamngoai { get; set; }
    public Khambenh_PhuKhoa_KhamTrong? khamtrong { get; set; }
}

public class Khambenh_PhuKhoa_KhamNgoai
{
    public DauchungPhan? dausinhducthuphat { get; set; }
    public DauchungPhan? moilon { get; set; }
    public DauchungPhan? moibe { get; set; }
    public DauchungPhan? amvat { get; set; }
    public DauchungPhan? amho { get; set; }
    public DauchungPhan? mangtrinh { get; set; }
    public DauchungPhan? tangsinhmon { get; set; }
}

public class Khambenh_PhuKhoa_KhamTrong
{
    public DauchungPhan? amdao { get; set; }
    public DauchungPhan? cotucung { get; set; }
    public DauchungPhan? thantucung { get; set; }
    public DauchungPhan? phanphu { get; set; }
    public DauchungPhan? cactuicung { get; set; }
}

public class Khambenh_SanKhoa
{
    public SanKhoa_KhamNgoai? khamngoai { get; set; }
    public SanKhoa_KhamTrong? khamtrong { get; set; }
}

public class SanKhoa_KhamNgoai
{
    public Vmc? vmc { get; set; }
    public string hinhdangtucung { get; set; } = string.Empty;
    public string kieuthe { get; set; } = string.Empty;
    public string chieucaotc { get; set; } = string.Empty;
    public string vongbung { get; set; } = string.Empty;
    public string concotc { get; set; } = string.Empty;
    public string timthai { get; set; } = string.Empty;
    public string vu { get; set; } = string.Empty;
    public string khungchau { get; set; } = string.Empty;
    public DauchungPhan? amho { get; set; }
    public DauchungPhan? tangsinhmon { get; set; }
}

public class SanKhoa_KhamTrong
{
    public string chisoBishop { get; set; } = string.Empty;
    public DauchungPhan? amdao { get; set; }
    public DauchungPhan? cotucung { get; set; }
    public DauchungPhan? phanphu { get; set; }
    public string oivoluc { get; set; } = string.Empty;
    public string oivotunhien { get; set; } = string.Empty;
    public string mausacoi { get; set; } = string.Empty;
    public string nuocoinhieuitmau { get; set; } = string.Empty;
    public string luongnuocoi { get; set; } = string.Empty;
    public string ngoithai { get; set; } = string.Empty;
    public string dolot { get; set; } = string.Empty;
    public string duongkinhnhohave { get; set; } = string.Empty;
}

public class Vmc
{
    public string vmc_lydo { get; set; } = string.Empty;
    public string vmc_ngay { get; set; } = string.Empty;
    public string vmc_taibenhvien { get; set; } = string.Empty;
}

public class DauchungMa
{
    public string VNCODE { get; set; } = string.Empty;
    public string SNOMED { get; set; } = string.Empty;
}

public class DauchungPhan
{
    public DauchungMa? dauchung_ma { get; set; }
    public string dauchung { get; set; } = string.Empty;
    public string dauchung_ghichu { get; set; } = string.Empty;
}

public class ThongTinDieuTri
{
    public string bacsidieutri { get; set; } = string.Empty;
    public string bacsidieutri_ma { get; set; } = string.Empty;
    public string khoadieutri_ma { get; set; } = string.Empty;
    public string khoadieutri { get; set; } = string.Empty;
    public string giuong_ma { get; set; } = string.Empty;
    public string giuong { get; set; } = string.Empty;
    public string khoavaovien_ma { get; set; } = string.Empty;
    public string khoavaovien { get; set; } = string.Empty;
    public string ngaynhapkhoa { get; set; } = string.Empty;
    public string songaydieutri { get; set; } = string.Empty;
    public string ngayravien { get; set; } = string.Empty;
    public string tongsongaydieutri { get; set; } = string.Empty;
    public string chandoan { get; set; } = string.Empty;
    public string chandoan_ma { get; set; } = string.Empty;
    public string chandoanvaovien { get; set; } = string.Empty;
    public string chandoanvaovien_ma { get; set; } = string.Empty;
    public string chandoantuyenduoi { get; set; } = string.Empty;
    public string chandoantuyenduoi_ma { get; set; } = string.Empty;
    public string maicd { get; set; } = string.Empty;
    public string tenicd { get; set; } = string.Empty;
    public string maicd_khac { get; set; } = string.Empty;
    public string tenicd_khac { get; set; } = string.Empty;
    public string ghichu { get; set; } = string.Empty;
    public string taibien { get; set; } = string.Empty;
    public string tomtat { get; set; } = string.Empty;
    public string bienchung { get; set; } = string.Empty;
    public string tienluong { get; set; } = string.Empty;
    public string huongdieutri { get; set; } = string.Empty;
    public List<ChuyenKhoa>? danhsachchuyenkhoa { get; set; }
    public List<SinhHieu>? danhsachsinhhieu { get; set; }
    public TinhTrangRaVien? tinhtrangravien { get; set; }
}

public class ChuyenKhoa
{
    public string khoa_ma { get; set; } = string.Empty;
    public string khoa { get; set; } = string.Empty;
    public string maicd { get; set; } = string.Empty;
    public string tenicd { get; set; } = string.Empty;
    public string tungay { get; set; } = string.Empty;
    public string denngay { get; set; } = string.Empty;
}

public class SinhHieu
{
    public string mach { get; set; } = string.Empty;
    public string nhietdo { get; set; } = string.Empty;
    public string huyetap_cao { get; set; } = string.Empty;
    public string huyetap_thap { get; set; } = string.Empty;
    public string nhiptho { get; set; } = string.Empty;
    public string cannang { get; set; } = string.Empty;
    public string chieucao { get; set; } = string.Empty;
    public string thoidiem { get; set; } = string.Empty;
}

public class TinhTrangRaVien
{
    public string ketquadieutri { get; set; } = string.Empty;
    public string loidanbacsi { get; set; } = string.Empty;
    public GiaiPhauBenh? giaiphaubenh { get; set; }
    public string ngayravien { get; set; } = string.Empty;
    public string thoigiantuvong { get; set; } = string.Empty;
    public string thoidiemtuvong { get; set; } = string.Empty;
    public string lydotuvong { get; set; } = string.Empty;
    public string nguyennhantuvong { get; set; } = string.Empty;
    public string nguyennhantuvong_ma { get; set; } = string.Empty;
    public string khamnghiemtuthi { get; set; } = string.Empty;
}

public class GiaiPhauBenh
{
    public string giaiphaubenh_ma { get; set; } = string.Empty;
    public string giaiphaubenh_mota { get; set; } = string.Empty;
}

public class YLenhThuocVatTu
{
    public string sovaovien { get; set; } = string.Empty;
    public List<YLenhThuocVatTuPhieu>? danhsach { get; set; }
}

public class YLenhThuocVatTuPhieu
{
    public string sophieu { get; set; } = string.Empty;
    public string bacsichidinh { get; set; } = string.Empty;
    public string mabacsichidinh { get; set; } = string.Empty;
    public string chandoansobo { get; set; } = string.Empty;
    public string doituongbn { get; set; } = string.Empty;
    public List<YLenhThuocVatTuItem>? danhsach { get; set; }
}

public class YLenhThuocVatTuItem
{
    public string machidinh { get; set; } = string.Empty;
    public string cachdung { get; set; } = string.Empty;
    public string ngaykedonthuoc { get; set; } = string.Empty;
    public string donvitinh { get; set; } = string.Empty;
    public string nhom { get; set; } = string.Empty;
    public string loaithuoc { get; set; } = string.Empty;
    public string mathuocvattu_bv { get; set; } = string.Empty;
    public string mathuocvattu_byt { get; set; } = string.Empty;
    public string soluongthuocvattu { get; set; } = string.Empty;
    public string sothutungaydungthuoc { get; set; } = string.Empty;
    public string tenthuocvattu { get; set; } = string.Empty;
    public string ylenhthuocvattu_ghichu { get; set; } = string.Empty;
}

public class PhieuChiDinh
{
    public string sovaovien { get; set; } = string.Empty;
    public List<PhieuChiDinhPhieu>? danhsach { get; set; }
}

public class PhieuChiDinhPhieu
{
    public string sophieu { get; set; } = string.Empty;
    public string bacsichidinh { get; set; } = string.Empty;
    public string mabacsichidinh { get; set; } = string.Empty;
    public string chandoansobo { get; set; } = string.Empty;
    public string doituongbn { get; set; } = string.Empty;
    public List<PhieuChiDinhItem>? danhsach { get; set; }
    public string giogiaomau { get; set; } = string.Empty;
    public string giolaymau { get; set; } = string.Empty;
    public string giuong { get; set; } = string.Empty;
    public string khoadieutri { get; set; } = string.Empty;
    public string mucdochidinh { get; set; } = string.Empty;
    public string nguoilaymau { get; set; } = string.Empty;
    public string noichidinh { get; set; } = string.Empty;
    public string phong { get; set; } = string.Empty;
    public string tongcong { get; set; } = string.Empty;
}

public class PhieuChiDinhItem
{
    public string machidinh { get; set; } = string.Empty;
    public string ngaychidinh { get; set; } = string.Empty;
    public string benhnhanphaitra { get; set; } = string.Empty;
    public string dongia { get; set; } = string.Empty;
    public string loaichidinh { get; set; } = string.Empty;
    public string loaimachidinh { get; set; } = string.Empty;
    public string noithuchienchidinh { get; set; } = string.Empty;
    public string manoithuchienchidinh { get; set; } = string.Empty;
    public string soluong { get; set; } = string.Empty;
    public string tenchidinh { get; set; } = string.Empty;
    public string thanhtien { get; set; } = string.Empty;
}

public class KetQuaChanDoanHinhAnh
{
    public string sovaovien { get; set; } = string.Empty;
    public List<KetQuaChanDoanHinhAnhItem>? danhsach { get; set; }
}

public class KetQuaChanDoanHinhAnhItem
{
    public string sophieu { get; set; } = string.Empty;
    public string machidinh { get; set; } = string.Empty;
    public string ngayketqua { get; set; } = string.Empty;
    public string bacsichidinh { get; set; } = string.Empty;
    public string mabacsichidinh { get; set; } = string.Empty;
    public string bacsithuchien { get; set; } = string.Empty;
    public string mabacsithuchien { get; set; } = string.Empty;
    public string chandoan { get; set; } = string.Empty;
    public List<string>? hinhanh { get; set; }
    public string kqcls_denghi { get; set; } = string.Empty;
    public string kqcls_ketluan { get; set; } = string.Empty;
    public string kqcls_linkpacs { get; set; } = string.Empty;
    public string kqcls_mota { get; set; } = string.Empty;
}

public class KetQuaXetNghiem
{
    public string sovaovien { get; set; } = string.Empty;
    public List<KetQuaXetNghiemPhieu>? danhsach { get; set; }
}

public class KetQuaXetNghiemPhieu
{
    public string sophieu { get; set; } = string.Empty;
    public string mabacsichidinh { get; set; } = string.Empty;
    public string bacsichidinh { get; set; } = string.Empty;
    public string chandoan { get; set; } = string.Empty;
    public string doituongbn { get; set; } = string.Empty;
    public string ghichu { get; set; } = string.Empty;
    public string giolaymau { get; set; } = string.Empty;
    public string khoa { get; set; } = string.Empty;
    public string masobenhpham { get; set; } = string.Empty;
    public string nguoithuchienxetnghiem { get; set; } = string.Empty;
    public string manguoithuchienxetnghiem { get; set; } = string.Empty;
    public string noithuchienchidinh { get; set; } = string.Empty;
    public List<KetQuaXetNghiemItem>? danhsach { get; set; }
}

public class KetQuaXetNghiemItem
{
    public string machidinh { get; set; } = string.Empty;
    public string machiso { get; set; } = string.Empty;
    public string ngayketqua { get; set; } = string.Empty;
    public string donvi { get; set; } = string.Empty;
    public string ketqua { get; set; } = string.Empty;
    public string ketquabatthuong { get; set; } = string.Empty;
    public string loaixetnghiem { get; set; } = string.Empty;
    public string nhom { get; set; } = string.Empty;
    public string noithuchienchidinh { get; set; } = string.Empty;
    public string pkqxn_tenxetnghiem { get; set; } = string.Empty;
    public string trisothamchieu { get; set; } = string.Empty;
}

public class GiayChuyenVien
{
    public string sovaovien { get; set; } = string.Empty;
    public string sophieu { get; set; } = string.Empty;
    public string chandoanchuyenvien { get; set; } = string.Empty;
    public string chuyentuyen { get; set; } = string.Empty;
    public string chuyentuyen_nguoiduadi { get; set; } = string.Empty;
    public string chuyentuyen_phuongtien { get; set; } = string.Empty;
    public string chuyentuyenbang { get; set; } = string.Empty;
    public string dauhieulamsang { get; set; } = string.Empty;
    public string huongdieutri { get; set; } = string.Empty;
    public string ketquaclsdalam { get; set; } = string.Empty;
    public string ketquadieutrituyentren { get; set; } = string.Empty;
    public string lydochuyentuyen { get; set; } = string.Empty;
    public string ngaychuyentuyen { get; set; } = string.Empty;
    public string ngaylapgiaychuyenvien { get; set; } = string.Empty;
    public string nguoilapgiaychuyenvien { get; set; } = string.Empty;
    public string manguoilapgiaychuyenvien { get; set; } = string.Empty;
    public string noilapgiaychuyenvien { get; set; } = string.Empty;
    public string thuocdadung { get; set; } = string.Empty;
    public string tinhtranglucchuyentuyen { get; set; } = string.Empty;
}

public class HoSoCapCuu
{
    public string sovaovien { get; set; } = string.Empty;
    public string bophanbithuong { get; set; } = string.Empty;
    public string cannang { get; set; } = string.Empty;
    public ChanDoan? cdkkbcapcuu_benhchinh { get; set; }
    public string cdnoigioithieu { get; set; } = string.Empty;
    public ChanDoan? cdrakhoa_benhchinh { get; set; }
    public ChanDoanKemTheo? cdrakhoa_benhkemtheo { get; set; }
    public string chieucao { get; set; } = string.Empty;
    public DiaDiemXayRa? diadiemxayra { get; set; }
    public string dienbiensautntt { get; set; } = string.Empty;
    public string huyetap_tamthu { get; set; } = string.Empty;
    public string huyetap_tamtruong { get; set; } = string.Empty;
    public string ketquadieutri { get; set; } = string.Empty;
    public string khambenh_cacbophan { get; set; } = string.Empty;
    public string khambenh_chuy { get; set; } = string.Empty;
    public string khambenh_daxuly { get; set; } = string.Empty;
    public string khambenh_toanthan { get; set; } = string.Empty;
    public string khambenh_tomtatketquacls { get; set; } = string.Empty;
    public string khoavao { get; set; } = string.Empty;
    public string lydovaovien { get; set; } = string.Empty;
    public string mach { get; set; } = string.Empty;
    public string mucdotntt { get; set; } = string.Empty;
    public string ngaygiovaokhoa { get; set; } = string.Empty;
    public string nguyennhanchitiet { get; set; } = string.Empty;
    public ChanDoan? nguyennhantainan { get; set; }
    public string nhietdo { get; set; } = string.Empty;
    public string nhiptho { get; set; } = string.Empty;
    public string quatrinhbenhly { get; set; } = string.Empty;
    public string thoidiemkhambenh { get; set; } = string.Empty;
    public string thoidiemtainan { get; set; } = string.Empty;
    public string tiensubenhbanthan { get; set; } = string.Empty;
    public string tiensubenhgiadinh { get; set; } = string.Empty;
    public string xutrikhambenh { get; set; } = string.Empty;
    public string xutrisautntt { get; set; } = string.Empty;
}

public class ChanDoan
{
    public string maicd { get; set; } = string.Empty;
    public string tenicd { get; set; } = string.Empty;
    public string motabenh { get; set; } = string.Empty;
}
public class ChanDoanKemTheo
{
    public string maicd { get; set; } = string.Empty;
    public string tenicd { get; set; } = string.Empty;
}

public class DiaDiemXayRa
{
    public string diadiemxayra_ten { get; set; } = string.Empty;
    public string maxaphuong { get; set; } = string.Empty;
    public string quanhuyen { get; set; } = string.Empty;
    public string quanhuyen_ma { get; set; } = string.Empty;
    public string sonha { get; set; } = string.Empty;
    public string tinhthanh { get; set; } = string.Empty;
    public string tinhthanh_ma { get; set; } = string.Empty;
    public string xaphuong { get; set; } = string.Empty;
}

public class PhieuThuThuat
{
    public string sovaovien { get; set; } = string.Empty;
    public List<PhieuThuThuatItem>? danhsach { get; set; }
}

public class PhieuThuThuatItem
{
    public string sophieu { get; set; } = string.Empty;
    public string phuongphapphauthuat { get; set; } = string.Empty;
    public string trinhtuphauthuatthuthuat { get; set; } = string.Empty;
    public string phauthuatvien { get; set; } = string.Empty;
    public string maphauthuatvien { get; set; } = string.Empty;
    public string buong { get; set; } = string.Empty;
    public string ngaygiovaovien { get; set; } = string.Empty;
    public string khoa_ma { get; set; } = string.Empty;
    public string gioitinh { get; set; } = string.Empty;
    public string giuong { get; set; } = string.Empty;
    public string bac { get; set; } = string.Empty;
    public ChanDoan? cdsauphauthuat { get; set; }
    public string ghichu { get; set; } = string.Empty;
    public string luocdophauthuat { get; set; } = string.Empty;
    public string ngaygiophauthuat { get; set; } = string.Empty;
    public string mabacsigaymehoisuc { get; set; } = string.Empty;
    public string bacsigaymehoisuc { get; set; } = string.Empty;
    public string tuoi { get; set; } = string.Empty;
    public string loaiphauthuat { get; set; } = string.Empty;
    public string khoa { get; set; } = string.Empty;
    public string phuongphapvocam { get; set; } = string.Empty;
    public string ngayrutchi { get; set; } = string.Empty;
    public ChanDoan? cdtruocphauthuat { get; set; }
    public string ngaycatchi { get; set; } = string.Empty;
    public string danluu { get; set; } = string.Empty;
}

public class PhieuPhauThuat
{
    public string sovaovien { get; set; } = string.Empty;
    public List<PhieuPhauThuatItem>? danhsach { get; set; }
}

public class PhieuPhauThuatItem
{
    public List<string>? sophieu { get; set; }
    public string trinhtuphauthuatthuthuat { get; set; } = string.Empty;
    public string phauthuatvien { get; set; } = string.Empty;
    public string maphauthuatvien { get; set; } = string.Empty;
    public string phudungcu { get; set; } = string.Empty;
    public string buong { get; set; } = string.Empty;
    public string khoa { get; set; } = string.Empty;
    public string khoa_ma { get; set; } = string.Empty;
    public string ngaygiovaovien { get; set; } = string.Empty;
    public string bacsigaymehoisuc_phu1 { get; set; } = string.Empty;
    public string mabacsigaymehoisuc_phu1 { get; set; } = string.Empty;
    public string gioitinh { get; set; } = string.Empty;
    public string giuong { get; set; } = string.Empty;
    public ChanDoan? cdsauphauthuat { get; set; }
    public string luocdophauthuat { get; set; } = string.Empty;
    public string ngaygiophauthuat { get; set; } = string.Empty;
    public string dvpt { get; set; } = string.Empty;
    public string bacsigaymehoisuc { get; set; } = string.Empty;
    public string mabacsigaymehoisuc { get; set; } = string.Empty;
    public string tuoi { get; set; } = string.Empty;
    public string loaiphauthuat { get; set; } = string.Empty;
    public string phuphauthuat { get; set; } = string.Empty;
    public string phuongphapvocam { get; set; } = string.Empty;
    public ChanDoan? cdtruocphauthuat { get; set; }
    public string ddvongngoai { get; set; } = string.Empty;
}