using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.EMRApi.Models;

/// <summary>
/// Request model cho API GetEMR
/// </summary>
public class GetEMRRequest
{
    /// <summary>
    /// <PERSON>ã bệnh viện
    /// </summary>
    [Json<PERSON>ropertyName("hospitalId")]
    public string HospitalId { get; set; } = string.Empty;

    /// <summary>
    /// Mã bệnh nhân
    /// </summary>
    [JsonPropertyName("patientId")]
    public string PatientId { get; set; } = string.Empty;
}
