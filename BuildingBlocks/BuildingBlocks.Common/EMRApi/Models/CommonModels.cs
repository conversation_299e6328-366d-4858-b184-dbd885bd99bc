using System.Text.Json.Serialization;

namespace BuildingBlocks.Common.EMRApi.Models;

/// <summary>
/// Response wrapper cho EMR API
/// Tương thích với Response<T> của hệ thống hiện tại
/// </summary>
/// <typeparam name="T">Kiểu dữ liệu của data</typeparam>
public class EMRApiResponse<T>
{
    /// <summary>
    /// Mã trạng thái
    /// "000" = Thành công
    /// Các mã khác = Lỗi
    /// </summary>
    [JsonPropertyName("code")]
    public string Code { get; set; } = "000";

    /// <summary>
    /// Thông điệp mô tả
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Thông tin lỗi chi tiết (nếu có)
    /// </summary>
    [JsonPropertyName("errors")]
    public object? Errors { get; set; }

    /// <summary>
    /// Trace ID để tracking
    /// </summary>
    [JsonPropertyName("traceId")]
    public string? TraceId { get; set; }

    /// <summary>
    /// Dữ liệu trả về
    /// </summary>
    [JsonPropertyName("data")]
    public T? Data { get; set; }

    /// <summary>
    /// Kiểm tra response có thành công không
    /// </summary>
    public bool IsSuccess => Code == "000";
}
