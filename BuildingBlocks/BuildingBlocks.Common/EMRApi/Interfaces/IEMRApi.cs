using BuildingBlocks.Common.EMRApi.Models;
using Refit;

namespace BuildingBlocks.Common.EMRApi.Interfaces;

/// <summary>
/// Interface API cho hệ thống EMR - Electronic Medical Records
/// Hỗ trợ truy vấn thông tin bệnh án qua Web-API
/// </summary>
public interface IEMRApi
{
    /// <summary>
    /// API lấy thông tin bệnh án theo request object
    /// Tương ứng với endpoint: GET /api/emr/get?HospitalId={HospitalId}&PatientId={PatientId}
    /// </summary>
    /// <param name="request">Request chứa thông tin truy vấn</param>
    /// <returns><PERSON>h s<PERSON>ch hồ sơ bệnh án</returns>
    [Get("/api/emr/get")]
    Task<EMRApiResponse<List<HoSoBenhAn>>> GetEMRAsync([Query] GetEMRRequest request);
}
