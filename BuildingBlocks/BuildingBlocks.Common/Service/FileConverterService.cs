using System.Text.Json;
using BuildingBlocks.Common.GoTrustApi.Interfaces;
using BuildingBlocks.Common.GoTrustApi.Models;
using BuildingBlocks.Common.Interface;
using Microsoft.Extensions.Logging;

namespace BuildingBlocks.Common.Service;

public class FileConverterService(IGoTrustApi goTrustApi, ILogger<FileConverterService> logger) : IFileConverterService
{
    public async Task<(bool Success, byte[]? Bytes, string? ErrorMessage)> ConvertHtmlToPdfBytesAsync(
        string htmlContent,
        string fileName,
        PdfConversionOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(htmlContent))
            return (false, null, "HTML content cannot be null or empty");

        if (string.IsNullOrWhiteSpace(fileName))
            return (false, null, "File name cannot be null or empty");

        options ??= new PdfConversionOptions();

        try
        {
            var request = new FileConversionRequest
            {
                HtmlContent = htmlContent,
                Filename = options.DocumentFilename ?? fileName, // fallback nếu DocumentFilename null
                Options = new PdfConversionOptionsDto
                {
                    Format = options.Format,
                    MarginTop = options.MarginTop,
                    MarginRight = options.MarginRight,
                    MarginBottom = options.MarginBottom,
                    MarginLeft = options.MarginLeft,
                    Landscape = options.Landscape
                }
            };

            logger.LogInformation("Calling GoTrust API to convert HTML to PDF for file: {FileName}", fileName);

            var response = await goTrustApi.ConvertHtmlToPdfAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var contentType = response.Content.Headers.ContentType?.MediaType;

                if (contentType == "application/pdf")
                {
                    var pdfBytes = await response.Content.ReadAsByteArrayAsync(cancellationToken);
                    logger.LogInformation("Successfully converted HTML to PDF for file: {FileName}, Size: {FileSize} bytes", fileName, pdfBytes.Length);
                    return (true, pdfBytes, null);
                }
                else if (contentType == "application/json")
                {
                    var jsonString = await response.Content.ReadAsStringAsync(cancellationToken);
                    var errorObj = JsonSerializer.Deserialize<ApiError>(jsonString);
                    var errorMsg = errorObj?.Message ?? "Unknown JSON error";
                    logger.LogError("Failed to convert HTML to PDF for file: {FileName}. Error: {Error}", fileName, errorMsg);
                    return (false, null, $"Failed to convert HTML to PDF: {errorMsg}");
                }
                else
                {
                    logger.LogError("Unexpected content-type when converting HTML to PDF for file: {FileName}. Content-Type: {ContentType}", fileName, contentType);
                    return (false, null, $"Unexpected content-type: {contentType}");
                }
            }
            else
            {
                var errorString = await response.Content.ReadAsStringAsync(cancellationToken);
                logger.LogError("Failed to convert HTML to PDF for file: {FileName}. Status: {StatusCode}, Error: {Error}", fileName, response.StatusCode, errorString);
                return (false, null, $"Failed to convert HTML to PDF: {errorString}");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during HTML to PDF conversion for file: {FileName}", fileName);
            return (false, null, $"An unexpected error occurred: {ex.Message}");
        }
    }

    public class ApiError
    {
        public string? Message { get; set; }
    }
}
