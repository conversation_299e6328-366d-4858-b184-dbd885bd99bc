using Amazon.S3;
using Amazon.S3.Model;
using BuildingBlocks.Common.Interface;
using Microsoft.Extensions.Configuration;
using System.Text.RegularExpressions;

namespace BuildingBlocks.Common.Service
{
    public class FileStorageService(IAmazonS3 s3Client, IConfiguration configuration) : IFileStorageService
    {
        private string GetS3KeyPrefix()
        {
            return configuration["S3_KeyPrefix"] ?? "emr";
        }

        public async Task<(bool Success, string Message, string FileUrl)> UploadFileAsync(MemoryStream data, string key)
        {
            string fileUrl = string.Empty;
            string message = string.Empty;
            string bucketName = configuration["S3_BucketName"] ?? string.Empty;
            bool result = false;

            if (string.IsNullOrEmpty(bucketName))
            {
                message = "Bucket name is empty";
                return (result, message, fileUrl);
            }

            try
            {
                string keyPrefix = GetS3KeyPrefix();
                string fullKey = $"{keyPrefix}/{key}";

                Console.WriteLine($"Upload stream length: {data.Length}");
                data.Position = 0; // Ensure the stream starts at the beginning
                var res = await s3Client.PutObjectAsync(new PutObjectRequest
                {
                    BucketName = bucketName,
                    Key = fullKey,
                    InputStream = data,
                    ContentType = "application/octet-stream",
                    CannedACL = S3CannedACL.PublicRead,
                    AutoCloseStream = true
                });

                result = res.HttpStatusCode == System.Net.HttpStatusCode.OK;
                fileUrl = $"{s3Client.Config.ServiceURL}/{bucketName}/{fullKey}";
            }
            catch (AmazonS3Exception e)
            {
                message = e.Message;
                Console.WriteLine("AmazonS3Exception: '{0}'", e.Message);
            }
            catch (Exception e)
            {
                message = e.Message;
                Console.WriteLine("Exception: '{0}'", e.Message);
            }

            return (result, message, Regex.Replace(fileUrl, @"(?<!:)//", "/"));
        }
        public async Task<(bool Success, string Message)> DeleteFileAsync(string key)
        {
            string bucketName = configuration["S3_BucketName"] ?? string.Empty;
            if (string.IsNullOrEmpty(bucketName))
            {
                return (false, "Bucket name is empty");
            }

            try
            {
                string fullKey = $"{key}";

                var response = await s3Client.DeleteObjectAsync(new DeleteObjectRequest
                {
                    BucketName = bucketName,
                    Key = fullKey
                });

                return (true, "File deleted successfully");
            }
            catch (AmazonS3Exception e)
            {
                Console.WriteLine($"AmazonS3Exception: '{e.Message}'");
                return (false, e.Message);
            }
            catch (Exception e)
            {
                Console.WriteLine($"Exception: '{e.Message}'");
                return (false, e.Message);
            }
        }
        public async Task<(bool Success, string Message)> DeleteFilesAsync(IEnumerable<string> keys)
        {
            string bucketName = configuration["S3_BucketName"] ?? string.Empty;
            if (string.IsNullOrEmpty(bucketName))
            {
                return (false, "Bucket name is empty");
            }

            try
            {
                var deleteObjectsRequest = new DeleteObjectsRequest
                {
                    BucketName = bucketName,
                    Objects = [.. keys.Select(k => new KeyVersion { Key = $"{k}" })]
                };

                var response = await s3Client.DeleteObjectsAsync(deleteObjectsRequest);

                if (response.DeleteErrors.Count > 0)
                {
                    var errors = string.Join(", ", response.DeleteErrors.Select(e => e.Message));
                    return (false, $"Partial failure: {errors}");
                }

                return (true, "All files deleted successfully");
            }
            catch (AmazonS3Exception e)
            {
                Console.WriteLine($"AmazonS3Exception: '{e.Message}'");
                return (false, e.Message);
            }
            catch (Exception e)
            {
                Console.WriteLine($"Exception: '{e.Message}'");
                return (false, e.Message);
            }
        }

        public Task<(bool Success, string Message, string FileUrl)> UploadFileAsync(FileStream data, string key)
        {
            throw new NotImplementedException();
        }
    }
}