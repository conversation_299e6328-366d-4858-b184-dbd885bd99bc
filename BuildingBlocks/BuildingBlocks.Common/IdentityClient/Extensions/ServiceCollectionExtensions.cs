using BuildingBlocks.Common.IdentityClient.Interfaces;
using BuildingBlocks.Common.IdentityClient.Services;
using BuildingBlocks.Common.Interface;
using Microsoft.Extensions.DependencyInjection;
using Refit;
using System.Text.Json;

namespace BuildingBlocks.Common.IdentityClient.Extensions;

/// <summary>
/// Extension methods cho việc đăng ký Identity services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Đăng ký Identity services với Dependency Injection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Cấu hình Identity</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddIdentityClient(this IServiceCollection services, IdentityConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);

        // Validate configuration
        var validationError = configuration.GetValidationError();
        if (validationError != null)
            throw new ArgumentException($"Invalid configuration: {validationError}", nameof(configuration));

        // Đăng ký configuration
        services.AddSingleton(configuration);

        // Cấu hình Refit settings
        var refitSettings = new RefitSettings
        {
            ContentSerializer = new SystemTextJsonContentSerializer(new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            })
        };

        // Đăng ký Refit client với HttpClient
        services.AddRefitClient<IIdentityApi>(refitSettings)
                .ConfigureHttpClient(client =>
                {
                    client.BaseAddress = new Uri(configuration.BaseUrl);
                    client.Timeout = TimeSpan.FromSeconds(configuration.TimeoutSeconds);

                    // Thêm User-Agent header
                    client.DefaultRequestHeaders.Add("User-Agent", "IdentityClient/1.0");
                });

        // Đăng ký IdentityService
        services.AddScoped<IIdentityService, IdentityService>();
        services.AddScoped<IdentityService>();

        return services;
    }

    /// <summary>
    /// Đăng ký Identity services với cấu hình từ action
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Action để cấu hình</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddIdentityClient(this IServiceCollection services, Action<IdentityConfiguration> configureOptions)
    {
        var configuration = new IdentityConfiguration();
        configureOptions(configuration);
        return services.AddIdentityClient(configuration);
    }
}
