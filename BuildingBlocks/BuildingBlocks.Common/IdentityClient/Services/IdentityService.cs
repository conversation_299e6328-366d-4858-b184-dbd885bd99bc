using BuildingBlocks.Common.IdentityClient.Interfaces;
using BuildingBlocks.Common.IdentityClient.Models;
using BuildingBlocks.Common.Interface;

namespace BuildingBlocks.Common.IdentityClient.Services
{
    /// <summary>
    /// Service wrapper cho Identity API với các tính năng bổ sung
    /// </summary>
    public class IdentityService : IIdentityService
    {
        private readonly IIdentityApi _api;
        private readonly IdentityConfiguration _configuration;

        public IdentityService(IIdentityApi api, IdentityConfiguration configuration)
        {
            _api = api ?? throw new ArgumentNullException(nameof(api));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

            // Validate configuration
            var validationError = _configuration.GetValidationError();
            if (validationError != null)
                throw new ArgumentException($"Invalid configuration: {validationError}", nameof(configuration));
        }

        #region LEEON Identity Verification

        /// <summary>
        /// Xác thực eID (CCCD gắn chip) thông qua LEEON
        /// </summary>
        public async Task<IdentityResponse<LeeoneIDVerificationResponse>> VerifyEIDAsync(LeeoneIDVerificationRequest request)
        {
            // Thiết lập thông tin device và project
            SetBaseVerificationInfo(request);

            return await _api.VerifyEIDAsync(request);
        }

        /// <summary>
        /// Xác thực tính sống (liveness detection) thông qua LEEON
        /// </summary>
        public async Task<IdentityResponse<LivenessVerificationResponse>> VerifyLivenessAsync(LeeonLivenessVerificationRequest request)
        {
            // Thiết lập thông tin device và project
            SetBaseVerificationInfo(request);

            return await _api.VerifyLivenessAsync(request);
        }

        /// <summary>
        /// So khớp khuôn mặt (face matching) thông qua LEEON
        /// </summary>
        public async Task<IdentityResponse<LeeonFaceMatchingVerificationResponse>> FaceMatchingAsync(LeeonFaceMatchingVerificationRequest request)
        {
            // Thiết lập thông tin device và project
            SetBaseVerificationInfo(request);

            return await _api.FaceMatchingAsync(request);
        }

        #endregion

        #region LEEON Face Search

        /// <summary>
        /// Thêm khuôn mặt vào hệ thống tìm kiếm
        /// </summary>
        public async Task<IdentityResponse<AddFaceResponse>> AddFaceAsync(AddFaceRequest request)
        {
            // Thiết lập thông tin device và project
            SetBaseVerificationInfo(request);

            // Thêm project name vào metadata
            if (!request.Metadata.ContainsKey("projectName"))
            {
                request.Metadata.Add("projectName", $"{_configuration.Project}_{_configuration.DeviceType}");
            }

            return await _api.AddFaceAsync(request);
        }

        /// <summary>
        /// Tìm kiếm khuôn mặt trong hệ thống
        /// </summary>
        public async Task<IdentityResponse<FindFaceResponse>> FindFaceAsync(FindFaceRequest request)
        {
            // Thiết lập thông tin device và project
            SetBaseVerificationInfo(request);

            return await _api.FindFaceAsync(request);
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Kiểm tra kết quả response có thành công hay không
        /// </summary>
        public bool IsSuccess<T>(IdentityResponse<T> response) where T : class
        {
            return response != null && response.Code == "00";
        }

        /// <summary>
        /// Lấy thông báo lỗi từ response
        /// </summary>
        public string GetErrorMessage<T>(IdentityResponse<T> response) where T : class
        {
            if (response == null)
                return "Response is null";

            if (IsSuccess(response))
                return string.Empty;

            return !string.IsNullOrEmpty(response.Message) ? response.Message : $"Error code: {response.Code}";
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Thiết lập thông tin cơ bản cho request verification
        /// </summary>
        private void SetBaseVerificationInfo(BaseVerificationRequest request)
        {
            request.Project = $"{_configuration.Project}_{_configuration.DeviceType}";
            request.DeviceType = _configuration.DeviceType;
        }

        #endregion
    }
}
