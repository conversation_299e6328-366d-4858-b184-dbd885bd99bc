namespace BuildingBlocks.Common.IdentityClient.Services
{
    /// <summary>
    /// Cấu hình kết nối Identity API
    /// </summary>
    public class IdentityConfiguration
    {
        /// <summary>
        /// URL API Identity
        /// Ví dụ: https://api-dev.gotrust.vn/identity/v1
        /// </summary>
        public string BaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// Timeout cho các request (giây)
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Project name (ví dụ: MEDIPAY_KIOSK, IDPAY...)
        /// </summary>
        public string Project { get; set; } = string.Empty;

        /// <summary>
        /// Loại thiết bị (ví dụ: KIOSK, Mobile)
        /// </summary>
        public string DeviceType { get; set; } = string.Empty;

        /// <summary>
        /// Ngưỡng matching cho face verification (0-100)
        /// </summary>
        public double MatchingThreshold { get; set; } = 82.5;

        /// <summary>
        /// <PERSON><PERSON><PERSON> tra cấu hình có hợp lệ không
        /// </summary>
        /// <returns>True nếu hợp lệ</returns>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(BaseUrl) &&
                   !string.IsNullOrEmpty(Project) &&
                   !string.IsNullOrEmpty(DeviceType) &&
                   TimeoutSeconds > 0 &&
                   MatchingThreshold >= 0 && MatchingThreshold <= 100;
        }

        /// <summary>
        /// Lấy thông báo lỗi cấu hình
        /// </summary>
        /// <returns>Thông báo lỗi hoặc null nếu hợp lệ</returns>
        public string? GetValidationError()
        {
            if (string.IsNullOrEmpty(BaseUrl))
                return "BaseUrl is required";

            if (string.IsNullOrEmpty(Project))
                return "Project is required";

            if (string.IsNullOrEmpty(DeviceType))
                return "DeviceType is required";

            if (TimeoutSeconds <= 0)
                return "TimeoutSeconds must be greater than 0";

            if (MatchingThreshold < 0 || MatchingThreshold > 100)
                return "MatchingThreshold must be between 0 and 100";

            return null;
        }
    }
}
