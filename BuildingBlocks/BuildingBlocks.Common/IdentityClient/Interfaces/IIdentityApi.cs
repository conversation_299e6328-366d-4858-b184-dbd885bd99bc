using BuildingBlocks.Common.IdentityClient.Models;
using Refit;

namespace BuildingBlocks.Common.IdentityClient.Interfaces
{
    /// <summary>
    /// Interface API cho hệ thống Identity - Xác thực khuôn mặt và định danh CCCD
    /// Hỗ trợ các chức năng: LEEON Identity Verification, LEEON Face Search
    /// </summary>
    public interface IIdentityApi
    {

        #region LEEON Identity Verification APIs

        /// <summary>
        /// API xác thực eID (CCCD gắn chip) thông qua LEEON
        /// </summary>
        /// <param name="request">Thông tin xác thực eID</param>
        /// <returns>Kết quả xác thực eID</returns>
        [Post("/identity/leeon/verify/eID")]
        Task<IdentityResponse<LeeoneIDVerificationResponse>> VerifyEIDAsync([Body] LeeoneIDVerificationRequest request);

        /// <summary>
        /// API xác thực tính sống (liveness detection) thông qua LEEON
        /// </summary>
        /// <param name="request">Thông tin ảnh để xác thực tính sống</param>
        /// <returns>Kết quả xác thực tính sống</returns>
        [Post("/identity/leeon/verify/liveness")]
        Task<IdentityResponse<LivenessVerificationResponse>> VerifyLivenessAsync([Body] LeeonLivenessVerificationRequest request);

        /// <summary>
        /// API so khớp khuôn mặt (face matching) thông qua LEEON
        /// </summary>
        /// <param name="request">Thông tin 2 ảnh để so khớp</param>
        /// <returns>Kết quả so khớp khuôn mặt</returns>
        [Post("/identity/leeon/verify/face-matching")]
        Task<IdentityResponse<LeeonFaceMatchingVerificationResponse>> FaceMatchingAsync([Body] LeeonFaceMatchingVerificationRequest request);

        #endregion

        #region LEEON Face Search APIs

        /// <summary>
        /// API thêm khuôn mặt vào hệ thống tìm kiếm
        /// </summary>
        /// <param name="request">Thông tin ảnh khuôn mặt và metadata</param>
        /// <returns>Kết quả thêm khuôn mặt</returns>
        [Post("/identity/leeon/facesearch/add-face")]
        Task<IdentityResponse<AddFaceResponse>> AddFaceAsync([Body] AddFaceRequest request);

        /// <summary>
        /// API tìm kiếm khuôn mặt trong hệ thống
        /// </summary>
        /// <param name="request">Thông tin ảnh để tìm kiếm</param>
        /// <returns>Danh sách khuôn mặt tương tự</returns>
        [Post("/identity/leeon/facesearch/find-face")]
        Task<IdentityResponse<FindFaceResponse>> FindFaceAsync([Body] FindFaceRequest request);

        #endregion
    }
}
