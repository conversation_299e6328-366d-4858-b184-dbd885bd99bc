using Newtonsoft.Json.Linq;

namespace BuildingBlocks.Common.IdentityClient.Models
{
    /// <summary>
    /// Base response model cho Identity API
    /// </summary>
    /// <typeparam name="T">Kiểu dữ liệu response</typeparam>
    public class IdentityResponse<T> where T : class
    {
        /// <summary>
        /// Mã phản hồi (00: thành công, 99: lỗi hệ thống)
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Thông báo phản hồi
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Dữ liệu phản hồi
        /// </summary>
        public T? Data { get; set; }
    }

    /// <summary>
    /// Base request cho các API xác thực
    /// </summary>
    public class BaseVerificationRequest
    {
        /// <summary>
        /// Project: MEDIPAY_KIOSK, IDPAY...
        /// </summary>
        public string? Project { get; set; }

        /// <summary>
        /// Lo<PERSON><PERSON> thiết bị mà người dùng cuối sử dụng để request API (ví dụ: KIOSK|Mobile)
        /// </summary>
        public string? DeviceType { get; set; }

        /// <summary>
        /// Mã partner: mã bệnh viện, mã đối tác uchi, mã đơn vị ủy quyền...
        /// </summary>
        public string? PartnerId { get; set; }

        /// <summary>
        /// Mã kiosk- Harddisk serialNumber
        /// </summary>
        public string? DeviceId { get; set; }

        /// <summary>
        /// Mã onboard của hệ thống gọi API, mỗi OnBoardId có thể có nhiều request
        /// </summary>
        public string? OnBoardId { get; set; }
    }

    /// <summary>
    /// Metadata cho Face Search
    /// </summary>
    public class FindFaceMetadataResponse
    {
        public string? dateOfBirth { get; set; }
        public string? _id { get; set; }
        public string? fullName { get; set; }
        public string? phone { get; set; }
        public string? sex { get; set; }
        public string? id { get; set; }
        public string? identityNo { get; set; }
        public string? customerId { get; set; }
    }

    /// <summary>
    /// Dữ liệu kết quả tìm kiếm khuôn mặt
    /// </summary>
    public class FindFaceDataResponse
    {
        public string? Distance { get; set; }
        public FindFaceMetadataResponse? Metadata { get; set; }
        public string? Url { get; set; }
        public long? FaceId { get; set; }
    }
}
