using Newtonsoft.Json.Linq;

namespace BuildingBlocks.Common.IdentityClient.Models
{

    #region LEEON Identity Verification Requests

    /// <summary>
    /// Request xác thực eID (CCCD gắn chip) thông qua LEEON
    /// </summary>
    public class LeeoneIDVerificationRequest : BaseVerificationRequest
    {
        /// <summary>
        /// Cert đọc được từ SODFile trong CCCD gắn chip dưới định dạng PEMfile covert sang base64 sử dụng encoding ASCII.
        /// </summary>
        public string? DsCert { get; set; }

        /// <summary>
        /// Số căn cước công dân của người dân gửi xác thực.
        /// </summary>
        public string? IdCard { get; set; }

        /// <summary>
        /// Mã của partner/subpartner
        /// </summary>
        public string? Province { get; set; }
    }

    /// <summary>
    /// Request xác thực tính sống (liveness detection) thông qua LEEON
    /// </summary>
    public class LeeonLivenessVerificationRequest : BaseVerificationRequest
    {
        /// <summary>
        /// Ảnh chụp khuôn mặt bên trái dạng Base64
        /// </summary>
        public string? PortraitLeft { get; set; }

        /// <summary>
        /// Ảnh chụp khuôn mặt chính diện dạng Base64
        /// </summary>
        public string? PortraitMid { get; set; }

        /// <summary>
        /// Ảnh chụp khuôn mặt bên phải dạng Base64
        /// </summary>
        public string? PortraitRight { get; set; }
    }

    /// <summary>
    /// Request so khớp khuôn mặt (face matching) thông qua LEEON
    /// </summary>
    public class LeeonFaceMatchingVerificationRequest : BaseVerificationRequest
    {
        /// <summary>
        /// Ảnh trong NFC dạng base64
        /// </summary>
        public string? Img1 { get; set; }

        /// <summary>
        /// Ảnh chụp khuôn mặt chính diện dạng Base64
        /// </summary>
        public string? Img2 { get; set; }
    }

    #endregion

    #region LEEON Face Search Requests

    /// <summary>
    /// Request thêm khuôn mặt vào hệ thống tìm kiếm
    /// </summary>
    public class AddFaceRequest : BaseVerificationRequest
    {
        /// <summary>
        /// Ảnh chụp khuôn mặt dạng Base64
        /// </summary>
        public string? Img { get; set; }

        /// <summary>
        /// Metadata của ảnh chụp khuôn mặt dạng object json
        /// </summary>
        public JObject Metadata { get; set; } = [];
    }

    /// <summary>
    /// Request tìm kiếm khuôn mặt trong hệ thống
    /// </summary>
    public class FindFaceRequest : BaseVerificationRequest
    {
        /// <summary>
        /// Ảnh chụp khuôn mặt dạng Base64
        /// </summary>
        public string? Img { get; set; }
    }

    #endregion
}
