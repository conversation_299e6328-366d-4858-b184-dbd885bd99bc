using Newtonsoft.Json.Linq;

namespace BuildingBlocks.Common.IdentityClient.Models
{

    #region LEEON Identity Verification Responses

    /// <summary>
    /// Response xác thực eID (CCCD gắn chip)
    /// </summary>
    public class LeeoneIDVerificationResponse
    {
        /// <summary>
        /// Id giao dịch
        /// </summary>
        public string? TransactionId { get; set; }

        /// <summary>
        /// Boolean – True nếu được xác thực, False nếu không được xác thực
        /// </summary>
        public bool IsValidIdCard { get; set; }

        public long Time { get; set; }
        public string? Signature { get; set; }
        public string? Message { get; set; }
        public int ExitCode { get; set; }
    }

    /// <summary>
    /// Response xác thực tính sống (liveness detection)
    /// </summary>
    public class LivenessVerificationResponse
    {
        public string TransactionId { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public long Time { get; set; }
        public string ExitCode { get; set; } = string.Empty;

        /// <summary>
        /// Warning code: có thể tùy chỉnh theo từng trường hợp xác thực cụ thể
        /// </summary>
        public string WarningCode { get; set; } = string.Empty;

        /// <summary>
        /// WarningMessage: có thể tùy chỉnh theo từng trường hợp xác thực cụ thể
        /// </summary>
        public string WarningMessage { get; set; } = string.Empty;

        public string MatchingMidLeft { get; set; } = string.Empty;
        public string MatchingMidRight { get; set; } = string.Empty;
        public bool IsValid { get; set; }
    }

    /// <summary>
    /// Response so khớp khuôn mặt (face matching)
    /// </summary>
    public class LeeonFaceMatchingVerificationResponse
    {
        /// <summary>
        /// Mã giao dịch của hệ thống VMG
        /// </summary>
        public string? TransactionId { get; set; }

        /// <summary>
        /// Message trả về từ hệ thống nhận diện khuôn mặt
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// Mã lỗi từ hệ thống nhận diện khuôn mặt.
        /// </summary>
        public string? ExitCode { get; set; }

        /// <summary>
        /// Warning code: có thể tùy chỉnh theo từng trường hợp xác thực cụ thể
        /// </summary>
        public string? WarningCode { get; set; }

        /// <summary>
        /// WarningMessage: có thể tùy chỉnh theo từng trường hợp xác thực cụ thể
        /// </summary>
        public string? WarningMessage { get; set; }

        /// <summary>
        /// Thời gian
        /// </summary>
        public long Time { get; set; }

        /// <summary>
        /// True/False nếu hình ở 2 khuôn mặt trùng khớp của bệnh viện
        /// </summary>
        public bool IsMatching { get; set; }

        /// <summary>
        /// True/False nếu hình ở 2 khuôn mặt trùng khớp của ngân hàng
        /// </summary>
        public bool IsMatchingByBank { get; set; }

        /// <summary>
        /// Điểm từ 0 tới 1 về độ chính xác chân dung ở hình cung cấp.
        /// </summary>
        public string? Face1Score { get; set; }

        /// <summary>
        /// Điểm từ 0 tới 1 về độ chính xác chân dung ở hình cung cấp.
        /// </summary>
        public string? Face2Score { get; set; }

        /// <summary>
        /// Điểm từ 0 tới 100 về xác thực khuôn mặt
        /// </summary>
        public string? Matching { get; set; }
    }

    #endregion

    #region LEEON Face Search Responses

    /// <summary>
    /// Response thêm khuôn mặt vào hệ thống
    /// </summary>
    public class AddFaceResponse
    {
        /// <summary>
        /// Ảnh chụp khuôn mặt dạng Base64
        /// </summary>
        public string? Img { get; set; }

        /// <summary>
        /// Metadata của ảnh chụp khuôn mặt dạng object json
        /// </summary>
        public JObject? Metadata { get; set; }
    }

    /// <summary>
    /// Response tìm kiếm khuôn mặt
    /// </summary>
    public class FindFaceResponse
    {
        public string? TransactionId { get; set; }
        public string? Message { get; set; }
        public int ExitCode { get; set; }
        public List<FindFaceDataResponse>? Result { get; set; }
    }

    #endregion
}
