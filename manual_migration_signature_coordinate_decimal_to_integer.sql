-- Manual Migration: Change SignatureCoordinate decimal properties to integer
-- Execute this script manually after code changes are deployed
-- Database: PostgreSQL

-- WARNING: This migration will truncate decimal values to integers
-- Make sure to backup your data before running this script

BEGIN;

-- Step 1: Add temporary columns with integer type
ALTER TABLE "SignatureCoordinates" 
ADD COLUMN "CoordinateX_temp" integer,
ADD COLUMN "CoordinateY_temp" integer,
ADD COLUMN "Width_temp" integer,
ADD COLUMN "Height_temp" integer;

-- Step 2: Copy data from decimal columns to integer columns (truncating decimal values)
UPDATE "SignatureCoordinates" 
SET 
    "CoordinateX_temp" = CAST("CoordinateX" AS integer),
    "CoordinateY_temp" = CAST("CoordinateY" AS integer),
    "Width_temp" = CAST("Width" AS integer),
    "Height_temp" = CAST("Height" AS integer);

-- Step 3: Drop the old decimal columns
ALTER TABLE "SignatureCoordinates" 
DROP COLUMN "CoordinateX",
DROP COLUMN "CoordinateY",
DROP COLUMN "Width",
DROP COLUMN "Height";

-- Step 4: Rename temporary columns to original names
ALTER TABLE "SignatureCoordinates" 
RENAME COLUMN "CoordinateX_temp" TO "CoordinateX";

ALTER TABLE "SignatureCoordinates" 
RENAME COLUMN "CoordinateY_temp" TO "CoordinateY";

ALTER TABLE "SignatureCoordinates" 
RENAME COLUMN "Width_temp" TO "Width";

ALTER TABLE "SignatureCoordinates" 
RENAME COLUMN "Height_temp" TO "Height";

-- Step 5: Add NOT NULL constraints back
ALTER TABLE "SignatureCoordinates" 
ALTER COLUMN "CoordinateX" SET NOT NULL,
ALTER COLUMN "CoordinateY" SET NOT NULL,
ALTER COLUMN "Width" SET NOT NULL,
ALTER COLUMN "Height" SET NOT NULL;

-- Verify the changes
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'SignatureCoordinates' 
    AND column_name IN ('CoordinateX', 'CoordinateY', 'Width', 'Height')
ORDER BY column_name;

COMMIT;

-- Expected output after migration:
-- column_name  | data_type | is_nullable | column_default
-- CoordinateX  | integer   | NO          | 
-- CoordinateY  | integer   | NO          | 
-- Height       | integer   | NO          | 
-- Width        | integer   | NO          |
