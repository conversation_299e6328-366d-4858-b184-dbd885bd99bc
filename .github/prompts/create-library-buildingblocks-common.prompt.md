---
mode: 'agent'
tools: ['changes', 'codebase', 'editFiles', 'problems']
description: 'Standard instructions for creating a new code library in BuildingBlocks.Common.'
---

# Create Code Library in BuildingBlocks.Common

## Instructions

- When asked to create a new code library, create a new subfolder under `BuildingBlocks/BuildingBlocks.Common/` named after the library.
- Inside the library folder, create subfolders as needed:
  - `Extensions/` – Extension methods for the library.
  - `Interfaces/` – Interfaces for the library, including HTTP API clients (use Refit for API clients).
  - `Models/` – Models, DTOs, or entities related to the library.
  - `Services/` – Service implementations and business logic for the library.
- Add a `README.md` describing the library's purpose, usage, and main components.
- Do not create a new project or modify `.csproj` files.
- Do not duplicate existing libraries; check for similar folders before creating a new one.
- For HTTP API clients, use Refit in the interface definition.
- If a global interface is needed for use across the repository, place it in `BuildingBlocks.Common/Interfaces/`.
- Follow repository coding standards and folder structure conventions.

## Folder structure:
```
BuildingBlocks/
  BuildingBlocks.Common/
    MyNewLibrary/
      Extensions/
        MyNewLibraryExtensions.cs
          IServiceCollection AddMyNewLibrary(this IServiceCollection services, MyNewLibraryConfiguration configuration)
          IServiceCollection AddMyNewLibrary(this IServiceCollection services, Action<MyNewLibraryConfiguration> configureOptions)
          ... // Other extension methods
      Interfaces/
        IMyNewLibraryApi.cs // Use Refit for HTTP API clients, implemented interface in the same folder (optional)
        ... // Other interfaces
      Models/
        MyNewLibraryModel.cs
        ... // Other models or DTOs
      Services/
        MyGlobalLibraryService.cs // Service implementation for IMyGlobalLibraryService
      README.md // Description of the library, usage, and main components
    Interfaces/
      IMyGlobalLibraryService.cs // Global interface for use across the library
```

## Example Usage
- "Create a new library called 'LocalCA' in BuildingBlocks.Common."
- "Add a folder for 'LocalCA' under BuildingBlocks.Common and include a README.md."

---

For more details, see the existing EasyCA library folders for reference.
