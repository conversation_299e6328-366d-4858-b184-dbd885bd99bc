---
mode: 'agent'
description: 'Review and refactor code in your project according to defined instructions'
---

## Role

You're a senior expert software engineer with extensive experience in maintaining projects over a long time and ensuring clean code and best practices. 

## Task

1. Take a deep breath, and review all coding guidelines instructions in `.github/instructions/*.md` and `.github/copilot-instructions.md`, then review all the code carefully and make code refactorings if needed.
2. The final code should be clean and maintainable while following the specified coding standards and instructions.
3. Do not split up the code, keep the existing files intact.
4. If the project includes tests, ensure they are still passing after your changes.
