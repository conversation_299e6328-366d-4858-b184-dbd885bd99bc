---
description: 'Create ASP.NET Minimal API endpoints with proper OpenAPI documentation'
applyTo: '**/*.cs, **/*.json, **/specs/*.md'
---

# GoTRUST EMR API - AI Coding Agent Instructions

## Architecture Overview

This is a **Clean Architecture ASP.NET Core Web API** for Electronic Medical Records (EMR) with **digital signature integration**. The solution follows **CQRS + MediatR** patterns with **<PERSON> for minimal APIs**.

### Solution Structure
- **`Services/GoTRUST.EMR.API`**: Web API entry point, Carter endpoints, SignalR hubs
- **`Services/GoTRUST.EMR.Application`**: Business logic, CQRS handlers, MediatR features  
- **`Services/GoTRUST.EMR.Domain`**: Domain models, enums, constants
- **`Services/GoTRUST.EMR.Infrastructure`**: EF Core, migrations, external integrations
- **`BuildingBlocks/`**: Shared libraries (CQRS interfaces, behaviors, exceptions, HTTP clients)

## Key Development Patterns

### API Endpoints
- Use **Carter modules** in `Services/GoTRUST.EMR.API/Endpoints/` instead of controllers. Example pattern:
```csharp
// In Services/GoTRUST.EMR.API/Endpoints/{Entity}/{Entity}Module.cs
public class PatientModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGroup("api/patients")
           .WithTags("Patients")
           .MapGet("/", GetPatientsAsync)
           .RequireAuthorization();
    }
}
```
- All routes prefixed with `/emr-management` via `app.UsePathBase("/emr-management")`
- Pattern: `DigitalSignatureModule.cs` shows Carter endpoint registration

### CQRS Implementation
- Commands/Queries implement `ICommand<TResponse>` or `IQuery<TResponse>` from `BuildingBlocks/CQRS/`
- Handlers in `Services/GoTRUST.EMR.Application/Features/{Entity}/Commands|Queries/`
- Example: `CreateMedicalRecordCommand.cs` shows proper CQRS structure

### Service Registration
- Each layer has `DependencyInjection.cs` with extension methods:
  - `AddApplicationServices()` - MediatR, validation, Hangfire
  - `AddInfrastructureServices()` - EF Core, repositories  
  - `AddApiServices()` - JWT, Swagger, CORS, logging

### Database & Migrations
- **PostgreSQL** with EF Core, models in `Domain/Models/`
- Use VS Code task `Add EF Migration` (prompts for name)
- Context: `ApplicationDbContext` with soft delete via `HasQueryFilter(x => x.IsDeleted != true)`

## External Integration Patterns

### Digital Signature (EasySign)
- Located in `BuildingBlocks/BuildingBlocks.Common/EasySign/`
- Refit-based HTTP client with interfaces like `IEasySignApi.cs`
- Registration: `services.AddEasySignDemo()` or `services.AddEasySignProduction()`
- Used for PDF/XML signing in medical records

### Configuration Management
- Environment variables in `Properties/launchSettings.json`
- Key variables: `EMR_BASE_URL`, `EASY_SIGN_*`, `DISCORD_LOGGING_*`
- Integration configs in `BuildingBlocks.Common` for external APIs

## Essential Development Workflows

### Build & Run
```bash
# Build (VS Code task available)
dotnet build Services/GoTRUST.EMR.API/GoTRUST.EMR.API.csproj

# Watch mode (VS Code task available) 
dotnet watch run --project Services/GoTRUST.EMR.API/GoTRUST.EMR.API.csproj
```

### Adding New Features
1. Create command/query in `Application/Features/{Entity}/`
2. Add Carter module in `API/Endpoints/{Entity}/`
3. Register services in appropriate `DependencyInjection.cs`
4. Add migrations if schema changes needed

### Logging & Monitoring
- **Serilog** with Discord webhook for errors (see `DependencyInjection.cs`)
- **SignalR Hub** at `/notification` for real-time updates
- **Hangfire** for background jobs with MongoDB storage

## Critical Conventions

### Entity Framework
- All entities have soft delete: `IsDeleted`, `CreatedAt`, `CreatedBy`, `UpdatedAt`, `UpdatedBy`
- Use `HasQueryFilter(x => x.IsDeleted != true)` in configurations
- Migrations stored in `Infrastructure/Migrations/`

### API Responses
- Use `Response<T>` wrapper from `BuildingBlocks/Abstractions/`
- Consistent error handling via `BuildingBlocks/Exceptions/`

### Authentication & Authorization  
- JWT Bearer tokens with role-based access
- Roles defined in `DependencyInjection.cs`: `HOSPITAL_DIRECTOR`, `NURSE_DOCTOR`, etc.

## Integration Points
- **EasySign**: Digital signature service for medical documents
- **Discord**: Error logging via webhook
- **MongoDB**: Hangfire background job storage
- **SignalR**: Real-time notifications
- **PostgreSQL**: Primary data storage

Remember: This is a **Vietnamese healthcare system** - many entities and comments are in Vietnamese for EMR compliance.
