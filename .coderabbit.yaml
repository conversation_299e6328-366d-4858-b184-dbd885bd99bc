# yaml-language-server: $schema=https://coderabbit.ai/integrations/schema.v2.json
language: "en-US"
early_access: true
reviews:
  profile: "assertive"
  request_changes_workflow: false
  high_level_summary: true
  poem: true
  review_status: true
  collapse_walkthrough: false
  auto_review:
    enabled: true
    drafts: false
    base_branches: ["dev", "main"]
  path_instructions:
    - path: "**/*.cs"
      instructions: |
        - Apply C# best practices and clean code principles
        - Adhere to SOLID principles
        - Validate error handling and input validation logic
        - Evaluate API design, including RESTful patterns and security
        - Verify correct usage of dependency injection and abstractions
        - Don't review using statements
    - path: "**/appsettings*.json"
      instructions: |
        - Review configuration security (no sensitive data in plain text)
        - Check for proper environment-specific settings
    - path: "**/*.csproj"
      instructions: |
        - Review package versions for security vulnerabilities
        - Check for consistent versioning across projects
chat:
  auto_reply: true
